# Scripts Directory

This directory contains utility scripts for the Yemen Market Integration project.

## Script Categories

### Analysis Scripts
Core econometric analysis and result generation scripts.

- **run_analysis.py** - Main analysis runner for three-tier econometric models
- **generate_executive_results.py** - Generate executive summary with key findings
- **generate_world_bank_results.py** - Format results for World Bank publication
- **generate_report.py** - Generate comprehensive analysis reports

### Data Collection (`data_collection/`)
Scripts for downloading data from external sources.

- **download_data.py** - Master script to download all data sources
- **download_acled_data.py** - Download ACLED conflict event data

### Data Processing (`data_processing/`)
Scripts for processing raw data into analysis-ready formats.

- **process_acled_data.py** - Process ACLED conflict events
- **process_wfp_data.py** - Process WFP price data
- **process_acaps_data.py** - Process ACAPS control area data
- **run_spatial_joins.py** - Perform spatial joins between datasets

### Analysis Utilities (`analysis/`)
Specialized analysis scripts.

- **analyze_price_transmission.py** - Price transmission analysis
- **create_balanced_panel.py** - Create balanced panel dataset
- **prepare_data_for_modeling.py** - Prepare data for econometric models

### Dashboards
Interactive visualization tools.

- **streamlit_dashboard.py** - Interactive Streamlit dashboard
- **yemen_market_dashboard.py** - Yemen-specific market dashboard

### Testing & Validation
Scripts for testing and validation.

- **run_tests.py** - Run test suite
- **run_validation_suite.py** - Run comprehensive validation
- **simple_validation_test.py** - Basic validation tests
- **test_validation_system.py** - Test the validation framework
- **generate_final_summary.py** - Generate validation summary
- **generate_coverage_report.py** - Generate test coverage report

### API Server
- **start_api_server.py** - Start the REST API server (supports V1 and V2)

## Usage Examples

### Running Analysis
```bash
# Run complete three-tier analysis
python scripts/run_analysis.py

# Generate executive summary
python scripts/generate_executive_results.py

# Generate World Bank formatted results
python scripts/generate_world_bank_results.py
```

### Data Pipeline
```bash
# Download all data sources
python scripts/data_collection/download_data.py

# Process WFP price data
python scripts/data_processing/process_wfp_data.py

# Create balanced panel
python scripts/analysis/create_balanced_panel.py
```

### Dashboards
```bash
# Launch interactive dashboard
python scripts/streamlit_dashboard.py

# Launch Yemen market dashboard
python scripts/yemen_market_dashboard.py
```

### Testing
```bash
# Run all tests
python scripts/run_tests.py

# Run validation suite
python scripts/run_validation_suite.py

# Generate coverage report
python scripts/generate_coverage_report.py
```

## Script Dependencies

Most scripts require:
- Python 3.9+
- Project dependencies installed: `pip install -r requirements.txt`
- Environment variables configured: `cp .env.example .env`

## Notes

- V2 deployment scripts have been archived in `archive/v2-deployment-scripts/`
- For V2 API usage, use `python src/main.py` instead of scripts
- Dashboard scripts require additional dependencies (streamlit)

---
*Last updated: June 2, 2025*