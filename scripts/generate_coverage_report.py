#!/usr/bin/env python3
"""
Generate comprehensive test coverage report for Yemen Market Integration Platform.

This script runs pytest with coverage analysis and generates multiple report formats.
It also creates coverage badges and integrates with CI/CD pipelines.
"""

import subprocess
import sys
import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from yemen_market.utils.logging import info, warning, error, timer, log_data_shape


def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    required = ['pytest', 'pytest-cov', 'coverage']
    missing = []
    
    for package in required:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing.append(package)
    
    if missing:
        error(f"Missing dependencies: {', '.join(missing)}")
        error("Run: pip install pytest pytest-cov coverage")
        return False
    
    return True


def run_coverage_analysis(
    source_dirs: List[str] = ['src/yemen_market'],
    test_dirs: List[str] = ['tests'],
    min_coverage: float = 90.0,
    generate_html: bool = True,
    generate_xml: bool = True,
    generate_json: bool = True,
    generate_badge: bool = True
) -> Tuple[bool, Dict[str, any]]:
    """
    Run test coverage analysis.
    
    Args:
        source_dirs: Directories containing source code
        test_dirs: Directories containing tests
        min_coverage: Minimum acceptable coverage percentage
        generate_html: Generate HTML report
        generate_xml: Generate XML report (for CI/CD)
        generate_json: Generate JSON report
        generate_badge: Generate coverage badge
        
    Returns:
        Tuple of (success, coverage_stats)
    """
    
    with timer("coverage_analysis"):
        # Build pytest command
        cmd = [
            'pytest',
            '--cov=' + ','.join(source_dirs),
            '--cov-report=term-missing:skip-covered',
            '--cov-fail-under=' + str(min_coverage),
            '-v'
        ]
        
        # Add test directories
        cmd.extend(test_dirs)
        
        # Add report formats
        if generate_html:
            cmd.append('--cov-report=html')
        if generate_xml:
            cmd.append('--cov-report=xml')
        if generate_json:
            cmd.append('--cov-report=json')
            
        info(f"Running coverage analysis: {' '.join(cmd)}")
        
        # Run pytest with coverage
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # Parse results
        success = result.returncode == 0
        coverage_stats = parse_coverage_output(result.stdout)
        
        if not success:
            error("Coverage analysis failed!")
            error(result.stderr)
        else:
            info(f"Coverage: {coverage_stats.get('total_coverage', 0):.1f}%")
            
        # Generate badge if requested
        if generate_badge and coverage_stats.get('total_coverage'):
            generate_coverage_badge(coverage_stats['total_coverage'])
            
        return success, coverage_stats


def parse_coverage_output(output: str) -> Dict[str, any]:
    """Parse coverage output to extract statistics."""
    stats = {
        'total_coverage': 0,
        'files_covered': 0,
        'lines_total': 0,
        'lines_covered': 0,
        'lines_missing': 0,
        'branches_total': 0,
        'branches_covered': 0,
        'modules': {}
    }
    
    # Extract total coverage percentage
    for line in output.split('\n'):
        if 'TOTAL' in line and '%' in line:
            parts = line.split()
            for i, part in enumerate(parts):
                if part == 'TOTAL':
                    # Find percentage
                    for j in range(i, len(parts)):
                        if '%' in parts[j]:
                            stats['total_coverage'] = float(parts[j].rstrip('%'))
                            break
                    # Extract other stats
                    if i + 1 < len(parts):
                        stats['lines_total'] = int(parts[i + 1])
                    if i + 2 < len(parts):
                        stats['lines_missing'] = int(parts[i + 2])
                    stats['lines_covered'] = stats['lines_total'] - stats['lines_missing']
                    break
    
    # Load detailed stats from JSON if available
    json_path = Path('coverage.json')
    if json_path.exists():
        with open(json_path) as f:
            coverage_data = json.load(f)
            
        stats['files_covered'] = len(coverage_data.get('files', {}))
        
        # Extract module-level coverage
        for file_path, file_data in coverage_data.get('files', {}).items():
            module_name = file_path.replace('/', '.').replace('.py', '')
            stats['modules'][module_name] = {
                'coverage': file_data['summary']['percent_covered'],
                'lines_covered': file_data['summary']['covered_lines'],
                'lines_total': file_data['summary']['num_statements']
            }
    
    return stats


def generate_coverage_badge(coverage_percentage: float) -> None:
    """Generate coverage badge SVG."""
    # Determine color based on coverage
    if coverage_percentage >= 90:
        color = 'brightgreen'
    elif coverage_percentage >= 80:
        color = 'green'
    elif coverage_percentage >= 70:
        color = 'yellowgreen'
    elif coverage_percentage >= 60:
        color = 'yellow'
    elif coverage_percentage >= 50:
        color = 'orange'
    else:
        color = 'red'
    
    # Create badge directory
    badge_dir = Path('docs/badges')
    badge_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate badge using shields.io format
    badge_svg = f"""<svg xmlns="http://www.w3.org/2000/svg" width="114" height="20">
    <linearGradient id="b" x2="0" y2="100%">
        <stop offset="0" stop-color="#bbb" stop-opacity=".1"/>
        <stop offset="1" stop-opacity=".1"/>
    </linearGradient>
    <mask id="a">
        <rect width="114" height="20" rx="3" fill="#fff"/>
    </mask>
    <g mask="url(#a)">
        <path fill="#555" d="M0 0h63v20H0z"/>
        <path fill="{color}" d="M63 0h51v20H63z"/>
        <path fill="url(#b)" d="M0 0h114v20H0z"/>
    </g>
    <g fill="#fff" text-anchor="middle" font-family="DejaVu Sans,Verdana,Geneva,sans-serif" font-size="11">
        <text x="31.5" y="15" fill="#010101" fill-opacity=".3">coverage</text>
        <text x="31.5" y="14">coverage</text>
        <text x="87.5" y="15" fill="#010101" fill-opacity=".3">{coverage_percentage:.1f}%</text>
        <text x="87.5" y="14">{coverage_percentage:.1f}%</text>
    </g>
</svg>"""
    
    badge_path = badge_dir / 'coverage.svg'
    with open(badge_path, 'w') as f:
        f.write(badge_svg)
    
    info(f"Coverage badge generated: {badge_path}")


def generate_coverage_report(
    stats: Dict[str, any],
    output_path: Path = Path('reports/testing/coverage_report.md')
) -> None:
    """Generate detailed coverage report."""
    
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    report = f"""# Test Coverage Report

**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Overall Coverage

- **Total Coverage**: {stats['total_coverage']:.1f}%
- **Files Covered**: {stats['files_covered']}
- **Lines Covered**: {stats['lines_covered']:,} / {stats['lines_total']:,}
- **Lines Missing**: {stats['lines_missing']:,}

## Coverage by Module

| Module | Coverage | Lines Covered | Total Lines |
|--------|----------|---------------|-------------|
"""
    
    # Sort modules by coverage (lowest first)
    sorted_modules = sorted(
        stats['modules'].items(),
        key=lambda x: x[1]['coverage']
    )
    
    for module, module_stats in sorted_modules:
        coverage = module_stats['coverage']
        # Add emoji indicator
        if coverage >= 90:
            indicator = '✅'
        elif coverage >= 80:
            indicator = '🟡'
        else:
            indicator = '❌'
            
        report += f"| {module} | {indicator} {coverage:.1f}% | {module_stats['lines_covered']} | {module_stats['lines_total']} |\n"
    
    report += f"""
## Test Execution Details

- **HTML Report**: [htmlcov/index.html](../../htmlcov/index.html)
- **XML Report**: coverage.xml (for CI/CD integration)
- **JSON Report**: coverage.json (for programmatic access)

## Coverage Standards

- **Target**: >90% (PRD requirement)
- **Current Status**: {'✅ PASSING' if stats['total_coverage'] >= 90 else '❌ FAILING'}

## Improving Coverage

To improve test coverage:

1. Focus on modules with low coverage (see table above)
2. Add tests for edge cases and error conditions
3. Test all public APIs and critical paths
4. Use mocks for external dependencies

## Running Coverage Locally

```bash
# Basic coverage
make test-coverage

# With specific options
pytest --cov=yemen_market --cov-report=html --cov-fail-under=90

# Generate all reports
python scripts/generate_coverage_report.py
```
"""
    
    with open(output_path, 'w') as f:
        f.write(report)
    
    info(f"Coverage report written to: {output_path}")


def setup_github_actions(
    coverage_threshold: float = 90.0
) -> None:
    """Generate GitHub Actions workflow for coverage reporting."""
    
    workflow_dir = Path('.github/workflows')
    workflow_dir.mkdir(parents=True, exist_ok=True)
    
    workflow = f"""name: Test Coverage

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  coverage:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"
    
    - name: Run tests with coverage
      run: |
        pytest --cov=yemen_market --cov-report=xml --cov-report=html --cov-fail-under={coverage_threshold}
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: true
        verbose: true
    
    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: htmlcov/
    
    - name: Comment coverage on PR
      if: github.event_name == 'pull_request'
      uses: py-cov-action/python-coverage-comment-action@v3
      with:
        GITHUB_TOKEN: ${{{{ github.token }}}}
        MINIMUM_GREEN: {coverage_threshold}
"""
    
    workflow_path = workflow_dir / 'coverage.yml'
    with open(workflow_path, 'w') as f:
        f.write(workflow)
    
    info(f"GitHub Actions workflow created: {workflow_path}")


def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Generate comprehensive test coverage reports'
    )
    parser.add_argument(
        '--min-coverage',
        type=float,
        default=90.0,
        help='Minimum coverage threshold (default: 90.0)'
    )
    parser.add_argument(
        '--no-html',
        action='store_true',
        help='Skip HTML report generation'
    )
    parser.add_argument(
        '--no-badge',
        action='store_true',
        help='Skip badge generation'
    )
    parser.add_argument(
        '--setup-ci',
        action='store_true',
        help='Setup GitHub Actions workflow'
    )
    parser.add_argument(
        '--v2',
        action='store_true',
        help='Include V2 code in coverage analysis'
    )
    
    args = parser.parse_args()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Determine source directories
    source_dirs = ['src/yemen_market']
    test_dirs = ['tests']
    
    if args.v2:
        source_dirs.append('v2/src')
        test_dirs.append('v2/tests')
        info("Including V2 code in coverage analysis")
    
    # Run coverage analysis
    success, stats = run_coverage_analysis(
        source_dirs=source_dirs,
        test_dirs=test_dirs,
        min_coverage=args.min_coverage,
        generate_html=not args.no_html,
        generate_badge=not args.no_badge
    )
    
    # Generate detailed report
    if stats.get('total_coverage'):
        generate_coverage_report(stats)
    
    # Setup CI if requested
    if args.setup_ci:
        setup_github_actions(args.min_coverage)
    
    # Exit with appropriate code
    if not success:
        error(f"Coverage below threshold: {stats.get('total_coverage', 0):.1f}% < {args.min_coverage}%")
        sys.exit(1)
    else:
        info("Coverage analysis completed successfully!")
        info(f"View HTML report: file://{os.path.abspath('htmlcov/index.html')}")


if __name__ == '__main__':
    main()