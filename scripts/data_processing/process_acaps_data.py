#!/usr/bin/env python3
"""Script to download and process ACAPS areas of control data.

This script:
1. Downloads ACAPS control zone data from HDX
2. Processes bi-weekly updates into time series
3. Creates monthly aligned dataset for analysis
4. Generates summary statistics
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.data.hdx_client import HDXClient
from yemen_market.data.acaps_processor import ACAPSProcessor
from yemen_market.utils.logging import (
    setup_logging, info, warning, error, timer, progress, log_data_shape, bind
)

# Set up logging
setup_logging()
bind(module=__name__)


def download_acaps_data():
    """Download ACAPS control zone data from HDX."""
    info("Initializing HDX client for ACAPS data download")

    with timer("download_acaps_data"):
        client = HDXClient()

        # Search for ACAPS Yemen areas of control dataset
        # The dataset ID might be different, we'll search for it
        dataset_ids = [
            "yemen-areas-of-control",
            "yemen-acaps-areas-control",
            "acaps-yemen-analysis-hub"
        ]

        for dataset_id in dataset_ids:
            try:
                info(f"Trying to download dataset: {dataset_id}")
                filepath = client.download_dataset(
                    dataset_id,
                    resource_filter=lambda r: 'control' in r.get('name', '').lower()
                                           or 'areas' in r.get('name', '').lower()
                )
                if filepath:
                    info(f"Successfully downloaded ACAPS data: {filepath}")
                    return filepath
            except Exception as e:
                warning(f"Failed to download {dataset_id}: {e}")
                continue

        error("Could not download ACAPS data from any known dataset ID")
        return None


def process_acaps_data():
    """Process ACAPS control zone data."""
    info("Starting ACAPS data processing")

    with timer("process_acaps_data"):
        processor = ACAPSProcessor()

        # Process all files in the ACAPS directory
        info("Processing all ACAPS control files")
        control_data = processor.process_all_files()

        if control_data.empty:
            warning("No ACAPS data found to process")
            return

        log_data_shape("control_data", control_data)
        info(f"Processed {len(control_data)} control zone records")
        info(f"Date range: {control_data['date'].min()} to {control_data['date'].max()}")
        info(f"Number of districts: {control_data[['governorate', 'district']].drop_duplicates().shape[0]}")

        # Create time series
        info("Creating control zone time series")
        time_series = processor.create_control_time_series(control_data)

        if not time_series.empty:
            log_data_shape("time_series", time_series)
            # Count control changes
            n_changes = time_series['control_changed'].sum()
            info(f"Total control changes detected: {n_changes}")

            # Show transition types
            transitions = time_series[time_series['control_changed']]['transition_type'].value_counts()
            info("Control zone transitions:")
            for transition, count in transitions.items():
                info(f"  {transition}: {count}")

        # Align to monthly frequency
        info("Aligning data to monthly frequency")
        monthly_data = processor.align_to_monthly(time_series)

        if not monthly_data.empty:
            log_data_shape("monthly_data", monthly_data)
            info(f"Created monthly dataset with {len(monthly_data)} records")

            # Show control zone distribution
            control_dist = monthly_data['control_zone'].value_counts()
            info("Monthly control zone distribution:")
            for zone, count in control_dist.items():
                info(f"  {zone}: {count} district-months")

        # Save processed data
        info("Saving processed data")
        saved_files = processor.save_processed_data(control_data, time_series, monthly_data)

        info("Saved files:")
        for file_type, path in saved_files.items():
            info(f"  {file_type}: {path}")

        # Generate summary statistics
        info("Generating summary statistics")
        summary = processor.generate_summary_stats(monthly_data)

        if not summary.empty:
            info("Summary statistics:")
            for _, row in summary.iterrows():
                info(f"  {row['metric']}: {row['value']}")

        info("ACAPS data processing complete")


def main():
    """Main execution function."""
    info("=" * 60)
    info("ACAPS Control Zone Data Processing")
    info("=" * 60)

    with timer("acaps_main_processing"):
        # First try to download latest data
        info("\nStep 1: Downloading ACAPS data from HDX")
        download_result = download_acaps_data()

        if download_result:
            info(f"Download successful: {download_result}")
        else:
            info("Download failed, will process existing files if available")

        # Process the data
        info("\nStep 2: Processing ACAPS control zone data")
        process_acaps_data()

        info("\nProcessing complete!")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        import traceback
        error(f"Error in ACAPS processing: {e}\n{traceback.format_exc()}")
        sys.exit(1)