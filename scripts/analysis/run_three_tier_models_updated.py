#!/usr/bin/env python3
"""
Run three-tier econometric analysis with flexible validation for conflict data.

This script is a thin wrapper that uses integrated modules:
- data_preparation for data loading and processing
- ThreeTierAnalysis for running models
- ResultsAnalyzer for extracting insights
"""

import sys
from pathlib import Path
import yaml
import json
import pandas as pd

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from yemen_market.utils.logging import (
    setup_logging, info, error, warning, timer, bind
)
from yemen_market.config.settings import PROCESSED_DATA_DIR
from yemen_market.models.three_tier.integration import ThreeTierAnalysis
from yemen_market.models.three_tier.integration.results_analyzer import ResultsAnalyzer
from yemen_market.models.three_tier.core.panel_data_handler import PanelDataConfig


def load_prepared_data():
    """Load the prepared panel data with econometric treatments."""
    bind(module="data_loading")
    
    with timer("load_prepared_data"):
        # Use the prepared data with winsorization and features
        panel_path = PROCESSED_DATA_DIR / "modeling_ready" / "panel_prepared_for_modeling.parquet"
        
        if not panel_path.exists():
            error(f"Prepared data not found at {panel_path}")
            error("Please run: python scripts/analysis/prepare_data_for_modeling.py")
            raise FileNotFoundError("Prepared data not found")
        
        import pandas as pd
        df = pd.read_parquet(panel_path)
        
        # Ensure column naming consistency
        column_mappings = {
            'usdprice': 'usd_price',
            'admin1': 'governorate',
            'zone_DFA': 'control_zone_DFA'
        }
        
        for old_col, new_col in column_mappings.items():
            if old_col in df.columns and new_col not in df.columns:
                df[new_col] = df[old_col]
                info(f"Created {new_col} from {old_col}")
        
        return df


def configure_three_tier_analysis():
    """Configure analysis using the updated model configuration."""
    bind(module="configuration")
    
    # Load configuration from YAML
    config_path = Path(__file__).parent.parent.parent / 'config' / 'model_config.yaml'
    with open(config_path, 'r') as f:
        full_config = yaml.safe_load(f)
    
    config = full_config.get('three_tier', {})
    
    # Build tier-specific configurations
    analysis_config = {
        'tier1_config': {
            'entity_effects': config['tier1'].get('include_entity_effects', True),
            'time_effects': config['tier1'].get('include_time_effects', True),
            'cluster_entity': config['tier1'].get('cluster_var') == 'entity',
            'cluster_time': config['tier1'].get('cluster_se') == 'twoway',
            'cov_type': 'clustered',
            'dependent_var': config['tier1'].get('dependent_var', 'log_price'),
            'independent_vars': config['tier1'].get('control_vars', []),
            'strict_validation': config['data_validation'].get('strict_mode', False),
            'world_bank_config': config['data_validation']
        },
        'tier2_config': {
            'min_markets': config['tier2'].get('min_markets', 3),
            'min_periods': config['tier2'].get('min_periods', 50),
            'min_observations': config['tier2'].get('min_observations', 50),
            'max_missing_pct': config['data_validation'].get('max_missing_pct', 0.30),
            # Enable threshold VECM
            'use_threshold_model': True,
            'threshold_search_method': 'grid',  # Grid search for threshold
            'min_regime_pct': 0.15,  # Minimum 15% observations in each regime
            'max_thresholds': 1,  # Single threshold for now
            'threshold_variable': 'price_change',  # Use price changes as threshold variable
        },
        'tier3_config': {
            'n_factors': config['tier3'].get('n_factors', None),
            'min_variance_explained': config['tier3'].get('min_variance_explained', 0.70),
            'standardize': config['tier3'].get('standardize', True),
            'method': config['tier3'].get('method', 'pca'),
            'rotation': config['tier3'].get('rotation', 'varimax'),
            'conflict_validation': config['tier3'].get('conflict_validation', True),
            # Enable advanced econometric validation
            'use_econometric_validation': True,  # Use advanced conflict_validation_econometric
            'granger_test': True,  # Test Granger causality
            'variance_decomposition': True,  # Analyze variance decomposition
            'impulse_response': True,  # Study impulse responses
        },
        'diagnostics': config.get('diagnostics', full_config.get('diagnostics', {})),
        'output_dir': 'results/three_tier_analysis_new',
        'run_diagnostics': True,
        'run_parallel': False
    }
    
    return analysis_config


def main():
    """Main execution function."""
    setup_logging()
    
    try:
        info("="*60)
        info("Three-Tier Econometric Analysis")
        info("With Flexible Validation for Conflict Data")
        info("="*60)
        
        # Load prepared data
        info("\nLoading prepared panel data...")
        df = load_prepared_data()
        info(f"Loaded panel: {len(df)} observations")
        
        # Configure analysis
        config = configure_three_tier_analysis()
        
        # Create panel data configuration
        panel_config = PanelDataConfig(
            market_col='market',
            commodity_col='commodity', 
            time_col='date',
            price_col='usd_price',
            required_columns=['date', 'market', 'commodity', 'usd_price']
        )
        config['panel_config'] = panel_config
        
        # Run analysis
        with timer("complete_three_tier_analysis"):
            analysis = ThreeTierAnalysis(config)
            results = analysis.run_full_analysis(df, conflict_data=None)
        
        # Save raw results
        output_dir = Path(config['output_dir'])
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize ResultsAnalyzer for comprehensive analysis
        analyzer = ResultsAnalyzer(output_dir)
        analyzer.load_results()
        
        # Extract and analyze results
        info("\n" + "="*40)
        info("EXTRACTING INSIGHTS")
        info("="*40)
        
        # Extract Tier 1 coefficients
        tier1_coefficients = analyzer.extract_tier1_coefficients(
            model_object=results.get('tier1') if hasattr(results.get('tier1', {}), 'results') else None
        )
        
        # Analyze conflict impact
        if tier1_coefficients['coefficients']:
            conflict_analysis = analyzer.analyze_conflict_impact(tier1_coefficients)
            
            info("\nConflict Impact Analysis:")
            for key, value in conflict_analysis.items():
                info(f"  {key}: {value}")
        
        # Generate comprehensive analysis
        if df is not None:
            integration_analysis = analyzer.analyze_market_integration(df)
            policy_insights = analyzer.generate_policy_insights(df)
            
            # Save comprehensive results
            comprehensive_results = {
                'analysis_date': pd.Timestamp.now().isoformat(),
                'methodology': 'Three-tier panel econometrics with flexible validation',
                'data_treatments': {
                    'outliers': 'Winsorized at 1% and 99%',
                    'missing_data': 'Interpolation ≤2 months, forward fill ≤3 months',
                    'non_stationarity': 'First differences available'
                },
                'tier1_coefficients': tier1_coefficients,
                'conflict_impact': conflict_analysis if tier1_coefficients['coefficients'] else {},
                'market_integration': integration_analysis,
                'policy_insights': policy_insights
            }
            
            with open(output_dir / 'comprehensive_results.json', 'w') as f:
                json.dump(comprehensive_results, f, indent=2, default=str)
        
        # Display key findings
        if policy_insights.get('key_recommendations'):
            info("\nKey Policy Recommendations:")
            for i, rec in enumerate(policy_insights['key_recommendations'], 1):
                info(f"  {i}. {rec}")
        
        info("\n" + "="*60)
        info("✓ Three-tier analysis completed successfully!")
        info(f"Results saved to: {output_dir}")
        info("="*60)
        
    except Exception as e:
        error(f"Analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()