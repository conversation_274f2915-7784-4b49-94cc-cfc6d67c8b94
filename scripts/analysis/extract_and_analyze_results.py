#!/usr/bin/env python3
"""Extract and analyze results from the three-tier analysis.

This script is a thin wrapper around the integrated ResultsAnalyzer class.
It provides a convenient interface for running comprehensive analysis.
"""

import sys
from pathlib import Path
import pandas as pd
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from yemen_market.utils.logging import setup_logging, info
from yemen_market.models.three_tier.integration.results_analyzer import ResultsAnalyzer


def main():
    """Run the results extraction and analysis."""
    setup_logging()
    
    info("=" * 60)
    info("EXTRACTING AND ANALYZING THREE-TIER RESULTS")
    info("=" * 60)
    
    # Initialize analyzer
    results_dir = Path("results/three_tier_analysis_new")
    analyzer = ResultsAnalyzer(results_dir)
    
    # Load results
    analyzer.load_results()
    
    # Extract Tier 1 results
    info("\n" + "=" * 60)
    info("TIER 1: POOLED PANEL RESULTS")
    info("=" * 60)
    tier1_coefficients = analyzer.extract_tier1_coefficients()
    
    # Analyze conflict impact
    if tier1_coefficients['coefficients']:
        conflict_analysis = analyzer.analyze_conflict_impact(tier1_coefficients)
        info("\nConflict Impact Analysis:")
        for key, value in conflict_analysis.items():
            info(f"  {key}: {value}")
    
    # Extract Tier 2 results
    info("\n" + "=" * 60)
    info("TIER 2: COMMODITY-SPECIFIC RESULTS")
    info("=" * 60)
    tier2_summary = analyzer.extract_tier2_results()
    
    # Load panel data for further analysis
    panel_path = Path("data/processed/modeling_ready/panel_prepared_for_modeling.parquet")
    if panel_path.exists():
        df = pd.read_parquet(panel_path)
        
        # Analyze market integration
        info("\n" + "=" * 60)
        info("MARKET INTEGRATION ANALYSIS")
        info("=" * 60)
        integration_analysis = analyzer.analyze_market_integration(df)
        
        # Generate policy insights
        info("\n" + "=" * 60)
        info("POLICY INSIGHTS")
        info("=" * 60)
        policy_insights = analyzer.generate_policy_insights(df)
        
        # Save comprehensive analysis
        comprehensive_results = {
            'analysis_date': pd.Timestamp.now().isoformat(),
            'tier1_coefficients': tier1_coefficients,
            'conflict_impact': conflict_analysis if tier1_coefficients['coefficients'] else {},
            'tier2_summary': tier2_summary,
            'market_integration': integration_analysis,
            'policy_insights': policy_insights
        }
        
        output_path = results_dir / "comprehensive_analysis_results.json"
        with open(output_path, 'w') as f:
            json.dump(comprehensive_results, f, indent=2, default=str)
        
        info(f"\nSaved comprehensive results to: {output_path}")
    
    # Save analysis report
    analyzer.save_analysis_report(results_dir / "analysis_report.json")
    
    info("\n" + "=" * 60)
    info("ANALYSIS COMPLETE")
    info("=" * 60)
    
    # Display key findings
    if 'key_recommendations' in policy_insights:
        info("\nKey Policy Recommendations:")
        for i, rec in enumerate(policy_insights['key_recommendations'], 1):
            info(f"  {i}. {rec}")
    
    info("\nResults saved to:")
    info(f"  - {results_dir}/comprehensive_analysis_results.json")
    info(f"  - {results_dir}/analysis_report.json")


if __name__ == "__main__":
    main()