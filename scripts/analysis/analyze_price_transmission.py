#!/usr/bin/env python3
"""Analyze price transmission between market pairs using World Bank methodology.

This is a thin wrapper script that uses the PriceTransmissionAnalyzer class
to analyze how price changes in one market affect prices in other markets.
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.analysis import PriceTransmissionAnalyzer
from yemen_market.utils.logging import setup_logging, info, warning, error, timer


def main():
    """Main execution function."""
    setup_logging()
    
    info("=" * 60)
    info("Price Transmission Analysis")
    info("Using World Bank Methodology")
    info("=" * 60)
    
    try:
        # Initialize analyzer
        analyzer = PriceTransmissionAnalyzer()
        
        # Load panel data
        panel_path = Path("data/processed/modeling_ready/panel_prepared_for_modeling.parquet")
        if not panel_path.exists():
            error("Prepared panel data not found. Run prepare_data_for_modeling.py first.")
            sys.exit(1)
            
        panel_data = pd.read_parquet(panel_path)
        info(f"Loaded panel: {len(panel_data)} observations")
        
        # Define key trade corridors
        corridors = [
            {
                'name': 'Southern Ports to Capital',
                'base': 'Aden',
                'targets': ["Sana'a City", 'Taiz', 'Ibb']
            },
            {
                'name': 'Western Ports to Interior',
                'base': 'Al Hodeidah',
                'targets': ["Sana'a City", 'Dhamar', 'Raymah']
            },
            {
                'name': 'Eastern Trade Routes',
                'base': 'Marib',
                'targets': ["Sana'a City", 'Al Bayda', 'Shabwah']
            }
        ]
        
        # Key commodities for transmission analysis
        key_commodities = ['Wheat', 'Rice (Imported)', 'Fuel (Diesel)', 'Oil (Vegetable)']
        
        # Analyze each corridor
        all_results = {'corridors': {}, 'exchange_passthrough': {}}
        
        with timer("price_transmission_analysis"):
            for corridor in corridors:
                info(f"\n{'='*40}")
                info(f"Analyzing: {corridor['name']}")
                info(f"{'='*40}")
                
                results = analyzer.analyze_corridor(
                    base_market=corridor['base'],
                    target_markets=corridor['targets'],
                    commodities=key_commodities,
                    panel_data=panel_data
                )
                
                all_results['corridors'][corridor['name']] = results
                
            # Analyze exchange rate pass-through
            info(f"\n{'='*40}")
            info("Exchange Rate Pass-through Analysis")
            info(f"{'='*40}")
            
            passthrough_results = analyzer.analyze_exchange_passthrough(panel_data)
            all_results['exchange_passthrough'] = passthrough_results
            
        # Save results
        analyzer.results = all_results
        analyzer.save_results()
        
        # Generate summary
        info("\n" + "="*60)
        info("SUMMARY OF KEY FINDINGS")
        info("="*60)
        
        # Summarize transmission speeds
        info("\nAverage Transmission Speeds by Corridor:")
        for corridor_name, corridor_data in all_results['corridors'].items():
            speeds = []
            for commodity, comm_data in corridor_data['commodities'].items():
                if comm_data['summary'].get('avg_transmission_speed'):
                    speeds.append(comm_data['summary']['avg_transmission_speed'])
            
            if speeds:
                avg_speed = np.mean(speeds)
                info(f"  {corridor_name}: {avg_speed:.1f} periods")
                
        # Summarize pass-through
        info("\nExchange Rate Pass-through by Commodity:")
        for commodity, passthrough in all_results['exchange_passthrough'].items():
            coef = passthrough['passthrough_coefficient']
            sig = "***" if passthrough['passthrough_pvalue'] < 0.01 else "**" if passthrough['passthrough_pvalue'] < 0.05 else "*" if passthrough['passthrough_pvalue'] < 0.10 else ""
            info(f"  {commodity}: {coef:.1%} {sig} ({passthrough['interpretation']})")
            
        info("\n✓ Price transmission analysis complete!")
        
    except Exception as e:
        error(f"Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()