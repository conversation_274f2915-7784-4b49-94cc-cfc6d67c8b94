#!/usr/bin/env python3
"""Enhanced Analysis Pipeline with World Bank Methodological Improvements.

This is a thin wrapper script that uses the EnhancedAnalysisPipeline class
to run the complete analysis with all World Bank enhancements.
"""

import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.pipelines import EnhancedAnalysisPipeline
from yemen_market.utils.logging import setup_logging, info, error


def main():
    """Main execution function."""
    setup_logging()
    
    # Custom configuration (optional)
    config = {
        'output_dir': 'results/enhanced_analysis_' + datetime.now().strftime('%Y%m%d_%H%M%S'),
        'use_spatial_features': True,
        'analyze_transmission': True,
        'run_model_comparison': True
    }
    
    # Initialize and run pipeline
    pipeline = EnhancedAnalysisPipeline(config)
    
    try:
        results = pipeline.run_complete_pipeline()
        
        if results['status'] == 'success':
            info("✓ Enhanced analysis completed successfully!")
            info(f"Results saved to: {pipeline.output_dir}")
        else:
            error("✗ Analysis failed!")
            error(f"Error: {results.get('error', 'Unknown error')}")
            
    except Exception as e:
        error(f"Pipeline execution failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()