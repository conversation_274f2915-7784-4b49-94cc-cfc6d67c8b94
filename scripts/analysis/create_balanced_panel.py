#!/usr/bin/env python3
"""Create a perfectly balanced panel dataset using PanelBuilder.

This is a thin wrapper script that uses the PanelBuilder class
to create a balanced panel dataset.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.data.panel_builder import PanelBuilder
from yemen_market.utils.logging import setup_logging, info, error


def main():
    """Main execution function."""
    setup_logging()
    
    info("=" * 60)
    info("Creating Perfectly Balanced Panel Dataset")
    info("=" * 60)
    
    try:
        # Initialize panel builder
        builder = PanelBuilder()
        
        # Create balanced panel with default parameters
        balanced_panel = builder.create_core_balanced_panel(
            min_coverage_pct=85.0,
            min_markets=20,
            min_commodities=15
        )
        
        # Save the panel in multiple formats
        saved_paths = builder.save_balanced_panels(balanced_panel)
        
        info("\n✓ Successfully created balanced panel dataset!")
        info(f"Saved files:")
        for name, path in saved_paths.items():
            info(f"  - {name}: {path}")
        
    except Exception as e:
        error(f"Error creating balanced panel: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()