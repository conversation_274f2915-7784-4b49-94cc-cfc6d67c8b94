#!/usr/bin/env python3
"""Prepare data for three-tier modeling with econometric best practices.

This script is a thin wrapper around the integrated data_preparation module.
It implements the recommendations from the methodological review:
1. Data quality assessment
2. Outlier treatment (Winsorization)
3. Stationarity testing
4. Conflict regime definition
5. Validation with flexible standards
"""

import sys
from pathlib import Path
import json
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.data.panel_builder import PanelBuilder
from yemen_market.utils.logging import setup_logging, info, warning, error, timer
from yemen_market.features.data_preparation import (
    generate_data_quality_report,
    winsorize_prices,
    test_panel_stationarity,
    define_conflict_regimes,
    add_econometric_features,
    validate_for_modeling,
    save_prepared_data
)
from yemen_market.features.feature_engineering import FeatureEngineer


def convert_numpy_types(obj):
    """Convert numpy types to Python types for JSON serialization."""
    if isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, dict):
        return {k: convert_numpy_types(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(v) for v in obj]
    return obj


def main():
    """Main execution function."""
    setup_logging()
    
    info("=" * 60)
    info("Preparing Data for Three-Tier Econometric Analysis")
    info("=" * 60)
    
    try:
        # Load integrated balanced panel
        builder = PanelBuilder()
        panel = builder.load_integrated_panel()
        info(f"Loaded panel: {len(panel)} observations")
        
        # 1. Generate data quality report
        quality_report = generate_data_quality_report(panel)
        info("\nData Quality Report:")
        for key, value in quality_report.items():
            info(f"  {key}: {value}")
        
        # 2. Test stationarity
        stationarity_results, stationarity_summary = test_panel_stationarity(panel)
        info(f"\nStationarity Test Summary:")
        for key, value in stationarity_summary.items():
            info(f"  {key}: {value}")
        
        # 3. Winsorize prices
        panel = winsorize_prices(panel, limits=(0.01, 0.01))
        
        # 4. Define conflict regimes
        panel = define_conflict_regimes(panel)
        
        # 5. Add econometric features
        panel = add_econometric_features(panel)
        
        # 5b. Add spatial features (NEW)
        info("\nAdding spatial features...")
        with timer("add_spatial_features"):
            engineer = FeatureEngineer()
            # Add spatial features based on geographic proximity
            panel = engineer.add_spatial_features(
                panel, 
                k_neighbors=3,  # Consider 3 nearest markets
                distance_weighted=True  # Weight by inverse distance
            )
            info(f"Added spatial features for {panel['market'].nunique()} markets")
            
        # 5c. Add interaction features (NEW)
        info("\nAdding interaction features...")
        with timer("add_interaction_features"):
            panel = engineer.add_interaction_features(panel)
            info("Added zone-time and conflict-zone interaction features")
        
        # 6. Validate for modeling (relaxed standards for policy work)
        validation = validate_for_modeling(panel, strict=False)
        info(f"\nValidation Results:")
        info(f"  Valid: {validation['valid']}")
        if validation['errors']:
            for err in validation['errors']:
                error(f"  ERROR: {err}")
        if validation['warnings']:
            for warn in validation['warnings']:
                warning(f"  WARNING: {warn}")
        
        # 7. Save prepared data
        output_dir = project_root / "data" / "processed" / "modeling_ready"
        save_prepared_data(panel, output_dir)
        
        # Save quality report
        with open(output_dir / "data_quality_report.json", 'w') as f:
            json.dump(quality_report, f, indent=2)
        
        # Save stationarity results
        with open(output_dir / "stationarity_test_results.json", 'w') as f:
            json.dump({
                'summary': convert_numpy_types(stationarity_summary),
                'detailed_results': convert_numpy_types(stationarity_results)
            }, f, indent=2)
        
        info("\n" + "=" * 60)
        info("✓ Data preparation complete!")
        info("Ready for three-tier econometric analysis")
        info("=" * 60)
        
    except Exception as e:
        error(f"Error in data preparation: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()