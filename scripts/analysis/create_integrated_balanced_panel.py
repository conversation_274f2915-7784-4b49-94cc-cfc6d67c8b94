#!/usr/bin/env python3
"""Create a fully integrated balanced panel using PanelBuilder.

This is a thin wrapper script that uses the PanelBuilder class
to create an integrated balanced panel with conflict and control zone data.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.data.panel_builder import PanelBuilder
from yemen_market.utils.logging import setup_logging, info, error


def main():
    """Main execution function."""
    setup_logging()
    
    info("=" * 60)
    info("Creating Integrated Balanced Panel Dataset")
    info("=" * 60)
    
    try:
        # Initialize panel builder
        builder = PanelBuilder()
        
        # Load the balanced panel (if it exists)
        try:
            info("Attempting to load existing balanced panel...")
            balanced_panel = builder.load_balanced_panel(panel_type='filled')
            info("✓ Loaded existing balanced panel")
        except FileNotFoundError:
            info("No existing balanced panel found. Creating new one...")
            balanced_panel = builder.create_core_balanced_panel()
            builder.save_balanced_panels(balanced_panel)
            info("✓ Created new balanced panel")
        
        # Integrate additional data
        info("\nIntegrating conflict, control zones, and geographic data...")
        integrated_panel = builder.integrate_panel_data(balanced_panel)
        
        # Validate the integrated panel
        info("\nValidating integrated panel...")
        validation_results = builder.validate_balanced_panel(integrated_panel)
        
        # Save the integrated panel
        info("\nSaving integrated panel...")
        saved_paths = builder.save_balanced_panels(integrated_panel)
        
        info("\n" + "=" * 60)
        info("✓ SUCCESSFULLY CREATED INTEGRATED BALANCED PANEL")
        info("=" * 60)
        info(f"Total observations: {len(integrated_panel):,}")
        info(f"Total columns: {len(integrated_panel.columns)}")
        info("Ready for three-tier econometric analysis!")
        
    except Exception as e:
        error(f"Error creating integrated panel: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()