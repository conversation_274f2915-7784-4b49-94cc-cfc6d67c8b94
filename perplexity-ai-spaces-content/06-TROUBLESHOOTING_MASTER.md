# Troubleshooting Master Document
## Comprehensive Problem Resolution Guide for Yemen Market Integration Analysis

**Document Type:** Master Troubleshooting Reference  
**Version:** 1.0  
**Date:** June 2, 2025  
**Audience:** Technical Analysts, Research Programmers, System Administrators  
**Quality Standard:** World Bank Publication Ready  

---

## Executive Summary

This master document consolidates all troubleshooting procedures, error resolution protocols, and debugging frameworks for the Yemen Market Integration research methodology. The guide addresses the unique technical challenges of implementing econometric analysis in conflict-affected settings where data limitations, infrastructure constraints, and methodological complexities create specialized debugging requirements.

**Critical Focus:** Yemen-specific troubleshooting scenarios including dual-currency harmonization errors, conflict data integration issues, exchange rate pipeline failures, and model convergence problems under extreme market conditions.

**Core Value Proposition:** Enable rapid problem resolution and system recovery to maintain continuous analytical capabilities in time-sensitive humanitarian contexts.

---

## Table of Contents

1. [Data Integration and Pipeline Issues](#1-data-integration-issues)
2. [Model Convergence and Estimation Problems](#2-model-convergence-problems)
3. [Currency Zone Classification Errors](#3-currency-zone-errors)
4. [Exchange Rate Pipeline Failures](#4-exchange-rate-failures)
5. [Conflict Data Processing Issues](#5-conflict-data-issues)
6. [Spatial Analysis and Mapping Problems](#6-spatial-analysis-problems)
7. [Performance and Scalability Issues](#7-performance-issues)
8. [API and System Integration Failures](#8-api-integration-failures)
9. [Validation and Quality Assurance Problems](#9-validation-problems)
10. [Emergency Recovery Procedures](#10-emergency-recovery)
11. [Preventive Maintenance Protocols](#11-preventive-maintenance)

---

## 1. Data Integration and Pipeline Issues

### 1.1 WFP Price Data Harmonization Failures

**Problem Category:** Data Processing Errors  
**Severity:** High (impacts all downstream analysis)  
**Common Symptoms:**
- Currency mismatch errors between YER and USD prices
- Missing price data after harmonization
- Extreme outliers in converted prices
- Index alignment failures between datasets

#### Diagnostic Steps

```python
def diagnose_price_harmonization_issues(wfp_data):
    """
    Comprehensive diagnostic for WFP price data issues.
    
    Returns detailed report of data quality problems and suggested fixes.
    """
    
    diagnostics = {
        'data_shape': wfp_data.shape,
        'missing_data': {},
        'currency_issues': {},
        'outlier_analysis': {},
        'recommendations': []
    }
    
    # 1. Check for missing data patterns
    missing_analysis = wfp_data.isnull().sum()
    diagnostics['missing_data'] = {
        'total_missing': missing_analysis.sum(),
        'columns_with_missing': missing_analysis[missing_analysis > 0].to_dict(),
        'missing_percentage': (missing_analysis / len(wfp_data) * 100).round(2).to_dict()
    }
    
    # 2. Currency field validation
    if 'currency' in wfp_data.columns:
        currency_counts = wfp_data['currency'].value_counts()
        diagnostics['currency_issues'] = {
            'currency_distribution': currency_counts.to_dict(),
            'unexpected_currencies': [c for c in currency_counts.index if c not in ['YER', 'USD']],
            'missing_currency': wfp_data['currency'].isnull().sum()
        }
    else:
        diagnostics['currency_issues']['error'] = 'Currency column not found'
    
    # 3. Price outlier detection
    price_columns = [col for col in wfp_data.columns if 'price' in col.lower()]
    
    for col in price_columns:
        if wfp_data[col].dtype in ['float64', 'int64']:
            q1 = wfp_data[col].quantile(0.25)
            q3 = wfp_data[col].quantile(0.75)
            iqr = q3 - q1
            
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outliers = wfp_data[(wfp_data[col] < lower_bound) | (wfp_data[col] > upper_bound)]
            
            diagnostics['outlier_analysis'][col] = {
                'n_outliers': len(outliers),
                'outlier_percentage': len(outliers) / len(wfp_data) * 100,
                'extreme_values': {
                    'min': wfp_data[col].min(),
                    'max': wfp_data[col].max(),
                    'q1': q1,
                    'q3': q3
                }
            }
    
    # 4. Generate recommendations
    if diagnostics['missing_data']['total_missing'] > len(wfp_data) * 0.3:
        diagnostics['recommendations'].append(
            "HIGH: Excessive missing data (>30%). Consider data quality review."
        )
    
    if diagnostics['currency_issues'].get('unexpected_currencies'):
        diagnostics['recommendations'].append(
            f"MEDIUM: Unexpected currencies found: {diagnostics['currency_issues']['unexpected_currencies']}"
        )
    
    high_outlier_cols = [
        col for col, analysis in diagnostics['outlier_analysis'].items()
        if analysis['outlier_percentage'] > 5
    ]
    
    if high_outlier_cols:
        diagnostics['recommendations'].append(
            f"MEDIUM: High outlier rates in columns: {high_outlier_cols}"
        )
    
    return diagnostics

def fix_common_harmonization_issues(wfp_data):
    """
    Apply common fixes for WFP data harmonization issues.
    """
    
    fixed_data = wfp_data.copy()
    fixes_applied = []
    
    # Fix 1: Standardize currency column
    if 'currency' in fixed_data.columns:
        # Map common variations
        currency_mapping = {
            'yer': 'YER',
            'usd': 'USD',
            'Yer': 'YER',
            'Usd': 'USD',
            'YER ': 'YER',
            'USD ': 'USD'
        }
        
        fixed_data['currency'] = fixed_data['currency'].map(
            lambda x: currency_mapping.get(x, x) if pd.notna(x) else x
        )
        fixes_applied.append("Standardized currency field")
    
    # Fix 2: Handle negative prices (data entry errors)
    price_columns = [col for col in fixed_data.columns if 'price' in col.lower()]
    
    for col in price_columns:
        if fixed_data[col].dtype in ['float64', 'int64']:
            negative_count = (fixed_data[col] < 0).sum()
            if negative_count > 0:
                fixed_data[col] = fixed_data[col].abs()
                fixes_applied.append(f"Fixed {negative_count} negative prices in {col}")
    
    # Fix 3: Remove extreme outliers (>10x median price)
    for col in price_columns:
        if fixed_data[col].dtype in ['float64', 'int64']:
            median_price = fixed_data[col].median()
            extreme_threshold = median_price * 10
            
            extreme_mask = fixed_data[col] > extreme_threshold
            extreme_count = extreme_mask.sum()
            
            if extreme_count > 0:
                fixed_data.loc[extreme_mask, col] = np.nan
                fixes_applied.append(f"Removed {extreme_count} extreme outliers from {col}")
    
    # Fix 4: Ensure required columns exist
    required_columns = ['market', 'commodity', 'date', 'price']
    missing_required = [col for col in required_columns if col not in fixed_data.columns]
    
    if missing_required:
        fixes_applied.append(f"WARNING: Missing required columns: {missing_required}")
    
    return fixed_data, fixes_applied
```

#### Common Fixes

**Issue:** Currency field inconsistencies
```python
# Solution: Standardize currency field
def standardize_currency_field(data):
    currency_mapping = {
        'yer': 'YER', 'usd': 'USD', 'Yer': 'YER', 'Usd': 'USD',
        'YER ': 'YER', 'USD ': 'USD', ' YER': 'YER', ' USD': 'USD'
    }
    
    data['currency'] = data['currency'].map(
        lambda x: currency_mapping.get(str(x).strip(), x) if pd.notna(x) else x
    )
    return data
```

**Issue:** Exchange rate lookup failures
```python
# Solution: Robust exchange rate matching
def robust_exchange_rate_lookup(price_data, exchange_rates):
    """
    Handle missing exchange rates with fallback strategies.
    """
    
    # Strategy 1: Direct date match
    merged = price_data.merge(
        exchange_rates, 
        left_on=['date', 'currency_zone'], 
        right_on=['date', 'zone'],
        how='left'
    )
    
    # Strategy 2: Forward fill for missing rates (max 7 days)
    missing_mask = merged['exchange_rate'].isnull()
    
    if missing_mask.sum() > 0:
        # Group by zone and forward fill within groups
        merged['exchange_rate'] = merged.groupby('currency_zone')['exchange_rate'].fillna(
            method='ffill', limit=7
        )
    
    # Strategy 3: Use alternative rate if primary unavailable
    still_missing = merged['exchange_rate'].isnull()
    if still_missing.sum() > 0:
        # Use parallel market rate with adjustment factor
        merged.loc[still_missing, 'exchange_rate'] = (
            merged.loc[still_missing, 'parallel_rate'] * 0.95  # 5% discount
        )
    
    return merged
```

### 1.2 Data Source Integration Conflicts

**Problem Category:** Multi-source Integration  
**Severity:** Medium  
**Common Symptoms:**
- Timestamp misalignment between datasets
- Conflicting market identifiers
- Duplicate observations after merging
- Spatial coordinate inconsistencies

#### Resolution Framework

```python
def resolve_data_integration_conflicts(datasets_dict):
    """
    Systematic resolution of conflicts between multiple data sources.
    
    Parameters
    ----------
    datasets_dict : dict
        Dictionary with keys as source names and values as DataFrames
    """
    
    integration_report = {
        'timestamp_issues': {},
        'identifier_conflicts': {},
        'spatial_inconsistencies': {},
        'resolution_steps': []
    }
    
    # 1. Timestamp standardization
    for source_name, df in datasets_dict.items():
        date_columns = [col for col in df.columns if any(x in col.lower() for x in ['date', 'time'])]
        
        for date_col in date_columns:
            # Check date format consistency
            try:
                df[date_col] = pd.to_datetime(df[date_col])
                integration_report['resolution_steps'].append(
                    f"Standardized {date_col} in {source_name}"
                )
            except Exception as e:
                integration_report['timestamp_issues'][f"{source_name}.{date_col}"] = str(e)
    
    # 2. Market identifier reconciliation
    market_columns = ['market', 'market_id', 'market_name']
    
    if len(datasets_dict) > 1:
        # Compare market identifiers across sources
        source_names = list(datasets_dict.keys())
        
        for i, source1 in enumerate(source_names):
            for source2 in source_names[i+1:]:
                df1, df2 = datasets_dict[source1], datasets_dict[source2]
                
                # Find overlapping market columns
                market_cols1 = [col for col in df1.columns if col in market_columns]
                market_cols2 = [col for col in df2.columns if col in market_columns]
                
                if market_cols1 and market_cols2:
                    markets1 = set(df1[market_cols1[0]].unique())
                    markets2 = set(df2[market_cols2[0]].unique())
                    
                    overlap = markets1.intersection(markets2)
                    
                    integration_report['identifier_conflicts'][f"{source1}_vs_{source2}"] = {
                        'total_markets_source1': len(markets1),
                        'total_markets_source2': len(markets2),
                        'overlapping_markets': len(overlap),
                        'overlap_percentage': len(overlap) / min(len(markets1), len(markets2)) * 100
                    }
    
    return integration_report

def create_unified_market_mapping(datasets_dict):
    """
    Create unified market identifier mapping across all sources.
    """
    
    # Extract all unique market identifiers
    all_markets = set()
    market_mappings = {}
    
    for source_name, df in datasets_dict.items():
        market_columns = [col for col in df.columns if 'market' in col.lower()]
        
        if market_columns:
            markets = df[market_columns[0]].unique()
            all_markets.update(markets)
            market_mappings[source_name] = markets
    
    # Create fuzzy matching for similar market names
    from difflib import SequenceMatcher
    
    unified_mapping = {}
    used_markets = set()
    
    for market in all_markets:
        if market not in used_markets:
            # Find similar markets
            similar_markets = [market]
            
            for other_market in all_markets:
                if other_market != market and other_market not in used_markets:
                    similarity = SequenceMatcher(None, str(market), str(other_market)).ratio()
                    if similarity > 0.8:  # 80% similarity threshold
                        similar_markets.append(other_market)
            
            # Create unified identifier
            unified_id = f"market_{len(unified_mapping) + 1:03d}"
            
            for similar_market in similar_markets:
                unified_mapping[similar_market] = unified_id
                used_markets.add(similar_market)
    
    return unified_mapping
```

---

## 2. Model Convergence and Estimation Problems

### 2.1 Panel Model Convergence Failures

**Problem Category:** Econometric Estimation  
**Severity:** High  
**Common Symptoms:**
- Maximum iterations reached without convergence
- Singular covariance matrix errors
- Extreme coefficient estimates
- Standard error calculation failures

#### Diagnostic Framework

```python
def diagnose_convergence_issues(panel_data, formula):
    """
    Comprehensive diagnosis of panel model convergence problems.
    """
    
    diagnostics = {
        'data_quality': {},
        'multicollinearity': {},
        'model_specification': {},
        'recommendations': []
    }
    
    # 1. Data quality checks
    # Check for perfect collinearity
    numeric_columns = panel_data.select_dtypes(include=[np.number]).columns
    correlation_matrix = panel_data[numeric_columns].corr()
    
    # Find highly correlated pairs (>0.95)
    high_corr_pairs = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            if abs(correlation_matrix.iloc[i, j]) > 0.95:
                high_corr_pairs.append({
                    'var1': correlation_matrix.columns[i],
                    'var2': correlation_matrix.columns[j],
                    'correlation': correlation_matrix.iloc[i, j]
                })
    
    diagnostics['multicollinearity'] = {
        'high_correlation_pairs': high_corr_pairs,
        'max_correlation': correlation_matrix.abs().max().max()
    }
    
    # 2. Check variance structure
    for col in numeric_columns:
        if panel_data[col].var() < 1e-10:
            diagnostics['data_quality'][f'{col}_low_variance'] = panel_data[col].var()
    
    # 3. Entity-time structure validation
    if isinstance(panel_data.index, pd.MultiIndex):
        entity_counts = panel_data.index.get_level_values(0).value_counts()
        time_counts = panel_data.index.get_level_values(1).value_counts()
        
        diagnostics['data_quality']['panel_structure'] = {
            'min_entity_observations': entity_counts.min(),
            'max_entity_observations': entity_counts.max(),
            'unbalanced_entities': (entity_counts != entity_counts.mode()[0]).sum(),
            'time_periods': len(time_counts),
            'balanced_panel': len(entity_counts.unique()) == 1
        }
    
    # 4. Generate recommendations
    if high_corr_pairs:
        diagnostics['recommendations'].append(
            f"CRITICAL: Remove one variable from highly correlated pairs: {[f'{p['var1']}-{p['var2']}' for p in high_corr_pairs]}"
        )
    
    if diagnostics['data_quality'].get('panel_structure', {}).get('min_entity_observations', 0) < 5:
        diagnostics['recommendations'].append(
            "WARNING: Some entities have very few observations. Consider dropping entities with <5 observations."
        )
    
    return diagnostics

def fix_convergence_issues(panel_data, formula, issue_type='auto_detect'):
    """
    Apply systematic fixes for convergence problems.
    """
    
    fixed_data = panel_data.copy()
    fixes_applied = []
    
    if issue_type in ['auto_detect', 'multicollinearity']:
        # Fix multicollinearity by removing highly correlated variables
        numeric_columns = fixed_data.select_dtypes(include=[np.number]).columns
        
        if len(numeric_columns) > 1:
            correlation_matrix = fixed_data[numeric_columns].corr()
            
            # Find variables to drop (keep first of each highly correlated pair)
            to_drop = set()
            
            for i in range(len(correlation_matrix.columns)):
                for j in range(i+1, len(correlation_matrix.columns)):
                    if abs(correlation_matrix.iloc[i, j]) > 0.95:
                        var_to_drop = correlation_matrix.columns[j]  # Drop second variable
                        to_drop.add(var_to_drop)
            
            if to_drop:
                fixed_data = fixed_data.drop(columns=list(to_drop))
                fixes_applied.append(f"Dropped highly correlated variables: {list(to_drop)}")
    
    if issue_type in ['auto_detect', 'scaling']:
        # Standardize variables with extreme ranges
        numeric_columns = fixed_data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            if fixed_data[col].std() > 1000 or fixed_data[col].std() < 0.001:
                fixed_data[f'{col}_scaled'] = (
                    fixed_data[col] - fixed_data[col].mean()
                ) / fixed_data[col].std()
                
                fixed_data = fixed_data.drop(columns=[col])
                fixes_applied.append(f"Standardized variable: {col}")
    
    if issue_type in ['auto_detect', 'outliers']:
        # Remove extreme outliers that might cause convergence issues
        numeric_columns = fixed_data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            Q1 = fixed_data[col].quantile(0.01)
            Q99 = fixed_data[col].quantile(0.99)
            
            outlier_mask = (fixed_data[col] < Q1) | (fixed_data[col] > Q99)
            outlier_count = outlier_mask.sum()
            
            if outlier_count > 0:
                fixed_data = fixed_data[~outlier_mask]
                fixes_applied.append(f"Removed {outlier_count} extreme outliers from {col}")
    
    return fixed_data, fixes_applied

def robust_model_estimation(panel_data, formula, fallback_methods=True):
    """
    Robust model estimation with multiple fallback strategies.
    """
    
    estimation_results = {
        'successful_method': None,
        'model_result': None,
        'diagnostics': None,
        'warnings': []
    }
    
    # Method 1: Standard panel OLS with clustering
    try:
        model = lm.PanelOLS.from_formula(formula, data=panel_data).fit(
            cov_type='clustered', cluster_entity=True, cluster_time=True
        )
        
        estimation_results['successful_method'] = 'clustered_panel_ols'
        estimation_results['model_result'] = model
        return estimation_results
        
    except Exception as e:
        estimation_results['warnings'].append(f"Clustered Panel OLS failed: {e}")
    
    # Method 2: Standard panel OLS with robust standard errors
    if fallback_methods:
        try:
            model = lm.PanelOLS.from_formula(formula, data=panel_data).fit(
                cov_type='robust'
            )
            
            estimation_results['successful_method'] = 'robust_panel_ols'
            estimation_results['model_result'] = model
            return estimation_results
            
        except Exception as e:
            estimation_results['warnings'].append(f"Robust Panel OLS failed: {e}")
    
    # Method 3: Fixed effects with entity effects only
    if fallback_methods:
        try:
            # Simplify formula to remove time effects
            simplified_formula = formula.replace('+ TimeEffects', '').replace('+ time_effects', '')
            
            model = lm.PanelOLS.from_formula(simplified_formula, data=panel_data).fit()
            
            estimation_results['successful_method'] = 'entity_fixed_effects'
            estimation_results['model_result'] = model
            estimation_results['warnings'].append("Used simplified specification (entity effects only)")
            return estimation_results
            
        except Exception as e:
            estimation_results['warnings'].append(f"Entity Fixed Effects failed: {e}")
    
    # Method 4: Pooled OLS as last resort
    if fallback_methods:
        try:
            # Convert to pooled regression
            pooled_data = panel_data.reset_index()
            pooled_formula = formula.replace('EntityEffects +', '').replace('TimeEffects +', '')
            pooled_formula = pooled_formula.replace('+ EntityEffects', '').replace('+ TimeEffects', '')
            
            from sklearn.linear_model import LinearRegression
            from sklearn.preprocessing import LabelEncoder
            
            # Extract variables from formula (simplified)
            dependent_var = pooled_formula.split('~')[0].strip()
            independent_vars = [v.strip() for v in pooled_formula.split('~')[1].split('+')]
            
            # Select numeric columns only
            available_vars = [var for var in independent_vars if var in pooled_data.select_dtypes(include=[np.number]).columns]
            
            if available_vars and dependent_var in pooled_data.columns:
                X = pooled_data[available_vars].dropna()
                y = pooled_data.loc[X.index, dependent_var]
                
                lr = LinearRegression()
                lr.fit(X, y)
                
                # Create simplified results object
                results = type('PooledResults', (), {
                    'params': pd.Series(lr.coef_, index=available_vars),
                    'rsquared': lr.score(X, y),
                    'method': 'pooled_ols_fallback'
                })()
                
                estimation_results['successful_method'] = 'pooled_ols_fallback'
                estimation_results['model_result'] = results
                estimation_results['warnings'].append("Used pooled OLS fallback - interpret with caution")
                return estimation_results
                
        except Exception as e:
            estimation_results['warnings'].append(f"Pooled OLS fallback failed: {e}")
    
    # All methods failed
    estimation_results['warnings'].append("All estimation methods failed")
    return estimation_results
```

### 2.2 Regime-Switching Model Issues

**Problem Category:** Advanced Econometric Models  
**Severity:** Medium  
**Common Symptoms:**
- Regime probabilities stuck at boundaries (0 or 1)
- Transition matrix not updating
- Model identifying only one regime
- Numerical instability in EM algorithm

#### Troubleshooting Regime-Switching Models

```python
def troubleshoot_regime_switching(data, n_regimes=2):
    """
    Diagnose and fix regime-switching model issues.
    """
    
    troubleshooting_report = {
        'data_issues': {},
        'model_issues': {},
        'suggested_fixes': []
    }
    
    # 1. Check data requirements for regime identification
    # Need sufficient variation in the data
    if 'log_price_diff' in data.columns:
        price_changes = data['log_price_diff'].dropna()
        
        troubleshooting_report['data_issues']['price_variation'] = {
            'standard_deviation': price_changes.std(),
            'min_change': price_changes.min(),
            'max_change': price_changes.max(),
            'sufficient_variation': price_changes.std() > 0.01  # Minimum 1% variation
        }
        
        if price_changes.std() <= 0.01:
            troubleshooting_report['suggested_fixes'].append(
                "Insufficient price variation for regime identification. Consider using levels instead of differences."
            )
    
    # 2. Check for outliers that might distort regime identification
    numeric_columns = data.select_dtypes(include=[np.number]).columns
    outlier_analysis = {}
    
    for col in numeric_columns:
        Q1 = data[col].quantile(0.25)
        Q3 = data[col].quantile(0.75)
        IQR = Q3 - Q1
        
        outliers = data[(data[col] < Q1 - 1.5*IQR) | (data[col] > Q3 + 1.5*IQR)]
        outlier_percentage = len(outliers) / len(data) * 100
        
        outlier_analysis[col] = {
            'outlier_count': len(outliers),
            'outlier_percentage': outlier_percentage,
            'extreme_outliers': outlier_percentage > 10
        }
    
    troubleshooting_report['data_issues']['outliers'] = outlier_analysis
    
    # 3. Check data length requirements
    min_obs_per_regime = 20
    if len(data) < n_regimes * min_obs_per_regime:
        troubleshooting_report['data_issues']['insufficient_data'] = {
            'current_length': len(data),
            'required_minimum': n_regimes * min_obs_per_regime,
            'sufficient': False
        }
        
        troubleshooting_report['suggested_fixes'].append(
            f"Insufficient data for {n_regimes} regimes. Need at least {n_regimes * min_obs_per_regime} observations."
        )
    
    return troubleshooting_report

def fix_regime_switching_issues(data, issue_type='convergence'):
    """
    Apply fixes for common regime-switching problems.
    """
    
    fixed_data = data.copy()
    fixes_applied = []
    
    if issue_type == 'convergence':
        # Fix 1: Remove extreme outliers
        numeric_cols = fixed_data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            # Use more conservative outlier removal (99.5th percentile)
            lower_bound = fixed_data[col].quantile(0.005)
            upper_bound = fixed_data[col].quantile(0.995)
            
            outlier_mask = (fixed_data[col] < lower_bound) | (fixed_data[col] > upper_bound)
            outlier_count = outlier_mask.sum()
            
            if outlier_count > 0:
                fixed_data = fixed_data[~outlier_mask]
                fixes_applied.append(f"Removed {outlier_count} extreme outliers from {col}")
        
        # Fix 2: Standardize features for better convergence
        feature_cols = [col for col in numeric_cols if col in ['conflict', 'exchange_rate_change']]
        
        for col in feature_cols:
            if col in fixed_data.columns:
                fixed_data[f'{col}_standardized'] = (
                    fixed_data[col] - fixed_data[col].mean()
                ) / fixed_data[col].std()
                fixes_applied.append(f"Standardized {col}")
    
    elif issue_type == 'regime_identification':
        # Fix: Enhance regime differentiation by creating interaction terms
        if 'conflict' in fixed_data.columns and 'exchange_rate_change' in fixed_data.columns:
            fixed_data['conflict_exchange_interaction'] = (
                fixed_data['conflict'] * fixed_data['exchange_rate_change']
            )
            fixes_applied.append("Added conflict-exchange rate interaction term")
        
        # Create volatility regime indicator
        if 'log_price_diff' in fixed_data.columns:
            rolling_volatility = fixed_data['log_price_diff'].rolling(7).std()
            fixed_data['high_volatility_period'] = (
                rolling_volatility > rolling_volatility.quantile(0.7)
            ).astype(int)
            fixes_applied.append("Added volatility regime indicator")
    
    return fixed_data, fixes_applied

def robust_regime_switching_estimation(data, max_regimes=3):
    """
    Robust regime-switching estimation with automatic regime selection.
    """
    
    from sklearn.mixture import GaussianMixture
    from sklearn.model_selection import cross_val_score
    
    # Prepare features
    feature_columns = ['log_price_diff', 'conflict', 'exchange_rate_change']
    available_features = [col for col in feature_columns if col in data.columns]
    
    if len(available_features) < 2:
        return {'error': 'Insufficient features for regime switching model'}
    
    X = data[available_features].dropna()
    
    if len(X) < 30:
        return {'error': 'Insufficient data points for regime switching model'}
    
    # Test different numbers of regimes
    regime_results = {}
    
    for n_regimes in range(2, max_regimes + 1):
        try:
            # Fit Gaussian Mixture Model
            gmm = GaussianMixture(
                n_components=n_regimes,
                covariance_type='full',
                max_iter=200,
                n_init=10,
                random_state=42
            )
            
            gmm.fit(X)
            
            # Calculate information criteria
            aic = gmm.aic(X)
            bic = gmm.bic(X)
            
            # Regime assignment
            regime_probs = gmm.predict_proba(X)
            regime_assignments = gmm.predict(X)
            
            # Check regime balance (avoid degenerate solutions)
            regime_counts = pd.Series(regime_assignments).value_counts()
            min_regime_size = regime_counts.min() / len(X)
            
            regime_results[n_regimes] = {
                'model': gmm,
                'aic': aic,
                'bic': bic,
                'log_likelihood': gmm.score(X) * len(X),
                'regime_assignments': regime_assignments,
                'regime_probabilities': regime_probs,
                'min_regime_size': min_regime_size,
                'converged': gmm.converged_,
                'valid': min_regime_size > 0.1 and gmm.converged_  # At least 10% in each regime
            }
            
        except Exception as e:
            regime_results[n_regimes] = {'error': str(e)}
    
    # Select best model based on BIC and validity
    valid_models = {k: v for k, v in regime_results.items() if v.get('valid', False)}
    
    if not valid_models:
        return {'error': 'No valid regime-switching models found', 'details': regime_results}
    
    # Choose model with lowest BIC among valid models
    best_n_regimes = min(valid_models.keys(), key=lambda k: valid_models[k]['bic'])
    best_model = valid_models[best_n_regimes]
    
    return {
        'best_model': best_model,
        'n_regimes': best_n_regimes,
        'all_results': regime_results,
        'recommendation': f"Use {best_n_regimes}-regime model (BIC: {best_model['bic']:.2f})"
    }
```

---

## 3. Currency Zone Classification Errors

### 3.1 Geographic Boundary Misclassification

**Problem Category:** Spatial Analysis  
**Severity:** Critical (affects all currency-dependent analysis)  
**Common Symptoms:**
- Markets assigned to wrong currency zones
- Boundary changes not reflected in classification
- Inconsistent zone assignments over time
- Missing geographic coordinates

#### Currency Zone Diagnostic System

```python
def diagnose_currency_zone_issues(market_data, control_data=None):
    """
    Comprehensive diagnosis of currency zone classification issues.
    """
    
    diagnostics = {
        'geographic_coverage': {},
        'temporal_consistency': {},
        'boundary_issues': {},
        'data_quality': {},
        'recommendations': []
    }
    
    # 1. Check geographic coordinate quality
    if 'latitude' in market_data.columns and 'longitude' in market_data.columns:
        coord_issues = {
            'missing_coordinates': market_data[['latitude', 'longitude']].isnull().any(axis=1).sum(),
            'invalid_coordinates': 0,
            'suspicious_coordinates': 0
        }
        
        # Check for invalid coordinates (outside Yemen bounds)
        yemen_bounds = {
            'lat_min': 12.0, 'lat_max': 19.0,
            'lon_min': 42.0, 'lon_max': 54.0
        }
        
        invalid_coords = (
            (market_data['latitude'] < yemen_bounds['lat_min']) |
            (market_data['latitude'] > yemen_bounds['lat_max']) |
            (market_data['longitude'] < yemen_bounds['lon_min']) |
            (market_data['longitude'] > yemen_bounds['lon_max'])
        )
        
        coord_issues['invalid_coordinates'] = invalid_coords.sum()
        
        # Check for suspiciously precise coordinates (potential data entry errors)
        if market_data['latitude'].dtype == 'float64':
            precise_coords = (
                (market_data['latitude'] % 1 == 0) |  # Exactly on degree lines
                (market_data['longitude'] % 1 == 0)
            )
            coord_issues['suspicious_coordinates'] = precise_coords.sum()
        
        diagnostics['geographic_coverage'] = coord_issues
    
    # 2. Check currency zone assignment consistency
    if 'currency_zone' in market_data.columns:
        zone_distribution = market_data['currency_zone'].value_counts()
        
        diagnostics['boundary_issues'] = {
            'zone_distribution': zone_distribution.to_dict(),
            'unassigned_markets': market_data['currency_zone'].isnull().sum(),
            'unexpected_zones': [z for z in zone_distribution.index if z not in ['houthi', 'government', 'contested']]
        }
    
    # 3. Temporal consistency analysis
    if 'date' in market_data.columns and 'currency_zone' in market_data.columns:
        # Check for markets that change zones frequently (suspicious)
        if 'market_id' in market_data.columns:
            zone_changes = market_data.groupby('market_id')['currency_zone'].nunique()
            frequent_changers = zone_changes[zone_changes > 2]  # Changed zones more than twice
            
            diagnostics['temporal_consistency'] = {
                'markets_with_zone_changes': len(zone_changes[zone_changes > 1]),
                'frequent_zone_changers': len(frequent_changers),
                'most_unstable_markets': frequent_changers.sort_values(ascending=False).head(5).to_dict()
            }
    
    # 4. Generate recommendations
    if diagnostics['geographic_coverage'].get('missing_coordinates', 0) > 0:
        diagnostics['recommendations'].append(
            f"CRITICAL: {diagnostics['geographic_coverage']['missing_coordinates']} markets missing coordinates"
        )
    
    if diagnostics['geographic_coverage'].get('invalid_coordinates', 0) > 0:
        diagnostics['recommendations'].append(
            f"HIGH: {diagnostics['geographic_coverage']['invalid_coordinates']} markets with invalid coordinates"
        )
    
    if diagnostics['boundary_issues'].get('unexpected_zones'):
        diagnostics['recommendations'].append(
            f"MEDIUM: Unexpected currency zones found: {diagnostics['boundary_issues']['unexpected_zones']}"
        )
    
    return diagnostics

def fix_currency_zone_classification(market_data, reference_boundaries=None):
    """
    Fix common currency zone classification issues.
    """
    
    fixed_data = market_data.copy()
    fixes_applied = []
    
    # Fix 1: Standardize zone names
    zone_standardization = {
        'houthis': 'houthi',
        'Houthi': 'houthi', 
        'houthi_controlled': 'houthi',
        'govt': 'government',
        'Government': 'government',
        'government_controlled': 'government',
        'contested_area': 'contested',
        'Contested': 'contested'
    }
    
    if 'currency_zone' in fixed_data.columns:
        old_zones = fixed_data['currency_zone'].unique()
        fixed_data['currency_zone'] = fixed_data['currency_zone'].map(
            lambda x: zone_standardization.get(x, x) if pd.notna(x) else x
        )
        
        new_zones = fixed_data['currency_zone'].unique()
        if set(old_zones) != set(new_zones):
            fixes_applied.append("Standardized currency zone names")
    
    # Fix 2: Geographic coordinate validation and correction
    if 'latitude' in fixed_data.columns and 'longitude' in fixed_data.columns:
        # Remove invalid coordinates
        yemen_bounds = {
            'lat_min': 12.0, 'lat_max': 19.0,
            'lon_min': 42.0, 'lon_max': 54.0
        }
        
        invalid_mask = (
            (fixed_data['latitude'] < yemen_bounds['lat_min']) |
            (fixed_data['latitude'] > yemen_bounds['lat_max']) |
            (fixed_data['longitude'] < yemen_bounds['lon_min']) |
            (fixed_data['longitude'] > yemen_bounds['lon_max'])
        )
        
        invalid_count = invalid_mask.sum()
        if invalid_count > 0:
            fixed_data.loc[invalid_mask, ['latitude', 'longitude']] = np.nan
            fixes_applied.append(f"Invalidated {invalid_count} coordinates outside Yemen bounds")
    
    # Fix 3: Assign currency zones based on coordinates (if reference boundaries available)
    if reference_boundaries is not None:
        fixed_data = assign_zones_from_coordinates(fixed_data, reference_boundaries)
        fixes_applied.append("Reassigned currency zones based on coordinates")
    
    # Fix 4: Handle temporal inconsistencies
    if 'date' in fixed_data.columns and 'market_id' in fixed_data.columns and 'currency_zone' in fixed_data.columns:
        # Forward fill zone assignments within markets (assume zones don't change daily)
        fixed_data = fixed_data.sort_values(['market_id', 'date'])
        fixed_data['currency_zone'] = fixed_data.groupby('market_id')['currency_zone'].fillna(method='ffill')
        fixes_applied.append("Forward-filled currency zone assignments within markets")
    
    return fixed_data, fixes_applied

def assign_zones_from_coordinates(market_data, boundary_definitions):
    """
    Assign currency zones based on geographic coordinates and boundary definitions.
    """
    
    # This is a simplified implementation
    # In practice, would use proper GIS libraries like GeoPandas
    
    def classify_by_coordinates(lat, lon):
        """Simplified coordinate-based classification for Yemen."""
        
        if pd.isna(lat) or pd.isna(lon):
            return 'unknown'
        
        # Simplified rules based on major geographic divisions
        # Northern/Western areas (Houthi-controlled)
        if lat > 15.5 and lon < 44.5:  # Sana'a, Sa'ada region
            return 'houthi'
        elif lat > 14.5 and lon < 43.5:  # Hodeidah region
            return 'houthi'
        
        # Southern/Eastern areas (Government-controlled)
        elif lat < 14.0 and lon > 45.0:  # Aden, Hadramout region
            return 'government'
        elif lat < 15.0 and lon > 47.0:  # Marib region
            return 'government'
        
        # Central contested areas
        elif 13.5 <= lat <= 15.0 and 43.5 <= lon <= 45.0:  # Taiz region
            return 'contested'
        
        # Default to contested for unclear cases
        else:
            return 'contested'
    
    # Apply classification
    market_data['currency_zone_from_coords'] = market_data.apply(
        lambda row: classify_by_coordinates(row['latitude'], row['longitude']),
        axis=1
    )
    
    # Use coordinate-based classification to fill missing zones
    missing_zones = market_data['currency_zone'].isnull()
    market_data.loc[missing_zones, 'currency_zone'] = market_data.loc[missing_zones, 'currency_zone_from_coords']
    
    return market_data

def validate_zone_assignments(market_data, validation_data=None):
    """
    Validate currency zone assignments against external sources.
    """
    
    validation_results = {
        'consistency_checks': {},
        'external_validation': {},
        'confidence_scores': {}
    }
    
    # 1. Internal consistency checks
    if 'market_id' in market_data.columns and 'currency_zone' in market_data.columns:
        # Check for markets with multiple zone assignments
        market_zones = market_data.groupby('market_id')['currency_zone'].nunique()
        inconsistent_markets = market_zones[market_zones > 1]
        
        validation_results['consistency_checks'] = {
            'total_markets': len(market_zones),
            'consistent_markets': len(market_zones[market_zones == 1]),
            'inconsistent_markets': len(inconsistent_markets),
            'consistency_rate': len(market_zones[market_zones == 1]) / len(market_zones)
        }
    
    # 2. External validation (if validation data provided)
    if validation_data is not None and 'market_id' in validation_data.columns:
        # Compare zone assignments
        comparison = market_data.set_index('market_id')['currency_zone'].to_dict()
        validation_zones = validation_data.set_index('market_id')['currency_zone'].to_dict()
        
        matches = 0
        total_comparable = 0
        
        for market_id, assigned_zone in comparison.items():
            if market_id in validation_zones:
                total_comparable += 1
                if assigned_zone == validation_zones[market_id]:
                    matches += 1
        
        validation_results['external_validation'] = {
            'comparable_markets': total_comparable,
            'matching_assignments': matches,
            'agreement_rate': matches / total_comparable if total_comparable > 0 else 0
        }
    
    # 3. Generate confidence scores
    # Based on multiple factors: coordinate quality, temporal consistency, external validation
    if 'market_id' in market_data.columns:
        market_confidence = {}
        
        for market_id in market_data['market_id'].unique():
            market_subset = market_data[market_data['market_id'] == market_id]
            
            confidence_factors = []
            
            # Factor 1: Coordinate availability
            has_coords = market_subset[['latitude', 'longitude']].notna().all(axis=1).any()
            confidence_factors.append(0.4 if has_coords else 0.0)
            
            # Factor 2: Temporal consistency
            zone_consistency = market_subset['currency_zone'].nunique() == 1
            confidence_factors.append(0.3 if zone_consistency else 0.0)
            
            # Factor 3: Zone assignment (non-missing)
            has_zone = market_subset['currency_zone'].notna().any()
            confidence_factors.append(0.3 if has_zone else 0.0)
            
            market_confidence[market_id] = sum(confidence_factors)
        
        validation_results['confidence_scores'] = market_confidence
    
    return validation_results
```

---

## 4. Exchange Rate Pipeline Failures

Exchange rate data pipeline failures are critical in Yemen's multi-currency environment as they affect all price conversions and analyses.

### 4.1 Rate Source Failures

**Problem Category:** Data Pipeline  
**Severity:** Critical  
**Common Symptoms:**
- API connection timeouts to rate sources
- Outdated exchange rate data
- Conflicting rates between sources
- Missing rates for specific currency zones

#### Exchange Rate Pipeline Diagnostics

```python
def diagnose_exchange_rate_pipeline():
    """
    Comprehensive diagnosis of exchange rate data pipeline issues.
    """
    
    pipeline_diagnostics = {
        'data_sources': {},
        'data_quality': {},
        'temporal_coverage': {},
        'rate_consistency': {},
        'recommendations': []
    }
    
    # Test all configured exchange rate sources
    rate_sources = {
        'cby_aden': 'https://api.cby-aden.gov.ye/rates',  # Example URLs
        'cby_sanaa': 'https://api.cby-sanaa.gov.ye/rates',
        'parallel_market': 'https://api.parallel-rates.com/yemen',
        'money_changers': 'local_database'  # Local collection
    }
    
    for source_name, source_url in rate_sources.items():
        try:
            # Test source connectivity and data availability
            if source_url.startswith('http'):
                # Test API connectivity
                import requests
                response = requests.get(source_url, timeout=10)
                
                pipeline_diagnostics['data_sources'][source_name] = {
                    'status': 'connected' if response.status_code == 200 else 'failed',
                    'response_code': response.status_code,
                    'response_time': response.elapsed.total_seconds()
                }
            else:
                # Test local database connectivity
                pipeline_diagnostics['data_sources'][source_name] = {
                    'status': 'local_source',
                    'accessible': True  # Would check actual database connection
                }
                
        except Exception as e:
            pipeline_diagnostics['data_sources'][source_name] = {
                'status': 'error',
                'error_message': str(e)
            }
    
    # Load recent exchange rate data for quality analysis
    try:
        exchange_rate_data = load_exchange_rate_data()  # Placeholder function
        
        if not exchange_rate_data.empty:
            # Analyze data quality
            pipeline_diagnostics['data_quality'] = analyze_rate_data_quality(exchange_rate_data)
            pipeline_diagnostics['temporal_coverage'] = analyze_temporal_coverage(exchange_rate_data)
            pipeline_diagnostics['rate_consistency'] = analyze_rate_consistency(exchange_rate_data)
        
    except Exception as e:
        pipeline_diagnostics['data_quality'] = {'error': str(e)}
    
    # Generate recommendations
    failed_sources = [
        source for source, status in pipeline_diagnostics['data_sources'].items()
        if status.get('status') == 'failed'
    ]
    
    if failed_sources:
        pipeline_diagnostics['recommendations'].append(
            f"CRITICAL: Failed rate sources: {failed_sources}"
        )
    
    return pipeline_diagnostics

def analyze_rate_data_quality(rate_data):
    """Analyze quality of exchange rate data."""
    
    quality_analysis = {
        'completeness': {},
        'outliers': {},
        'volatility': {}
    }
    
    # Completeness analysis
    for zone in ['houthi', 'government']:
        zone_data = rate_data[rate_data['currency_zone'] == zone]
        
        if len(zone_data) > 0:
            quality_analysis['completeness'][zone] = {
                'total_observations': len(zone_data),
                'missing_rates': zone_data['exchange_rate'].isnull().sum(),
                'completeness_rate': (1 - zone_data['exchange_rate'].isnull().mean()) * 100,
                'latest_date': zone_data['date'].max(),
                'data_freshness_days': (pd.Timestamp.now() - zone_data['date'].max()).days
            }
    
    # Outlier detection
    for zone in ['houthi', 'government']:
        zone_data = rate_data[rate_data['currency_zone'] == zone]
        
        if len(zone_data) > 10:
            # Detect extreme daily changes
            daily_changes = zone_data['exchange_rate'].pct_change().abs()
            extreme_changes = daily_changes > 0.1  # >10% daily change
            
            quality_analysis['outliers'][zone] = {
                'extreme_changes': extreme_changes.sum(),
                'max_daily_change': daily_changes.max() * 100,
                'outlier_dates': zone_data.loc[extreme_changes, 'date'].tolist()[:5]  # First 5
            }
    
    return quality_analysis

def fix_exchange_rate_pipeline(issue_type='all'):
    """
    Fix common exchange rate pipeline issues.
    """
    
    fixes_applied = []
    
    if issue_type in ['all', 'connectivity']:
        # Fix 1: Implement rate source failover
        fixes_applied.extend(implement_rate_source_failover())
    
    if issue_type in ['all', 'data_quality']:
        # Fix 2: Clean and validate rate data
        fixes_applied.extend(clean_exchange_rate_data())
    
    if issue_type in ['all', 'imputation']:
        # Fix 3: Implement missing rate imputation
        fixes_applied.extend(implement_rate_imputation())
    
    return fixes_applied

def implement_rate_source_failover():
    """
    Implement automatic failover between exchange rate sources.
    """
    
    failover_fixes = []
    
    # Primary source priority order for each zone
    source_priority = {
        'houthi': ['cby_sanaa', 'parallel_market', 'money_changers'],
        'government': ['cby_aden', 'parallel_market', 'money_changers']
    }
    
    def get_rate_with_failover(date, zone):
        """Get exchange rate with automatic source failover."""
        
        for source in source_priority[zone]:
            try:
                rate = fetch_rate_from_source(source, date, zone)
                if rate is not None and rate > 0:
                    return rate, source
            except Exception:
                continue
        
        return None, None
    
    # Would implement actual failover logic here
    failover_fixes.append("Implemented automatic rate source failover")
    
    return failover_fixes

def clean_exchange_rate_data():
    """
    Clean and validate exchange rate data.
    """
    
    cleaning_fixes = []
    
    # Load current rate data
    rate_data = load_exchange_rate_data()
    
    if rate_data.empty:
        return ["No rate data to clean"]
    
    original_length = len(rate_data)
    
    # 1. Remove impossible rates (negative or zero)
    rate_data = rate_data[rate_data['exchange_rate'] > 0]
    cleaning_fixes.append(f"Removed {original_length - len(rate_data)} invalid rates")
    
    # 2. Remove extreme outliers
    for zone in rate_data['currency_zone'].unique():
        zone_mask = rate_data['currency_zone'] == zone
        zone_rates = rate_data.loc[zone_mask, 'exchange_rate']
        
        # Use IQR method for outlier detection
        Q1 = zone_rates.quantile(0.25)
        Q3 = zone_rates.quantile(0.75)
        IQR = Q3 - Q1
        
        outlier_mask = (
            (zone_rates < Q1 - 3 * IQR) |  # 3 IQR instead of 1.5 for exchange rates
            (zone_rates > Q3 + 3 * IQR)
        )
        
        outlier_count = outlier_mask.sum()
        if outlier_count > 0:
            rate_data.loc[zone_mask & outlier_mask, 'exchange_rate'] = np.nan
            cleaning_fixes.append(f"Flagged {outlier_count} outliers in {zone} zone")
    
    # 3. Smooth extreme volatility
    for zone in rate_data['currency_zone'].unique():
        zone_mask = rate_data['currency_zone'] == zone
        zone_data = rate_data[zone_mask].sort_values('date')
        
        # Calculate daily changes
        daily_changes = zone_data['exchange_rate'].pct_change().abs()
        
        # Flag changes > 20% as potentially erroneous
        extreme_changes = daily_changes > 0.2
        
        if extreme_changes.sum() > 0:
            # Apply smoothing to extreme changes
            smoothed_rates = zone_data['exchange_rate'].rolling(3, center=True).median()
            rate_data.loc[zone_mask & extreme_changes, 'exchange_rate'] = smoothed_rates[extreme_changes]
            cleaning_fixes.append(f"Smoothed {extreme_changes.sum()} extreme changes in {zone} zone")
    
    # Save cleaned data
    save_exchange_rate_data(rate_data)
    
    return cleaning_fixes

def implement_rate_imputation():
    """
    Implement sophisticated missing rate imputation.
    """
    
    imputation_fixes = []
    
    rate_data = load_exchange_rate_data()
    
    # Method 1: Forward fill for short gaps (up to 3 days)
    for zone in rate_data['currency_zone'].unique():
        zone_mask = rate_data['currency_zone'] == zone
        zone_data = rate_data[zone_mask].sort_values('date')
        
        # Forward fill short gaps
        filled_rates = zone_data['exchange_rate'].fillna(method='ffill', limit=3)
        
        imputed_count = filled_rates.notna().sum() - zone_data['exchange_rate'].notna().sum()
        if imputed_count > 0:
            rate_data.loc[zone_mask, 'exchange_rate'] = filled_rates
            imputation_fixes.append(f"Forward-filled {imputed_count} short gaps in {zone} zone")
    
    # Method 2: Interpolation for medium gaps (up to 7 days)
    for zone in rate_data['currency_zone'].unique():
        zone_mask = rate_data['currency_zone'] == zone
        zone_data = rate_data[zone_mask].sort_values('date')
        
        # Linear interpolation
        interpolated_rates = zone_data['exchange_rate'].interpolate(method='linear', limit=7)
        
        additional_imputed = interpolated_rates.notna().sum() - zone_data['exchange_rate'].notna().sum()
        if additional_imputed > 0:
            rate_data.loc[zone_mask, 'exchange_rate'] = interpolated_rates
            imputation_fixes.append(f"Interpolated {additional_imputed} medium gaps in {zone} zone")
    
    # Method 3: Model-based imputation for longer gaps
    for zone in rate_data['currency_zone'].unique():
        zone_mask = rate_data['currency_zone'] == zone
        zone_data = rate_data[zone_mask].sort_values('date')
        
        # Use other zone's rate with adjustment factor for very long gaps
        other_zones = [z for z in rate_data['currency_zone'].unique() if z != zone]
        
        if other_zones and zone_data['exchange_rate'].isnull().sum() > 0:
            # Calculate historical spread between zones
            comparison_data = rate_data.pivot(index='date', columns='currency_zone', values='exchange_rate')
            
            if zone in comparison_data.columns and len(other_zones) > 0:
                other_zone = other_zones[0]
                
                if other_zone in comparison_data.columns:
                    # Calculate average ratio when both rates available
                    ratio_data = comparison_data[zone] / comparison_data[other_zone]
                    avg_ratio = ratio_data.median()
                    
                    # Fill missing values using ratio
                    missing_mask = comparison_data[zone].isnull() & comparison_data[other_zone].notna()
                    estimated_rates = comparison_data.loc[missing_mask, other_zone] * avg_ratio
                    
                    if len(estimated_rates) > 0:
                        # Update original data
                        missing_dates = comparison_data.index[missing_mask]
                        
                        for date in missing_dates:
                            date_mask = (rate_data['date'] == date) & (rate_data['currency_zone'] == zone)
                            rate_data.loc[date_mask, 'exchange_rate'] = estimated_rates[date]
                        
                        imputation_fixes.append(f"Model-imputed {len(estimated_rates)} long gaps in {zone} zone")
    
    # Save imputed data
    save_exchange_rate_data(rate_data)
    
    return imputation_fixes

# Placeholder functions for actual implementation
def load_exchange_rate_data():
    """Load exchange rate data from storage."""
    # Would connect to actual data source
    return pd.DataFrame()

def save_exchange_rate_data(data):
    """Save exchange rate data to storage."""
    # Would save to actual data store
    pass

def fetch_rate_from_source(source, date, zone):
    """Fetch rate from specific source."""
    # Would implement actual API calls
    return None
```

---

## 5. Conflict Data Processing Issues

### 5.1 ACLED Data Integration Problems

**Problem Category:** Conflict Data Processing  
**Severity:** Medium-High  
**Common Symptoms:**
- Geocoding failures for conflict events
- Event type classification inconsistencies
- Temporal aggregation errors
- Proximity calculation failures

#### ACLED Processing Diagnostics

```python
def diagnose_acled_processing_issues(acled_data):
    """
    Diagnose common ACLED data processing problems.
    """
    
    diagnostics = {
        'data_completeness': {},
        'geocoding_issues': {},
        'event_classification': {},
        'temporal_issues': {},
        'recommendations': []
    }
    
    # 1. Data completeness analysis
    required_columns = ['event_date', 'latitude', 'longitude', 'event_type', 'fatalities']
    missing_columns = [col for col in required_columns if col not in acled_data.columns]
    
    diagnostics['data_completeness'] = {
        'total_events': len(acled_data),
        'missing_columns': missing_columns,
        'column_completeness': {}
    }
    
    for col in required_columns:
        if col in acled_data.columns:
            missing_count = acled_data[col].isnull().sum()
            diagnostics['data_completeness']['column_completeness'][col] = {
                'missing_count': missing_count,
                'completeness_rate': (1 - missing_count / len(acled_data)) * 100
            }
    
    # 2. Geocoding quality analysis
    if 'latitude' in acled_data.columns and 'longitude' in acled_data.columns:
        # Check for invalid coordinates
        yemen_bounds = {'lat_min': 12.0, 'lat_max': 19.0, 'lon_min': 42.0, 'lon_max': 54.0}
        
        invalid_coords = (
            (acled_data['latitude'] < yemen_bounds['lat_min']) |
            (acled_data['latitude'] > yemen_bounds['lat_max']) |
            (acled_data['longitude'] < yemen_bounds['lon_min']) |
            (acled_data['longitude'] > yemen_bounds['lon_max'])
        )
        
        # Check for suspiciously precise coordinates (0.000000)
        precise_coords = (
            (acled_data['latitude'] % 1 == 0) |
            (acled_data['longitude'] % 1 == 0)
        )
        
        diagnostics['geocoding_issues'] = {
            'invalid_coordinates': invalid_coords.sum(),
            'precise_coordinates': precise_coords.sum(),
            'missing_coordinates': acled_data[['latitude', 'longitude']].isnull().any(axis=1).sum()
        }
    
    # 3. Event type analysis
    if 'event_type' in acled_data.columns:
        event_type_counts = acled_data['event_type'].value_counts()
        
        # Expected ACLED event types for Yemen
        expected_types = [
            'Battles', 'Violence against civilians', 'Explosions/Remote violence',
            'Riots', 'Strategic developments', 'Protests'
        ]
        
        unexpected_types = [t for t in event_type_counts.index if t not in expected_types]
        
        diagnostics['event_classification'] = {
            'event_type_distribution': event_type_counts.to_dict(),
            'unexpected_event_types': unexpected_types,
            'missing_event_types': acled_data['event_type'].isnull().sum()
        }
    
    # 4. Temporal issues
    if 'event_date' in acled_data.columns:
        # Convert to datetime if not already
        try:
            acled_data['event_date'] = pd.to_datetime(acled_data['event_date'])
            
            # Check date range reasonableness
            date_range = {
                'earliest_date': acled_data['event_date'].min(),
                'latest_date': acled_data['event_date'].max(),
                'date_span_days': (acled_data['event_date'].max() - acled_data['event_date'].min()).days
            }
            
            # Check for future dates (data errors)
            future_dates = acled_data['event_date'] > pd.Timestamp.now()
            
            diagnostics['temporal_issues'] = {
                'date_range': date_range,
                'future_dates': future_dates.sum(),
                'missing_dates': acled_data['event_date'].isnull().sum()
            }
            
        except Exception as e:
            diagnostics['temporal_issues'] = {'error': f"Date parsing failed: {e}"}
    
    # Generate recommendations
    if missing_columns:
        diagnostics['recommendations'].append(
            f"CRITICAL: Missing required columns: {missing_columns}"
        )
    
    if diagnostics['geocoding_issues'].get('invalid_coordinates', 0) > 0:
        diagnostics['recommendations'].append(
            f"HIGH: {diagnostics['geocoding_issues']['invalid_coordinates']} events with invalid coordinates"
        )
    
    if diagnostics['event_classification'].get('unexpected_event_types'):
        diagnostics['recommendations'].append(
            f"MEDIUM: Unexpected event types found: {diagnostics['event_classification']['unexpected_event_types']}"
        )
    
    return diagnostics

def fix_acled_processing_issues(acled_data):
    """
    Fix common ACLED data processing issues.
    """
    
    fixed_data = acled_data.copy()
    fixes_applied = []
    
    # Fix 1: Standardize event types
    event_type_mapping = {
        'Battle': 'Battles',
        'battle': 'Battles',
        'Violence Against Civilians': 'Violence against civilians',
        'violence against civilians': 'Violence against civilians',
        'Remote violence': 'Explosions/Remote violence',
        'remote violence': 'Explosions/Remote violence',
        'Explosion': 'Explosions/Remote violence',
        'Strategic development': 'Strategic developments'
    }
    
    if 'event_type' in fixed_data.columns:
        original_types = fixed_data['event_type'].unique()
        fixed_data['event_type'] = fixed_data['event_type'].map(
            lambda x: event_type_mapping.get(x, x) if pd.notna(x) else x
        )
        
        new_types = fixed_data['event_type'].unique()
        if set(original_types) != set(new_types):
            fixes_applied.append("Standardized event type classifications")
    
    # Fix 2: Clean coordinates
    if 'latitude' in fixed_data.columns and 'longitude' in fixed_data.columns:
        yemen_bounds = {'lat_min': 12.0, 'lat_max': 19.0, 'lon_min': 42.0, 'lon_max': 54.0}
        
        # Flag invalid coordinates
        invalid_coords = (
            (fixed_data['latitude'] < yemen_bounds['lat_min']) |
            (fixed_data['latitude'] > yemen_bounds['lat_max']) |
            (fixed_data['longitude'] < yemen_bounds['lon_min']) |
            (fixed_data['longitude'] > yemen_bounds['lon_max'])
        )
        
        invalid_count = invalid_coords.sum()
        if invalid_count > 0:
            fixed_data.loc[invalid_coords, ['latitude', 'longitude']] = np.nan
            fixes_applied.append(f"Invalidated {invalid_count} coordinates outside Yemen bounds")
    
    # Fix 3: Handle date issues
    if 'event_date' in fixed_data.columns:
        try:
            # Convert to datetime
            fixed_data['event_date'] = pd.to_datetime(fixed_data['event_date'])
            
            # Remove future dates
            future_dates = fixed_data['event_date'] > pd.Timestamp.now()
            future_count = future_dates.sum()
            
            if future_count > 0:
                fixed_data = fixed_data[~future_dates]
                fixes_applied.append(f"Removed {future_count} events with future dates")
                
        except Exception as e:
            fixes_applied.append(f"Date conversion failed: {e}")
    
    # Fix 4: Handle missing fatalities
    if 'fatalities' in fixed_data.columns:
        # Replace negative fatalities with 0
        negative_fatalities = fixed_data['fatalities'] < 0
        negative_count = negative_fatalities.sum()
        
        if negative_count > 0:
            fixed_data.loc[negative_fatalities, 'fatalities'] = 0
            fixes_applied.append(f"Fixed {negative_count} negative fatality counts")
        
        # Fill missing fatalities with 0 for certain event types
        no_fatality_events = ['Strategic developments', 'Protests']
        for event_type in no_fatality_events:
            if event_type in fixed_data['event_type'].values:
                missing_fatalities = (
                    (fixed_data['event_type'] == event_type) & 
                    fixed_data['fatalities'].isnull()
                )
                
                fill_count = missing_fatalities.sum()
                if fill_count > 0:
                    fixed_data.loc[missing_fatalities, 'fatalities'] = 0
                    fixes_applied.append(f"Filled {fill_count} missing fatalities for {event_type}")
    
    return fixed_data, fixes_applied

def implement_robust_conflict_aggregation(acled_data, market_data, radius_km=50):
    """
    Implement robust conflict aggregation around markets with error handling.
    """
    
    aggregation_results = {
        'successful_markets': 0,
        'failed_markets': 0,
        'aggregated_data': None,
        'errors': []
    }
    
    try:
        from geopy.distance import geodesic
        
        aggregated_conflicts = []
        
        for _, market in market_data.iterrows():
            try:
                market_lat = market['latitude']
                market_lon = market['longitude']
                market_id = market.get('market_id', market.name)
                
                if pd.isna(market_lat) or pd.isna(market_lon):
                    aggregation_results['errors'].append(f"Missing coordinates for market {market_id}")
                    aggregation_results['failed_markets'] += 1
                    continue
                
                # Calculate distances to all conflict events
                market_point = (market_lat, market_lon)
                
                # Filter ACLED data for valid coordinates
                valid_acled = acled_data.dropna(subset=['latitude', 'longitude'])
                
                if len(valid_acled) == 0:
                    aggregation_results['errors'].append("No valid ACLED coordinates found")
                    continue
                
                # Calculate distances
                distances = []
                for _, event in valid_acled.iterrows():
                    event_point = (event['latitude'], event['longitude'])
                    
                    try:
                        distance = geodesic(market_point, event_point).kilometers
                        distances.append(distance)
                    except Exception as e:
                        distances.append(np.inf)  # Invalid distance
                
                valid_acled['distance_km'] = distances
                
                # Filter events within radius
                nearby_events = valid_acled[valid_acled['distance_km'] <= radius_km]
                
                # Aggregate by month
                if len(nearby_events) > 0 and 'event_date' in nearby_events.columns:
                    nearby_events['year_month'] = nearby_events['event_date'].dt.to_period('M')
                    
                    monthly_conflicts = nearby_events.groupby('year_month').agg({
                        'latitude': 'count',  # Number of events
                        'fatalities': 'sum'   # Total fatalities
                    }).reset_index()
                    
                    monthly_conflicts.columns = ['year_month', 'conflict_events', 'conflict_fatalities']
                    monthly_conflicts['market_id'] = market_id
                    monthly_conflicts['date'] = monthly_conflicts['year_month'].dt.to_timestamp()
                    
                    aggregated_conflicts.append(monthly_conflicts)
                
                aggregation_results['successful_markets'] += 1
                
            except Exception as e:
                aggregation_results['errors'].append(f"Failed to process market {market_id}: {e}")
                aggregation_results['failed_markets'] += 1
        
        # Combine all aggregated data
        if aggregated_conflicts:
            combined_data = pd.concat(aggregated_conflicts, ignore_index=True)
            aggregation_results['aggregated_data'] = combined_data
        else:
            aggregation_results['errors'].append("No successful conflict aggregations")
    
    except ImportError:
        aggregation_results['errors'].append("geopy library not available for distance calculations")
    
    except Exception as e:
        aggregation_results['errors'].append(f"Aggregation failed: {e}")
    
    return aggregation_results
```

This completes the first 5 sections of the Troubleshooting Master Document. The remaining sections (6-11) will cover spatial analysis problems, performance issues, API integration failures, validation problems, emergency recovery procedures, and preventive maintenance protocols.

Would you like me to continue with the remaining sections or proceed to create the next master document?