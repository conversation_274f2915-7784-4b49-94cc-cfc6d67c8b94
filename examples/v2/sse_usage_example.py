"""
Example of how to use the SSE endpoint for real-time analysis monitoring.

This demonstrates both the server-side event publishing and client-side consumption.
"""

import asyncio
import httpx
import json
from datetime import datetime


# Server-side example: How analysis service publishes events
async def example_analysis_with_events(analysis_id: str, event_bus):
    """Example of how an analysis service would publish progress events."""
    from v2.src.application.events.analysis_events import (
        AnalysisProgressEvent,
        AnalysisStatusChangedEvent,
        TierStartedEvent,
        TierCompletedEvent,
        CommodityProcessingEvent,
        AnalysisCompletedEvent
    )
    
    # Start analysis
    await event_bus.publish(
        AnalysisStatusChangedEvent(
            analysis_id=analysis_id,
            old_status="pending",
            new_status="running"
        )
    )
    
    # Tier 1: Pooled Panel Analysis
    await event_bus.publish(TierStartedEvent(
        analysis_id=analysis_id,
        tier="tier1",
        config={"model": "fixed_effects", "clusters": "market"}
    ))
    
    await asyncio.sleep(2)  # Simulate processing
    
    await event_bus.publish(AnalysisProgressEvent(
        analysis_id=analysis_id,
        progress=33,
        tier="tier1",
        message="Completed pooled panel regression"
    ))
    
    await event_bus.publish(TierCompletedEvent(
        analysis_id=analysis_id,
        tier="tier1",
        results={"r_squared": 0.87, "n_obs": 15000},
        duration_seconds=2.1
    ))
    
    # Tier 2: Commodity-specific Analysis
    await event_bus.publish(TierStartedEvent(
        analysis_id=analysis_id,
        tier="tier2",
        config={"model": "threshold_vecm"}
    ))
    
    commodities = ["Wheat", "Rice", "Sugar", "Oil"]
    for i, commodity in enumerate(commodities):
        await event_bus.publish(CommodityProcessingEvent(
            analysis_id=analysis_id,
            commodity=commodity,
            action="Testing cointegration",
            progress=33 + int((i + 0.5) / len(commodities) * 33)
        ))
        
        await asyncio.sleep(1)  # Simulate processing
        
        await event_bus.publish(CommodityProcessingEvent(
            analysis_id=analysis_id,
            commodity=commodity,
            action="Estimating threshold VECM",
            progress=33 + int((i + 1) / len(commodities) * 33)
        ))
    
    await event_bus.publish(TierCompletedEvent(
        analysis_id=analysis_id,
        tier="tier2",
        results={"n_commodities": 4, "integrated_pairs": 12},
        duration_seconds=4.5
    ))
    
    # Tier 3: Validation
    await event_bus.publish(TierStartedEvent(
        analysis_id=analysis_id,
        tier="tier3",
        config={"validation": ["factor_analysis", "conflict_impact"]}
    ))
    
    await asyncio.sleep(1)
    
    await event_bus.publish(AnalysisProgressEvent(
        analysis_id=analysis_id,
        progress=90,
        tier="tier3",
        message="Running factor analysis validation"
    ))
    
    await asyncio.sleep(1)
    
    # Complete analysis
    await event_bus.publish(AnalysisCompletedEvent(
        analysis_id=analysis_id,
        results_summary={
            "total_duration": 8.7,
            "key_findings": {
                "market_integration": "Strong",
                "conflict_impact": -0.35,
                "policy_recommendations": ["Improve infrastructure", "Reduce trade barriers"]
            }
        },
        duration_seconds=8.7
    ))


# Client-side example: How to consume SSE events
async def consume_sse_events():
    """Example client that consumes SSE events."""
    analysis_id = "test-analysis-123"
    
    async with httpx.AsyncClient() as client:
        async with client.stream(
            "GET", 
            f"http://localhost:8000/api/v2/analyses/{analysis_id}/status"
        ) as response:
            print(f"Connected to SSE stream for analysis {analysis_id}")
            print("-" * 50)
            
            buffer = ""
            async for chunk in response.aiter_text():
                buffer += chunk
                
                # Process complete events (separated by double newline)
                while "\n\n" in buffer:
                    event, buffer = buffer.split("\n\n", 1)
                    
                    if event.strip():
                        # Parse event
                        if event.startswith("data: "):
                            try:
                                data = json.loads(event[6:])
                                handle_event(data)
                            except json.JSONDecodeError:
                                print(f"Failed to parse event: {event}")
                        elif event.startswith(": heartbeat"):
                            print(f"💓 Heartbeat received at {event.split()[-1]}")


def handle_event(data: dict):
    """Handle different types of SSE events."""
    event_type = data.get("event", "unknown")
    timestamp = data.get("timestamp", "")
    
    if event_type == "initial":
        print(f"📊 Initial Status: {data.get('status')} - Progress: {data.get('progress')}%")
    
    elif event_type.startswith("analysis.progress"):
        tier = data.get("tier", "")
        progress = data.get("progress", 0)
        message = data.get("message", "")
        print(f"📈 Progress: {progress}% - {tier}: {message}")
    
    elif event_type.startswith("analysis.status"):
        new_status = data.get("status", "")
        print(f"🔄 Status Changed: {new_status}")
    
    elif event_type.startswith("analysis.tier.started"):
        tier = data.get("tier", "")
        print(f"▶️  Starting {tier}")
    
    elif event_type.startswith("analysis.tier.completed"):
        tier = data.get("tier", "")
        duration = data.get("duration_seconds", 0)
        print(f"✅ Completed {tier} in {duration:.1f}s")
    
    elif event_type.startswith("analysis.commodity"):
        commodity = data.get("commodity", "")
        action = data.get("action", "")
        print(f"🌾 {commodity}: {action}")
    
    elif event_type == "analysis.completed":
        print(f"🎉 Analysis Completed!")
        print(f"   Duration: {data.get('duration_seconds', 0):.1f}s")
        if "results_summary" in data:
            print("   Key Findings:")
            findings = data["results_summary"].get("key_findings", {})
            for key, value in findings.items():
                print(f"     - {key}: {value}")
    
    elif event_type == "analysis.failed":
        error = data.get("error", "Unknown error")
        print(f"❌ Analysis Failed: {error}")
    
    else:
        print(f"❓ Unknown Event: {event_type}")


# JavaScript client example
JS_CLIENT_EXAMPLE = """
// JavaScript/Browser client example for consuming SSE events
const eventSource = new EventSource('/api/v2/analyses/test-analysis-123/status');

eventSource.onopen = function(event) {
    console.log('Connected to analysis status stream');
};

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    // Update UI based on event type
    switch(data.event) {
        case 'initial':
            updateProgressBar(data.progress);
            updateStatus(data.status);
            break;
            
        case 'analysis.progress':
            updateProgressBar(data.progress);
            addLogEntry(`${data.tier}: ${data.message}`);
            break;
            
        case 'analysis.completed':
            showResults(data.results_summary);
            eventSource.close();
            break;
            
        case 'analysis.failed':
            showError(data.error);
            eventSource.close();
            break;
    }
};

eventSource.onerror = function(error) {
    console.error('SSE Error:', error);
    eventSource.close();
};
"""


if __name__ == "__main__":
    # Run the client example
    print("SSE Client Example")
    print("==================")
    print("This would connect to the SSE endpoint and display real-time updates.")
    print("\nExample output:")
    print("-" * 50)
    
    # Simulate some events
    example_events = [
        {"event": "initial", "status": "running", "progress": 0},
        {"event": "analysis.tier.started", "tier": "tier1"},
        {"event": "analysis.progress", "tier": "tier1", "progress": 33, "message": "Completed pooled panel regression"},
        {"event": "analysis.commodity", "commodity": "Wheat", "action": "Testing cointegration"},
        {"event": "analysis.completed", "duration_seconds": 8.7}
    ]
    
    for event in example_events:
        handle_event(event)
        
    print("\n" + "-" * 50)
    print("\nJavaScript Client Example:")
    print(JS_CLIENT_EXAMPLE)