"""
Example demonstrating how to use the V2 plugin system.

This shows how to discover, load, configure, and use plugins for
extensible data ingestion in the Yemen Market Integration platform.
"""

import asyncio
from pathlib import Path
from datetime import datetime

from v2.src.shared.plugins.manager import PluginManager
from v2.src.shared.plugins.registry import PluginRegistry


async def main():
    """Demonstrate plugin system usage."""
    
    # 1. Initialize Plugin Manager
    print("=== Plugin System Demo ===\n")
    
    # Set up plugin directories (can have multiple)
    plugin_dirs = [
        Path(__file__).parent.parent / "plugins",
        Path.home() / ".yemen_market/plugins"  # User plugins
    ]
    
    manager = PluginManager(plugin_dirs=plugin_dirs)
    
    # 2. Discover Available Plugins
    print("1. Discovering plugins...")
    discovered = manager.discover_plugins()
    
    print(f"\nDiscovered plugins:")
    for plugin_type, plugins in discovered.items():
        print(f"  {plugin_type}: {plugins}")
    
    # 3. Load a Specific Plugin
    print("\n2. Loading WFP data source plugin...")
    
    try:
        wfp_plugin = manager.load_plugin("data_sources", "wfp_poc")
        print(f"   ✓ Loaded: {wfp_plugin.metadata.name} v{wfp_plugin.metadata.version}")
        print(f"   Description: {wfp_plugin.metadata.description}")
    except Exception as e:
        print(f"   ✗ Failed to load: {e}")
        return
    
    # 4. Configure the Plugin
    print("\n3. Configuring plugin...")
    
    # Get configuration schema
    schema = wfp_plugin.metadata.config_schema
    print(f"   Configuration schema:")
    for prop, details in schema.get("properties", {}).items():
        required = prop in schema.get("required", [])
        print(f"     - {prop}: {details['type']} {'(required)' if required else '(optional)'}")
        print(f"       {details.get('description', '')}")
    
    # Set up configuration
    config = {
        "data_path": "data/raw/wfp/wfp_food_prices.csv",
        "cache_enabled": True
    }
    
    # Validate configuration
    if wfp_plugin.validate_config(config):
        print(f"\n   ✓ Configuration valid")
        wfp_plugin.initialize(config)
    else:
        print(f"\n   ✗ Invalid configuration")
        return
    
    # 5. Use the Plugin to Fetch Data
    print("\n4. Fetching data using plugin...")
    
    # Example 1: Fetch price data
    print("\n   a) Fetching recent price data...")
    query = {
        "data_type": "prices",
        "markets": ["Sana'a", "Aden"],
        "commodities": ["Wheat", "Rice"]
    }
    
    data = await wfp_plugin.fetch_data(
        query=query,
        start_date="2024-01-01",
        end_date="2024-01-31"
    )
    
    print(f"      Retrieved {len(data)} price observations")
    if data:
        print(f"      Sample: {data[0]}")
    
    # Example 2: Fetch specific data using typed method
    print("\n   b) Using typed method for price data...")
    price_data = await wfp_plugin.fetch_price_data(
        start_date=datetime(2024, 1, 1),
        end_date=datetime(2024, 1, 31),
        markets=["Sana'a"],
        commodities=["Wheat"]
    )
    
    print(f"      Retrieved {len(price_data)} Wheat prices from Sana'a")
    
    # Example 3: Transform to DataFrame
    print("\n   c) Transforming to DataFrame...")
    df = wfp_plugin.transform_data(price_data)
    if not df.empty:
        print(f"      DataFrame shape: {df.shape}")
        print(f"      Columns: {df.columns.tolist()}")
        print(f"      Date range: {df['date'].min()} to {df['date'].max()}")
    
    # 6. List All Loaded Plugins
    print("\n5. Listing all loaded plugins...")
    loaded = manager.list_plugins()
    for plugin_type, plugin_names in loaded.items():
        if plugin_names:
            print(f"   {plugin_type}:")
            for name in plugin_names:
                plugin = manager.get_plugin(plugin_type, name)
                print(f"     - {name}: {plugin.metadata.description}")
    
    # 7. Load All Available Plugins
    print("\n6. Loading all discovered plugins...")
    loaded_counts = manager.load_all_plugins()
    for plugin_type, count in loaded_counts.items():
        print(f"   {plugin_type}: {count} loaded")
    
    # 8. Plugin Development Example
    print("\n=== Plugin Development Guide ===")
    print("""
To create a new data source plugin:

1. Create directory structure:
   plugins/
   └── data_sources/
       └── your_plugin/
           ├── __init__.py
           └── plugin.py

2. Implement the plugin class:
   ```python
   from v2.plugins.data_sources.__plugin_interface import MarketDataSourcePlugin
   
   class YourPlugin(MarketDataSourcePlugin):
       @property
       def metadata(self) -> PluginMetadata:
           return PluginMetadata(
               name="your_plugin",
               version="1.0.0",
               author="Your Name",
               description="Description of your plugin"
           )
       
       async def fetch_price_data(self, ...):
           # Implement data fetching logic
           pass
   ```

3. Register and use:
   - Place in plugins directory
   - Plugin manager will auto-discover
   - Load and use as shown above
""")
    
    # 9. Integration with V2 Services
    print("\n=== Integration Example ===")
    print("""
In V2 services, plugins can be injected and used:

```python
class DataPreparationService:
    def __init__(self, plugin_manager: PluginManager):
        self.plugin_manager = plugin_manager
    
    async def load_market_data(self, source: str, config: dict):
        # Load appropriate plugin
        plugin = self.plugin_manager.get_plugin("data_sources", source)
        if not plugin:
            plugin = self.plugin_manager.load_plugin("data_sources", source)
        
        # Configure and use
        plugin.initialize(config)
        data = await plugin.fetch_price_data(...)
        return plugin.transform_data(data)
```
""")
    
    # 10. Cleanup
    print("\n7. Cleaning up...")
    manager.unload_plugin("data_sources", "wfp_poc")
    print("   ✓ Plugin unloaded")


if __name__ == "__main__":
    # Note: This is a demonstration. In actual usage, the data files
    # would need to exist at the specified paths.
    print("Note: This is a demonstration of the plugin system.")
    print("In production, ensure data files exist at configured paths.\n")
    
    # Run the async main function
    asyncio.run(main())