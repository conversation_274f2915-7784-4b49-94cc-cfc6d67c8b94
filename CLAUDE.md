# Yemen Market Integration - AI Assistant Guide

## Project Context
**Research Question**: Why do high-conflict areas in Yemen show LOWER prices than peaceful areas?

**Key Insight**: Exchange rate fragmentation explains the paradox:
- Houthi areas: ~535 YER/USD (controlled rate)
- Government areas: ~2,100 YER/USD (4x depreciation)

## Critical Project Knowledge

### Exchange Rate Complexity
- Always specify YER vs USD in analysis - results completely change
- Multiple rates exist: Official CBY-Aden, CBY-Sana'a, parallel markets
- Black market premiums vary by location and time

### Data Quirks
- Missing data is NOT random - markets stop reporting during conflict
- WFP prices include both YER and USD - check currency field
- Aid distribution is endogenous - needs instruments
- Ramadan effects are huge - always include month fixed effects

### Architecture (V2)
- Clean hexagonal architecture in `src/`
- FastAPI REST endpoints
- PostgreSQL + Redis for persistence
- JWT auth (not fully implemented)
- See `.env.example` for configuration

## Common Pitfalls

1. **Currency Mix-up**: Never combine YER and USD prices in same analysis
2. **Survivor Bias**: Only resilient traders remain in conflict zones
3. **Aid Endogeneity**: Aid targets worst-affected areas
4. **Missing Not Random**: Conflict drives reporting gaps

## Quick Commands

```bash
# V2 API Server
python src/main.py

# Run Analysis
python src/cli.py analysis run --type three-tier

# Migration from V1
python src/infrastructure/migration/migrate_cli.py

# Generate Results
python scripts/generate_executive_results.py
```

## Current Status
- V2: 75-80% complete (needs auth implementation, database setup)
- V1: Archived in `archive/v1_reference/`
- Docs: Organized in `docs/` with V2 API reference

---
*Focus on econometric identification, not code optimization.*