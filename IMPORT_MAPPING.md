# Import Mapping from V1 to V2 Architecture

## Structure Changes
- `yemen_market.*` → `src.*` (with Clean Architecture structure)

## Detailed Mappings

### Core Utilities
- `yemen_market.utils.logging` → `src.infrastructure.logging`

### Data Processors
- `yemen_market.data.PanelBuilder` → `src.infrastructure.processors.panel_builder.PanelBuilder`
- `yemen_market.data.panel_builder` → `src.infrastructure.processors.panel_builder`

### Three-Tier Models
- `yemen_market.models.three_tier.integration.ThreeTierAnalysis` → `src.application.commands.run_three_tier_analysis`
- `yemen_market.models.three_tier.migration.ModelMigrationHelper` → `src.infrastructure.migration.v1_to_v2_migrator`

### Features
- `yemen_market.features.data_preparation` → `src.application.services.data_preparation_service`
- `yemen_market.features.feature_engineering` → No direct equivalent - functionality distributed

### Individual Model Components
- `yemen_market.models.three_tier.tier1_pooled.*` → `src.core.models.panel.*`
- `yemen_market.models.three_tier.tier2_commodity.*` → `src.core.models.time_series.*`
- `yemen_market.models.three_tier.tier3_validation.*` → `src.core.models.validation.*`

### Analysis and Pipelines
- `yemen_market.analysis.price_transmission` → `src.application.services.analysis_orchestrator`
- `yemen_market.pipelines.enhanced_analysis` → `src.application.services.three_tier_analysis_service`

### Visualization
- `yemen_market.visualization.*` → Needs to be implemented in `src.interfaces.api.rest.routes.*`

## Common Import Patterns

### Old Style
```python
from yemen_market.utils.logging import setup_logging, info, error
from yemen_market.data import PanelBuilder
from yemen_market.models.three_tier.integration import ThreeTierAnalysis
```

### New Style
```python
from src.infrastructure.logging import setup_logging, info, error
from src.infrastructure.processors.panel_builder import PanelBuilder
from src.application.commands.run_three_tier_analysis import ThreeTierAnalysisCommand
```

## Files Requiring Import Updates
- All files in `/scripts/`
- All files in `/examples/`
- Some files in `/src/` that still reference old structure
- Test files in `/tests/`

## Action Required
1. Update all import statements systematically
2. Ensure new imports point to existing modules
3. Create missing bridge modules if needed
4. Update all script entry points