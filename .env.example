# Yemen Market Integration V2 Environment Configuration
# Copy this file to .env and update with your values

# Application Settings
APP_NAME="Yemen Market Integration"
APP_ENV="development"  # development, staging, production
APP_DEBUG="true"
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# API Server Settings
API_HOST="0.0.0.0"
API_PORT="8000"
API_RELOAD="true"  # Auto-reload on code changes (dev only)
API_LOG_LEVEL="info"
API_WORKERS="4"  # Number of worker processes

# Database Configuration
DATABASE_URL="postgresql://user:password@localhost:5432/yemen_market_v2"
DATABASE_POOL_SIZE="20"
DATABASE_MAX_OVERFLOW="40"
DATABASE_POOL_TIMEOUT="30"
DATABASE_ECHO="false"  # SQL query logging

# Redis Configuration (for caching and events)
REDIS_URL="redis://localhost:6379/0"
CACHE_TYPE="redis"  # memory or redis
CACHE_TTL="3600"  # Default cache TTL in seconds
CACHE_MAX_SIZE="1000"  # For memory cache

# Event Bus Configuration
EVENT_BUS_TYPE="redis"  # inmemory or redis
EVENT_QUEUE_SIZE="10000"

# Authentication & Security
JWT_SECRET_KEY="your-secret-key-here-change-in-production"
JWT_ALGORITHM="HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES="30"
JWT_REFRESH_TOKEN_EXPIRE_DAYS="7"
BCRYPT_ROUNDS="12"

# External API Keys
HDX_API_KEY=""  # Humanitarian Data Exchange
HDX_TIMEOUT="30"
WFP_API_KEY=""  # World Food Programme
WFP_TIMEOUT="30"
ACLED_API_KEY=""  # Armed Conflict Location & Event Data
ACLED_EMAIL=""  # Required for ACLED API
ACLED_TIMEOUT="60"

# Storage Paths
DATA_PATH="./data"
POLICY_RESULTS_PATH="./data/policy_results"
MIGRATION_BACKUP_PATH="./data/migrations/backups"
LOG_PATH="./logs"

# Monitoring & Observability
ENABLE_METRICS="true"
METRICS_PORT="9090"
ENABLE_TRACING="false"
JAEGER_AGENT_HOST="localhost"
JAEGER_AGENT_PORT="6831"

# Analysis Configuration
MAX_ANALYSIS_DURATION="3600"  # Maximum analysis time in seconds
ANALYSIS_QUEUE_SIZE="100"
ENABLE_PARALLEL_PROCESSING="true"
MAX_PARALLEL_WORKERS="4"

# Policy Simulation
POLICY_SIMULATION_TIMEOUT="600"
POLICY_CACHE_DURATION="86400"  # 24 hours

# Development Tools
ENABLE_PROFILING="false"
ENABLE_DEBUG_TOOLBAR="false"
ENABLE_SQL_LOGGING="false"

# CORS Settings
CORS_ORIGINS="http://localhost:3000,http://localhost:8000"
CORS_ALLOW_CREDENTIALS="true"
CORS_ALLOW_METHODS="GET,POST,PUT,DELETE,OPTIONS"
CORS_ALLOW_HEADERS="*"

# Rate Limiting
RATE_LIMIT_ENABLED="true"
RATE_LIMIT_DEFAULT="100/hour"
RATE_LIMIT_ANALYSIS="10/hour"

# File Upload Limits
MAX_UPLOAD_SIZE="100MB"
ALLOWED_UPLOAD_EXTENSIONS=".csv,.xlsx,.json,.geojson"

# Notification Settings (optional)
SMTP_HOST=""
SMTP_PORT="587"
SMTP_USER=""
SMTP_PASSWORD=""
SMTP_FROM_EMAIL="<EMAIL>"
ENABLE_EMAIL_NOTIFICATIONS="false"

# Backup Configuration
ENABLE_AUTO_BACKUP="true"
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS="30"

# Feature Flags
ENABLE_V2_API="true"
ENABLE_LEGACY_ENDPOINTS="false"
ENABLE_EXPERIMENTAL_FEATURES="false"
ENABLE_POLICY_SIMULATIONS="true"
ENABLE_REAL_TIME_UPDATES="true"

# Performance Tuning
ENABLE_QUERY_OPTIMIZATION="true"
ENABLE_RESULT_CACHING="true"
ENABLE_CONNECTION_POOLING="true"
BATCH_SIZE="1000"
CHUNK_SIZE="10000"

# Deployment Environment
DEPLOYMENT_ENV="local"  # local, docker, kubernetes
KUBERNETES_NAMESPACE="yemen-market"
DOCKER_REGISTRY=""

# Health Check Settings
HEALTH_CHECK_INTERVAL="30"
HEALTH_CHECK_TIMEOUT="10"
STARTUP_TIMEOUT="60"

# Data Quality Settings
ENABLE_DATA_VALIDATION="true"
DATA_QUALITY_THRESHOLD="0.95"
MISSING_DATA_STRATEGY="interpolate"  # interpolate, drop, or impute

# Debug Settings (disable all in production)
DEBUG_MODE="false"
DEBUG_SQL="false"
DEBUG_CACHE="false"
DEBUG_AUTH="false"
DEBUG_EXTERNAL_APIS="false"