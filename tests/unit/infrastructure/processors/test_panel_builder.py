"""Unit tests for panel data builder.

These tests validate the panel data construction which is fundamental
for all econometric analysis in the Yemen market integration study.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, date
from unittest.mock import Mock, patch, MagicMock

from src.infrastructure.processors.panel_builder import (
    PanelBuilder,
    BalancedPanelBuilder,
    PanelValidator,
    PanelDataQuality
)


class TestPanelBuilder:
    """Test suite for panel data builder."""
    
    @pytest.fixture
    def sample_price_data(self):
        """Create sample price data for panel construction."""
        np.random.seed(42)
        
        markets = ['SANAA', 'ADEN', 'TAIZ', 'HODEIDAH']
        commodities = ['Wheat', 'Rice', 'Oil']
        dates = pd.date_range('2019-01', '2023-12', freq='M')
        
        data_list = []
        for market in markets:
            for commodity in commodities:
                for date in dates:
                    # Some markets may have missing data
                    if np.random.random() < 0.85:  # 85% data availability
                        price = 1000 + np.random.normal(0, 100)
                        data_list.append({
                            'market_id': market,
                            'commodity': commodity,
                            'date': date,
                            'price_yer': price,
                            'currency': 'YER'
                        })
        
        return pd.DataFrame(data_list)
    
    @pytest.fixture
    def sample_conflict_data(self):
        """Create sample conflict data."""
        markets = ['SANAA', 'ADEN', 'TAIZ', 'HODEIDAH']
        dates = pd.date_range('2019-01', '2023-12', freq='M')
        
        data_list = []
        for market in markets:
            for date in dates:
                events = np.random.poisson(5)  # Average 5 events per month
                data_list.append({
                    'market_id': market,
                    'date': date,
                    'events_total': events,
                    'high_conflict': int(events > 10)
                })
        
        return pd.DataFrame(data_list)
    
    @pytest.fixture
    def sample_control_data(self):
        """Create sample control zone data."""
        return pd.DataFrame({
            'market_id': ['SANAA', 'ADEN', 'TAIZ', 'HODEIDAH'],
            'control_zone': ['houthi', 'government', 'contested', 'houthi'],
            'governorate': ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah'],
            'latitude': [15.3694, 12.7855, 13.5795, 14.7975],
            'longitude': [44.1910, 45.0187, 44.0211, 42.9451]
        })
    
    def test_builder_initialization(self):
        """Test PanelBuilder initialization."""
        builder = PanelBuilder(
            entity_col='market_id',
            time_col='date',
            freq='M',
            min_periods=12
        )
        
        assert builder.entity_col == 'market_id'
        assert builder.time_col == 'date'
        assert builder.freq == 'M'
        assert builder.min_periods == 12
    
    def test_build_basic_panel(self, sample_price_data):
        """Test basic panel construction."""
        builder = PanelBuilder(
            entity_col='market_id',
            time_col='date',
            freq='M'
        )
        
        panel = builder.build_panel(sample_price_data)
        
        # Should create a proper panel structure
        assert isinstance(panel, pd.DataFrame)
        assert 'market_id' in panel.columns
        assert 'date' in panel.columns
        assert 'price_yer' in panel.columns
        
        # Should have data for multiple markets and times
        assert panel['market_id'].nunique() > 1
        assert panel['date'].nunique() > 1
    
    def test_merge_datasets(self, sample_price_data, sample_conflict_data, sample_control_data):
        """Test merging multiple datasets into panel."""
        builder = PanelBuilder(
            entity_col='market_id',
            time_col='date'
        )
        
        # Build panel with multiple datasets
        panel = builder.build_panel(
            price_data=sample_price_data,
            conflict_data=sample_conflict_data,
            control_data=sample_control_data
        )
        
        # Should have columns from all datasets
        assert 'price_yer' in panel.columns
        assert 'events_total' in panel.columns
        assert 'control_zone' in panel.columns
        assert 'governorate' in panel.columns
    
    def test_handle_missing_observations(self, sample_price_data):
        """Test handling of missing observations."""
        builder = PanelBuilder(
            entity_col='market_id',
            time_col='date',
            fill_method='forward'
        )
        
        # Introduce missing data
        incomplete_data = sample_price_data.copy()
        # Remove some observations to create gaps
        incomplete_data = incomplete_data.sample(frac=0.7, random_state=42)
        
        panel = builder.build_panel(incomplete_data)
        
        # Should handle missing data
        assert len(panel) >= len(incomplete_data)
        
        # Check that missing data was filled or flagged
        if builder.fill_method == 'forward':
            # Forward fill should reduce missing values
            original_missing = incomplete_data['price_yer'].isna().sum()
            panel_missing = panel['price_yer'].isna().sum()
            assert panel_missing <= original_missing
    
    def test_create_lagged_variables(self, sample_price_data):
        """Test creation of lagged variables."""
        builder = PanelBuilder(
            entity_col='market_id',
            time_col='date',
            create_lags={'price_yer': [1, 2, 3]}
        )
        
        panel = builder.build_panel(sample_price_data)
        
        # Should have lagged variables
        assert 'price_yer_lag1' in panel.columns
        assert 'price_yer_lag2' in panel.columns
        assert 'price_yer_lag3' in panel.columns
        
        # Lagged values should be properly shifted
        for market in panel['market_id'].unique():
            market_data = panel[panel['market_id'] == market].sort_values('date')
            if len(market_data) > 3:
                # Check that lag1 is previous period's value
                assert market_data.iloc[1]['price_yer_lag1'] == market_data.iloc[0]['price_yer']
    
    def test_create_differences(self, sample_price_data):
        """Test creation of first differences."""
        builder = PanelBuilder(
            entity_col='market_id',
            time_col='date',
            create_differences=['price_yer']
        )
        
        panel = builder.build_panel(sample_price_data)
        
        # Should have differenced variables
        assert 'price_yer_diff' in panel.columns
        
        # Check difference calculation
        for market in panel['market_id'].unique():
            market_data = panel[panel['market_id'] == market].sort_values('date')
            if len(market_data) > 1:
                expected_diff = market_data.iloc[1]['price_yer'] - market_data.iloc[0]['price_yer']
                actual_diff = market_data.iloc[1]['price_yer_diff']
                assert abs(expected_diff - actual_diff) < 1e-6
    
    def test_filter_by_time_coverage(self, sample_price_data):
        """Test filtering entities by time coverage."""
        builder = PanelBuilder(
            entity_col='market_id',
            time_col='date',
            min_periods=24  # Require at least 24 months of data
        )
        
        panel = builder.build_panel(sample_price_data)
        
        # Should only include markets with sufficient data
        for market in panel['market_id'].unique():
            market_obs = panel[panel['market_id'] == market]
            assert len(market_obs) >= builder.min_periods
    
    def test_commodity_specific_panels(self, sample_price_data):
        """Test building commodity-specific panels."""
        builder = PanelBuilder(
            entity_col='market_id',
            time_col='date'
        )
        
        # Build panels for each commodity
        wheat_data = sample_price_data[sample_price_data['commodity'] == 'Wheat']
        wheat_panel = builder.build_panel(wheat_data)
        
        # Should only contain wheat data
        assert wheat_panel['commodity'].nunique() == 1
        assert wheat_panel['commodity'].iloc[0] == 'Wheat'
    
    def test_temporal_aggregation(self, sample_price_data):
        """Test temporal aggregation (monthly to quarterly)."""
        # Start with monthly data
        monthly_builder = PanelBuilder(
            entity_col='market_id',
            time_col='date',
            freq='M'
        )
        
        monthly_panel = monthly_builder.build_panel(sample_price_data)
        
        # Aggregate to quarterly
        quarterly_builder = PanelBuilder(
            entity_col='market_id',
            time_col='date',
            freq='Q',
            aggregation_method='mean'
        )
        
        quarterly_panel = quarterly_builder.aggregate_panel(monthly_panel)
        
        # Should have fewer observations (quarterly vs monthly)
        assert len(quarterly_panel) < len(monthly_panel)
        
        # Should preserve market structure
        assert quarterly_panel['market_id'].nunique() == monthly_panel['market_id'].nunique()


class TestBalancedPanelBuilder:
    """Test suite for balanced panel builder."""
    
    @pytest.fixture
    def unbalanced_data(self):
        """Create unbalanced panel data."""
        data_list = []
        
        # Market A: Full data
        for i in range(60):  # 5 years monthly
            data_list.append({
                'market_id': 'A',
                'date': pd.Timestamp('2019-01-01') + pd.DateOffset(months=i),
                'price': 1000 + i * 10
            })
        
        # Market B: Missing middle period
        for i in range(24):  # First 2 years
            data_list.append({
                'market_id': 'B',
                'date': pd.Timestamp('2019-01-01') + pd.DateOffset(months=i),
                'price': 1200 + i * 15
            })
        for i in range(36, 60):  # Last 2 years (missing middle year)
            data_list.append({
                'market_id': 'B',
                'date': pd.Timestamp('2019-01-01') + pd.DateOffset(months=i),
                'price': 1200 + i * 15
            })
        
        # Market C: Short series
        for i in range(12):  # Only 1 year
            data_list.append({
                'market_id': 'C',
                'date': pd.Timestamp('2022-01-01') + pd.DateOffset(months=i),
                'price': 900 + i * 5
            })
        
        return pd.DataFrame(data_list)
    
    def test_balanced_panel_creation(self, unbalanced_data):
        """Test creation of balanced panel."""
        builder = BalancedPanelBuilder(
            entity_col='market_id',
            time_col='date',
            balance_method='intersection'
        )
        
        balanced = builder.create_balanced_panel(unbalanced_data)
        
        # Should be balanced (same number of observations per entity)
        obs_per_entity = balanced.groupby('market_id').size()
        assert obs_per_entity.nunique() == 1  # All entities have same number of obs
    
    def test_balance_methods(self, unbalanced_data):
        """Test different balancing methods."""
        # Intersection method (common time periods only)
        intersection_builder = BalancedPanelBuilder(
            entity_col='market_id',
            time_col='date',
            balance_method='intersection'
        )
        intersection_panel = intersection_builder.create_balanced_panel(unbalanced_data)
        
        # Union method (all time periods, fill missing)
        union_builder = BalancedPanelBuilder(
            entity_col='market_id',
            time_col='date',
            balance_method='union',
            fill_method='interpolate'
        )
        union_panel = union_builder.create_balanced_panel(unbalanced_data)
        
        # Union should have more observations than intersection
        assert len(union_panel) >= len(intersection_panel)
    
    def test_minimum_coverage_requirement(self, unbalanced_data):
        """Test minimum coverage requirements."""
        builder = BalancedPanelBuilder(
            entity_col='market_id',
            time_col='date',
            min_coverage=0.8  # Require 80% data coverage
        )
        
        balanced = builder.create_balanced_panel(unbalanced_data)
        
        # Should exclude entities with insufficient coverage
        # Market C has only 12 months out of 60, so should be excluded
        assert 'C' not in balanced['market_id'].values
    
    def test_interpolation_methods(self, unbalanced_data):
        """Test different interpolation methods for missing data."""
        # Linear interpolation
        linear_builder = BalancedPanelBuilder(
            entity_col='market_id',
            time_col='date',
            fill_method='linear'
        )
        linear_panel = linear_builder.create_balanced_panel(unbalanced_data)
        
        # Forward fill
        ffill_builder = BalancedPanelBuilder(
            entity_col='market_id',
            time_col='date',
            fill_method='forward'
        )
        ffill_panel = ffill_builder.create_balanced_panel(unbalanced_data)
        
        # Both should handle missing data
        assert linear_panel['price'].isna().sum() <= ffill_panel['price'].isna().sum()


class TestPanelValidator:
    """Test suite for panel data validator."""
    
    @pytest.fixture
    def valid_panel(self):
        """Create a valid panel dataset."""
        entities = ['A', 'B', 'C']
        time_periods = pd.date_range('2019-01', '2023-12', freq='M')
        
        data_list = []
        for entity in entities:
            for time in time_periods:
                data_list.append({
                    'entity_id': entity,
                    'date': time,
                    'price': 1000 + np.random.normal(0, 100),
                    'quantity': 100 + np.random.normal(0, 10)
                })
        
        return pd.DataFrame(data_list)
    
    def test_panel_structure_validation(self, valid_panel):
        """Test panel structure validation."""
        validator = PanelValidator(
            entity_col='entity_id',
            time_col='date'
        )
        
        is_valid = validator.validate_panel_structure(valid_panel)
        
        assert is_valid is True
        
        # Test invalid structure (missing entity column)
        invalid_panel = valid_panel.drop('entity_id', axis=1)
        is_invalid = validator.validate_panel_structure(invalid_panel)
        assert is_invalid is False
    
    def test_balance_validation(self, valid_panel):
        """Test panel balance validation."""
        validator = PanelValidator(
            entity_col='entity_id',
            time_col='date'
        )
        
        # Valid panel should be balanced
        is_balanced = validator.is_balanced(valid_panel)
        assert is_balanced is True
        
        # Create unbalanced panel
        unbalanced = valid_panel[valid_panel['entity_id'] != 'C'].copy()
        unbalanced = unbalanced[unbalanced['date'] < '2023-01-01']  # Remove some time periods
        
        is_unbalanced = validator.is_balanced(unbalanced)
        assert is_unbalanced is False
    
    def test_temporal_gaps_detection(self):
        """Test detection of temporal gaps."""
        validator = PanelValidator(
            entity_col='entity_id',
            time_col='date'
        )
        
        # Create data with gaps
        data_with_gaps = pd.DataFrame({
            'entity_id': ['A'] * 10,
            'date': [pd.Timestamp(f'2019-{i:02d}-01') for i in [1, 2, 3, 5, 6, 8, 9, 11, 12, 12]],  # Missing months 4, 7, 10
            'price': range(10)
        })
        
        gaps = validator.detect_temporal_gaps(data_with_gaps)
        
        assert len(gaps) > 0  # Should detect gaps
    
    def test_outlier_detection(self, valid_panel):
        """Test outlier detection in panel data."""
        validator = PanelValidator(
            entity_col='entity_id',
            time_col='date'
        )
        
        # Add outliers
        panel_with_outliers = valid_panel.copy()
        panel_with_outliers.loc[0, 'price'] = 10000  # Extreme outlier
        panel_with_outliers.loc[1, 'price'] = -1000  # Negative price
        
        outliers = validator.detect_outliers(panel_with_outliers, 'price')
        
        assert len(outliers) >= 2  # Should detect at least the 2 outliers we added
    
    def test_stationarity_testing(self, valid_panel):
        """Test stationarity testing for panel variables."""
        validator = PanelValidator(
            entity_col='entity_id',
            time_col='date'
        )
        
        # Add a trending variable (non-stationary)
        panel_with_trend = valid_panel.copy()
        panel_with_trend['trend_var'] = panel_with_trend.groupby('entity_id').cumcount() * 10
        
        stationarity_results = validator.test_stationarity(panel_with_trend, ['price', 'trend_var'])
        
        assert 'price' in stationarity_results
        assert 'trend_var' in stationarity_results
        
        # Price should be more stationary than trend variable
        assert stationarity_results['price']['p_value'] < stationarity_results['trend_var']['p_value']


class TestPanelDataQuality:
    """Test suite for panel data quality assessment."""
    
    @pytest.fixture
    def quality_test_panel(self):
        """Create panel for quality testing."""
        np.random.seed(42)
        entities = ['MARKET_A', 'MARKET_B', 'MARKET_C', 'MARKET_D']
        dates = pd.date_range('2019-01', '2023-12', freq='M')
        
        data_list = []
        for entity in entities:
            for date in dates:
                # Introduce various quality issues
                if np.random.random() < 0.9:  # 90% availability
                    price = 1000 + np.random.normal(0, 100)
                    
                    # Add some extreme values
                    if np.random.random() < 0.02:
                        price *= 10  # Outlier
                    
                    data_list.append({
                        'market_id': entity,
                        'date': date,
                        'price': price,
                        'currency': 'YER' if np.random.random() < 0.8 else 'USD',
                        'source': np.random.choice(['WFP', 'FAO', 'Local'])
                    })
        
        return pd.DataFrame(data_list)
    
    def test_completeness_assessment(self, quality_test_panel):
        """Test data completeness assessment."""
        quality = PanelDataQuality(
            entity_col='market_id',
            time_col='date'
        )
        
        completeness = quality.assess_completeness(quality_test_panel)
        
        assert 'overall_completeness' in completeness
        assert 'entity_completeness' in completeness
        assert 'temporal_completeness' in completeness
        
        # Completeness should be less than 100% due to missing data
        assert completeness['overall_completeness'] < 1.0
    
    def test_consistency_assessment(self, quality_test_panel):
        """Test data consistency assessment."""
        quality = PanelDataQuality(
            entity_col='market_id',
            time_col='date'
        )
        
        consistency = quality.assess_consistency(quality_test_panel)
        
        assert 'price_consistency' in consistency
        assert 'temporal_consistency' in consistency
        
        # Should detect inconsistencies due to currency mixing
        assert consistency['price_consistency']['currency_mixing'] > 0
    
    def test_coverage_analysis(self, quality_test_panel):
        """Test temporal and cross-sectional coverage analysis."""
        quality = PanelDataQuality(
            entity_col='market_id',
            time_col='date'
        )
        
        coverage = quality.analyze_coverage(quality_test_panel)
        
        assert 'temporal_coverage' in coverage
        assert 'entity_coverage' in coverage
        assert 'balanced_periods' in coverage
        
        # Should identify the time period with best coverage
        assert coverage['temporal_coverage']['best_coverage_period'] is not None
    
    def test_generate_quality_report(self, quality_test_panel):
        """Test comprehensive quality report generation."""
        quality = PanelDataQuality(
            entity_col='market_id',
            time_col='date'
        )
        
        report = quality.generate_quality_report(quality_test_panel)
        
        # Should have all major sections
        assert 'summary' in report
        assert 'completeness' in report
        assert 'consistency' in report
        assert 'coverage' in report
        assert 'recommendations' in report
        
        # Summary should have overall score
        assert 'overall_quality_score' in report['summary']
        assert 0 <= report['summary']['overall_quality_score'] <= 1