"""Unit tests for WFP data processor.

These tests validate the WFP price data processing which is critical
for the Yemen market integration analysis.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, date
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from src.infrastructure.processors.wfp_processor import (
    WFPProcessor,
    WFPDataValidator,
    WFPPriceRecord
)


class TestWFPProcessor:
    """Test suite for WFP data processor."""
    
    @pytest.fixture
    def sample_wfp_data(self):
        """Create sample WFP price data."""
        return pd.DataFrame({
            'admin1_name': ['Sana\'a', 'Aden', 'Taiz', 'Sana\'a', 'Aden'],
            'admin2_name': ['Sana\'a City', 'Aden City', 'Taiz City', 'Sana\'a City', 'Aden City'],
            'market_name': ['Central Market', 'Main Market', 'City Market', 'Central Market', 'Main Market'],
            'commodity_name': ['Wheat flour', 'Wheat flour', 'Wheat flour', 'Rice', 'Rice'],
            'unit_name': ['kg', 'kg', 'kg', 'kg', 'kg'],
            'currency_name': ['YER', 'YER', 'USD', 'YER', 'YER'],
            'price': [1000, 1200, 0.8, 1500, 1800],
            'date': ['2023-01-15', '2023-01-15', '2023-01-15', '2023-01-15', '2023-01-15'],
            'latitude': [15.3694, 12.7855, 13.5795, 15.3694, 12.7855],
            'longitude': [44.1910, 45.0187, 44.0211, 44.1910, 45.0187]
        })
    
    @pytest.fixture
    def sample_raw_wfp_csv(self, tmp_path):
        """Create sample raw WFP CSV file."""
        csv_content = '''admin1_name,admin2_name,market_name,commodity_name,unit_name,currency_name,price,date,latitude,longitude
Sana'a,Sana'a City,Central Market,Wheat flour,kg,YER,1000,2023-01-15,15.3694,44.1910
Aden,Aden City,Main Market,Wheat flour,kg,YER,1200,2023-01-15,12.7855,45.0187
Taiz,Taiz City,City Market,Wheat flour,kg,USD,0.8,2023-01-15,13.5795,44.0211
Sana'a,Sana'a City,Central Market,Rice,kg,YER,1500,2023-01-15,15.3694,44.1910
Aden,Aden City,Main Market,Rice,kg,YER,1800,2023-01-15,12.7855,45.0187'''
        
        csv_file = tmp_path / "wfp_data.csv"
        csv_file.write_text(csv_content)
        return csv_file
    
    def test_processor_initialization(self):
        """Test WFPProcessor initialization."""
        processor = WFPProcessor(
            normalize_commodities=True,
            validate_prices=True,
            currency_handling='convert'
        )
        
        assert processor.normalize_commodities is True
        assert processor.validate_prices is True
        assert processor.currency_handling == 'convert'
    
    def test_load_data(self, sample_raw_wfp_csv):
        """Test loading WFP data from CSV."""
        processor = WFPProcessor()
        
        data = processor.load_data(sample_raw_wfp_csv)
        
        assert isinstance(data, pd.DataFrame)
        assert len(data) == 5
        assert 'price' in data.columns
        assert 'date' in data.columns
        assert 'commodity_name' in data.columns
    
    def test_commodity_normalization(self, sample_wfp_data):
        """Test commodity name normalization."""
        processor = WFPProcessor(normalize_commodities=True)
        
        # Add some commodities that need normalization
        test_data = sample_wfp_data.copy()
        test_data['commodity_name'] = [
            'Wheat flour (local)',
            'Wheat Flour',
            'WHEAT FLOUR',
            'Rice (imported)',
            'Rice'
        ]
        
        normalized = processor._normalize_commodity_names(test_data)
        
        # Should normalize to standard names
        wheat_flour_variants = normalized[normalized['commodity_normalized'].str.contains('wheat', case=False, na=False)]
        assert len(wheat_flour_variants['commodity_normalized'].unique()) <= 2  # Should be fewer variants
    
    def test_currency_conversion(self, sample_wfp_data):
        """Test currency conversion functionality."""
        processor = WFPProcessor(currency_handling='convert')
        
        # Mock exchange rate data
        exchange_rates = pd.DataFrame({
            'date': ['2023-01-15'],
            'YER_USD': [1500.0]  # 1500 YER per USD
        })
        
        with patch.object(processor, '_get_exchange_rates', return_value=exchange_rates):
            converted = processor._convert_currencies(sample_wfp_data)
            
            # Should have USD prices for all records
            assert 'price_usd' in converted.columns
            assert converted['price_usd'].notna().all()
    
    def test_price_validation(self, sample_wfp_data):
        """Test price validation and outlier detection."""
        processor = WFPProcessor(validate_prices=True)
        
        # Add some outliers
        test_data = sample_wfp_data.copy()
        test_data.loc[0, 'price'] = 100000  # Extreme outlier
        test_data.loc[1, 'price'] = -50     # Negative price
        
        validated = processor._validate_prices(test_data)
        
        # Should flag or remove outliers
        assert 'price_outlier' in validated.columns
        assert validated['price_outlier'].any()  # Should detect outliers
        
        # Negative prices should be flagged
        negative_prices = validated[validated['price'] < 0]
        assert len(negative_prices) == 0 or negative_prices['price_outlier'].all()
    
    def test_market_standardization(self, sample_wfp_data):
        """Test market name and location standardization."""
        processor = WFPProcessor()
        
        standardized = processor._standardize_markets(sample_wfp_data)
        
        # Should have standardized market IDs
        assert 'market_id' in standardized.columns
        assert standardized['market_id'].notna().all()
        
        # Market IDs should be consistent for same locations
        sanaa_markets = standardized[standardized['admin1_name'] == "Sana'a"]
        assert sanaa_markets['market_id'].nunique() >= 1
    
    def test_temporal_processing(self, sample_wfp_data):
        """Test temporal data processing."""
        processor = WFPProcessor()
        
        # Add various date formats
        test_data = sample_wfp_data.copy()
        test_data['date'] = [
            '2023-01-15',
            '15/01/2023',
            '2023-01-15T00:00:00',
            'January 15, 2023',
            '2023-01-15'
        ]
        
        processed = processor._process_temporal_data(test_data)
        
        # Should parse all date formats
        assert 'date_parsed' in processed.columns
        assert processed['date_parsed'].notna().all()
        
        # Should add temporal features
        assert 'year' in processed.columns
        assert 'month' in processed.columns
        assert 'quarter' in processed.columns
    
    def test_missing_data_handling(self):
        """Test handling of missing data."""
        processor = WFPProcessor()
        
        # Create data with missing values
        data_with_missing = pd.DataFrame({
            'admin1_name': ['Sana\'a', None, 'Taiz'],
            'market_name': ['Central Market', 'Main Market', None],
            'commodity_name': ['Wheat flour', 'Rice', 'Wheat flour'],
            'price': [1000, None, 1200],
            'date': ['2023-01-15', '2023-01-15', None],
            'currency_name': ['YER', 'YER', 'YER']
        })
        
        cleaned = processor._handle_missing_data(data_with_missing)
        
        # Should handle missing data appropriately
        assert len(cleaned) <= len(data_with_missing)  # May drop rows
        
        # Critical fields should not be missing
        assert cleaned['price'].notna().all()
        assert cleaned['date'].notna().all()
    
    def test_duplicate_detection(self):
        """Test duplicate record detection and handling."""
        processor = WFPProcessor()
        
        # Create data with duplicates
        data_with_duplicates = pd.DataFrame({
            'admin1_name': ['Sana\'a', 'Sana\'a', 'Aden'],
            'market_name': ['Central Market', 'Central Market', 'Main Market'],
            'commodity_name': ['Wheat flour', 'Wheat flour', 'Rice'],
            'price': [1000, 1000, 1500],
            'date': ['2023-01-15', '2023-01-15', '2023-01-15'],
            'currency_name': ['YER', 'YER', 'YER']
        })
        
        deduplicated = processor._remove_duplicates(data_with_duplicates)
        
        # Should remove or flag duplicates
        assert len(deduplicated) <= len(data_with_duplicates)
    
    def test_unit_standardization(self, sample_wfp_data):
        """Test unit standardization (kg, ton, etc.)."""
        processor = WFPProcessor()
        
        # Add various units
        test_data = sample_wfp_data.copy()
        test_data['unit_name'] = ['kg', 'Kg', 'KG', 'kilogram', 'ton']
        test_data['price'] = [1000, 1000, 1000, 1000, 1000000]  # ton should be much higher
        
        standardized = processor._standardize_units(test_data)
        
        # Should standardize units
        assert 'unit_standardized' in standardized.columns
        assert 'price_per_kg' in standardized.columns
        
        # Prices should be converted to per-kg basis
        ton_record = standardized[standardized['unit_name'] == 'ton']
        if len(ton_record) > 0:
            assert ton_record['price_per_kg'].iloc[0] < ton_record['price'].iloc[0]
    
    def test_geographical_validation(self, sample_wfp_data):
        """Test geographical coordinate validation."""
        processor = WFPProcessor()
        
        # Add invalid coordinates
        test_data = sample_wfp_data.copy()
        test_data['latitude'] = [15.3694, 91.0, 13.5795, -91.0, 12.7855]  # Invalid lat
        test_data['longitude'] = [44.1910, 45.0187, 181.0, 44.0211, -181.0]  # Invalid lon
        
        validated = processor._validate_geography(test_data)
        
        # Should flag invalid coordinates
        assert 'geo_valid' in validated.columns
        invalid_coords = validated[~validated['geo_valid']]
        assert len(invalid_coords) > 0  # Should detect invalid coords
    
    def test_process_full_pipeline(self, sample_raw_wfp_csv):
        """Test full processing pipeline."""
        processor = WFPProcessor(
            normalize_commodities=True,
            validate_prices=True,
            currency_handling='preserve',
            remove_outliers=True
        )
        
        # Mock external dependencies
        with patch.object(processor, '_get_exchange_rates', return_value=pd.DataFrame({
            'date': ['2023-01-15'],
            'YER_USD': [1500.0]
        })):
            result = processor.process(sample_raw_wfp_csv)
            
            # Should return processed data
            assert isinstance(result, pd.DataFrame)
            assert len(result) > 0
            
            # Should have required columns
            required_columns = ['market_id', 'commodity_normalized', 'price_validated', 'date_parsed']
            for col in required_columns:
                assert col in result.columns, f"Missing column: {col}"
    
    def test_commodity_grouping(self, sample_wfp_data):
        """Test commodity grouping for analysis."""
        processor = WFPProcessor()
        
        # Add more diverse commodities
        test_data = sample_wfp_data.copy()
        test_data['commodity_name'] = [
            'Wheat flour',
            'Wheat grain',
            'Rice (imported)',
            'Vegetable oil',
            'Diesel fuel'
        ]
        
        grouped = processor._group_commodities(test_data)
        
        # Should have commodity groups
        assert 'commodity_group' in grouped.columns
        
        # Similar commodities should be grouped
        wheat_items = grouped[grouped['commodity_name'].str.contains('wheat', case=False, na=False)]
        if len(wheat_items) > 1:
            assert wheat_items['commodity_group'].nunique() <= 2  # Should group wheat items
    
    def test_price_aggregation_by_period(self, sample_wfp_data):
        """Test price aggregation by time periods."""
        processor = WFPProcessor()
        
        # Create data with multiple dates
        test_data = pd.concat([
            sample_wfp_data,
            sample_wfp_data.copy()
        ])
        test_data.loc[len(sample_wfp_data):, 'date'] = '2023-01-22'  # One week later
        test_data.loc[len(sample_wfp_data):, 'price'] *= 1.1  # Slight price increase
        
        aggregated = processor._aggregate_by_period(test_data, period='weekly')
        
        # Should aggregate prices by week
        assert len(aggregated) <= len(test_data)
        assert 'price_mean' in aggregated.columns
        assert 'price_std' in aggregated.columns
        assert 'observation_count' in aggregated.columns


class TestWFPDataValidator:
    """Test suite for WFP data validator."""
    
    def test_price_range_validation(self):
        """Test price range validation."""
        validator = WFPDataValidator()
        
        # Test valid prices
        assert validator.validate_price_range(1000, 'Wheat flour') is True
        assert validator.validate_price_range(1500, 'Rice') is True
        
        # Test invalid prices
        assert validator.validate_price_range(-100, 'Wheat flour') is False
        assert validator.validate_price_range(1000000, 'Rice') is False
        assert validator.validate_price_range(0, 'Wheat flour') is False
    
    def test_commodity_validation(self):
        """Test commodity name validation."""
        validator = WFPDataValidator()
        
        # Test valid commodities
        assert validator.validate_commodity('Wheat flour') is True
        assert validator.validate_commodity('Rice') is True
        assert validator.validate_commodity('Vegetable oil') is True
        
        # Test invalid/suspicious commodities
        assert validator.validate_commodity('') is False
        assert validator.validate_commodity(None) is False
        assert validator.validate_commodity('Unknown item') is False
    
    def test_market_validation(self):
        """Test market location validation."""
        validator = WFPDataValidator()
        
        # Test valid Yemen locations
        assert validator.validate_market_location(15.3694, 44.1910, "Sana'a") is True
        assert validator.validate_market_location(12.7855, 45.0187, "Aden") is True
        
        # Test invalid locations
        assert validator.validate_market_location(91.0, 44.1910, "Sana'a") is False  # Invalid lat
        assert validator.validate_market_location(15.3694, 181.0, "Sana'a") is False  # Invalid lon
        assert validator.validate_market_location(40.7128, -74.0060, "New York") is False  # Not in Yemen
    
    def test_date_validation(self):
        """Test date validation."""
        validator = WFPDataValidator()
        
        # Test valid dates
        assert validator.validate_date('2023-01-15') is True
        assert validator.validate_date('2019-03-01') is True
        
        # Test invalid dates
        assert validator.validate_date('2025-01-01') is False  # Future date
        assert validator.validate_date('2000-01-01') is False  # Too old
        assert validator.validate_date('invalid-date') is False
        assert validator.validate_date('') is False
    
    def test_currency_validation(self):
        """Test currency validation."""
        validator = WFPDataValidator()
        
        # Test valid currencies for Yemen
        assert validator.validate_currency('YER') is True
        assert validator.validate_currency('USD') is True
        
        # Test invalid currencies
        assert validator.validate_currency('EUR') is False
        assert validator.validate_currency('GBP') is False
        assert validator.validate_currency('') is False
        assert validator.validate_currency(None) is False