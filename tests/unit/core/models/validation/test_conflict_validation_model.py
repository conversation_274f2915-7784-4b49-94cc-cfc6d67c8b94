"""Unit tests for conflict validation model.

These tests validate the core finding that conflict reduces prices by ~35%
in Yemen markets, which contradicts standard economic theory.
"""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch

from src.core.models.validation.conflict_validation_model import (
    ConflictValidationModel,
    ConflictEffectEstimator,
    ConflictValidationResults
)


class TestConflictValidationModel:
    """Test suite for conflict effect validation."""
    
    @pytest.fixture
    def yemen_conflict_data(self):
        """Create realistic Yemen conflict and price data."""
        np.random.seed(42)
        
        # Generate 5 years of monthly data for 10 markets
        dates = pd.date_range('2019-01', '2023-12', freq='M')
        markets = ['SANAA', 'ADEN', 'TAIZ', 'HODEIDAH', 'IBB', 
                  'MUKALLA', 'ZINJ<PERSON><PERSON>', 'LAHJ', 'ABYAN', 'MARIB']
        
        data_list = []
        
        for market in markets:
            # Market characteristics
            is_houthi = market in ['SANAA', 'HODEIDAH']
            is_frontline = market in ['TAIZ', 'MARIB']
            
            for date in dates:
                # Generate conflict events with realistic patterns
                if is_frontline:
                    base_conflict = 8  # Higher baseline conflict
                elif is_houthi:
                    base_conflict = 2  # Lower conflict in controlled areas
                else:
                    base_conflict = 4  # Medium conflict
                
                # Add temporal variation (escalations)
                if date.year == 2021:  # War escalation
                    base_conflict *= 1.8
                elif date.year == 2022:  # Truce period
                    base_conflict *= 0.4
                
                events_total = np.random.poisson(base_conflict)
                high_conflict = events_total > 10
                
                # Generate realistic exchange rates
                if is_houthi:
                    # Houthi areas: stable around 540 YER/USD
                    exchange_rate = 540 + np.random.normal(0, 20)
                else:
                    # Government areas: depreciated around 1500 YER/USD
                    exchange_rate = 1500 + np.random.normal(0, 150)
                
                # Generate prices with conflict effect
                base_wheat_price = 800  # Base price in USD terms
                
                # KEY FINDING: Conflict reduces prices
                # This is the "negative price premium" that contradicts theory
                conflict_effect = -0.35 * (events_total / 10)  # -35% at high conflict
                
                # Exchange rate effect (converts to YER)
                price_yer = base_wheat_price * (1 + conflict_effect) * exchange_rate
                log_price_yer = np.log(price_yer)
                
                # Price in USD terms (should show different pattern)
                price_usd = base_wheat_price * (1 + conflict_effect)
                log_price_usd = np.log(price_usd)
                
                data_list.append({
                    'market_id': market,
                    'date': date,
                    'events_total': events_total,
                    'high_conflict': int(high_conflict),
                    'events_lag1': max(0, events_total - np.random.poisson(1)),
                    'events_lag2': max(0, events_total - np.random.poisson(2)),
                    'exchange_rate': exchange_rate,
                    'price_yer': price_yer,
                    'price_usd': price_usd,
                    'log_price_yer': log_price_yer,
                    'log_price_usd': log_price_usd,
                    'control_zone': 'houthi' if is_houthi else 'government',
                    'frontline': int(is_frontline)
                })
        
        return pd.DataFrame(data_list)
    
    def test_model_initialization(self):
        """Test ConflictValidationModel initialization."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.35,
            tolerance=0.05,
            confidence_level=0.95
        )
        
        assert model.expected_effect_magnitude == -0.35
        assert model.tolerance == 0.05
        assert model.confidence_level == 0.95
    
    def test_validate_core_finding_yer(self, yemen_conflict_data):
        """Test validation of core finding in YER terms."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.35,
            tolerance=0.10
        )
        
        results = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_yer',
            conflict_col='events_total',
            entity_col='market_id',
            time_col='date'
        )
        
        # Should validate the negative price effect
        assert isinstance(results, ConflictValidationResults)
        assert results.effect_detected is True
        assert results.coefficient < 0  # Negative effect
        assert abs(results.coefficient - (-0.35)) < model.tolerance
    
    def test_validate_core_finding_usd(self, yemen_conflict_data):
        """Test validation in USD terms (should be different)."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.10,  # Smaller effect in USD
            tolerance=0.15
        )
        
        results = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_usd',
            conflict_col='events_total',
            entity_col='market_id',
            time_col='date'
        )
        
        # Effect should be smaller in USD terms
        assert results.effect_detected is True
        assert results.coefficient < 0
        # USD effect should be smaller than YER effect
        assert abs(results.coefficient) < 0.25
    
    def test_high_conflict_indicator(self, yemen_conflict_data):
        """Test validation with high conflict indicator."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.40,  # Stronger effect for high conflict
            tolerance=0.15
        )
        
        results = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_yer',
            conflict_col='high_conflict',
            entity_col='market_id',
            time_col='date'
        )
        
        # High conflict should have stronger effect
        assert results.effect_detected is True
        assert results.coefficient < -0.20
        assert results.p_value < 0.05
    
    def test_lagged_effects(self, yemen_conflict_data):
        """Test validation of lagged conflict effects."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.15,  # Smaller lag effects
            tolerance=0.10
        )
        
        # Test first lag
        results_lag1 = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_yer',
            conflict_col='events_lag1',
            entity_col='market_id',
            time_col='date'
        )
        
        # Test second lag
        results_lag2 = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_yer',
            conflict_col='events_lag2',
            entity_col='market_id',
            time_col='date'
        )
        
        # Lag effects should be significant but smaller
        assert results_lag1.effect_detected is True
        assert results_lag2.effect_detected is True
        assert abs(results_lag1.coefficient) < abs(results_lag2.coefficient)
    
    def test_control_zone_heterogeneity(self, yemen_conflict_data):
        """Test heterogeneous effects by control zone."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.35,
            tolerance=0.20
        )
        
        # Test Houthi areas
        houthi_data = yemen_conflict_data[yemen_conflict_data['control_zone'] == 'houthi']
        houthi_results = model.validate_conflict_effect(
            data=houthi_data,
            price_col='log_price_yer',
            conflict_col='events_total',
            entity_col='market_id',
            time_col='date'
        )
        
        # Test Government areas
        gov_data = yemen_conflict_data[yemen_conflict_data['control_zone'] == 'government']
        gov_results = model.validate_conflict_effect(
            data=gov_data,
            price_col='log_price_yer',
            conflict_col='events_total',
            entity_col='market_id',
            time_col='date'
        )
        
        # Both should show negative effects but may differ in magnitude
        assert houthi_results.coefficient < 0
        assert gov_results.coefficient < 0
    
    def test_temporal_stability(self, yemen_conflict_data):
        """Test temporal stability of conflict effects."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.35,
            tolerance=0.15
        )
        
        # Split by time periods
        period_results = {}
        
        for year in [2019, 2020, 2021, 2022, 2023]:
            year_data = yemen_conflict_data[yemen_conflict_data['date'].dt.year == year]
            
            if len(year_data) > 50:  # Enough observations
                results = model.validate_conflict_effect(
                    data=year_data,
                    price_col='log_price_yer',
                    conflict_col='events_total',
                    entity_col='market_id',
                    time_col='date'
                )
                period_results[year] = results.coefficient
        
        # Should find negative effects in most periods
        negative_coeffs = sum(1 for coeff in period_results.values() if coeff < 0)
        assert negative_coeffs >= len(period_results) * 0.7  # At least 70%
    
    def test_robustness_checks(self, yemen_conflict_data):
        """Test robustness of conflict effect validation."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.35,
            tolerance=0.15,
            run_robustness_checks=True
        )
        
        results = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_yer',
            conflict_col='events_total',
            entity_col='market_id',
            time_col='date'
        )
        
        # Should have robustness check results
        assert hasattr(results, 'robustness_checks')
        assert 'alternative_specifications' in results.robustness_checks
        assert 'subsample_stability' in results.robustness_checks
        assert 'outlier_sensitivity' in results.robustness_checks
    
    def test_mechanism_validation(self, yemen_conflict_data):
        """Test validation of proposed mechanisms."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.35,
            tolerance=0.15
        )
        
        # Test demand destruction mechanism
        # Include population proxy (frontline indicator)
        mechanism_results = model.validate_mechanism(
            data=yemen_conflict_data,
            price_col='log_price_yer',
            conflict_col='events_total',
            mechanism_col='frontline',
            entity_col='market_id',
            time_col='date'
        )
        
        # Mechanism should be significant
        assert mechanism_results.mechanism_significant is True
        assert 'interaction_coefficient' in mechanism_results.__dict__
    
    def test_currency_comparison(self, yemen_conflict_data):
        """Test comparison between YER and USD effects."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.35,
            tolerance=0.20
        )
        
        # Validate YER effects
        yer_results = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_yer',
            conflict_col='events_total',
            entity_col='market_id',
            time_col='date'
        )
        
        # Validate USD effects
        usd_results = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_usd',
            conflict_col='events_total',
            entity_col='market_id',
            time_col='date'
        )
        
        # Compare effects
        comparison = model.compare_currency_effects(yer_results, usd_results)
        
        assert 'yer_effect' in comparison
        assert 'usd_effect' in comparison
        assert 'difference' in comparison
        assert 'difference_significant' in comparison
        
        # YER effect should be larger (more negative)
        assert abs(comparison['yer_effect']) > abs(comparison['usd_effect'])
    
    def test_statistical_significance(self, yemen_conflict_data):
        """Test statistical significance of conflict effects."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.35,
            tolerance=0.15,
            confidence_level=0.99  # Strict significance test
        )
        
        results = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_yer',
            conflict_col='events_total',
            entity_col='market_id',
            time_col='date'
        )
        
        # Should be highly significant
        assert results.p_value < 0.01
        assert results.statistically_significant is True
        
        # Confidence interval should not include zero
        ci_lower, ci_upper = results.confidence_interval
        assert ci_upper < 0  # Entire CI should be negative
    
    def test_economic_significance(self, yemen_conflict_data):
        """Test economic significance of effects."""
        model = ConflictValidationModel(
            expected_effect_magnitude=-0.35,
            tolerance=0.10,
            min_economic_significance=0.05  # 5% price change minimum
        )
        
        results = model.validate_conflict_effect(
            data=yemen_conflict_data,
            price_col='log_price_yer',
            conflict_col='events_total',
            entity_col='market_id',
            time_col='date'
        )
        
        # Should be economically significant
        assert results.economically_significant is True
        assert abs(results.coefficient) > model.min_economic_significance
        
        # Calculate implied price reduction
        price_reduction = 1 - np.exp(results.coefficient)
        assert price_reduction > 0.20  # At least 20% price reduction


class TestConflictEffectEstimator:
    """Test the underlying conflict effect estimator."""
    
    def test_fixed_effects_estimation(self, yemen_conflict_data):
        """Test fixed effects estimation of conflict effects."""
        estimator = ConflictEffectEstimator(method='fixed_effects')
        
        results = estimator.estimate(
            data=yemen_conflict_data,
            dependent_var='log_price_yer',
            conflict_var='events_total',
            entity_col='market_id',
            time_col='date',
            controls=['exchange_rate']
        )
        
        assert 'coefficient' in results
        assert 'standard_error' in results
        assert 'p_value' in results
        assert results['coefficient'] < 0
    
    def test_instrumental_variables(self, yemen_conflict_data):
        """Test IV estimation for conflict effects."""
        # Create spatial lag instrument
        estimator = ConflictEffectEstimator(method='instrumental_variables')
        
        # Mock spatial instrument (would use actual spatial data in practice)
        yemen_conflict_data['conflict_spatial_lag'] = (
            yemen_conflict_data.groupby('date')['events_total']
            .transform(lambda x: x.shift(1).fillna(x.mean()))
        )
        
        results = estimator.estimate(
            data=yemen_conflict_data,
            dependent_var='log_price_yer',
            conflict_var='events_total',
            instrument='conflict_spatial_lag',
            entity_col='market_id',
            time_col='date'
        )
        
        assert 'coefficient' in results
        assert 'first_stage_f' in results
        assert results['first_stage_f'] > 10  # Strong instrument
    
    def test_difference_in_differences(self):
        """Test difference-in-differences estimation."""
        # Create treatment/control setup
        np.random.seed(42)
        
        # Generate panel data with treatment
        data_list = []
        for entity in range(20):
            treated = entity < 10  # First 10 entities are treated
            
            for time in range(24):
                post_treatment = time >= 12  # Treatment starts at period 12
                
                # Treatment effect: -30% price reduction
                treatment_effect = -0.30 if (treated and post_treatment) else 0
                
                price = 8 + treatment_effect + np.random.normal(0, 0.2)
                
                data_list.append({
                    'entity': entity,
                    'time': time,
                    'price': price,
                    'treated': int(treated),
                    'post': int(post_treatment),
                    'treated_post': int(treated and post_treatment)
                })
        
        data = pd.DataFrame(data_list)
        
        estimator = ConflictEffectEstimator(method='difference_in_differences')
        
        results = estimator.estimate(
            data=data,
            dependent_var='price',
            treatment_var='treated_post',
            entity_col='entity',
            time_col='time'
        )
        
        # Should recover treatment effect
        assert abs(results['coefficient'] - (-0.30)) < 0.1