"""
Global pytest configuration and fixtures for Yemen Market Integration tests.

This file is automatically loaded by pytest and provides shared fixtures
and configuration for all tests.
"""

import os
import sys
from pathlib import Path
from typing import Generator

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Test data directory
TEST_DATA_DIR = Path(__file__).parent / "fixtures" / "data"


# --- Global Fixtures ---

@pytest.fixture(scope="session")
def test_data_dir() -> Path:
    """Return path to test data directory."""
    return TEST_DATA_DIR


@pytest.fixture
def sample_price_data() -> pd.DataFrame:
    """Generate sample price data for testing."""
    np.random.seed(42)
    
    markets = ["SANAA", "ADEN", "TAIZ"]
    commodities = ["WHEAT", "RICE", "FUEL"]
    dates = pd.date_range("2023-01-01", "2023-12-31", freq="W")
    
    data = []
    for market in markets:
        for commodity in commodities:
            for date in dates:
                # Generate realistic price with some noise
                base_price = {"WHEAT": 1000, "RICE": 1500, "FUEL": 800}[commodity]
                price = base_price + np.random.normal(0, base_price * 0.1)
                
                data.append({
                    "market_id": market,
                    "commodity": commodity,
                    "date": date,
                    "price_yer": price,
                    "currency": "YER"
                })
    
    return pd.DataFrame(data)


@pytest.fixture
def sample_conflict_data() -> pd.DataFrame:
    """Generate sample conflict event data for testing."""
    np.random.seed(42)
    
    markets = ["SANAA", "ADEN", "TAIZ"]
    dates = pd.date_range("2023-01-01", "2023-12-31", freq="D")
    
    data = []
    for _ in range(100):  # 100 random events
        data.append({
            "event_date": np.random.choice(dates),
            "location": np.random.choice(markets),
            "event_type": np.random.choice(["Battles", "Explosions", "Protests"]),
            "fatalities": np.random.poisson(2),
            "latitude": np.random.uniform(12, 17),
            "longitude": np.random.uniform(42, 48)
        })
    
    return pd.DataFrame(data)


@pytest.fixture
def sample_market_metadata() -> dict:
    """Generate sample market metadata for testing."""
    return {
        "SANAA": {
            "governorate": "Sana'a",
            "district": "Old City",
            "market_type": "URBAN",
            "latitude": 15.3694,
            "longitude": 44.1910,
            "control_zone": "HOUTHI"
        },
        "ADEN": {
            "governorate": "Aden",
            "district": "Crater",
            "market_type": "URBAN",
            "latitude": 12.7855,
            "longitude": 45.0187,
            "control_zone": "GOVERNMENT"
        },
        "TAIZ": {
            "governorate": "Taiz",
            "district": "Al Qahirah",
            "market_type": "URBAN",
            "latitude": 13.5795,
            "longitude": 44.0211,
            "control_zone": "CONTESTED"
        }
    }


@pytest.fixture
def mock_database_connection():
    """Mock database connection for testing."""
    # This would be replaced with actual test database setup
    # For now, using in-memory implementations
    pass


# --- Pytest Configuration ---

def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "requires_data: marks tests that require external data"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test location."""
    for item in items:
        # Add markers based on test file location
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        
        # Mark slow tests
        if "slow" in item.name or "integration" in str(item.fspath):
            item.add_marker(pytest.mark.slow)


# --- Utility Functions ---

def create_test_panel_data(
    n_markets: int = 5,
    n_periods: int = 52,
    start_date: str = "2023-01-01"
) -> pd.DataFrame:
    """Create a balanced panel dataset for testing."""
    markets = [f"MARKET_{i}" for i in range(n_markets)]
    dates = pd.date_range(start_date, periods=n_periods, freq="W")
    
    panel = pd.MultiIndex.from_product(
        [markets, dates], 
        names=["market_id", "date"]
    )
    
    df = pd.DataFrame(index=panel).reset_index()
    
    # Add random price data
    np.random.seed(42)
    df["price"] = 1000 + np.random.normal(0, 100, size=len(df))
    
    # Add conflict intensity
    df["conflict_events"] = np.random.poisson(1, size=len(df))
    
    return df


# --- Environment Setup ---

# Set test environment variables
os.environ["TESTING"] = "1"
os.environ["LOG_LEVEL"] = "DEBUG"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"