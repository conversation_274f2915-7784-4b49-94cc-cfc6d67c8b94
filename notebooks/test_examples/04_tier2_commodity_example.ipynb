{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tier 2: Commodity-Specific Analysis Example\n", "\n", "This notebook demonstrates Tier 2 of the three-tier methodology: commodity-specific models with threshold effects.\n", "\n", "**Key Features:**\n", "- Extract individual commodity panels from 3D data\n", "- Test for threshold effects (e.g., conflict intensity)\n", "- Estimate regime-specific models\n", "- Compare integration patterns across commodities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup\n", "import sys\n", "from pathlib import Path\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from IPython.display import display\n", "\n", "# Add project root to path\n", "sys.path.insert(0, str(Path.cwd().parent))\n", "\n", "# Import three-tier models\n", "from yemen_market.models.three_tier.tier2_commodity import CommodityExtractor\n", "from yemen_market.models.three_tier.core.panel_data_handler import PanelDataHandler\n", "from yemen_market.utils.logging import setup_logging, info, bind\n", "\n", "# Setup\n", "setup_logging('INFO')\n", "bind(notebook='tier2_commodity_example')\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "sns.set_palette('husl')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON> and Prepare Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sample data with threshold effects\n", "np.random.seed(42)\n", "\n", "# Generate dates\n", "dates = pd.date_range('2019-01-01', periods=150, freq='W')\n", "\n", "# Markets and commodities\n", "markets = ['Sana\\'a', 'Aden', '<PERSON>z', 'Ho<PERSON><PERSON>h', 'Ibb']\n", "commodities = ['wheat', 'rice', 'sugar', 'oil']\n", "\n", "# Create data with different integration patterns by commodity\n", "data = []\n", "for date in dates:\n", "    # Generate conflict intensity that varies over time\n", "    base_conflict = 10 + 20 * np.sin(2 * np.pi * dates.get_loc(date) / 52)\n", "    if date > '2020-01-01':\n", "        base_conflict += 15  # Conflict escalation\n", "    \n", "    for market in markets:\n", "        for commodity in commodities:\n", "            # Base price with commodity-specific patterns\n", "            if commodity == 'wheat':\n", "                base_price = 100 + 0.1 * dates.get_loc(date)\n", "                threshold_effect = 1.2 if base_conflict > 25 else 1.0\n", "            elif commodity == 'rice':\n", "                base_price = 120 + 0.05 * dates.get_loc(date)\n", "                threshold_effect = 1.1 if base_conflict > 30 else 1.0\n", "            elif commodity == 'sugar':\n", "                base_price = 80 + 0.15 * dates.get_loc(date)\n", "                threshold_effect = 1.3 if base_conflict > 20 else 1.0\n", "            else:  # oil\n", "                base_price = 150 + 0.2 * dates.get_loc(date)\n", "                threshold_effect = 1.4 if base_conflict > 15 else 1.0\n", "            \n", "            # Market-specific variation\n", "            market_effect = hash(market) % 10 - 5\n", "            \n", "            # Final price with noise\n", "            price = base_price * threshold_effect + market_effect + np.random.randn() * 5\n", "            \n", "            data.append({\n", "                'date': date,\n", "                'governorate': market,\n", "                'commodity': commodity,\n", "                'usd_price': max(price, 10),  # Ensure positive\n", "                'conflict_intensity': base_conflict + np.random.randn() * 5\n", "            })\n", "\n", "df = pd.DataFrame(data)\n", "info(f\"Created panel data: {df.shape[0]} observations\")\n", "display(df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Initialize Commodity Extractor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure Tier 2 analysis\n", "config = {\n", "    'min_observations': 50,\n", "    'test_thresholds': True,\n", "    'max_lags': 4,\n", "    'threshold_var': 'conflict_intensity',\n", "    'n_regimes': 2,  # Low/high conflict regimes\n", "    'significance_level': 0.05\n", "}\n", "\n", "# Initialize extractor\n", "extractor = CommodityExtractor(config)\n", "info(\"Commodity extractor initialized\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Analyze Individual Commodities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze each commodity\n", "commodity_results = {}\n", "\n", "for commodity in commodities:\n", "    print(f\"\\n{'='*50}\")\n", "    print(f\"Analyzing {commodity.upper()}\")\n", "    print(f\"{'='*50}\")\n", "    \n", "    try:\n", "        # Run analysis\n", "        results = extractor.analyze_commodity(df, commodity)\n", "        commodity_results[commodity] = results\n", "        \n", "        # Display key findings\n", "        if 'results' in results and results['results']:\n", "            res = results['results']\n", "            \n", "            # Check for threshold\n", "            if hasattr(res, 'tier_specific') and 'threshold_value' in res.tier_specific:\n", "                threshold = res.tier_specific['threshold_value']\n", "                print(f\"\\n✓ Threshold detected at {threshold:.1f} conflict events\")\n", "                \n", "                # Regime splits\n", "                if 'regime_splits' in res.tier_specific:\n", "                    splits = res.tier_specific['regime_splits']\n", "                    print(f\"  - Low regime: {splits.get('low', 0)} observations\")\n", "                    print(f\"  - High regime: {splits.get('high', 0)} observations\")\n", "            else:\n", "                print(\"\\n✗ No significant threshold found\")\n", "            \n", "            # Model fit\n", "            if hasattr(res, 'comparison_metrics'):\n", "                print(f\"\\nModel fit:\")\n", "                print(f\"  - R-squared: {res.comparison_metrics.r_squared:.3f}\")\n", "                print(f\"  - AIC: {res.comparison_metrics.aic:.1f}\")\n", "            \n", "            # Integration level\n", "            if hasattr(res, 'tier_specific') and 'integration_level' in res.tier_specific:\n", "                print(f\"\\nIntegration level: {res.tier_specific['integration_level']}\")\n", "                \n", "    except Exception as e:\n", "        print(f\"\\n❌ Error analyzing {commodity}: {str(e)}\")\n", "        commodity_results[commodity] = {'error': str(e)}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Compare Across Commodities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comparison DataFrame\n", "comparison_data = []\n", "\n", "for commodity, results in commodity_results.items():\n", "    if 'results' in results and results['results']:\n", "        res = results['results']\n", "        \n", "        row = {\n", "            'commodity': commodity,\n", "            'threshold': None,\n", "            'r_squared': None,\n", "            'integration_level': 'Unknown',\n", "            'n_observations': 0\n", "        }\n", "        \n", "        # Extract threshold\n", "        if hasattr(res, 'tier_specific'):\n", "            row['threshold'] = res.tier_specific.get('threshold_value')\n", "            row['integration_level'] = res.tier_specific.get('integration_level', 'Unknown')\n", "        \n", "        # Extract R-squared\n", "        if hasattr(res, 'comparison_metrics'):\n", "            row['r_squared'] = res.comparison_metrics.r_squared\n", "        \n", "        # Extract sample size\n", "        if hasattr(res, 'metadata'):\n", "            row['n_observations'] = res.metadata.get('n_observations', 0)\n", "        \n", "        comparison_data.append(row)\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "display(comparison_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualize Threshold Effects"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot threshold values by commodity\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "# Threshold values\n", "threshold_data = comparison_df[comparison_df['threshold'].notna()]\n", "if not threshold_data.empty:\n", "    ax1.bar(threshold_data['commodity'], threshold_data['threshold'])\n", "    ax1.set_xlabel('Commodity')\n", "    ax1.set_ylabel('Conflict Threshold')\n", "    ax1.set_title('Threshold Values by Commodity')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "\n", "# R-squared comparison\n", "r2_data = comparison_df[comparison_df['r_squared'].notna()]\n", "if not r2_data.empty:\n", "    ax2.bar(r2_data['commodity'], r2_data['r_squared'])\n", "    ax2.set_xlabel('Commodity')\n", "    ax2.set_ylabel('R-squared')\n", "    ax2.set_title('Model Fit by Commodity')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    ax2.set_ylim(0, 1)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Analyze Price Dynamics by Regime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# For commodities with thresholds, show price dynamics in each regime\n", "for commodity in ['wheat', 'sugar']:  # Focus on two examples\n", "    if commodity in commodity_results and 'results' in commodity_results[commodity]:\n", "        res = commodity_results[commodity]['results']\n", "        \n", "        if hasattr(res, 'tier_specific') and 'threshold_value' in res.tier_specific:\n", "            threshold = res.tier_specific['threshold_value']\n", "            \n", "            # Extract commodity data\n", "            comm_data = df[df['commodity'] == commodity].copy()\n", "            \n", "            # Split by regime\n", "            comm_data['regime'] = comm_data['conflict_intensity'].apply(\n", "                lambda x: 'High Conflict' if x > threshold else 'Low Conflict'\n", "            )\n", "            \n", "            # Plot price distributions by regime\n", "            fig, ax = plt.subplots(figsize=(8, 5))\n", "            \n", "            for regime in ['Low Conflict', 'High Conflict']:\n", "                regime_data = comm_data[comm_data['regime'] == regime]\n", "                ax.hist(regime_data['usd_price'], alpha=0.6, label=regime, bins=20)\n", "            \n", "            ax.set_xlabel('Price (USD)')\n", "            ax.set_ylabel('Frequency')\n", "            ax.set_title(f'{commodity.capitalize()} Price Distribution by Conflict Regime')\n", "            ax.legend()\n", "            plt.show()\n", "            \n", "            # Summary statistics by regime\n", "            print(f\"\\n{commodity.upper()} - Price Statistics by Regime:\")\n", "            regime_stats = comm_data.groupby('regime')['usd_price'].agg(['mean', 'std', 'count'])\n", "            display(regime_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Time Series of Threshold Crossings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show when threshold crossings occur\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "axes = axes.ravel()\n", "\n", "for i, commodity in enumerate(commodities[:4]):\n", "    ax = axes[i]\n", "    \n", "    # Get commodity data\n", "    comm_data = df[df['commodity'] == commodity].copy()\n", "    \n", "    # Get average conflict by date\n", "    conflict_ts = comm_data.groupby('date')['conflict_intensity'].mean()\n", "    \n", "    # Plot conflict intensity\n", "    ax.plot(conflict_ts.index, conflict_ts.values, label='Conflict Intensity', color='red', alpha=0.7)\n", "    \n", "    # Add threshold line if exists\n", "    if commodity in commodity_results and 'results' in commodity_results[commodity]:\n", "        res = commodity_results[commodity]['results']\n", "        if hasattr(res, 'tier_specific') and 'threshold_value' in res.tier_specific:\n", "            threshold = res.tier_specific['threshold_value']\n", "            ax.axhline(y=threshold, color='black', linestyle='--', label=f'Threshold: {threshold:.1f}')\n", "            \n", "            # Shade high-conflict periods\n", "            high_conflict = conflict_ts > threshold\n", "            ax.fill_between(conflict_ts.index, 0, 50, \n", "                          where=high_conflict, alpha=0.2, color='red', \n", "                          label='High Conflict Regime')\n", "    \n", "    ax.set_title(f'{commodity.capitalize()}')\n", "    ax.set_xlabel('Date')\n", "    ax.set_ylabel('Conflict Intensity')\n", "    ax.legend()\n", "    ax.tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save commodity comparison\n", "output_dir = Path('results/tier2_commodity')\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save comparison table\n", "comparison_df.to_csv(output_dir / 'commodity_comparison.csv', index=False)\n", "print(f\"Saved comparison to {output_dir / 'commodity_comparison.csv'}\")\n", "\n", "# Save detailed results for each commodity\n", "for commodity, results in commodity_results.items():\n", "    if 'results' in results and results['results']:\n", "        res = results['results']\n", "        if hasattr(res, 'save'):\n", "            res.save(output_dir / f'{commodity}_results.json')\n", "            print(f\"Saved {commodity} results\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Insights from Tier 2 Analysis\n", "\n", "### 1. **Commodity Heterogeneity**\n", "Different commodities show different sensitivity to conflict:\n", "- Essential goods (wheat, rice) may have lower thresholds\n", "- Luxury/imported goods may be more sensitive\n", "\n", "### 2. **Threshold Effects**\n", "- Markets behave differently in low vs high conflict regimes\n", "- Price dynamics change when conflict exceeds critical levels\n", "- Integration patterns may break down in high-conflict periods\n", "\n", "### 3. **Policy Implications**\n", "- Different commodities require different intervention strategies\n", "- Monitoring conflict thresholds can help predict market disruptions\n", "- Early warning systems should be commodity-specific\n", "\n", "### Next Steps\n", "- Run Tier 3 factor analysis to validate these patterns\n", "- Compare results across different time periods\n", "- Test robustness with different threshold variables"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}