{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tier 3: Validation Analysis Example\n", "\n", "This notebook demonstrates Tier 3 of the three-tier methodology: factor analysis and external validation.\n", "\n", "**Key Components:**\n", "- Static factor analysis (PCA)\n", "- Dynamic factor models\n", "- Market integration measurement\n", "- Conflict event validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup\n", "import sys\n", "from pathlib import Path\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from IPython.display import display\n", "\n", "# Add project root to path\n", "sys.path.insert(0, str(Path.cwd().parent))\n", "\n", "# Import Tier 3 models\n", "from yemen_market.models.three_tier.tier3_validation import (\n", "    StaticFactorModel,\n", "    PCAMarketIntegration,\n", "    ConflictIntegrationValidator\n", ")\n", "from yemen_market.utils.logging import setup_logging, info, bind\n", "\n", "# Setup\n", "setup_logging('INFO')\n", "bind(notebook='tier3_validation_example')\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "sns.set_palette('husl')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON> Sam<PERSON> Data with Integration Patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate data with common factors\n", "np.random.seed(42)\n", "\n", "# Time periods\n", "dates = pd.date_range('2019-01-01', periods=200, freq='W')\n", "markets = ['Sana\\'a', 'Aden', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Ibb', '<PERSON><PERSON><PERSON>']\n", "commodities = ['wheat', 'rice', 'sugar', 'oil', 'beans']\n", "\n", "# Generate common factors\n", "n_periods = len(dates)\n", "\n", "# Factor 1: National trend\n", "factor1 = np.cumsum(np.random.randn(n_periods) * 0.5) + 100\n", "\n", "# Factor 2: Seasonal pattern\n", "factor2 = 10 * np.sin(2 * np.pi * np.arange(n_periods) / 52)\n", "\n", "# Factor 3: Conflict shocks\n", "factor3 = np.zeros(n_periods)\n", "shock_dates = [50, 100, 150]\n", "for shock in shock_dates:\n", "    factor3[shock:shock+10] = 15\n", "\n", "# Create panel data\n", "data = []\n", "conflict_events = []\n", "\n", "for i, date in enumerate(dates):\n", "    # Generate conflict events\n", "    if i in shock_dates:\n", "        conflict_events.append({\n", "            'date': date,\n", "            'governorate': np.random.choice(markets),\n", "            'fatalities': np.random.randint(20, 100),\n", "            'event_type': 'Battle'\n", "        })\n", "    \n", "    for market in markets:\n", "        for commodity in commodities:\n", "            # Market-commodity specific loadings\n", "            if commodity in ['wheat', 'rice']:  # <PERSON><PERSON><PERSON>\n", "                loading1 = 0.8 + np.random.randn() * 0.1\n", "                loading2 = 0.2\n", "                loading3 = 0.5\n", "            elif commodity in ['sugar', 'beans']:  # Secondary\n", "                loading1 = 0.6 + np.random.randn() * 0.1\n", "                loading2 = 0.4\n", "                loading3 = 0.3\n", "            else:  # Oil\n", "                loading1 = 0.4 + np.random.randn() * 0.1\n", "                loading2 = 0.1\n", "                loading3 = 0.8\n", "            \n", "            # Regional effects\n", "            if market in ['Sana\\'a', 'Taiz']:  # Central\n", "                loading1 *= 1.1\n", "            elif market in ['Aden', 'Hodeidah']:  # Coastal\n", "                loading2 *= 1.5\n", "            \n", "            # Generate price\n", "            price = (loading1 * factor1[i] + \n", "                    loading2 * factor2[i] + \n", "                    loading3 * factor3[i] +\n", "                    np.random.randn() * 5)\n", "            \n", "            data.append({\n", "                'date': date,\n", "                'governorate': market,\n", "                'commodity': commodity,\n", "                'usd_price': max(price, 10)\n", "            })\n", "\n", "# Create DataFrames\n", "df = pd.DataFrame(data)\n", "conflict_df = pd.DataFrame(conflict_events)\n", "\n", "info(f\"Created panel data: {df.shape[0]} price observations\")\n", "info(f\"Created conflict data: {len(conflict_df)} events\")\n", "display(df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Static Factor Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure and run static factor model\n", "static_config = {\n", "    'n_factors': None,  # Auto-select\n", "    'standardize': True,\n", "    'min_variance_explained': 0.8,\n", "    'max_factors': 5\n", "}\n", "\n", "static_model = StaticFactorModel(static_config)\n", "static_model.fit(df)\n", "\n", "# Get results\n", "static_results = static_model.get_results()\n", "\n", "# Display variance explained\n", "variance_explained = static_results.tier_specific['variance_explained']\n", "cumulative_variance = static_results.tier_specific['cumulative_variance']\n", "\n", "print(f\"Number of factors selected: {static_model.n_factors}\")\n", "print(f\"Total variance explained: {cumulative_variance[-1]:.1%}\")\n", "print(\"\\nVariance by factor:\")\n", "for i, var in enumerate(variance_explained):\n", "    print(f\"  Factor {i+1}: {var:.1%}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Visualize Factor Loadings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get loadings\n", "loadings = static_model.loadings\n", "\n", "# Plot loadings heatmap\n", "fig, ax = plt.subplots(figsize=(10, 8))\n", "\n", "# Sort by first factor loading\n", "loadings_sorted = loadings.sort_values('Factor_1', ascending=False)\n", "\n", "# Create heatmap\n", "sns.heatmap(loadings_sorted.iloc[:20],  # Top 20 series\n", "            cmap='RdBu_r', center=0, \n", "            cbar_kws={'label': 'Loading'},\n", "            ax=ax)\n", "\n", "ax.set_title('Top 20 Factor Loadings')\n", "ax.set_xlabel('Factor')\n", "ax.set_ylabel('Market-Commodity Pair')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Interpret factors\n", "print(\"\\nFactor Interpretations:\")\n", "if 'factor_interpretations' in static_results.tier_specific:\n", "    for interp in static_results.tier_specific['factor_interpretations']:\n", "        print(f\"  - {interp}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Market Integration Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize PCA integration analyzer\n", "pca_analyzer = PCAMarketIntegration()\n", "\n", "# Overall integration strength\n", "integration = pca_analyzer.analyze_integration_strength(df)\n", "\n", "print(\"Market Integration Analysis\")\n", "print(\"=\" * 40)\n", "print(f\"Integration level: {integration['integration_level']}\")\n", "print(f\"PC1 variance explained: {integration['pc1_variance_explained']:.1%}\")\n", "print(f\"Effective number of factors: {integration['effective_n_factors']:.1f}\")\n", "print(f\"Number of market-commodity pairs: {integration['n_series']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Rolling Integration Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze integration over time\n", "rolling_integration = pca_analyzer.rolling_pca_analysis(df)\n", "\n", "# Plot results\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)\n", "\n", "# PC1 variance over time\n", "ax1.plot(rolling_integration.index, rolling_integration['pc1_variance'], \n", "         label='PC1 Variance', color='blue')\n", "ax1.fill_between(rolling_integration.index, \n", "                 rolling_integration['pc1_variance'] - 0.05,\n", "                 rolling_integration['pc1_variance'] + 0.05,\n", "                 alpha=0.2)\n", "ax1.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='50% threshold')\n", "ax1.set_ylabel('PC1 Variance Explained')\n", "ax1.set_title('Market Integration Over Time')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Effective factors over time\n", "ax2.plot(rolling_integration.index, rolling_integration['effective_factors'],\n", "         label='Effective Factors', color='green')\n", "ax2.set_ylabel('Effective Number of Factors')\n", "ax2.set_xlabel('Date')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Mark conflict events\n", "if not conflict_df.empty:\n", "    for _, event in conflict_df.iterrows():\n", "        ax1.axvline(x=event['date'], color='red', alpha=0.3, linestyle=':')\n", "        ax2.axvline(x=event['date'], color='red', alpha=0.3, linestyle=':')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Identify regime changes\n", "regime_changes = rolling_integration[rolling_integration['regime_change']]\n", "if not regime_changes.empty:\n", "    print(f\"\\nDetected {len(regime_changes)} integration regime changes:\")\n", "    for date in regime_changes.index:\n", "        print(f\"  - {date.strftime('%Y-%m-%d')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Commodity-Specific Integration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze integration by commodity\n", "commodity_integration = pca_analyzer.commodity_specific_pca(df)\n", "\n", "# Create comparison plot\n", "commodity_data = []\n", "for commodity, results in commodity_integration.items():\n", "    commodity_data.append({\n", "        'commodity': commodity,\n", "        'pc1_variance': results['pc1_variance'],\n", "        'integration_level': results['integration_level'],\n", "        'n_markets': results['n_markets'],\n", "        'volatility': results['price_volatility']\n", "    })\n", "\n", "commodity_df = pd.DataFrame(commodity_data)\n", "commodity_df = commodity_df.sort_values('pc1_variance', ascending=False)\n", "\n", "# Plot commodity comparison\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "bars = ax.bar(commodity_df['commodity'], commodity_df['pc1_variance'])\n", "\n", "# Color by integration level\n", "colors = {'Very High': 'darkgreen', 'High': 'green', 'Moderate': 'yellow', \n", "          'Low': 'orange', 'Very Low': 'red'}\n", "for i, (_, row) in enumerate(commodity_df.iterrows()):\n", "    bars[i].set_color(colors.get(row['integration_level'], 'gray'))\n", "\n", "ax.set_xlabel('Commodity')\n", "ax.set_ylabel('PC1 Variance Explained')\n", "ax.set_title('Market Integration by Commodity')\n", "ax.set_ylim(0, 1)\n", "\n", "# Add integration level annotations\n", "for i, (_, row) in enumerate(commodity_df.iterrows()):\n", "    ax.text(i, row['pc1_variance'] + 0.02, row['integration_level'],\n", "            ha='center', va='bottom', fontsize=8)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Display summary table\n", "print(\"\\nCommodity Integration Summary:\")\n", "display(commodity_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Market Clustering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform hierarchical clustering\n", "clustering = pca_analyzer.hierarchical_pca(df)\n", "\n", "print(f\"Identified {clustering['n_clusters']} market clusters\")\n", "print(\"\\nCluster composition:\")\n", "\n", "for cluster_id, members in clustering['market_groups'].items():\n", "    print(f\"\\nCluster {cluster_id} ({len(members)} members):\")\n", "    # Show first 5 members\n", "    for member in members[:5]:\n", "        print(f\"  - {member}\")\n", "    if len(members) > 5:\n", "        print(f\"  ... and {len(members) - 5} more\")\n", "\n", "# Visualize dendrogram\n", "from scipy.cluster import hierarchy\n", "\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "\n", "# Plot dendrogram\n", "dendrogram = hierarchy.dendrogram(\n", "    clustering['linkage_matrix'],\n", "    labels=clustering['distance_matrix'].index,\n", "    ax=ax,\n", "    leaf_rotation=90,\n", "    leaf_font_size=8\n", ")\n", "\n", "ax.set_title('Market-Commodity Hierarchical Clustering')\n", "ax.set_xlabel('Market-Commodity Pair')\n", "ax.set_ylabel('Distance')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Conflict Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize conflict validator\n", "conflict_config = {\n", "    'conflict_threshold': 10,\n", "    'window_before': 30,\n", "    'window_after': 30\n", "}\n", "\n", "validator = ConflictIntegrationValidator(conflict_config)\n", "\n", "# Fit with integration scores from rolling analysis\n", "validator.fit(df, conflict_data=conflict_df, integration_scores=rolling_integration)\n", "\n", "# Get results\n", "conflict_results = validator.get_results()\n", "\n", "# Display event study results\n", "if 'event_study' in conflict_results.tier_specific:\n", "    event_study = conflict_results.tier_specific['event_study']\n", "    \n", "    print(\"Conflict Impact on Market Integration\")\n", "    print(\"=\" * 40)\n", "    print(f\"Events analyzed: {event_study.get('n_events_analyzed', 0)}\")\n", "    print(f\"Events reducing integration: {event_study.get('pct_events_reducing_integration', 0):.0f}%\")\n", "    print(f\"Average integration change: {event_study.get('avg_integration_change', 0):.3f}\")\n", "    \n", "    # Show event details if available\n", "    if 'event_details' in event_study and not event_study['event_details'].empty:\n", "        print(\"\\nEvent Details:\")\n", "        display(event_study['event_details'][['event_date', 'governorate', 'fatalities', \n", "                                             'integration_change', 'significant']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Generate Comprehensive Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate full integration report\n", "full_report = pca_analyzer.generate_integration_report(df)\n", "\n", "print(\"Tier 3 Validation Summary\")\n", "print(\"=\" * 50)\n", "print(full_report.summary())\n", "\n", "# Save results\n", "output_dir = Path('results/tier3_validation')\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save individual components\n", "static_results.save(output_dir / 'static_factors.json')\n", "full_report.save(output_dir / 'integration_report.json')\n", "\n", "if not rolling_integration.empty:\n", "    rolling_integration.to_csv(output_dir / 'rolling_integration.csv')\n", "\n", "print(f\"\\nResults saved to {output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Insights from Tier 3 Validation\n", "\n", "### 1. **Common Price Drivers**\n", "- Factor 1 typically captures national trends affecting all markets\n", "- Factor 2 often represents seasonal or commodity-specific patterns\n", "- Factor 3 may capture conflict or supply shocks\n", "\n", "### 2. **Integration Patterns**\n", "- High PC1 variance (>50%) indicates strong market integration\n", "- Effective number of factors shows market complexity\n", "- Rolling analysis reveals integration dynamics over time\n", "\n", "### 3. **Commodity Differences**\n", "- Essential commodities (wheat, rice) often show higher integration\n", "- Luxury/imported goods may have lower integration\n", "- Perishables may show different patterns\n", "\n", "### 4. **Conflict Impact**\n", "- Major conflicts can reduce market integration\n", "- Effects may persist beyond the conflict event\n", "- Some markets/commodities are more resilient\n", "\n", "### 5. **Policy Implications**\n", "- Monitor integration metrics as early warning indicators\n", "- Target interventions based on clustering patterns\n", "- Consider commodity-specific strategies\n", "\n", "### Next Steps\n", "- Compare Tier 3 findings with Tier 1-2 results\n", "- Test robustness with different time windows\n", "- Incorporate additional external shocks (e.g., COVID-19)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}