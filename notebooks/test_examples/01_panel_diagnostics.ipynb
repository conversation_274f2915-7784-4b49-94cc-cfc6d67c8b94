{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Panel Data Diagnostics for Three-Tier Analysis\n", "\n", "This notebook performs comprehensive diagnostics for the 3D panel data structure and validates the three-tier methodology."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup\n", "import sys\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "\n", "# Add project root\n", "project_root = Path.cwd().parent.parent\n", "sys.path.insert(0, str(project_root))\n", "\n", "from yemen_market.utils.logging import setup_logging, info, warning\n", "\n", "setup_logging(\"INFO\")\n", "plt.style.use('seaborn-v0_8-whitegrid')\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Panel Structure Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data\n", "panel_path = project_root / \"data/processed/panels/integrated_panel.parquet\"\n", "df = pd.read_parquet(panel_path)\n", "\n", "# Basic structure\n", "print(\"3D Panel Structure:\")\n", "print(\"=\" * 50)\n", "print(f\"Markets: {df['market'].nunique()}\")\n", "print(f\"Commodities: {df['commodity'].nunique()}\")\n", "print(f\"Time periods: {df['date'].nunique()}\")\n", "print(f\"Date range: {df['date'].min()} to {df['date'].max()}\")\n", "print(f\"\\nTotal observations: {len(df):,}\")\n", "print(f\"Complete panel would have: {df['market'].nunique() * df['commodity'].nunique() * df['date'].nunique():,}\")\n", "print(f\"Coverage: {len(df) / (df['market'].nunique() * df['commodity'].nunique() * df['date'].nunique()) * 100:.1f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for duplicates\n", "duplicates = df.duplicated(['market', 'commodity', 'date'])\n", "if duplicates.any():\n", "    warning(f\"Found {duplicates.sum()} duplicate observations!\")\n", "    display(df[duplicates].head())\n", "else:\n", "    info(\"✓ No duplicate observations found\")\n", "\n", "# Check entity creation\n", "df['entity'] = df['market'] + '_' + df['commodity']\n", "print(f\"\\nUnique entities (market-commodity pairs): {df['entity'].nunique()}\")\n", "print(f\"Average observations per entity: {len(df) / df['entity'].nunique():.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Coverage Analysis by Dimension"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Coverage by market\n", "market_coverage = df.groupby('market').size() / (df['commodity'].nunique() * df['date'].nunique())\n", "market_coverage = market_coverage.sort_values(ascending=False)\n", "\n", "# Plot\n", "fig, ax = plt.subplots(figsize=(12, 6))\n", "market_coverage.plot(kind='bar', ax=ax)\n", "ax.axhline(0.5, color='red', linestyle='--', alpha=0.7, label='50% threshold')\n", "ax.set_ylabel('Coverage Rate')\n", "ax.set_xlabel('Market')\n", "ax.set_title('Data Coverage by Market')\n", "ax.legend()\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Markets with >50% coverage: {(market_coverage > 0.5).sum()} of {len(market_coverage)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Coverage by commodity\n", "commodity_coverage = df.groupby('commodity').size() / (df['market'].nunique() * df['date'].nunique())\n", "commodity_coverage = commodity_coverage.sort_values(ascending=False)\n", "\n", "# Create visualization\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))\n", "\n", "# Bar plot\n", "commodity_coverage.plot(kind='barh', ax=ax1)\n", "ax1.axvline(0.5, color='red', linestyle='--', alpha=0.7)\n", "ax1.set_xlabel('Coverage Rate')\n", "ax1.set_title('Data Coverage by Commodity')\n", "\n", "# Heatmap of market-commodity coverage\n", "coverage_matrix = df.groupby(['market', 'commodity']).size().unstack(fill_value=0)\n", "coverage_matrix = coverage_matrix / df['date'].nunique()\n", "\n", "sns.heatmap(coverage_matrix.T, cmap='YlOrRd', cbar_kws={'label': 'Coverage'}, ax=ax2)\n", "ax2.set_title('Market-Commodity Coverage Matrix')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON> <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze missing patterns\n", "missing_cols = ['price_usd', 'conflict_intensity', 'exchange_rate']\n", "missing_stats = pd.DataFrame({\n", "    'Column': missing_cols,\n", "    'Missing_Count': [df[col].isna().sum() for col in missing_cols if col in df.columns],\n", "    'Missing_Pct': [df[col].isna().mean() * 100 for col in missing_cols if col in df.columns]\n", "})\n", "\n", "display(missing_stats)\n", "\n", "# Missing by time\n", "missing_by_time = df.groupby('date')['price_usd'].apply(lambda x: x.isna().mean())\n", "\n", "fig, ax = plt.subplots(figsize=(12, 5))\n", "missing_by_time.plot(ax=ax)\n", "ax.set_ylabel('Missing Price Rate')\n", "ax.set_title('Missing Data Pattern Over Time')\n", "ax.grid(True, alpha=0.3)\n", "\n", "# Mark potential conflict events\n", "if 'conflict_intensity' in df.columns:\n", "    high_conflict = df.groupby('date')['conflict_intensity'].mean()\n", "    high_conflict_dates = high_conflict[high_conflict > high_conflict.quantile(0.9)].index\n", "    for date in high_conflict_dates:\n", "        if date in missing_by_time.index:\n", "            ax.axvline(date, color='red', alpha=0.2, linewidth=1)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Panel Balance Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create entity-time panel\n", "entity_time = df.groupby(['entity', 'date']).size().unstack(fill_value=0)\n", "balance_stats = {\n", "    'Entities': len(entity_time),\n", "    'Time_Periods': len(entity_time.columns),\n", "    'Fully_Balanced': (entity_time > 0).all().all(),\n", "    'Avg_Periods_Per_Entity': (entity_time > 0).sum(axis=1).mean(),\n", "    'Min_Periods': (entity_time > 0).sum(axis=1).min(),\n", "    'Max_Periods': (entity_time > 0).sum(axis=1).max()\n", "}\n", "\n", "print(\"Panel Balance Statistics:\")\n", "print(\"=\" * 40)\n", "for key, value in balance_stats.items():\n", "    print(f\"{key:25s}: {value}\")\n", "\n", "# Distribution of entity observations\n", "obs_per_entity = (entity_time > 0).sum(axis=1)\n", "\n", "fig, ax = plt.subplots(figsize=(10, 5))\n", "obs_per_entity.hist(bins=30, ax=ax, edgecolor='black')\n", "ax.axvline(obs_per_entity.mean(), color='red', linestyle='--', \n", "           label=f'Mean: {obs_per_entity.mean():.1f}')\n", "ax.set_xlabel('Number of Time Periods')\n", "ax.set_ylabel('Number of Entities')\n", "ax.set_title('Distribution of Observations per Entity')\n", "ax.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Price Distribution Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Price distributions by commodity\n", "commodities = ['Wheat', 'Rice', 'Sugar', 'Fuel']\n", "available_commodities = [c for c in commodities if c in df['commodity'].unique()]\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 10))\n", "axes = axes.ravel()\n", "\n", "for i, commodity in enumerate(available_commodities[:4]):\n", "    comm_data = df[df['commodity'] == commodity]['price_usd'].dropna()\n", "    \n", "    # Box plot\n", "    axes[i].boxplot([comm_data], labels=[commodity])\n", "    axes[i].set_ylabel('Price (USD)')\n", "    axes[i].set_title(f'{commodity} Price Distribution')\n", "    \n", "    # Add statistics\n", "    stats_text = f\"Mean: ${comm_data.mean():.2f}\\nStd: ${comm_data.std():.2f}\\nCV: {comm_data.std()/comm_data.mean():.2f}\"\n", "    axes[i].text(0.02, 0.98, stats_text, transform=axes[i].transAxes, \n", "                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Conflict Data Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'conflict_intensity' in df.columns:\n", "    # Conflict statistics\n", "    conflict_stats = df['conflict_intensity'].describe()\n", "    print(\"Conflict Intensity Statistics:\")\n", "    print(conflict_stats)\n", "    \n", "    # Conflict distribution\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))\n", "    \n", "    # Histogram\n", "    df['conflict_intensity'].hist(bins=50, ax=ax1, edgecolor='black')\n", "    ax1.axvline(50, color='red', linestyle='--', label='Potential threshold')\n", "    ax1.set_xlabel('Conflict Intensity (events/month)')\n", "    ax1.set_ylabel('Frequency')\n", "    ax1.set_title('Distribution of Conflict Intensity')\n", "    ax1.legend()\n", "    \n", "    # Time series\n", "    conflict_ts = df.groupby('date')['conflict_intensity'].agg(['mean', 'std'])\n", "    ax2.plot(conflict_ts.index, conflict_ts['mean'], label='Mean')\n", "    ax2.fill_between(conflict_ts.index, \n", "                     conflict_ts['mean'] - conflict_ts['std'],\n", "                     conflict_ts['mean'] + conflict_ts['std'],\n", "                     alpha=0.3, label='±1 Std Dev')\n", "    ax2.set_xlabel('Date')\n", "    ax2.set_ylabel('Conflict Intensity')\n", "    ax2.set_title('Conflict Intensity Over Time')\n", "    ax2.legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    warning(\"Conflict intensity data not found in panel\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Diagnostic Summary and Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate diagnostic summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"DIAGNOSTIC SUMMARY FOR THREE-TIER ANALYSIS\")\n", "print(\"=\"*60)\n", "\n", "# Panel structure\n", "print(\"\\n1. PANEL STRUCTURE:\")\n", "print(f\"   - 3D structure confirmed: {df['market'].nunique()} × {df['commodity'].nunique()} × {df['date'].nunique()}\")\n", "print(f\"   - Entity creation successful: {df['entity'].nunique()} market-commodity pairs\")\n", "print(f\"   - No duplicates: {'✓' if not duplicates.any() else '✗'}\")\n", "\n", "# Coverage\n", "overall_coverage = len(df) / (df['market'].nunique() * df['commodity'].nunique() * df['date'].nunique()) * 100\n", "print(\"\\n2. COVERAGE ASSESSMENT:\")\n", "print(f\"   - Overall coverage: {overall_coverage:.1f}%\")\n", "print(f\"   - Suitable for Tier 1 (pooled): {'✓' if overall_coverage > 50 else '✗'}\")\n", "print(f\"   - High-coverage commodities: {(commodity_coverage > 0.5).sum()}\")\n", "print(f\"   - Suitable for Tier 2 (commodity): {'✓' if (commodity_coverage > 0.5).sum() >= 4 else '✗'}\")\n", "\n", "# Missing data\n", "print(\"\\n3. MISSING DATA:\")\n", "print(f\"   - Price missing: {df['price_usd'].isna().mean()*100:.1f}%\")\n", "if 'conflict_intensity' in df.columns:\n", "    print(f\"   - Conflict missing: {df['conflict_intensity'].isna().mean()*100:.1f}%\")\n", "\n", "# Balance\n", "print(\"\\n4. PANEL BALANCE:\")\n", "print(f\"   - Fully balanced: {'No' if not balance_stats['Fully_Balanced'] else 'Yes'}\")\n", "print(f\"   - Average periods per entity: {balance_stats['Avg_Periods_Per_Entity']:.1f}\")\n", "print(f\"   - Suitable for fixed effects: {'✓' if balance_stats['Avg_Periods_Per_Entity'] > 10 else '✗'}\")\n", "\n", "# Recommendations\n", "print(\"\\n5. RECOMMENDATIONS:\")\n", "print(\"   ✓ Proceed with Tier 1 pooled panel regression\")\n", "print(\"   ✓ Focus Tier 2 on high-coverage commodities\")\n", "print(\"   ✓ Use robust standard errors for unbalanced panel\")\n", "print(\"   ✓ Document all sample restrictions clearly\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}