#!/usr/bin/env python3
"""
V2 Import Test Script

Tests all major V2 components to identify import issues and circular dependencies.
"""

import sys
import traceback
from typing import List, Tuple

def test_import(module_path: str, description: str) -> Tuple[bool, str]:
    """Test a single import and return success status and error message."""
    try:
        exec(f"from {module_path} import *")
        return True, f"✅ {description}"
    except Exception as e:
        return False, f"❌ {description}: {str(e)}"

def main():
    """Run systematic import tests for V2 components."""
    print("🔍 V2 Import Analysis")
    print("=" * 50)
    
    # Test cases: (module_path, description)
    test_cases = [
        # Core Domain
        ("src.core.domain.market.entities", "Market Entities"),
        ("src.core.domain.conflict.entities", "Conflict Entities"),
        ("src.core.domain.geography.entities", "Geography Entities"),
        ("src.core.domain.shared.entities", "Shared Entities"),
        
        # Core Models
        ("src.core.models.interfaces", "Model Interfaces"),
        ("src.core.models.panel", "Panel Models"),
        ("src.core.models.time_series", "Time Series Models"),
        ("src.core.models.validation", "Validation Models"),
        
        # Infrastructure
        ("src.infrastructure.estimators", "Estimators"),
        ("src.infrastructure.diagnostics", "Diagnostics"),
        ("src.infrastructure.persistence", "Persistence"),
        ("src.infrastructure.external_services", "External Services"),
        ("src.infrastructure.caching", "Caching"),
        
        # Application Layer
        ("src.application.services", "Application Services"),
        ("src.application.commands", "Commands"),
        ("src.application.queries", "Queries"),
        ("src.application.analysis_tiers", "Analysis Tiers"),
        
        # Interfaces
        ("src.interfaces.api", "REST API"),
        ("src.interfaces.cli", "CLI"),
        
        # Shared
        ("src.shared.container", "DI Container"),
        ("src.shared.plugins", "Plugin System"),
        
        # Specific critical classes
        ("src.application.services.three_tier_analysis_service", "Three Tier Analysis Service"),
        ("src.infrastructure.persistence.repositories", "Repository Layer"),
        ("src.core.models.validation.pca_model", "PCA Model"),
        ("src.infrastructure.estimators.validation.dynamic_factor_analyzer", "Dynamic Factor Analyzer"),
    ]
    
    # Run tests
    passed = 0
    failed = 0
    results = []
    
    for module_path, description in test_cases:
        success, message = test_import(module_path, description)
        results.append((success, message))
        if success:
            passed += 1
        else:
            failed += 1
            print(message)
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed > 0:
        print("\n🚨 Failed Imports:")
        for success, message in results:
            if not success:
                print(f"  {message}")
    
    print("\n🔧 Next Steps:")
    print("1. Fix circular dependencies between core and infrastructure")
    print("2. Ensure all __init__.py files export required classes")
    print("3. Convert relative imports to absolute imports")
    print("4. Add missing dependencies to requirements")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)