#!/usr/bin/env python3
"""
Comprehensive API testing script for Yemen Market Integration v2 API.

This script tests all API endpoints, validates schemas, and provides examples
for users. It can be run as a complete test suite or individual endpoint tests.
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime, date, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import uuid

import httpx
import pytest
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import print as rprint

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

console = Console()

class APITester:
    """Comprehensive API testing class."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip("/")
        self.client = httpx.AsyncClient(timeout=60.0)
        self.test_results = {}
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def log_test(self, test_name: str, success: bool, details: str = "", response_data: Dict = None):
        """Log test result."""
        self.test_results[test_name] = {
            "success": success,
            "details": details,
            "response_data": response_data,
            "timestamp": datetime.now().isoformat()
        }
        
        status = "✅ PASS" if success else "❌ FAIL"
        console.print(f"{status} {test_name}: {details}")
        
        if response_data and success:
            console.print(f"   Response sample: {json.dumps(response_data, indent=2)[:200]}...")
    
    async def test_health_endpoints(self):
        """Test basic health and info endpoints."""
        console.print("\n[bold blue]Testing Health & Info Endpoints[/bold blue]")
        
        # Test root endpoint
        try:
            response = await self.client.get(f"{self.base_url}/")
            if response.status_code == 200:
                data = response.json()
                self.log_test(
                    "Root Endpoint",
                    True,
                    f"Status: {response.status_code}",
                    data
                )
            else:
                self.log_test("Root Endpoint", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Root Endpoint", False, f"Error: {e}")
        
        # Test health endpoint
        try:
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                self.log_test(
                    "Health Check",
                    True,
                    f"Status: {data.get('status', 'unknown')}",
                    data
                )
            else:
                self.log_test("Health Check", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Health Check", False, f"Error: {e}")
    
    async def test_openapi_docs(self):
        """Test OpenAPI documentation endpoints."""
        console.print("\n[bold blue]Testing API Documentation[/bold blue]")
        
        # Test OpenAPI JSON
        try:
            response = await self.client.get(f"{self.base_url}/openapi.json")
            if response.status_code == 200:
                data = response.json()
                self.log_test(
                    "OpenAPI Schema",
                    True,
                    f"Endpoints: {len(data.get('paths', {}))}, Version: {data.get('info', {}).get('version')}",
                    {"title": data.get("info", {}).get("title"), "version": data.get("info", {}).get("version")}
                )
            else:
                self.log_test("OpenAPI Schema", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("OpenAPI Schema", False, f"Error: {e}")
        
        # Test Swagger UI
        try:
            response = await self.client.get(f"{self.base_url}/docs")
            if response.status_code == 200:
                self.log_test("Swagger UI", True, "Interactive docs accessible")
            else:
                self.log_test("Swagger UI", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Swagger UI", False, f"Error: {e}")
        
        # Test ReDoc
        try:
            response = await self.client.get(f"{self.base_url}/redoc")
            if response.status_code == 200:
                self.log_test("ReDoc", True, "Alternative docs accessible")
            else:
                self.log_test("ReDoc", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("ReDoc", False, f"Error: {e}")
    
    async def test_analysis_endpoints(self):
        """Test analysis endpoints with real requests."""
        console.print("\n[bold blue]Testing Analysis Endpoints[/bold blue]")
        
        # Sample request data
        sample_request = {
            "name": "API Test Analysis",
            "description": "Test analysis created via API testing script",
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "markets": ["SANAA_CENTRAL", "ADEN_MAIN"],
            "commodities": ["WHEAT_FLOUR", "RICE_IMPORTED"],
            "confidence_level": 0.95,
            "include_diagnostics": True
        }
        
        # Test Three-Tier Analysis Creation
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/analysis/three-tier",
                json=sample_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 202:
                data = response.json()
                analysis_id = data.get("id")
                self.log_test(
                    "Create Three-Tier Analysis",
                    True,
                    f"Analysis queued with ID: {analysis_id}",
                    data
                )
                
                # Store ID for status checks
                self.analysis_id = analysis_id
                
            elif response.status_code == 401:
                self.log_test(
                    "Create Three-Tier Analysis",
                    False,
                    "Authentication required - this is expected without API key"
                )
            else:
                self.log_test(
                    "Create Three-Tier Analysis",
                    False,
                    f"Status: {response.status_code}, Response: {response.text[:200]}"
                )
        except Exception as e:
            self.log_test("Create Three-Tier Analysis", False, f"Error: {e}")
        
        # Test individual tier endpoints
        tier_configs = {
            "tier1": {"include_fixed_effects": True},
            "tier2": {"commodity_groups": ["FOOD"]},
            "tier3": {"n_factors": 3}
        }
        
        for tier in ["tier1", "tier2", "tier3"]:
            try:
                tier_request = sample_request.copy()
                tier_request["tier_config"] = tier_configs[tier]
                
                response = await self.client.post(
                    f"{self.base_url}/api/v1/analysis/{tier}",
                    json=tier_request,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 202:
                    data = response.json()
                    self.log_test(
                        f"Create {tier.upper()} Analysis",
                        True,
                        f"Analysis queued with ID: {data.get('id')}",
                        data
                    )
                elif response.status_code == 401:
                    self.log_test(
                        f"Create {tier.upper()} Analysis",
                        False,
                        "Authentication required - this is expected without API key"
                    )
                else:
                    self.log_test(
                        f"Create {tier.upper()} Analysis",
                        False,
                        f"Status: {response.status_code}"
                    )
            except Exception as e:
                self.log_test(f"Create {tier.upper()} Analysis", False, f"Error: {e}")
    
    async def test_status_endpoints(self):
        """Test analysis status and results endpoints."""
        console.print("\n[bold blue]Testing Status & Results Endpoints[/bold blue]")
        
        # Use a fake UUID to test endpoint structure
        test_id = str(uuid.uuid4())
        
        # Test analysis status
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/analysis/{test_id}/status")
            
            if response.status_code == 404:
                self.log_test(
                    "Get Analysis Status",
                    True,
                    "Correctly returns 404 for non-existent analysis"
                )
            elif response.status_code == 401:
                self.log_test(
                    "Get Analysis Status",
                    False,
                    "Authentication required - this is expected without API key"
                )
            else:
                data = response.json()
                self.log_test(
                    "Get Analysis Status",
                    True,
                    f"Status: {response.status_code}",
                    data
                )
        except Exception as e:
            self.log_test("Get Analysis Status", False, f"Error: {e}")
        
        # Test analysis results
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/analysis/{test_id}/results")
            
            if response.status_code == 404:
                self.log_test(
                    "Get Analysis Results",
                    True,
                    "Correctly returns 404 for non-existent analysis"
                )
            elif response.status_code == 401:
                self.log_test(
                    "Get Analysis Results",
                    False,
                    "Authentication required - this is expected without API key"
                )
            else:
                data = response.json()
                self.log_test(
                    "Get Analysis Results",
                    True,
                    f"Status: {response.status_code}",
                    data
                )
        except Exception as e:
            self.log_test("Get Analysis Results", False, f"Error: {e}")
    
    async def test_data_endpoints(self):
        """Test markets, commodities, and prices endpoints."""
        console.print("\n[bold blue]Testing Data Endpoints[/bold blue]")
        
        # Test markets endpoint
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/markets")
            
            if response.status_code == 200:
                data = response.json()
                self.log_test(
                    "Get Markets",
                    True,
                    f"Retrieved {len(data.get('items', []))} markets",
                    {"sample": data.get('items', [])[:2] if data.get('items') else []}
                )
            elif response.status_code == 401:
                self.log_test(
                    "Get Markets",
                    False,
                    "Authentication required - this is expected without API key"
                )
            else:
                self.log_test("Get Markets", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Get Markets", False, f"Error: {e}")
        
        # Test commodities endpoint
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/commodities")
            
            if response.status_code == 200:
                data = response.json()
                self.log_test(
                    "Get Commodities",
                    True,
                    f"Retrieved {len(data.get('items', []))} commodities",
                    {"sample": data.get('items', [])[:2] if data.get('items') else []}
                )
            elif response.status_code == 401:
                self.log_test(
                    "Get Commodities",
                    False,
                    "Authentication required - this is expected without API key"
                )
            else:
                self.log_test("Get Commodities", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Get Commodities", False, f"Error: {e}")
        
        # Test prices endpoint with query parameters
        try:
            params = {
                "start_date": "2023-01-01",
                "end_date": "2023-01-31",
                "limit": 10
            }
            response = await self.client.get(f"{self.base_url}/api/v1/prices", params=params)
            
            if response.status_code == 200:
                data = response.json()
                self.log_test(
                    "Get Prices",
                    True,
                    f"Retrieved {len(data.get('items', []))} price records",
                    {"sample": data.get('items', [])[:2] if data.get('items') else []}
                )
            elif response.status_code == 401:
                self.log_test(
                    "Get Prices",
                    False,
                    "Authentication required - this is expected without API key"
                )
            else:
                self.log_test("Get Prices", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Get Prices", False, f"Error: {e}")
    
    async def test_sse_endpoint(self):
        """Test Server-Sent Events endpoint."""
        console.print("\n[bold blue]Testing SSE Endpoint[/bold blue]")
        
        test_id = str(uuid.uuid4())
        
        try:
            # Test SSE connection (with timeout since it's a stream)
            async with self.client.stream(
                "GET", 
                f"{self.base_url}/api/v1/analysis/{test_id}/status",
                headers={"Accept": "text/event-stream"},
                timeout=5.0
            ) as response:
                
                if response.status_code == 404:
                    self.log_test(
                        "SSE Analysis Status",
                        True,
                        "Correctly handles non-existent analysis for SSE"
                    )
                elif response.status_code == 401:
                    self.log_test(
                        "SSE Analysis Status",
                        False,
                        "Authentication required - this is expected without API key"
                    )
                else:
                    # Try to read one event
                    try:
                        async for chunk in response.aiter_text():
                            if chunk.strip():
                                self.log_test(
                                    "SSE Analysis Status",
                                    True,
                                    "SSE stream established successfully",
                                    {"first_chunk": chunk[:100]}
                                )
                                break
                    except asyncio.TimeoutError:
                        self.log_test(
                            "SSE Analysis Status",
                            True,
                            "SSE endpoint accessible (timeout expected)"
                        )
                        
        except Exception as e:
            self.log_test("SSE Analysis Status", False, f"Error: {e}")
        
        # Test general SSE endpoint
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/sse")
            
            if response.status_code in [200, 404, 405]:  # Various acceptable responses
                self.log_test(
                    "SSE General Endpoint",
                    True,
                    f"SSE endpoint accessible (status: {response.status_code})"
                )
            elif response.status_code == 401:
                self.log_test(
                    "SSE General Endpoint",
                    False,
                    "Authentication required - this is expected without API key"
                )
            else:
                self.log_test("SSE General Endpoint", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("SSE General Endpoint", False, f"Error: {e}")
    
    async def test_error_handling(self):
        """Test API error handling and validation."""
        console.print("\n[bold blue]Testing Error Handling & Validation[/bold blue]")
        
        # Test invalid JSON
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/analysis/three-tier",
                content="invalid json",
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 422:  # Validation error
                self.log_test(
                    "Invalid JSON Handling",
                    True,
                    "Correctly rejects invalid JSON"
                )
            else:
                self.log_test(
                    "Invalid JSON Handling",
                    False,
                    f"Expected 422, got {response.status_code}"
                )
        except Exception as e:
            self.log_test("Invalid JSON Handling", False, f"Error: {e}")
        
        # Test invalid date range
        try:
            invalid_request = {
                "name": "Invalid Date Test",
                "start_date": "2023-12-31",
                "end_date": "2023-01-01",  # End before start
                "commodities": ["WHEAT_FLOUR"]
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/analysis/three-tier",
                json=invalid_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 422:  # Validation error
                self.log_test(
                    "Invalid Date Range",
                    True,
                    "Correctly rejects invalid date range"
                )
            else:
                self.log_test(
                    "Invalid Date Range",
                    False,
                    f"Expected 422, got {response.status_code}"
                )
        except Exception as e:
            self.log_test("Invalid Date Range", False, f"Error: {e}")
        
        # Test missing required fields
        try:
            incomplete_request = {
                "description": "Missing required fields"
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/analysis/three-tier",
                json=incomplete_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 422:  # Validation error
                self.log_test(
                    "Missing Required Fields",
                    True,
                    "Correctly rejects incomplete requests"
                )
            else:
                self.log_test(
                    "Missing Required Fields",
                    False,
                    f"Expected 422, got {response.status_code}"
                )
        except Exception as e:
            self.log_test("Missing Required Fields", False, f"Error: {e}")
    
    async def run_comprehensive_test(self):
        """Run all API tests."""
        console.print(Panel.fit(
            "[bold green]Yemen Market Integration API v2 - Comprehensive Test Suite[/bold green]",
            border_style="green"
        ))
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Test server connectivity
            task = progress.add_task("Testing server connectivity...", total=None)
            try:
                response = await self.client.get(f"{self.base_url}/health", timeout=10.0)
                if response.status_code != 200:
                    console.print(f"[red]Server not accessible at {self.base_url}[/red]")
                    console.print("Please ensure the API server is running with: python -m uvicorn src.interfaces.api.rest.app:app --reload")
                    return
            except Exception as e:
                console.print(f"[red]Cannot connect to server at {self.base_url}: {e}[/red]")
                console.print("Please ensure the API server is running.")
                return
            
            progress.update(task, description="Server accessible, running tests...")
            
            # Run all test suites
            await self.test_health_endpoints()
            await self.test_openapi_docs()
            await self.test_analysis_endpoints()
            await self.test_status_endpoints()
            await self.test_data_endpoints()
            await self.test_sse_endpoint()
            await self.test_error_handling()
            
            progress.update(task, description="All tests completed!")
        
        # Display results summary
        self.display_results_summary()
    
    def display_results_summary(self):
        """Display test results summary."""
        console.print("\n" + "="*80)
        console.print("[bold cyan]Test Results Summary[/bold cyan]")
        console.print("="*80)
        
        passed = sum(1 for result in self.test_results.values() if result["success"])
        total = len(self.test_results)
        
        # Results table
        table = Table(title="API Test Results")
        table.add_column("Test", style="cyan", no_wrap=True)
        table.add_column("Status", style="bold")
        table.add_column("Details", style="dim")
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            table.add_row(test_name, status, result["details"])
        
        console.print(table)
        
        # Summary stats
        pass_rate = (passed / total * 100) if total > 0 else 0
        console.print(f"\n[bold]Summary: {passed}/{total} tests passed ({pass_rate:.1f}%)[/bold]")
        
        if pass_rate >= 80:
            console.print("[green]🎉 API is working well![/green]")
        elif pass_rate >= 60:
            console.print("[yellow]⚠️  API has some issues that need attention[/yellow]")
        else:
            console.print("[red]🚨 API has significant issues[/red]")
        
        # Save results to file
        results_file = Path("api_test_results.json")
        with open(results_file, "w") as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "base_url": self.base_url,
                "summary": {"passed": passed, "total": total, "pass_rate": pass_rate},
                "results": self.test_results
            }, f, indent=2)
        
        console.print(f"\n📄 Detailed results saved to: [cyan]{results_file}[/cyan]")


def create_startup_script():
    """Create API server startup script."""
    startup_script_content = '''#!/usr/bin/env python3
"""
API Server Startup Script for Yemen Market Integration v2
"""

import os
import sys
import subprocess
from pathlib import Path

def setup_environment():
    """Set up environment variables for API server."""
    env_vars = {
        "DATABASE_URL": "postgresql://localhost/yemen_market",
        "CACHE_TYPE": "memory",
        "CACHE_TTL": "3600",
        "CACHE_MAX_SIZE": "1000",
        "EVENT_BUS_TYPE": "inmemory",
        "EVENT_QUEUE_SIZE": "10000",
        "HDX_TIMEOUT": "30",
        "WFP_API_KEY": "",
        "ACLED_API_KEY": "",
        "ACLED_EMAIL": "",
        "POLICY_RESULTS_PATH": "data/policy_results",
        "API_HOST": "0.0.0.0",
        "API_PORT": "8000",
        "API_RELOAD": "true",
        "API_LOG_LEVEL": "info"
    }
    
    for key, default_value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = default_value
    
    print("Environment variables configured:")
    for key, value in env_vars.items():
        print(f"  {key}={os.environ.get(key)}")

def main():
    """Start the API server."""
    print("🚀 Starting Yemen Market Integration API v2...")
    
    # Setup environment
    setup_environment()
    
    # Change to project directory
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # Start server
    try:
        # Method 1: Try using the main.py from the main src directory
        if (project_root / "src" / "interfaces" / "api" / "rest" / "app.py").exists():
            print("Starting server using main src API...")
            subprocess.run([
                sys.executable, "-m", "uvicorn", 
                "src.interfaces.api.rest.app:app",
                "--host", os.environ.get("API_HOST", "0.0.0.0"),
                "--port", os.environ.get("API_PORT", "8000"),
                "--reload" if os.environ.get("API_RELOAD", "false").lower() == "true" else "--no-reload",
                "--log-level", os.environ.get("API_LOG_LEVEL", "info")
            ], check=True)
        
        # Method 2: Try using v2/main.py
        elif (project_root / "v2" / "main.py").exists():
            print("Starting server using v2 main.py...")
            subprocess.run([
                sys.executable, str(project_root / "v2" / "main.py")
            ], check=True)
        
        else:
            print("❌ Could not find API application to start")
            print("Please ensure one of these exists:")
            print("  - src/interfaces/api/rest/app.py")
            print("  - v2/main.py")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\\n🛑 Server stopped by user")

if __name__ == "__main__":
    main()
'''
    
    script_path = Path("start_api_server.py")
    with open(script_path, "w") as f:
        f.write(startup_script_content)
    
    # Make script executable
    script_path.chmod(0o755)
    
    return script_path


async def main():
    """Main function to run API tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Yemen Market Integration v2 API")
    parser.add_argument(
        "--url", 
        default="http://localhost:8000",
        help="API base URL (default: http://localhost:8000)"
    )
    parser.add_argument(
        "--create-startup-script",
        action="store_true",
        help="Create API server startup script"
    )
    
    args = parser.parse_args()
    
    if args.create_startup_script:
        script_path = create_startup_script()
        console.print(f"[green]✅ Created startup script: {script_path}[/green]")
        console.print(f"Run with: [cyan]python {script_path}[/cyan]")
        return
    
    # Run comprehensive API tests
    async with APITester(args.url) as tester:
        await tester.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())