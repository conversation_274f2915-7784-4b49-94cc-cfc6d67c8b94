#!/usr/bin/env python3
"""
Test the working API setup.
"""

import working_api

def main():
    print("Testing working API setup...")
    
    # Test app creation
    app = working_api.app
    print(f"✓ FastAPI app created with {len(app.routes)} routes")
    
    # List routes
    print("\nAvailable routes:")
    for route in app.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            for method in route.methods:
                if method != 'HEAD':
                    print(f"  {method} {route.path}")
    
    # Test schemas
    try:
        from working_api import ThreeTierAnalysisRequest, TierAnalysisRequest
        print("✓ Schemas imported successfully")
    except ImportError as e:
        print(f"✗ Schema import error: {e}")
        return False
    
    # Test mock service
    try:
        service = working_api.analysis_service
        print(f"✓ Mock analysis service created: {type(service).__name__}")
    except Exception as e:
        print(f"✗ Service creation error: {e}")
        return False
    
    print("\n✓ Working API is ready for testing!")
    print("To start the server: python working_api.py")
    print("To run endpoint tests: python test_api_endpoints.py")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)