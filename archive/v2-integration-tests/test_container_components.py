#!/usr/bin/env python3
"""Test container and dependency injection components."""

import sys
from pathlib import Path

def test_individual_dependencies():
    """Test individual components that the container needs."""
    print("Testing individual container dependencies...")
    
    results = {}
    
    # Test repository interfaces
    try:
        from core.domain.market.repositories import MarketRepository, PriceRepository
        print("✅ Repository interfaces import successful")
        results['repositories'] = True
    except Exception as e:
        print(f"❌ Repository interfaces failed: {e}")
        results['repositories'] = False
    
    # Test value objects
    try:
        from core.domain.market.value_objects import Price, MarketType
        print("✅ Value objects import successful")
        results['value_objects'] = True
    except Exception as e:
        print(f"❌ Value objects failed: {e}")
        results['value_objects'] = False
    
    # Test API schemas (lightweight test)
    try:
        from interfaces.api.rest.schemas.analysis import AnalysisRequest
        print("✅ API schemas import successful")
        results['api_schemas'] = True
    except Exception as e:
        print(f"❌ API schemas failed: {e}")
        results['api_schemas'] = False
    
    return results

def test_direct_service_imports():
    """Test importing services directly without going through __init__.py files."""
    print("\nTesting direct service imports...")
    
    results = {}
    
    # Try to import the three-tier analysis service directly
    try:
        # Read the file directly to see if it exists and what it contains
        service_path = Path("src/application/services/three_tier_analysis_service.py")
        if service_path.exists():
            print("✅ ThreeTierAnalysisService file exists")
            results['service_file'] = True
        else:
            print("❌ ThreeTierAnalysisService file missing")
            results['service_file'] = False
    except Exception as e:
        print(f"❌ Service file check failed: {e}")
        results['service_file'] = False
    
    return results

def test_mock_container():
    """Test a mock dependency container to see what works."""
    print("\nTesting mock container functionality...")
    
    results = {}
    
    try:
        # Test basic dependency injection concepts
        from dependency_injector import containers, providers
        
        class MockContainer(containers.DeclarativeContainer):
            # This should work since it's just the framework
            config = providers.Configuration()
        
        container = MockContainer()
        print("✅ Basic container creation successful")
        results['basic_container'] = True
        
    except Exception as e:
        print(f"❌ Basic container failed: {e}")
        results['basic_container'] = False
    
    return results

def test_existing_working_components():
    """Test components we know are working."""
    print("\nTesting known working components...")
    
    results = {}
    
    # Test working domain entities
    try:
        from core.domain.market.entities import Market, PriceObservation
        market = Market(id="test", name="Test Market", country="Yemen")
        print("✅ Domain entity instantiation successful")
        results['entity_instantiation'] = True
    except Exception as e:
        print(f"❌ Entity instantiation failed: {e}")
        results['entity_instantiation'] = False
    
    # Test working model interfaces
    try:
        from core.models.interfaces.model import Model
        from core.models.interfaces.estimator import Estimator
        print("✅ Model interface imports successful")
        results['model_interfaces'] = True
    except Exception as e:
        print(f"❌ Model interfaces failed: {e}")
        results['model_interfaces'] = False
    
    return results

def main():
    """Run container and component tests."""
    print("=" * 60)
    print("CONTAINER & DEPENDENCY INJECTION VALIDATION")
    print("=" * 60)
    
    # Test components
    dependency_results = test_individual_dependencies()
    service_results = test_direct_service_imports()
    container_results = test_mock_container()
    working_results = test_existing_working_components()
    
    # Combine results
    all_results = {**dependency_results, **service_results, **container_results, **working_results}
    
    # Summary
    print("\n" + "=" * 60)
    print("CONTAINER SYSTEM SUMMARY")
    print("=" * 60)
    
    working = sum(all_results.values())
    total = len(all_results)
    
    print(f"Overall Status: {working}/{total} container tests passed ({working/total*100:.1f}%)")
    
    # Show what's working
    working_components = [name for name, status in all_results.items() if status]
    if working_components:
        print("\n✅ WORKING COMPONENTS:")
        for comp in working_components:
            print(f"   • {comp}")
    
    # Show what's broken
    broken_components = [name for name, status in all_results.items() if not status]
    if broken_components:
        print("\n❌ BROKEN COMPONENTS:")
        for comp in broken_components:
            print(f"   • {comp}")
    
    if working >= total * 0.6:
        print("\n✅ Container foundation is solid")
        return True
    else:
        print("\n❌ Container system needs work")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)