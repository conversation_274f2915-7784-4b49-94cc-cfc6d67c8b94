#!/usr/bin/env python3
"""Test tier runners and key V2 components."""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_import(module_path, class_name=None):
    """Test importing a module and optionally a class."""
    try:
        if class_name:
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_path}.{class_name} import successful")
        else:
            __import__(module_path)
            print(f"✅ {module_path} import successful")
        return True
    except Exception as e:
        print(f"❌ {module_path}" + (f".{class_name}" if class_name else "") + f" import failed: {e}")
        return False

def main():
    """Test tier runners and services."""
    print("=" * 60)
    print("V2 TIER RUNNERS & SERVICES VALIDATION")
    print("=" * 60)
    
    tests = [
        # Tier runners (these might have dependency issues)
        ("application.analysis_tiers.tier1_runner", "Tier1Runner"),
        ("application.analysis_tiers.tier2_runner", "Tier2Runner"), 
        ("application.analysis_tiers.tier3_runner", "Tier3Runner"),
        
        # Infrastructure components
        ("infrastructure.diagnostics.panel_diagnostics", "PanelDiagnostics"),
        ("infrastructure.estimators.panel_estimators", "PanelEstimator"),
        ("infrastructure.estimators.standard_errors", "StandardErrors"),
        
        # External services
        ("infrastructure.external_services.hdx_client", "HDXClient"),
        ("infrastructure.external_services.wfp_client", "WFPClient"),
        ("infrastructure.external_services.acled_client", "ACLEDClient"),
        
        # Caching (might fail due to redis)
        ("infrastructure.caching.memory_cache", "MemoryCache"),
        
        # Container and plugins
        ("shared.container", "Container"),
        ("shared.plugins.manager", "PluginManager"),
    ]
    
    passed = 0
    total = len(tests)
    
    for module_path, class_name in tests:
        if test_import(module_path, class_name):
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} imports successful")
    
    if passed >= total * 0.7:  # 70% threshold
        print("✅ MOST COMPONENTS WORKING!")
        print("The V2 system has good coverage.")
    else:
        print("❌ MANY IMPORTS FAILED!")
        print("Significant implementation or dependency issues.")
    
    print("=" * 60)
    
    return passed >= total * 0.7

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)