#!/usr/bin/env python3
"""Test plugin system functionality."""

import sys
from pathlib import Path

def test_plugin_imports():
    """Test that plugins can be imported directly."""
    print("Testing plugin imports...")
    
    results = {}
    
    # Test WFP plugin
    try:
        from plugins.data_sources.wfp_poc.plugin import WFPDataSourcePlugin
        print("✅ WFP plugin import successful")
        results['wfp'] = True
    except Exception as e:
        print(f"❌ WFP plugin import failed: {e}")
        results['wfp'] = False
    
    # Test World Bank plugin
    try:
        from plugins.data_sources.world_bank.plugin import WorldBankDataPlugin
        print("✅ World Bank plugin import successful")
        results['world_bank'] = True
    except Exception as e:
        print(f"❌ World Bank plugin import failed: {e}")
        results['world_bank'] = False
    
    # Test VECM plugin
    try:
        from plugins.models.custom_vecm.plugin import CustomVECMPlugin
        print("✅ Custom VECM plugin import successful") 
        results['vecm'] = True
    except Exception as e:
        print(f"❌ Custom VECM plugin import failed: {e}")
        results['vecm'] = False
    
    # Test LaTeX plugin
    try:
        from plugins.outputs.latex_report.plugin import LaTeXReportPlugin
        print("✅ LaTeX report plugin import successful")
        results['latex'] = True
    except Exception as e:
        print(f"❌ LaTeX report plugin import failed: {e}")
        results['latex'] = False
    
    return results

def test_plugin_instantiation():
    """Test that plugins can be instantiated."""
    print("\nTesting plugin instantiation...")
    
    results = {}
    
    # Test WFP plugin instantiation
    try:
        from plugins.data_sources.wfp_poc.plugin import WFPDataSourcePlugin
        plugin = WFPDataSourcePlugin()
        print("✅ WFP plugin instantiation successful")
        results['wfp_instance'] = True
    except Exception as e:
        print(f"❌ WFP plugin instantiation failed: {e}")
        results['wfp_instance'] = False
    
    # Test World Bank plugin instantiation
    try:
        from plugins.data_sources.world_bank.plugin import WorldBankDataPlugin
        plugin = WorldBankDataPlugin()
        print("✅ World Bank plugin instantiation successful")
        results['world_bank_instance'] = True
    except Exception as e:
        print(f"❌ World Bank plugin instantiation failed: {e}")
        results['world_bank_instance'] = False
    
    return results

def test_plugin_manager():
    """Test plugin manager functionality."""
    print("\nTesting plugin manager...")
    
    results = {}
    
    # Test plugin manager import
    try:
        # Try the V2 import first
        from shared.plugins.manager import PluginManager
        print("✅ Plugin manager import successful (V2)")
        
        # Test initialization
        manager = PluginManager(plugin_dirs=[Path("plugins")])
        print("✅ Plugin manager initialization successful")
        results['manager'] = True
        
        # Test discovery (this might fail due to dependencies)
        try:
            discovered = manager.discover_plugins()
            print(f"✅ Plugin discovery successful: {list(discovered.keys())}")
            results['discovery'] = True
        except Exception as e:
            print(f"❌ Plugin discovery failed: {e}")
            results['discovery'] = False
            
    except Exception as e:
        print(f"❌ Plugin manager failed: {e}")
        results['manager'] = False
        results['discovery'] = False
    
    return results

def main():
    """Run plugin system tests."""
    print("=" * 60)
    print("PLUGIN SYSTEM VALIDATION")
    print("=" * 60)
    
    # Test imports
    import_results = test_plugin_imports()
    
    # Test instantiation
    instance_results = test_plugin_instantiation()
    
    # Test manager
    manager_results = test_plugin_manager()
    
    # Combine results
    all_results = {**import_results, **instance_results, **manager_results}
    
    # Summary
    print("\n" + "=" * 60)
    print("PLUGIN SYSTEM SUMMARY")
    print("=" * 60)
    
    working = sum(all_results.values())
    total = len(all_results)
    
    print(f"Overall Status: {working}/{total} plugin tests passed ({working/total*100:.1f}%)")
    
    if working >= total * 0.7:
        print("✅ Plugin system is working well")
        return True
    else:
        print("❌ Plugin system has significant issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)