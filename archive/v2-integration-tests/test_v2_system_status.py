#!/usr/bin/env python3
"""Test V2 system status and create comprehensive report."""

import sys
import traceback
from pathlib import Path

def test_component(test_name, test_func):
    """Test a component and return result."""
    try:
        test_func()
        print(f"✅ {test_name}: WORKING")
        return True
    except Exception as e:
        print(f"❌ {test_name}: FAILED - {str(e)[:100]}")
        return False

def main():
    """Run comprehensive V2 system tests."""
    print("=" * 80)
    print("YEMEN MARKET INTEGRATION V2 SYSTEM STATUS REPORT")
    print("=" * 80)
    
    results = {}
    
    # Test 1: Core Domain Entities
    def test_domain_entities():
        from core.domain.market.entities import Market, PriceObservation, PanelData
        from core.domain.market.repositories import MarketRepository, PriceRepository
        from core.domain.market.value_objects import Price, MarketType
        return True
    
    results['domain_entities'] = test_component("Domain Entities", test_domain_entities)
    
    # Test 2: Model Interfaces
    def test_model_interfaces():
        from core.models.interfaces.model import Model
        from core.models.interfaces.estimator import Estimator
        from core.models.panel.pooled_panel import PooledPanelModel
        from core.models.panel.fixed_effects import FixedEffectsModel
        return True
    
    results['model_interfaces'] = test_component("Model Interfaces", test_model_interfaces)
    
    # Test 3: Infrastructure Components (lighter ones)
    def test_infrastructure_basic():
        from infrastructure.estimators.standard_errors import StandardErrorCalculator
        return True
    
    results['infrastructure_basic'] = test_component("Infrastructure (Basic)", test_infrastructure_basic)
    
    # Test 4: External Services
    def test_external_services():
        from infrastructure.external_services.hdx_client import HDXClientImpl
        from infrastructure.external_services.wfp_client import WFPClientImpl
        from infrastructure.external_services.acled_client import ACLEDClientImpl
        return True
    
    results['external_services'] = test_component("External Services", test_external_services)
    
    # Test 5: Caching (Memory only)
    def test_caching():
        from infrastructure.caching.memory_cache import MemoryCache
        return True
    
    results['caching'] = test_component("Caching (Memory)", test_caching)
    
    # Test 6: API Interfaces
    def test_api_interfaces():
        from interfaces.api.rest.schemas.analysis import AnalysisRequest
        from interfaces.api.rest.schemas.markets import MarketResponse
        return True
    
    results['api_interfaces'] = test_component("API Interfaces", test_api_interfaces)
    
    # Test 7: Application Layer (this will likely fail due to circular imports)
    def test_application_commands():
        from application.commands.run_three_tier_analysis import RunThreeTierAnalysisCommand
        return True
    
    results['application_commands'] = test_component("Application Commands", test_application_commands)
    
    # Test 8: Plugin System
    def test_plugin_system():
        from shared.plugins.interfaces import PluginInterface
        from shared.plugins.manager import PluginManager
        return True
    
    results['plugin_system'] = test_component("Plugin System", test_plugin_system)
    
    # Test 9: Container (this will likely fail)
    def test_container():
        from shared.container import Container
        container = Container()
        return True
    
    results['container'] = test_component("Dependency Container", test_container)
    
    # Test 10: Tier Runners (these will likely fail)
    def test_tier_runners():
        from application.analysis_tiers.tier1_runner import Tier1Runner
        from application.analysis_tiers.tier2_runner import Tier2Runner
        from application.analysis_tiers.tier3_runner import Tier3Runner
        return True
    
    results['tier_runners'] = test_component("Tier Runners", test_tier_runners)
    
    # Summary
    print("\n" + "=" * 80)
    print("COMPREHENSIVE SYSTEM STATUS")
    print("=" * 80)
    
    working = sum(results.values())
    total = len(results)
    
    print(f"Overall Status: {working}/{total} components working ({working/total*100:.1f}%)")
    print()
    
    # Working components
    working_components = [name for name, status in results.items() if status]
    if working_components:
        print("✅ WORKING COMPONENTS:")
        for comp in working_components:
            print(f"   • {comp}")
        print()
    
    # Broken components
    broken_components = [name for name, status in results.items() if not status]
    if broken_components:
        print("❌ BROKEN COMPONENTS:")
        for comp in broken_components:
            print(f"   • {comp}")
        print()
    
    # Assessment
    if working >= 8:
        print("🎯 ASSESSMENT: V2 system is in EXCELLENT shape")
        print("   Ready for production deployment with minor fixes")
    elif working >= 6:
        print("🎯 ASSESSMENT: V2 system is in GOOD shape")  
        print("   Ready for research use with some components needing attention")
    elif working >= 4:
        print("🎯 ASSESSMENT: V2 system is PARTIALLY working")
        print("   Core functionality available but significant issues remain")
    else:
        print("🎯 ASSESSMENT: V2 system has MAJOR issues")
        print("   Significant rework needed before use")
    
    print("\n" + "=" * 80)
    
    return working >= 6

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)