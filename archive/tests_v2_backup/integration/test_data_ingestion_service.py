"""Integration tests for the V2 data ingestion service.

These tests validate the complete data ingestion pipeline with sample data,
ensuring proper integration between processors, repositories, and monitoring.
"""

import asyncio
import pytest
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, AsyncMock
import pandas as pd
import json
from uuid import uuid4

from application.services.data_ingestion_service import DataIngestionService, IngestionResult
from application.services.ingestion_orchestrator import (
    IngestionOrchestrator, IngestionJob, IngestionPriority, IngestionStatus
)
from infrastructure.processors.wfp_processor import WFPProcessor, WFPProcessingConfig
from infrastructure.processors.acled_processor import ACLEDProcessor, ACLEDProcessingConfig
from infrastructure.processors.acaps_processor import ACAPSProcessor, ACAPSProcessingConfig
from infrastructure.monitoring.data_quality_monitor import DataQualityMonitor
from core.domain.market.entities import Market, PriceObservation
from core.domain.market.value_objects import MarketId, Commodity, Price, Currency, Coordinates
from core.domain.conflict.entities import ConflictEvent
from core.domain.conflict.value_objects import ConflictType, ConflictIntensity


@pytest.fixture
def sample_wfp_data():
    """Create sample WFP price data for testing."""
    return pd.DataFrame({
        'date': [
            '2024-01-15', '2024-01-15', '2024-01-15',
            '2024-02-15', '2024-02-15', '2024-02-15'
        ],
        'admin1': ['Sana\'a', 'Aden', 'Al Hodeidah'] * 2,
        'admin2': ['Al Wahdah', 'Al Mansurah', 'Al Hodeidah'] * 2,
        'market': ['Central Market', 'Main Market', 'Port Market'] * 2,
        'latitude': [15.3694, 12.7797, 14.7978] * 2,
        'longitude': [44.1910, 45.0367, 42.9545] * 2,
        'commodity': ['Wheat Flour', 'Rice (Imported)', 'Oil (Vegetable)'] * 2,
        'unit': ['kg', 'kg', 'liter'] * 2,
        'price': [800, 1200, 2500, 850, 1250, 2600],  # YER prices
        'usdprice': [1.5, 2.2, 4.5, 1.6, 2.3, 4.7],  # USD prices
        'currency': ['YER'] * 6
    })


@pytest.fixture
def sample_acled_data():
    """Create sample ACLED conflict data for testing."""
    return pd.DataFrame({
        'event_date': [
            '2024-01-10', '2024-01-15', '2024-01-20',
            '2024-02-05', '2024-02-10', '2024-02-15'
        ],
        'latitude': [15.3500, 12.8000, 14.8000, 15.4000, 12.7500, 14.7500],
        'longitude': [44.2000, 45.0000, 42.9000, 44.1500, 45.0500, 43.0000],
        'event_type': [
            'Battles', 'Violence against civilians', 'Explosions/Remote violence',
            'Battles', 'Protests', 'Strategic developments'
        ],
        'fatalities': [5, 2, 0, 8, 0, 1],
        'actor1': ['Houthis', 'Government', 'Unknown', 'Houthis', 'Civilians', 'Government'],
        'actor2': ['Government', 'Civilians', 'Government', 'Coalition', 'Government', 'Houthis']
    })


@pytest.fixture
def mock_repositories():
    """Create mock repositories for testing."""
    return {
        'market_repo': AsyncMock(),
        'price_repo': AsyncMock(),
        'conflict_repo': AsyncMock(),
        'geography_repo': AsyncMock(),
        'commodity_repo': AsyncMock()
    }


@pytest.fixture
def mock_external_clients():
    """Create mock external service clients."""
    return {
        'wfp_client': AsyncMock(),
        'acled_client': AsyncMock(),
        'hdx_client': AsyncMock()
    }


@pytest.fixture
def mock_infrastructure():
    """Create mock infrastructure services."""
    return {
        'metrics': Mock(),
        'event_bus': AsyncMock(),
        'cache': AsyncMock(),
        'container': Mock()
    }


@pytest.fixture
async def wfp_processor():
    """Create WFP processor instance for testing."""
    config = WFPProcessingConfig(
        min_market_coverage=0.1,  # Lower threshold for testing
        enable_quality_checks=True
    )
    metrics = Mock()
    return WFPProcessor(config, metrics)


@pytest.fixture
async def acled_processor():
    """Create ACLED processor instance for testing."""
    config = ACLEDProcessingConfig(
        spatial_radius_km=25.0,  # Smaller radius for testing
        enable_quality_checks=True
    )
    metrics = Mock()
    return ACLEDProcessor(config, metrics)


@pytest.fixture
async def data_ingestion_service(mock_repositories, mock_external_clients, mock_infrastructure):
    """Create data ingestion service instance for testing."""
    
    # Create processor mocks
    wfp_processor = AsyncMock()
    acled_processor = AsyncMock()
    acaps_processor = AsyncMock()
    
    # Configure container mock
    mock_infrastructure['container'].get_service.side_effect = lambda name: {
        'wfp_processor': wfp_processor,
        'acled_processor': acled_processor,
        'acaps_processor': acaps_processor
    }.get(name)
    
    service = DataIngestionService(
        market_repository=mock_repositories['market_repo'],
        price_repository=mock_repositories['price_repo'],
        conflict_repository=mock_repositories['conflict_repo'],
        geography_repository=mock_repositories['geography_repo'],
        wfp_client=mock_external_clients['wfp_client'],
        acled_client=mock_external_clients['acled_client'],
        metrics=mock_infrastructure['metrics'],
        container=mock_infrastructure['container']
    )
    
    return service


class TestWFPProcessor:
    """Test WFP data processor."""
    
    async def test_process_price_data_success(self, wfp_processor, sample_wfp_data):
        """Test successful processing of WFP price data."""
        
        # Process the sample data
        result = await wfp_processor.process_price_data(
            sample_wfp_data,
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 2, 29)
        )
        
        # Verify results structure
        assert 'markets' in result
        assert 'price_observations' in result
        assert 'exchange_rate_observations' in result
        assert 'processing_metrics' in result
        
        # Verify markets were created
        markets = result['markets']
        assert len(markets) == 3  # Three unique markets
        
        for market in markets:
            assert isinstance(market, Market)
            assert market.name
            assert market.governorate
            assert market.coordinates is not None
        
        # Verify price observations were created
        prices = result['price_observations']
        assert len(prices) > 0
        
        for price_obs in prices:
            assert isinstance(price_obs, PriceObservation)
            assert price_obs.commodity
            assert price_obs.price
            assert price_obs.observed_date
    
    async def test_process_empty_data(self, wfp_processor):
        """Test processing empty WFP data."""
        
        empty_df = pd.DataFrame()
        
        with pytest.raises(ValueError, match="No WFP data available"):
            await wfp_processor._clean_raw_data(empty_df)
    
    async def test_extract_markets_with_coordinates(self, wfp_processor, sample_wfp_data):
        """Test market extraction with coordinate validation."""
        
        cleaned_data = await wfp_processor._clean_raw_data(sample_wfp_data)
        markets = await wfp_processor._extract_markets(cleaned_data)
        
        # All test markets should have coordinates
        markets_with_coords = [m for m in markets if m.coordinates is not None]
        assert len(markets_with_coords) == len(markets)
        
        # Verify coordinate precision
        for market in markets:
            assert isinstance(market.coordinates, Coordinates)
            assert -90 <= market.coordinates.latitude <= 90
            assert -180 <= market.coordinates.longitude <= 180
    
    async def test_price_outlier_detection(self, wfp_processor):
        """Test price outlier detection functionality."""
        
        # Create test price observations with outliers
        prices = []
        
        # Normal prices for wheat flour
        for i in range(10):
            price_obs = PriceObservation(
                id=uuid4(),
                market_id=MarketId("test_market"),
                commodity=Commodity(code="wheat_flour", name="Wheat Flour", category="grains", unit="kg"),
                price=Price(amount=800 + i * 10, currency=Currency.YER, unit="kg"),
                observed_date=datetime.utcnow(),
                source="TEST"
            )
            prices.append(price_obs)
        
        # Add outlier
        outlier_price = PriceObservation(
            id=uuid4(),
            market_id=MarketId("test_market"),
            commodity=Commodity(code="wheat_flour", name="Wheat Flour", category="grains", unit="kg"),
            price=Price(amount=5000, currency=Currency.YER, unit="kg"),  # Outlier
            observed_date=datetime.utcnow(),
            source="TEST"
        )
        prices.append(outlier_price)
        
        # Detect outliers
        filtered_prices = await wfp_processor._remove_price_outliers(prices)
        
        # Outlier should be removed
        assert len(filtered_prices) == 10  # Original 10 without the outlier


class TestACLEDProcessor:
    """Test ACLED conflict processor."""
    
    async def test_process_conflict_events_success(self, acled_processor, sample_acled_data):
        """Test successful processing of ACLED conflict data."""
        
        # Process the sample data
        events = await acled_processor.process_conflict_events(
            sample_acled_data,
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 2, 29)
        )
        
        # Verify events were created
        assert len(events) == 6  # All sample events should be processed
        
        for event in events:
            assert isinstance(event, ConflictEvent)
            assert event.event_date
            assert event.location
            assert event.conflict_type
            assert event.intensity
            assert event.actors
    
    async def test_conflict_intensity_calculation(self, acled_processor, sample_acled_data):
        """Test conflict intensity calculation from fatalities."""
        
        events = await acled_processor.process_conflict_events(sample_acled_data)
        
        # Check intensity assignment based on fatalities
        for event in events:
            if event.fatalities == 0:
                assert event.intensity == ConflictIntensity.LOW
            elif event.fatalities <= 5:
                assert event.intensity in [ConflictIntensity.LOW, ConflictIntensity.MEDIUM]
            else:
                assert event.intensity in [ConflictIntensity.MEDIUM, ConflictIntensity.HIGH]
    
    async def test_geographic_filtering(self, acled_processor):
        """Test geographic filtering for Yemen bounds."""
        
        # Create data with some events outside Yemen
        test_data = pd.DataFrame({
            'event_date': ['2024-01-15', '2024-01-16', '2024-01-17'],
            'latitude': [15.0, 25.0, 5.0],  # Middle one outside Yemen bounds
            'longitude': [44.0, 44.0, 44.0],
            'event_type': ['Battles'] * 3,
            'fatalities': [1, 2, 3],
            'actor1': ['Actor1'] * 3,
            'actor2': ['Actor2'] * 3
        })
        
        cleaned_data = await acled_processor._clean_raw_data(test_data)
        
        # Should filter out events outside Yemen bounds
        assert len(cleaned_data) == 2  # Only events within Yemen bounds
        assert all(12.0 <= lat <= 19.0 for lat in cleaned_data['latitude'])


class TestDataIngestionService:
    """Test data ingestion service integration."""
    
    async def test_ingest_wfp_data_success(
        self, 
        data_ingestion_service, 
        sample_wfp_data,
        mock_external_clients,
        mock_repositories
    ):
        """Test successful WFP data ingestion."""
        
        # Setup mocks
        mock_external_clients['wfp_client'].fetch_price_data.return_value = sample_wfp_data
        
        # Mock processor result
        mock_processed_data = {
            'markets': [Mock() for _ in range(3)],
            'price_observations': [Mock() for _ in range(6)],
            'exchange_rate_observations': [Mock() for _ in range(2)]
        }
        
        data_ingestion_service.wfp_processor.process_price_data.return_value = mock_processed_data
        
        # Run ingestion
        result = await data_ingestion_service._ingest_wfp_data(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 2, 29)
        )
        
        # Verify result
        assert isinstance(result, IngestionResult)
        assert result.success
        assert result.source == "wfp"
        assert result.records_processed > 0
        assert result.records_saved > 0
        
        # Verify repository calls
        assert mock_repositories['market_repo'].save.call_count == 3
        mock_repositories['price_repo'].save_batch.assert_called_once()
    
    async def test_ingest_all_sources_success(
        self,
        data_ingestion_service,
        sample_wfp_data,
        sample_acled_data,
        mock_external_clients
    ):
        """Test successful ingestion from all sources."""
        
        # Setup mocks
        mock_external_clients['wfp_client'].fetch_price_data.return_value = sample_wfp_data
        mock_external_clients['acled_client'].fetch_events.return_value = sample_acled_data
        
        # Mock processor results
        data_ingestion_service.wfp_processor.process_price_data.return_value = {
            'markets': [Mock()],
            'price_observations': [Mock()],
            'exchange_rate_observations': []
        }
        
        data_ingestion_service.acled_processor.process_conflict_events.return_value = [Mock()]
        data_ingestion_service.acaps_processor.process_control_areas.return_value = [Mock()]
        
        # Run ingestion
        results = await data_ingestion_service.ingest_all_sources(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 2, 29)
        )
        
        # Verify all sources were processed
        assert 'geography' in results
        assert 'wfp' in results
        assert 'acled' in results
        assert 'acaps' in results
        
        # Verify all results are successful
        for source, result in results.items():
            assert isinstance(result, IngestionResult)
            assert result.success


class TestIngestionOrchestrator:
    """Test ingestion orchestration."""
    
    @pytest.fixture
    async def orchestrator(self, data_ingestion_service, mock_infrastructure):
        """Create orchestrator instance for testing."""
        
        orchestrator = IngestionOrchestrator(
            data_ingestion_service=data_ingestion_service,
            metrics=mock_infrastructure['metrics'],
            event_bus=mock_infrastructure['event_bus'],
            cache=mock_infrastructure['cache']
        )
        
        return orchestrator
    
    async def test_submit_job_success(self, orchestrator):
        """Test successful job submission."""
        
        job_id = await orchestrator.submit_job(
            source="wfp",
            priority=IngestionPriority.HIGH,
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 2, 29)
        )
        
        # Verify job was created
        assert job_id in orchestrator.active_jobs
        
        job = orchestrator.active_jobs[job_id]
        assert job.source == "wfp"
        assert job.priority == IngestionPriority.HIGH
        assert job.status == IngestionStatus.PENDING
    
    async def test_job_execution_flow(self, orchestrator, data_ingestion_service):
        """Test complete job execution flow."""
        
        # Setup mock successful result
        mock_result = IngestionResult(
            source="wfp",
            success=True,
            records_processed=100,
            records_saved=95,
            errors=[],
            processing_time_seconds=60.0,
            data_quality_score=0.95
        )
        
        data_ingestion_service._execute_single_source = AsyncMock(return_value=mock_result)
        
        # Submit and start orchestrator
        await orchestrator.start()
        
        try:
            job_id = await orchestrator.submit_job(source="wfp")
            
            # Wait for job to complete
            max_wait = 10  # seconds
            waited = 0
            
            while waited < max_wait:
                job = await orchestrator.get_job_status(job_id)
                if job and job.is_complete:
                    break
                await asyncio.sleep(0.1)
                waited += 0.1
            
            # Verify job completed successfully
            final_job = await orchestrator.get_job_status(job_id)
            assert final_job.status == IngestionStatus.COMPLETED
            assert final_job.result.success
            
        finally:
            await orchestrator.stop()
    
    async def test_batch_job_submission(self, orchestrator):
        """Test batch job submission."""
        
        sources = ["wfp", "acled", "acaps"]
        job_ids = await orchestrator.submit_batch_job(
            sources=sources,
            priority=IngestionPriority.NORMAL
        )
        
        # Verify all jobs were created
        assert len(job_ids) == 3
        
        for job_id in job_ids:
            assert job_id in orchestrator.active_jobs
        
        # Verify source distribution
        submitted_sources = [orchestrator.active_jobs[jid].source for jid in job_ids]
        assert set(submitted_sources) == set(sources)


class TestDataQualityMonitor:
    """Test data quality monitoring."""
    
    @pytest.fixture
    async def quality_monitor(self, mock_repositories, mock_infrastructure):
        """Create quality monitor instance for testing."""
        
        monitor = DataQualityMonitor(
            market_repository=mock_repositories['market_repo'],
            price_repository=mock_repositories['price_repo'],
            conflict_repository=mock_repositories['conflict_repo'],
            metrics=mock_infrastructure['metrics'],
            event_bus=mock_infrastructure['event_bus']
        )
        
        return monitor
    
    async def test_wfp_quality_check(self, quality_monitor, mock_repositories):
        """Test WFP data quality assessment."""
        
        # Setup mock data
        mock_markets = [
            Mock(coordinates=Mock()),  # Market with coordinates
            Mock(coordinates=None)     # Market without coordinates
        ]
        
        mock_prices = [
            Mock(price=Mock(currency=Mock(value="USD")), observed_date=datetime.utcnow()),
            Mock(price=Mock(currency=Mock(value="YER")), observed_date=datetime.utcnow()),
            Mock(price=Mock(currency=Mock(value="USD")), observed_date=datetime.utcnow() - timedelta(days=1))
        ]
        
        mock_repositories['market_repo'].find_all.return_value = mock_markets
        mock_repositories['price_repo'].find_by_date_range.return_value = mock_prices
        
        # Run quality check
        metrics = await quality_monitor._check_wfp_quality()
        
        # Verify metrics were generated
        assert len(metrics) > 0
        
        # Check specific metrics
        coord_metrics = [m for m in metrics.values() if "Coordinate" in m.name]
        assert len(coord_metrics) > 0
        
        price_metrics = [m for m in metrics.values() if "Price" in m.name]
        assert len(price_metrics) > 0
    
    async def test_quality_alert_generation(self, quality_monitor):
        """Test quality alert generation for poor metrics."""
        
        from infrastructure.monitoring.data_quality_monitor import QualityMetric, AlertSeverity
        
        # Create metric with poor quality
        poor_metric = QualityMetric(
            name="Test Metric",
            description="Test metric with poor quality",
            source="test",
            category="completeness",
            current_value=0.60,  # Below warning threshold
            threshold_warning=0.85,
            threshold_critical=0.70
        )
        
        # Check for alerts
        await quality_monitor._check_quality_alerts([poor_metric])
        
        # Verify alert was created
        assert len(quality_monitor.active_alerts) > 0
        
        alert = list(quality_monitor.active_alerts.values())[0]
        assert alert.severity == AlertSeverity.WARNING
        assert alert.metric_id == poor_metric.metric_id
    
    async def test_quality_dashboard_generation(self, quality_monitor, mock_repositories):
        """Test quality dashboard data generation."""
        
        # Setup minimal mock data
        mock_repositories['market_repo'].find_all.return_value = []
        mock_repositories['price_repo'].find_by_date_range.return_value = []
        mock_repositories['conflict_repo'].find_by_date_range.return_value = []
        
        # Generate dashboard
        dashboard = await quality_monitor.get_quality_dashboard()
        
        # Verify dashboard structure
        assert 'overall_score' in dashboard
        assert 'quality_by_source' in dashboard
        assert 'quality_by_category' in dashboard
        assert 'active_alerts' in dashboard
        assert 'generated_at' in dashboard
        
        # Verify data types
        assert isinstance(dashboard['overall_score'], (int, float))
        assert isinstance(dashboard['quality_by_source'], dict)
        assert isinstance(dashboard['active_alerts'], list)


@pytest.mark.integration
class TestEndToEndIngestion:
    """End-to-end integration tests."""
    
    async def test_complete_ingestion_pipeline(
        self,
        data_ingestion_service,
        sample_wfp_data,
        sample_acled_data,
        mock_external_clients,
        mock_repositories
    ):
        """Test complete ingestion pipeline from raw data to stored entities."""
        
        # Setup mock external data
        mock_external_clients['wfp_client'].fetch_price_data.return_value = sample_wfp_data
        mock_external_clients['acled_client'].fetch_events.return_value = sample_acled_data
        
        # Setup successful processor responses
        data_ingestion_service.wfp_processor.process_price_data.return_value = {
            'markets': [Mock() for _ in range(3)],
            'price_observations': [Mock() for _ in range(6)],
            'exchange_rate_observations': []
        }
        
        data_ingestion_service.acled_processor.process_conflict_events.return_value = [
            Mock() for _ in range(6)
        ]
        
        data_ingestion_service.acaps_processor.process_control_areas.return_value = [
            Mock() for _ in range(10)
        ]
        
        # Run complete ingestion
        results = await data_ingestion_service.ingest_all_sources(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 2, 29),
            force_refresh=True
        )
        
        # Verify all sources completed successfully
        assert all(result.success for result in results.values())
        assert len(results) == 4  # geography, wfp, acled, acaps
        
        # Verify repository interactions
        mock_repositories['market_repo'].save.assert_called()
        mock_repositories['price_repo'].save_batch.assert_called()
        
        # Verify metrics were updated
        data_ingestion_service.metrics.gauge.assert_called()
        data_ingestion_service.metrics.increment_counter.assert_called()


if __name__ == "__main__":
    # Run specific test
    pytest.main([__file__, "-v", "--tb=short"])