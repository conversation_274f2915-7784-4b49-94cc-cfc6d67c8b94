"""Integration tests for monitoring and observability components."""

import pytest
import requests
import time
from unittest.mock import patch, MagicMock
import json

from src.infrastructure.observability.observability_manager import observability
from src.infrastructure.observability.business_metrics import business_metrics
from src.infrastructure.observability.structured_logging import (
    StructuredLogger, set_correlation_id, get_correlation_id
)
from src.infrastructure.observability.sentry_integration import sentry_manager


class TestObservabilityIntegration:
    """Test observability components integration."""
    
    def test_metrics_collection(self):
        """Test that metrics are properly collected."""
        # Record some test metrics
        business_metrics.record_data_coverage(
            commodity="Wheat",
            region="Sana'a", 
            data_source="WFP",
            coverage_ratio=0.95
        )
        
        business_metrics.record_analysis_duration(
            analysis_type="cointegration",
            tier="tier2",
            commodity="Wheat",
            duration_seconds=120.5
        )
        
        # Check that metrics endpoint returns data
        metrics_data = observability.get_metrics_endpoint()
        assert b"yemen_market_data_coverage_ratio" in metrics_data
        assert b"yemen_market_analysis_duration_seconds" in metrics_data
    
    def test_structured_logging_with_correlation(self):
        """Test structured logging with correlation IDs."""
        logger = StructuredLogger("test_logger")
        
        # Set correlation ID
        correlation_id = set_correlation_id("test-correlation-123")
        
        # Log some events
        logger.info("Test message", test_field="test_value")
        logger.error("Test error", error_code=500)
        
        # Verify correlation ID is set
        assert get_correlation_id() == correlation_id
    
    def test_operation_context_manager(self):
        """Test the unified operation context manager."""
        with observability.track_analysis(
            analysis_id="test-analysis-001",
            analysis_type="cointegration",
            commodity="Wheat",
            tier="tier2"
        ) as ctx:
            # Verify context contains expected fields
            assert ctx["correlation_id"] is not None
            assert ctx["logger"] is not None
            assert ctx["span"] is not None
            assert ctx["start_time"] is not None
            
            # Log within context
            ctx["logger"].info("Analysis step completed", step="data_loading")
            
            # Simulate work
            time.sleep(0.1)
    
    def test_slo_checking(self):
        """Test SLO status checking."""
        # Check availability SLO
        availability_slo = observability.check_slo("availability")
        assert "target" in availability_slo
        assert "current" in availability_slo
        assert "status" in availability_slo
        
        # Check analysis latency SLO
        latency_slo = observability.check_slo("analysis_latency")
        assert "target" in latency_slo
        assert "error_budget" in latency_slo
    
    def test_health_status_endpoint(self):
        """Test comprehensive health status."""
        health = observability.get_health_status()
        
        assert health["status"] == "healthy"
        assert health["service"] == "yemen-market-integration"
        assert "components" in health
        assert "slos" in health
        
        # Check component health
        components = health["components"]
        assert "logging" in components
        assert "metrics" in components
        assert "tracing" in components
        assert "error_tracking" in components


class TestMetricsIntegration:
    """Test Prometheus metrics integration."""
    
    def test_business_metrics_recording(self):
        """Test recording of business-specific metrics."""
        # Test data coverage metrics
        business_metrics.record_data_coverage(
            commodity="Rice",
            region="Aden",
            data_source="WFP", 
            coverage_ratio=0.87
        )
        
        # Test conflict impact metrics
        business_metrics.record_conflict_impact(
            market="Sana'a Central Market",
            commodity="Wheat",
            impact_type="price_increase",
            score=0.25
        )
        
        # Test exchange rate metrics
        business_metrics.record_exchange_rate(
            currency_zone="houthi_controlled",
            rate_type="parallel_market",
            rate=535.0
        )
        
        business_metrics.record_exchange_rate(
            currency_zone="government_controlled", 
            rate_type="official",
            rate=2100.0
        )
        
        # Test model convergence metrics
        business_metrics.record_model_convergence(
            model_type="vecm",
            commodity="Wheat",
            converged=True
        )
    
    def test_alert_metrics(self):
        """Test alert-related metrics."""
        # Record alert triggered
        business_metrics.record_alert_triggered(
            alert_type="high_latency",
            severity="warning",
            component="api"
        )
        
        # Record SLO violation
        business_metrics.record_slo_violation(
            slo_type="availability",
            service="yemen-market-api",
            severity="critical"
        )


class TestTracingIntegration:
    """Test distributed tracing integration."""
    
    @pytest.mark.asyncio
    async def test_async_operation_tracing(self):
        """Test tracing of async operations."""
        from src.infrastructure.observability.tracing import traced
        
        @traced("test_async_operation")
        async def async_operation():
            await asyncio.sleep(0.1)
            return "success"
        
        result = await async_operation()
        assert result == "success"
    
    def test_sync_operation_tracing(self):
        """Test tracing of sync operations."""
        from src.infrastructure.observability.tracing import traced
        
        @traced("test_sync_operation")
        def sync_operation():
            time.sleep(0.1)
            return "success"
        
        result = sync_operation()
        assert result == "success"


class TestLoggingIntegration:
    """Test log aggregation and search."""
    
    def test_log_aggregation(self):
        """Test log aggregation functionality."""
        from src.infrastructure.observability.structured_logging import LogAggregator
        
        aggregator = LogAggregator()
        
        # This would normally search actual log files
        # For testing, we'll mock the search
        with patch.object(aggregator, 'search_logs') as mock_search:
            mock_search.return_value = [
                {
                    "timestamp": "2024-01-01T12:00:00Z",
                    "level": "INFO",
                    "message": "Test message",
                    "correlation_id": "test-123"
                }
            ]
            
            results = aggregator.search_logs(
                correlation_id="test-123",
                level="INFO"
            )
            
            assert len(results) == 1
            assert results[0]["correlation_id"] == "test-123"


class TestSentryIntegration:
    """Test Sentry error tracking integration."""
    
    def test_sentry_context_setting(self):
        """Test setting Sentry context for analysis."""
        sentry_manager.set_analysis_context(
            analysis_id="test-001",
            analysis_type="cointegration", 
            commodity="Wheat",
            tier="tier2"
        )
        
        sentry_manager.set_data_context(
            data_source="WFP",
            dataset="prices",
            coverage=0.95,
            quality_score=0.87
        )
    
    def test_error_capture(self):
        """Test error capture functionality."""
        # Test data quality issue capture
        sentry_manager.capture_data_quality_issue(
            issue_type="missing_values",
            dataset="wfp_prices",
            details={"missing_count": 150, "total_count": 1000},
            severity="warning"
        )
        
        # Test model error capture
        sentry_manager.capture_model_error(
            model_type="vecm",
            error_type="convergence_failure",
            commodity="Rice",
            details={"iterations": 1000, "tolerance": 1e-6}
        )


class TestEndToEndMonitoring:
    """End-to-end monitoring tests."""
    
    def test_complete_analysis_monitoring(self):
        """Test monitoring of a complete analysis workflow."""
        analysis_id = "e2e-test-001"
        
        with observability.track_analysis(
            analysis_id=analysis_id,
            analysis_type="three_tier",
            commodity="Wheat",
            tier="all"
        ) as ctx:
            logger = ctx["logger"]
            
            # Step 1: Data loading
            logger.info("Starting data loading", step="data_loading")
            business_metrics.record_data_coverage(
                commodity="Wheat",
                region="All",
                data_source="WFP",
                coverage_ratio=0.93
            )
            
            # Step 2: Model estimation
            logger.info("Starting model estimation", step="model_estimation")
            business_metrics.record_model_convergence(
                model_type="pooled_panel",
                commodity="Wheat", 
                converged=True
            )
            
            # Step 3: Results generation
            logger.info("Generating results", step="results")
            business_metrics.record_computation_time(
                operation_type="analysis_complete",
                complexity_level="high",
                duration_seconds=180.0
            )
            
            # Mark analysis complete
            logger.info("Analysis completed successfully", 
                       analysis_id=analysis_id,
                       total_duration=180.0)
    
    @pytest.mark.integration
    def test_metrics_endpoint_accessibility(self):
        """Test that metrics endpoint is accessible."""
        # This would test against actual running service
        # Skip if not in integration test environment
        pytest.skip("Requires running service")
        
        try:
            response = requests.get("http://localhost:9090/metrics", timeout=5)
            assert response.status_code == 200
            assert "yemen_market" in response.text
        except requests.ConnectionError:
            pytest.skip("Metrics endpoint not available")
    
    @pytest.mark.integration
    def test_grafana_dashboard_accessibility(self):
        """Test that Grafana dashboards are accessible."""
        pytest.skip("Requires running Grafana instance")
        
        try:
            response = requests.get(
                "http://localhost:3000/api/dashboards/search",
                auth=("admin", "yemen-market-admin"),
                timeout=5
            )
            assert response.status_code == 200
            
            dashboards = response.json()
            dashboard_titles = [d["title"] for d in dashboards]
            
            expected_dashboards = [
                "Yemen Market Integration - System Overview",
                "Yemen Market Integration - Analysis Performance", 
                "Yemen Market Integration - Data Pipeline Health"
            ]
            
            for expected in expected_dashboards:
                assert expected in dashboard_titles
                
        except requests.ConnectionError:
            pytest.skip("Grafana not available")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])