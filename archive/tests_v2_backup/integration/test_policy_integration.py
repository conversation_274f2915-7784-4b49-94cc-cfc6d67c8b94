"""Integration tests for policy models with real data pipeline."""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock

from src.application.services.policy_orchestrator import PolicyOrchestrator
from src.application.services.policy_data_adapter import PolicyDataAdapter
from src.application.services.analysis_orchestrator import AnalysisOrchestrator
from src.core.models.policy.welfare_impact_model import PolicyIntervention
from src.core.domain.shared.value_objects import Money
from src.infrastructure.persistence.policy_repository import PolicyResultsRepository
from src.infrastructure.messaging import InMemoryEventBus
from src.infrastructure.caching import MemoryCache


@pytest.fixture
def sample_panel_data():
    """Create sample panel data for testing."""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    markets = ['Sanaa', 'Aden', 'Taiz']
    
    data = []
    for date in dates:
        for market in markets:
            # Create realistic price variations
            base_prices = {
                'WHEAT': 500 + np.random.normal(0, 20),
                'RICE': 800 + np.random.normal(0, 30),
                'OIL': 1200 + np.random.normal(0, 40),
                'SUGAR': 600 + np.random.normal(0, 25)
            }
            
            # Add conflict effect (higher prices in conflict areas)
            if market == 'Taiz':
                conflict_multiplier = 1.1
            else:
                conflict_multiplier = 1.0
            
            for commodity, base_price in base_prices.items():
                data.append({
                    'date': date,
                    'market_id': market,
                    f'{commodity}_price': base_price * conflict_multiplier * (1 + 0.002 * (date - dates[0]).days)  # inflation
                })
    
    df = pd.DataFrame(data)
    df = df.pivot_table(
        index='date',
        columns=['market_id'],
        values=[col for col in df.columns if col.endswith('_price')],
        aggfunc='first'
    )
    
    # Flatten column names and add back market_id
    df.columns = ['_'.join(col).strip() if col[1] else col[0] for col in df.columns.values]
    df['market_id'] = 'all'  # Simplified for testing
    
    return df.reset_index()


@pytest.fixture
def mock_repositories():
    """Create mock repositories."""
    market_repo = Mock()
    price_repo = Mock()
    
    # Mock market data
    market_repo.find_all = AsyncMock(return_value=[
        Mock(id='Sanaa', name='Sanaa', governorate='Sanaa'),
        Mock(id='Aden', name='Aden', governorate='Aden'),
        Mock(id='Taiz', name='Taiz', governorate='Taiz')
    ])
    
    return market_repo, price_repo


@pytest.fixture
def policy_orchestrator(mock_repositories):
    """Create policy orchestrator with mocked dependencies."""
    market_repo, price_repo = mock_repositories
    
    # Create real services
    data_adapter = PolicyDataAdapter(market_repo, price_repo)
    
    # Create mock analysis orchestrator
    analysis_orchestrator = Mock(spec=AnalysisOrchestrator)
    analysis_orchestrator.start_analysis = AsyncMock(return_value="test-job-123")
    analysis_orchestrator.update_progress = AsyncMock()
    analysis_orchestrator.complete_analysis = AsyncMock()
    analysis_orchestrator.fail_analysis = AsyncMock()
    
    # Create event bus
    event_bus = InMemoryEventBus()
    
    # Create orchestrator
    orchestrator = PolicyOrchestrator(
        data_adapter=data_adapter,
        analysis_orchestrator=analysis_orchestrator,
        event_bus=event_bus
    )
    
    return orchestrator


@pytest.mark.asyncio
async def test_welfare_impact_analysis(policy_orchestrator, sample_panel_data):
    """Test welfare impact analysis with cash transfer intervention."""
    # Create cash transfer intervention
    intervention = PolicyIntervention(
        type='cash_transfer',
        target_markets=['Sanaa', 'Aden', 'Taiz'],
        target_commodities=['WHEAT', 'RICE'],
        magnitude=50000,  # 50,000 YER per household
        duration_months=6,
        targeting_criteria={'income_below': 200000},  # Target poor households
        budget_constraint=Money(10000000000, "YER")  # 10 billion YER
    )
    
    # Run analysis
    results = await policy_orchestrator.analyze_policy_intervention(
        intervention=intervention,
        panel_data=sample_panel_data
    )
    
    # Verify results structure
    assert 'welfare_impact' in results
    assert 'optimal_intervention' in results
    assert 'metadata' in results
    
    # Check welfare impact components
    impact = results['welfare_impact']
    assert 'consumer_surplus_change' in impact
    assert 'producer_surplus_change' in impact
    assert 'government_cost' in impact
    assert 'net_welfare_change' in impact
    assert 'distributional_effects' in impact
    assert 'beneficiary_count' in impact
    
    # Verify distributional effects by quintile
    dist_effects = impact['distributional_effects']
    assert 'Q1' in dist_effects
    assert 'Q5' in dist_effects
    assert dist_effects['Q1'] > dist_effects['Q5']  # Poor benefit more from cash transfers
    
    # Check that analysis was tracked
    assert policy_orchestrator.analysis_orchestrator.start_analysis.called
    assert policy_orchestrator.analysis_orchestrator.complete_analysis.called


@pytest.mark.asyncio
async def test_early_warning_generation(policy_orchestrator, sample_panel_data):
    """Test early warning system with price spike detection."""
    # Add price spike to data
    spike_data = sample_panel_data.copy()
    last_date = spike_data['date'].max()
    
    # Create artificial price spike in last 7 days
    spike_mask = spike_data['date'] >= (last_date - timedelta(days=7))
    for col in spike_data.columns:
        if 'WHEAT_price' in col:
            spike_data.loc[spike_mask, col] *= 1.5  # 50% increase
    
    # Generate warnings
    results = await policy_orchestrator.generate_early_warnings(
        panel_data=spike_data,
        forecast_horizon=30
    )
    
    # Verify results
    assert 'alerts' in results
    assert 'crisis_indicators' in results
    assert 'recommendations' in results
    
    # Should detect price spike
    alerts = results['alerts']
    price_spike_alerts = [a for a in alerts if a['alert_type'] == 'price_spike']
    assert len(price_spike_alerts) > 0
    
    # Check alert details
    spike_alert = price_spike_alerts[0]
    assert spike_alert['alert_level'] >= 2  # At least MEDIUM
    assert 'WHEAT' in spike_alert['affected_commodities']
    assert spike_alert['probability'] > 0.5
    
    # Check crisis indicators
    indicators = results['crisis_indicators']
    assert indicators['price_spike_probability'] > 0.5
    assert 'confidence_interval' in indicators


@pytest.mark.asyncio
async def test_policy_comparison(policy_orchestrator, sample_panel_data):
    """Test comparison of multiple policy interventions."""
    # Create different interventions
    interventions = [
        PolicyIntervention(
            type='cash_transfer',
            target_markets=['all'],
            target_commodities=['WHEAT', 'RICE'],
            magnitude=50000,
            duration_months=6,
            targeting_criteria={'quintiles': ['Q1', 'Q2']},
            budget_constraint=Money(5000000000, "YER")
        ),
        PolicyIntervention(
            type='price_subsidy',
            target_markets=['all'],
            target_commodities=['WHEAT'],
            magnitude=0.2,  # 20% subsidy
            duration_months=12,
            targeting_criteria={},
            budget_constraint=Money(5000000000, "YER")
        ),
        PolicyIntervention(
            type='in_kind',
            target_markets=['Taiz'],  # Conflict area
            target_commodities=['WHEAT', 'RICE', 'OIL'],
            magnitude=30,  # 30kg per household
            duration_months=3,
            targeting_criteria={'location': ['Taiz']},
            budget_constraint=Money(5000000000, "YER")
        )
    ]
    
    # Run comparison
    results = await policy_orchestrator.run_policy_comparison(
        interventions=interventions,
        panel_data=sample_panel_data
    )
    
    # Verify results
    assert 'interventions' in results
    assert len(results['interventions']) == 3
    assert 'best_welfare' in results
    assert 'most_cost_effective' in results
    
    # Check ranking
    for i, result in enumerate(results['interventions']):
        assert 'intervention' in result
        assert 'impact' in result
        assert 'cost_effectiveness' in result
        
        # Verify ordering (by net welfare change)
        if i > 0:
            assert result['impact']['net_welfare_change'] <= results['interventions'][i-1]['impact']['net_welfare_change']


@pytest.mark.asyncio
async def test_conflict_aware_analysis(policy_orchestrator, sample_panel_data):
    """Test that policy analysis accounts for conflict dynamics."""
    # Create conflict data
    conflict_data = pd.DataFrame({
        'date': pd.date_range(start='2023-01-01', end='2023-12-31', freq='D'),
        'event_date': pd.date_range(start='2023-01-01', end='2023-12-31', freq='D'),
        'location': ['Taiz'] * 365,
        'event_type': ['battles'] * 365,
        'fatalities': np.random.poisson(2, 365)
    })
    
    # Add conflict escalation
    conflict_data.loc[conflict_data.index[-30:], 'fatalities'] = np.random.poisson(10, 30)
    
    # Generate early warnings with conflict data
    results = await policy_orchestrator.generate_early_warnings(
        panel_data=sample_panel_data,
        conflict_data=conflict_data,
        forecast_horizon=30
    )
    
    # Should detect supply disruption risk
    supply_alerts = [a for a in results['alerts'] if a['alert_type'] == 'supply_shock']
    assert len(supply_alerts) > 0
    
    # Check that conflict is mentioned in evidence
    conflict_alert = supply_alerts[0]
    assert 'recent_conflict_rate' in conflict_alert['supporting_evidence']
    assert conflict_alert['supporting_evidence']['escalation_factor'] > 1.5


@pytest.mark.asyncio
async def test_monitoring_active_interventions(policy_orchestrator, sample_panel_data):
    """Test monitoring of ongoing policy interventions."""
    # Define active interventions
    active_interventions = [
        {
            'id': 'cash-001',
            'specification': {
                'type': 'cash_transfer',
                'target_markets': ['all'],
                'target_commodities': ['WHEAT', 'RICE'],
                'magnitude': 50000,
                'duration_months': 6,
                'targeting_criteria': {'quintiles': ['Q1', 'Q2']}
            },
            'start_date': '2023-06-01',
            'expected_impacts': {
                'WHEAT_price_change': -0.05,  # 5% reduction expected
                'RICE_price_change': -0.03,
                'market_integration_cv': 0.15
            }
        }
    ]
    
    # Run monitoring
    results = await policy_orchestrator.monitor_policy_impacts(
        active_interventions=active_interventions,
        panel_data=sample_panel_data[sample_panel_data['date'] >= '2023-06-01']
    )
    
    # Verify monitoring results
    assert 'monitoring_results' in results
    assert len(results['monitoring_results']) == 1
    
    monitoring = results['monitoring_results'][0]
    assert 'actual_metrics' in monitoring
    assert 'performance_ratio' in monitoring
    assert 'recommendations' in monitoring
    
    # Check that actual metrics were calculated
    actual = monitoring['actual_metrics']
    assert 'WHEAT_price_change' in actual
    assert 'RICE_price_change' in actual


@pytest.mark.asyncio
async def test_policy_repository_integration():
    """Test policy results repository functionality."""
    # Create repository with temp directory
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        repo = PolicyResultsRepository(storage_path=temp_dir)
        
        # Test saving welfare analysis
        welfare_results = {
            'welfare_impact': {
                'net_welfare_change': 1000000,
                'beneficiary_count': 50000,
                'distributional_effects': {'Q1': 10.5, 'Q2': 8.3, 'Q3': 5.1, 'Q4': 2.2, 'Q5': 0.5}
            },
            'metadata': {'markets': ['Sanaa', 'Aden']}
        }
        
        await repo.save_welfare_analysis('test-001', welfare_results)
        
        # Test retrieval
        retrieved = await repo.get_welfare_analysis('test-001')
        assert retrieved is not None
        assert retrieved['welfare_impact']['net_welfare_change'] == 1000000
        
        # Test early warning save/retrieve
        warning_results = {
            'alerts': [
                {
                    'alert_level': 3,
                    'alert_type': 'price_spike',
                    'affected_markets': ['all'],
                    'affected_commodities': ['WHEAT'],
                    'probability': 0.8,
                    'time_horizon': 7
                }
            ],
            'crisis_indicators': {
                'food_security_phase': 3
            }
        }
        
        await repo.save_early_warning('warn-001', warning_results)
        
        # Test getting recent warnings
        recent = await repo.get_recent_early_warnings(days=1, alert_level_min=3)
        assert len(recent) == 1
        assert recent[0]['alerts'][0]['alert_type'] == 'price_spike'
        
        # Test performance metrics
        metrics = await repo.get_performance_metrics(
            start_date=datetime.utcnow() - timedelta(days=1),
            end_date=datetime.utcnow()
        )
        
        assert metrics['welfare_analyses'] == 1
        assert metrics['early_warnings'] == 1
        assert metrics['total_beneficiaries'] == 50000


def test_exchange_rate_awareness():
    """Test that policy models handle exchange rate complexities."""
    # This test ensures our models are aware of the Yemen exchange rate issue
    from src.core.domain.shared.value_objects import Money
    
    # Create money values in different currencies
    yer_amount = Money(1000000, "YER")
    usd_amount = Money(2000, "USD")
    
    # In real implementation, we'd have exchange rate service
    # For now, just verify the abstraction exists
    assert yer_amount.currency == "YER"
    assert usd_amount.currency == "USD"
    assert yer_amount.amount != usd_amount.amount