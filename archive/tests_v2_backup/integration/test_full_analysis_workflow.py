"""Integration tests for full analysis workflow."""

import asyncio
import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch

from src.application.services.analysis_orchestrator import AnalysisOrchestrator
from src.application.services.data_preparation_service import DataPreparationService
from src.application.services.three_tier_analysis_service import ThreeTierAnalysisService
from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import MarketId, Commodity, Price, Coordinates, MarketType
from src.infrastructure.messaging.event_bus import EventBus
from src.infrastructure.persistence.unit_of_work import PostgresUnitOfWork


@pytest.fixture
async def integration_container():
    """Create a test container for integration testing."""
    from src.shared.container import Container
    
    container = Container()
    
    # Configure for integration testing
    container.config.database.url.from_value("postgresql://test:test@localhost:5432/test_db")
    container.config.cache.type.from_value("memory")
    container.config.events.type.from_value("inmemory")
    container.config.logging.level.from_value("DEBUG")
    
    # Wire dependencies
    container.wire(modules=["src.application", "src.infrastructure"])
    
    return container


@pytest.fixture
async def sample_markets():
    """Create sample markets for testing."""
    return [
        Market(
            market_id=MarketId("SANAA_CENTRAL"),
            name="Sanaa Central Market",
            coordinates=Coordinates(latitude=15.3694, longitude=44.1910),
            market_type=MarketType.WHOLESALE,
            governorate="Sana'a",
            district="Al Tahrir",
            active_since=datetime(2015, 1, 1)
        ),
        Market(
            market_id=MarketId("ADEN_MAIN"),
            name="Aden Main Market",
            coordinates=Coordinates(latitude=12.7797, longitude=45.0365),
            market_type=MarketType.RETAIL,
            governorate="Aden",
            district="Crater",
            active_since=datetime(2015, 1, 1)
        ),
        Market(
            market_id=MarketId("HODEIDAH_PORT"),
            name="Hodeidah Port Market",
            coordinates=Coordinates(latitude=14.7978, longitude=42.9545),
            market_type=MarketType.WHOLESALE,
            governorate="Hodeidah",
            district="Al Mina",
            active_since=datetime(2015, 1, 1)
        )
    ]


@pytest.fixture
async def sample_price_data(sample_markets):
    """Create sample price observations."""
    commodities = [
        Commodity(code="WHEAT", name="Wheat", category="cereals", standard_unit="kg"),
        Commodity(code="RICE", name="Rice", category="cereals", standard_unit="kg"),
        Commodity(code="OIL", name="Vegetable Oil", category="oils", standard_unit="liter")
    ]
    
    prices = []
    start_date = datetime(2020, 1, 1)
    
    for market in sample_markets:
        for commodity in commodities:
            for month in range(48):  # 4 years of monthly data
                observation_date = start_date + timedelta(days=30 * month)
                
                # Generate realistic price variations
                base_price = {"WHEAT": 400, "RICE": 800, "OIL": 1200}[commodity.code]
                market_multiplier = {"SANAA_CENTRAL": 1.0, "ADEN_MAIN": 1.1, "HODEIDAH_PORT": 0.95}[market.market_id.value]
                time_trend = 1 + (month * 0.02)  # 2% monthly inflation
                
                price_amount = base_price * market_multiplier * time_trend
                
                prices.append(PriceObservation(
                    market_id=market.market_id,
                    commodity=commodity,
                    price=Price(amount=price_amount, currency="YER", unit="kg"),
                    observed_date=observation_date,
                    source="WFP",
                    quality="standard",
                    observations_count=1
                ))
    
    return prices


class TestFullAnalysisWorkflow:
    """Integration tests for complete analysis workflow."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_analysis_execution(self, integration_container, sample_markets, sample_price_data):
        """Test complete end-to-end analysis execution."""
        # Arrange
        analysis_id = str(uuid4())
        
        with patch('src.infrastructure.persistence.repositories.postgres.market_repository.PostgresMarketRepository') as mock_market_repo, \
             patch('src.infrastructure.persistence.repositories.postgres.price_repository.PostgresPriceRepository') as mock_price_repo:
            
            # Setup repository mocks
            mock_market_repo.return_value.get_by_id.side_effect = lambda mid: next((m for m in sample_markets if m.market_id == mid), None)
            mock_market_repo.return_value.get_all.return_value = sample_markets
            mock_price_repo.return_value.get_price_series.return_value = sample_price_data
            
            # Get services from container
            orchestrator = integration_container.analysis_orchestrator()
            
            # Act
            result = await orchestrator.run_full_analysis(
                analysis_id=analysis_id,
                markets=[m.market_id.value for m in sample_markets],
                commodities=["WHEAT", "RICE", "OIL"],
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 12, 31)
            )
            
            # Assert
            assert result['analysis_id'] == analysis_id
            assert result['status'] == 'completed'
            assert 'tier1_results' in result
            assert 'tier2_results' in result
            assert 'tier3_results' in result
            assert 'metadata' in result
            
            # Verify result structure
            assert 'execution_time' in result['metadata']
            assert 'data_points' in result['metadata']
            assert result['metadata']['markets_count'] == 3
            assert result['metadata']['commodities_count'] == 3
    
    @pytest.mark.asyncio
    async def test_analysis_with_missing_data_handling(self, integration_container, sample_markets):
        """Test analysis workflow with missing data scenarios."""
        # Arrange - Create incomplete price data
        incomplete_prices = [
            PriceObservation(
                market_id=sample_markets[0].market_id,
                commodity=Commodity(code="WHEAT", name="Wheat", category="cereals", standard_unit="kg"),
                price=Price(amount=400.0, currency="YER", unit="kg"),
                observed_date=datetime(2020, 1, 1),
                source="WFP",
                quality="standard",
                observations_count=1
            )
            # Only one data point - insufficient for analysis
        ]
        
        analysis_id = str(uuid4())
        
        with patch('src.infrastructure.persistence.repositories.postgres.market_repository.PostgresMarketRepository') as mock_market_repo, \
             patch('src.infrastructure.persistence.repositories.postgres.price_repository.PostgresPriceRepository') as mock_price_repo:
            
            mock_market_repo.return_value.get_all.return_value = sample_markets
            mock_price_repo.return_value.get_price_series.return_value = incomplete_prices
            
            orchestrator = integration_container.analysis_orchestrator()
            
            # Act & Assert
            with pytest.raises(Exception):  # Should raise validation error for insufficient data
                await orchestrator.run_full_analysis(
                    analysis_id=analysis_id,
                    markets=[m.market_id.value for m in sample_markets],
                    commodities=["WHEAT"],
                    start_date=datetime(2020, 1, 1),
                    end_date=datetime(2023, 12, 31)
                )
    
    @pytest.mark.asyncio
    async def test_concurrent_analysis_execution(self, integration_container, sample_markets, sample_price_data):
        """Test multiple concurrent analysis executions."""
        # Arrange
        analysis_ids = [str(uuid4()) for _ in range(3)]
        
        with patch('src.infrastructure.persistence.repositories.postgres.market_repository.PostgresMarketRepository') as mock_market_repo, \
             patch('src.infrastructure.persistence.repositories.postgres.price_repository.PostgresPriceRepository') as mock_price_repo:
            
            mock_market_repo.return_value.get_all.return_value = sample_markets
            mock_price_repo.return_value.get_price_series.return_value = sample_price_data
            
            orchestrator = integration_container.analysis_orchestrator()
            
            # Act - Run multiple analyses concurrently
            tasks = []
            for i, analysis_id in enumerate(analysis_ids):
                task = orchestrator.run_full_analysis(
                    analysis_id=analysis_id,
                    markets=[sample_markets[i].market_id.value],  # Different market for each
                    commodities=["WHEAT"],
                    start_date=datetime(2020, 1, 1),
                    end_date=datetime(2023, 12, 31)
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            
            # Assert
            assert len(results) == 3
            for i, result in enumerate(results):
                assert result['analysis_id'] == analysis_ids[i]
                assert result['status'] == 'completed'
    
    @pytest.mark.asyncio
    async def test_analysis_cancellation_workflow(self, integration_container, sample_markets, sample_price_data):
        """Test analysis cancellation during execution."""
        # Arrange
        analysis_id = str(uuid4())
        
        with patch('src.infrastructure.persistence.repositories.postgres.market_repository.PostgresMarketRepository') as mock_market_repo, \
             patch('src.infrastructure.persistence.repositories.postgres.price_repository.PostgresPriceRepository') as mock_price_repo:
            
            mock_market_repo.return_value.get_all.return_value = sample_markets
            mock_price_repo.return_value.get_price_series.return_value = sample_price_data
            
            # Mock slow-running analysis
            async def slow_analysis(*args, **kwargs):
                await asyncio.sleep(2)  # Simulate long-running analysis
                return {'tier1_results': {}, 'tier2_results': {}, 'tier3_results': {}}
            
            orchestrator = integration_container.analysis_orchestrator()
            
            with patch.object(orchestrator, '_run_three_tier_analysis', side_effect=slow_analysis):
                # Act - Start analysis and cancel it
                analysis_task = asyncio.create_task(
                    orchestrator.run_full_analysis(
                        analysis_id=analysis_id,
                        markets=[m.market_id.value for m in sample_markets],
                        commodities=["WHEAT"],
                        start_date=datetime(2020, 1, 1),
                        end_date=datetime(2023, 12, 31)
                    )
                )
                
                # Wait a bit then cancel
                await asyncio.sleep(0.1)
                cancel_result = await orchestrator.cancel_analysis(analysis_id)
                
                # Assert
                assert cancel_result['analysis_id'] == analysis_id
                assert cancel_result['status'] == 'cancelled'
                
                # The original task should be cancelled
                with pytest.raises(asyncio.CancelledError):
                    await analysis_task
    
    @pytest.mark.asyncio
    async def test_analysis_status_tracking_integration(self, integration_container, sample_markets, sample_price_data):
        """Test analysis status tracking throughout execution."""
        # Arrange
        analysis_id = str(uuid4())
        status_updates = []
        
        # Mock event bus to capture status updates
        def capture_event(event):
            if hasattr(event, 'analysis_id') and event.analysis_id == analysis_id:
                status_updates.append(event.status)
        
        with patch('src.infrastructure.persistence.repositories.postgres.market_repository.PostgresMarketRepository') as mock_market_repo, \
             patch('src.infrastructure.persistence.repositories.postgres.price_repository.PostgresPriceRepository') as mock_price_repo:
            
            mock_market_repo.return_value.get_all.return_value = sample_markets
            mock_price_repo.return_value.get_price_series.return_value = sample_price_data
            
            # Patch event bus to capture events
            event_bus = integration_container.event_bus()
            original_publish = event_bus.publish
            event_bus.publish = lambda event: (capture_event(event), original_publish(event))[1]
            
            orchestrator = integration_container.analysis_orchestrator()
            
            # Act
            result = await orchestrator.run_full_analysis(
                analysis_id=analysis_id,
                markets=[m.market_id.value for m in sample_markets],
                commodities=["WHEAT"],
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 12, 31)
            )
            
            # Assert
            assert result['status'] == 'completed'
            assert 'started' in status_updates
            assert 'completed' in status_updates
            assert len(status_updates) >= 2  # At least started and completed
    
    @pytest.mark.asyncio
    async def test_analysis_error_handling_and_recovery(self, integration_container, sample_markets, sample_price_data):
        """Test error handling and recovery in analysis workflow."""
        # Arrange
        analysis_id = str(uuid4())
        
        with patch('src.infrastructure.persistence.repositories.postgres.market_repository.PostgresMarketRepository') as mock_market_repo, \
             patch('src.infrastructure.persistence.repositories.postgres.price_repository.PostgresPriceRepository') as mock_price_repo:
            
            mock_market_repo.return_value.get_all.return_value = sample_markets
            mock_price_repo.return_value.get_price_series.return_value = sample_price_data
            
            # Mock a failing component
            async def failing_preparation(*args, **kwargs):
                raise RuntimeError("Simulated preparation failure")
            
            orchestrator = integration_container.analysis_orchestrator()
            
            with patch.object(orchestrator, '_prepare_analysis_data', side_effect=failing_preparation):
                # Act & Assert
                with pytest.raises(RuntimeError):
                    await orchestrator.run_full_analysis(
                        analysis_id=analysis_id,
                        markets=[m.market_id.value for m in sample_markets],
                        commodities=["WHEAT"],
                        start_date=datetime(2020, 1, 1),
                        end_date=datetime(2023, 12, 31)
                    )
                
                # Verify error was logged and status updated
                status = await orchestrator.get_analysis_status(analysis_id)
                assert status['status'] == 'failed'
    
    @pytest.mark.asyncio
    async def test_analysis_performance_monitoring(self, integration_container, sample_markets, sample_price_data):
        """Test performance monitoring during analysis execution."""
        # Arrange
        analysis_id = str(uuid4())
        
        with patch('src.infrastructure.persistence.repositories.postgres.market_repository.PostgresMarketRepository') as mock_market_repo, \
             patch('src.infrastructure.persistence.repositories.postgres.price_repository.PostgresPriceRepository') as mock_price_repo:
            
            mock_market_repo.return_value.get_all.return_value = sample_markets
            mock_price_repo.return_value.get_price_series.return_value = sample_price_data
            
            orchestrator = integration_container.analysis_orchestrator()
            
            # Act
            start_time = datetime.now()
            result = await orchestrator.run_full_analysis(
                analysis_id=analysis_id,
                markets=[m.market_id.value for m in sample_markets],
                commodities=["WHEAT", "RICE"],
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 12, 31)
            )
            end_time = datetime.now()
            
            # Assert
            execution_time = (end_time - start_time).total_seconds()
            assert execution_time < 30  # Should complete within 30 seconds
            
            assert 'performance_metrics' in result['metadata']
            assert 'execution_time_seconds' in result['metadata']['performance_metrics']
            assert 'memory_usage_mb' in result['metadata']['performance_metrics']
            assert 'data_processing_time_seconds' in result['metadata']['performance_metrics']