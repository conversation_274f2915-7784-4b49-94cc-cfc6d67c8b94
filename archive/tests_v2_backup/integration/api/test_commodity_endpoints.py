"""Integration tests for commodity API endpoints."""

import pytest
from datetime import date
from httpx import AsyncClient
from fastapi import status

from v2.src.interfaces.api.rest.app import app


@pytest.mark.asyncio
class TestCommodityEndpoints:
    """Test suite for commodity endpoints."""
    
    async def test_list_commodities(self, client: AsyncClient):
        """Test listing commodities."""
        response = await client.get("/api/v1/commodities")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify response structure
        assert "data" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        assert "has_more" in data
        
        # Verify commodity data
        if data["data"]:
            commodity = data["data"][0]
            assert "id" in commodity
            assert "code" in commodity
            assert "name" in commodity
            assert "category" in commodity
            assert "unit" in commodity
    
    async def test_list_commodities_with_category_filter(self, client: AsyncClient):
        """Test listing commodities filtered by category."""
        response = await client.get(
            "/api/v1/commodities",
            params={"category": "FOOD"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # All commodities should be in FOOD category
        for commodity in data["data"]:
            assert commodity["category"] == "FOOD"
    
    async def test_list_commodities_with_search(self, client: AsyncClient):
        """Test searching commodities by name."""
        response = await client.get(
            "/api/v1/commodities",
            params={"search": "wheat"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # All results should contain "wheat" in name or code
        for commodity in data["data"]:
            assert "wheat" in commodity["name"].lower() or "wheat" in commodity["code"].lower()
    
    async def test_list_commodities_pagination(self, client: AsyncClient):
        """Test commodity pagination."""
        # First page
        response1 = await client.get(
            "/api/v1/commodities",
            params={"skip": 0, "limit": 5}
        )
        assert response1.status_code == status.HTTP_200_OK
        data1 = response1.json()
        
        # Second page
        response2 = await client.get(
            "/api/v1/commodities",
            params={"skip": 5, "limit": 5}
        )
        assert response2.status_code == status.HTTP_200_OK
        data2 = response2.json()
        
        # Verify pagination
        assert data1["skip"] == 0
        assert data2["skip"] == 5
        assert len(data1["data"]) <= 5
        assert len(data2["data"]) <= 5
    
    async def test_get_commodity_by_id(self, client: AsyncClient):
        """Test getting a specific commodity."""
        # First, get a commodity ID from the list
        list_response = await client.get("/api/v1/commodities?limit=1")
        commodities = list_response.json()["data"]
        
        if commodities:
            commodity_id = commodities[0]["id"]
            
            response = await client.get(f"/api/v1/commodities/{commodity_id}")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            # Verify commodity data
            assert data["id"] == commodity_id
            assert "code" in data
            assert "name" in data
            assert "category" in data
            assert "unit" in data
    
    async def test_get_commodity_not_found(self, client: AsyncClient):
        """Test getting non-existent commodity."""
        response = await client.get("/api/v1/commodities/non-existent-id")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "error" in data
    
    async def test_get_commodity_prices(self, client: AsyncClient):
        """Test getting price series for a commodity."""
        # First, get a commodity ID
        list_response = await client.get("/api/v1/commodities?limit=1")
        commodities = list_response.json()["data"]
        
        if commodities:
            commodity_id = commodities[0]["id"]
            
            response = await client.get(
                f"/api/v1/commodities/{commodity_id}/prices",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31",
                    "currency": "USD",
                    "aggregation": "monthly"
                }
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            # Verify response structure
            assert data["commodity_id"] == commodity_id
            assert "commodity_name" in data
            assert data["currency"] == "USD"
            assert data["aggregation"] == "monthly"
            assert "market_prices" in data
            assert "statistics" in data
            
            # Verify statistics
            stats = data["statistics"]
            assert "count" in stats
            assert "mean" in stats
            assert "std" in stats
            assert "cv" in stats
            assert "trend" in stats
            assert "markets_count" in stats
    
    async def test_get_commodity_prices_with_market_filter(self, client: AsyncClient):
        """Test getting commodity prices for specific market."""
        # Get commodity and market IDs
        list_response = await client.get("/api/v1/commodities?limit=1")
        commodities = list_response.json()["data"]
        
        if commodities:
            commodity_id = commodities[0]["id"]
            
            response = await client.get(
                f"/api/v1/commodities/{commodity_id}/prices",
                params={
                    "market_id": "SANAA_CENTRAL",
                    "currency": "YER"
                }
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            # Should only have prices for the specified market
            assert len(data["market_prices"]) <= 1
            if "SANAA_CENTRAL" in data["market_prices"]:
                assert all(
                    price["market_id"] == "SANAA_CENTRAL" 
                    for price in data["market_prices"]["SANAA_CENTRAL"]
                )
    
    async def test_get_commodity_prices_invalid_aggregation(self, client: AsyncClient):
        """Test getting prices with invalid aggregation."""
        response = await client.get(
            "/api/v1/commodities/wheat_001/prices",
            params={"aggregation": "yearly"}  # Invalid aggregation
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.fixture
async def client():
    """Create test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac