"""Integration tests for analysis API endpoints."""

import pytest
from datetime import date, datetime
from httpx import AsyncClient
from fastapi import status

from v2.src.interfaces.api.rest.app import app
from v2.src.interfaces.api.rest.schemas.analysis import AnalysisStatus


@pytest.mark.asyncio
class TestAnalysisEndpoints:
    """Test suite for analysis endpoints."""
    
    async def test_create_three_tier_analysis(self, client: AsyncClient):
        """Test creating a new three-tier analysis."""
        request_data = {
            "name": "Test Analysis",
            "description": "Integration test for three-tier analysis",
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "commodities": ["Wheat", "Rice"],
            "markets": ["SANAA_CENTRAL", "ADEN_MAIN"],
            "confidence_level": 0.95,
            "include_diagnostics": True
        }
        
        response = await client.post("/api/v1/analysis/three-tier", json=request_data)
        
        assert response.status_code == status.HTTP_202_ACCEPTED
        data = response.json()
        
        # Verify response structure
        assert "id" in data
        assert data["status"] == "pending"
        assert data["message"] == "Analysis queued for processing"
        assert data["estimated_duration_seconds"] == 300
        assert "created_at" in data
        
        # Store ID for subsequent tests
        return data["id"]
    
    async def test_create_analysis_validation_error(self, client: AsyncClient):
        """Test analysis creation with invalid data."""
        request_data = {
            "name": "Test Analysis",
            "start_date": "2023-12-31",
            "end_date": "2023-01-01",  # End before start
            "commodities": ["Wheat"]
        }
        
        response = await client.post("/api/v1/analysis/three-tier", json=request_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "error" in data
        assert data["error"]["type"] == "ValidationError"
    
    async def test_get_analysis_status(self, client: AsyncClient):
        """Test getting analysis status."""
        # First create an analysis
        create_response = await client.post(
            "/api/v1/analysis/three-tier",
            json={
                "name": "Status Test",
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "commodities": ["Wheat"]
            }
        )
        analysis_id = create_response.json()["id"]
        
        # Get status
        response = await client.get(f"/api/v1/analysis/{analysis_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["analysis_id"] == analysis_id
        assert data["status"] in ["pending", "running", "completed", "failed"]
        assert "progress" in data
        assert "started_at" in data
    
    async def test_get_analysis_not_found(self, client: AsyncClient):
        """Test getting non-existent analysis."""
        response = await client.get("/api/v1/analysis/non-existent-id")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "error" in data
        assert "not found" in data["error"]["message"].lower()
    
    async def test_get_analysis_results(self, client: AsyncClient):
        """Test getting analysis results."""
        # This would need a completed analysis in a real test
        # For now, test the error case
        response = await client.get("/api/v1/analysis/test-id/results")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    async def test_delete_analysis(self, client: AsyncClient):
        """Test deleting an analysis."""
        # First create an analysis
        create_response = await client.post(
            "/api/v1/analysis/three-tier",
            json={
                "name": "Delete Test",
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "commodities": ["Wheat"]
            }
        )
        analysis_id = create_response.json()["id"]
        
        # Delete it
        response = await client.delete(f"/api/v1/analysis/{analysis_id}")
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Verify it's gone
        get_response = await client.get(f"/api/v1/analysis/{analysis_id}")
        assert get_response.status_code == status.HTTP_404_NOT_FOUND
    
    async def test_analysis_sse_endpoint(self, client: AsyncClient):
        """Test Server-Sent Events endpoint for analysis status."""
        # This is harder to test with httpx, but we can at least verify the endpoint exists
        response = await client.get(
            "/api/v1/analysis/analyses/test-id/status",
            headers={"Accept": "text/event-stream"}
        )
        
        # Should return 404 for non-existent analysis
        assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.fixture
async def client():
    """Create test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac