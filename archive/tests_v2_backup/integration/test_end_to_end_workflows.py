"""End-to-end tests for complete workflows."""

import asyncio
from datetime import datetime, timed<PERSON>ta
from decimal import Dec<PERSON><PERSON>
from typing import Dict, List

import pytest
from httpx import AsyncClient

from src.core.domain.market.value_objects import MarketId, Commodity


class TestCompleteDataIngestionWorkflow:
    """Test complete data ingestion workflow from upload to analysis."""
    
    @pytest.mark.asyncio
    async def test_wfp_data_ingestion_workflow(self, test_client: AsyncClient,
                                              auth_headers: Dict,
                                              sample_wfp_file):
        """Test complete WFP data ingestion workflow."""
        # Step 1: Upload WFP data file
        with open(sample_wfp_file, "rb") as f:
            response = await test_client.post(
                "/api/v1/data/upload/wfp",
                files={"file": ("wfp_prices.csv", f, "text/csv")},
                headers=auth_headers
            )
        
        assert response.status_code == 202
        upload_data = response.json()
        job_id = upload_data["job_id"]
        
        # Step 2: Wait for processing to complete
        processing_complete = False
        for _ in range(30):  # Max 30 seconds
            response = await test_client.get(
                f"/api/v1/data/jobs/{job_id}",
                headers=auth_headers
            )
            
            job_status = response.json()
            if job_status["status"] == "completed":
                processing_complete = True
                break
            elif job_status["status"] == "failed":
                pytest.fail(f"Data processing failed: {job_status.get('error')}")
            
            await asyncio.sleep(1)
        
        assert processing_complete, "Data processing did not complete in time"
        
        # Step 3: Verify data was ingested
        response = await test_client.get(
            "/api/v1/prices",
            params={
                "source": "WFP",
                "limit": 10
            }
        )
        
        assert response.status_code == 200
        prices = response.json()["items"]
        assert len(prices) > 0
        assert all(p["source"] == "WFP" for p in prices)
        
        # Step 4: Run price transmission analysis on ingested data
        markets = list(set(p["market_id"] for p in prices))[:2]
        commodities = list(set(p["commodity_code"] for p in prices))[:1]
        
        if len(markets) >= 2 and commodities:
            response = await test_client.post(
                "/api/v1/analysis/price-transmission",
                json={
                    "source_market": markets[0],
                    "target_market": markets[1],
                    "commodity_code": commodities[0],
                    "start_date": job_status["metadata"]["start_date"],
                    "end_date": job_status["metadata"]["end_date"]
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            analysis = response.json()
            assert "correlation" in analysis
            assert "beta_coefficient" in analysis


class TestMarketMonitoringWorkflow:
    """Test market monitoring and alerting workflow."""
    
    @pytest.mark.asyncio
    async def test_price_spike_detection_workflow(self, test_client: AsyncClient,
                                                 auth_headers: Dict,
                                                 setup_price_history):
        """Test workflow for detecting and alerting on price spikes."""
        market_id = "SANAA_CENTRAL"
        commodity_code = "WHEAT_FLOUR"
        
        # Step 1: Set up monitoring rule
        response = await test_client.post(
            "/api/v1/monitoring/rules",
            json={
                "name": "Wheat price spike detection",
                "rule_type": "price_spike",
                "parameters": {
                    "market_id": market_id,
                    "commodity_code": commodity_code,
                    "threshold_percent": 20.0,
                    "time_window_days": 7
                },
                "notifications": {
                    "email": ["<EMAIL>"],
                    "webhook": "https://example.com/webhooks/price-alerts"
                }
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        rule_id = response.json()["rule_id"]
        
        # Step 2: Submit normal price
        normal_price = 100.0
        response = await test_client.post(
            "/api/v1/prices",
            json={
                "market_id": market_id,
                "commodity_code": commodity_code,
                "price": {
                    "amount": str(normal_price),
                    "currency": "YER",
                    "unit": "kg"
                },
                "observed_date": datetime.utcnow().date().isoformat()
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        
        # Step 3: Submit spike price
        spike_price = normal_price * 1.25  # 25% increase
        response = await test_client.post(
            "/api/v1/prices",
            json={
                "market_id": market_id,
                "commodity_code": commodity_code,
                "price": {
                    "amount": str(spike_price),
                    "currency": "YER",
                    "unit": "kg"
                },
                "observed_date": (datetime.utcnow() + timedelta(days=1)).date().isoformat()
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        
        # Step 4: Check if alert was triggered
        response = await test_client.get(
            f"/api/v1/monitoring/alerts",
            params={
                "rule_id": rule_id,
                "status": "triggered"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        alerts = response.json()["items"]
        assert len(alerts) > 0
        
        alert = alerts[0]
        assert alert["rule_id"] == rule_id
        assert alert["severity"] == "high"
        assert "price increased by" in alert["message"].lower()
        
        # Step 5: Acknowledge alert
        response = await test_client.put(
            f"/api/v1/monitoring/alerts/{alert['alert_id']}/acknowledge",
            json={"notes": "Investigating price spike"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assert response.json()["status"] == "acknowledged"


class TestReportGenerationWorkflow:
    """Test report generation workflow."""
    
    @pytest.mark.asyncio
    async def test_monthly_report_generation(self, test_client: AsyncClient,
                                           auth_headers: Dict,
                                           setup_monthly_data):
        """Test generating a monthly market report."""
        # Step 1: Create report template
        response = await test_client.post(
            "/api/v1/reports/templates",
            json={
                "name": "Monthly Market Report",
                "template_type": "monthly_analysis",
                "parameters": {
                    "include_sections": [
                        "executive_summary",
                        "price_trends",
                        "market_integration",
                        "conflict_impacts",
                        "recommendations"
                    ],
                    "governorates": ["Sana'a", "Aden", "Taiz"],
                    "commodities": ["WHEAT_FLOUR", "RICE_IMPORTED", "FUEL_DIESEL"]
                }
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        template_id = response.json()["template_id"]
        
        # Step 2: Generate report
        response = await test_client.post(
            "/api/v1/reports/generate",
            json={
                "template_id": template_id,
                "period": {
                    "year": 2023,
                    "month": 6
                },
                "format": "pdf",
                "language": "en"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 202
        report_job_id = response.json()["job_id"]
        
        # Step 3: Wait for report generation
        report_ready = False
        report_url = None
        
        for _ in range(60):  # Max 60 seconds
            response = await test_client.get(
                f"/api/v1/reports/jobs/{report_job_id}",
                headers=auth_headers
            )
            
            job_status = response.json()
            if job_status["status"] == "completed":
                report_ready = True
                report_url = job_status["result"]["download_url"]
                break
            elif job_status["status"] == "failed":
                pytest.fail(f"Report generation failed: {job_status.get('error')}")
            
            await asyncio.sleep(1)
        
        assert report_ready, "Report generation did not complete in time"
        assert report_url is not None
        
        # Step 4: Download report
        response = await test_client.get(report_url, headers=auth_headers)
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/pdf"
        assert len(response.content) > 1000  # Non-empty PDF
        
        # Step 5: Schedule recurring report
        response = await test_client.post(
            "/api/v1/reports/schedules",
            json={
                "template_id": template_id,
                "schedule": {
                    "frequency": "monthly",
                    "day_of_month": 5,
                    "time": "09:00",
                    "timezone": "Asia/Aden"
                },
                "recipients": [
                    {"email": "<EMAIL>"},
                    {"webhook": "https://example.com/webhooks/reports"}
                ]
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        assert "schedule_id" in response.json()


class TestDataQualityWorkflow:
    """Test data quality monitoring and improvement workflow."""
    
    @pytest.mark.asyncio
    async def test_data_quality_improvement_workflow(self, test_client: AsyncClient,
                                                   auth_headers: Dict):
        """Test workflow for monitoring and improving data quality."""
        # Step 1: Run data quality assessment
        response = await test_client.post(
            "/api/v1/data-quality/assess",
            json={
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "checks": [
                    "completeness",
                    "consistency",
                    "timeliness",
                    "accuracy"
                ]
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assessment = response.json()
        
        assert "overall_score" in assessment
        assert "issues" in assessment
        assert "recommendations" in assessment
        
        # Step 2: Identify specific issues
        issues = assessment["issues"]
        missing_data_issue = next(
            (i for i in issues if i["type"] == "missing_data"),
            None
        )
        
        if missing_data_issue:
            # Step 3: Create data collection task
            response = await test_client.post(
                "/api/v1/tasks",
                json={
                    "task_type": "data_collection",
                    "priority": "high",
                    "description": f"Collect missing price data for {missing_data_issue['details']}",
                    "assigned_to": "field_team",
                    "due_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
                    "metadata": {
                        "markets": missing_data_issue["affected_markets"],
                        "commodities": missing_data_issue["affected_commodities"],
                        "date_range": missing_data_issue["date_range"]
                    }
                },
                headers=auth_headers
            )
            
            assert response.status_code == 201
            task_id = response.json()["task_id"]
            
            # Step 4: Simulate task completion with data submission
            await asyncio.sleep(1)  # Simulate time passing
            
            response = await test_client.put(
                f"/api/v1/tasks/{task_id}/complete",
                json={
                    "completed_by": "field_agent_1",
                    "notes": "Data collected from market visits",
                    "attachments": ["price_survey_results.xlsx"]
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            
            # Step 5: Re-run quality assessment
            response = await test_client.post(
                "/api/v1/data-quality/assess",
                json={
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31",
                    "checks": ["completeness"]
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            new_assessment = response.json()
            
            # Quality score should improve
            assert new_assessment["overall_score"] >= assessment["overall_score"]


class TestMachineLearningWorkflow:
    """Test machine learning model training and prediction workflow."""
    
    @pytest.mark.asyncio
    async def test_price_prediction_workflow(self, test_client: AsyncClient,
                                           auth_headers: Dict,
                                           setup_training_data):
        """Test workflow for training and using price prediction models."""
        # Step 1: Create ML experiment
        response = await test_client.post(
            "/api/v1/ml/experiments",
            json={
                "name": "Wheat price prediction - June 2023",
                "description": "Predict wheat prices using historical data and conflict events",
                "model_type": "price_prediction",
                "parameters": {
                    "commodity": "WHEAT_FLOUR",
                    "features": [
                        "historical_prices",
                        "seasonal_factors",
                        "conflict_intensity",
                        "fuel_prices",
                        "exchange_rates"
                    ],
                    "target_horizon_days": 30
                }
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        experiment_id = response.json()["experiment_id"]
        
        # Step 2: Train model
        response = await test_client.post(
            f"/api/v1/ml/experiments/{experiment_id}/train",
            json={
                "training_data": {
                    "start_date": "2020-01-01",
                    "end_date": "2023-05-31"
                },
                "validation_split": 0.2,
                "hyperparameters": {
                    "model_type": "gradient_boosting",
                    "n_estimators": 100,
                    "learning_rate": 0.1,
                    "max_depth": 5
                }
            },
            headers=auth_headers
        )
        
        assert response.status_code == 202
        training_job_id = response.json()["job_id"]
        
        # Step 3: Wait for training to complete
        model_id = None
        for _ in range(120):  # Max 2 minutes
            response = await test_client.get(
                f"/api/v1/ml/jobs/{training_job_id}",
                headers=auth_headers
            )
            
            job_status = response.json()
            if job_status["status"] == "completed":
                model_id = job_status["result"]["model_id"]
                break
            elif job_status["status"] == "failed":
                pytest.fail(f"Model training failed: {job_status.get('error')}")
            
            await asyncio.sleep(1)
        
        assert model_id is not None, "Model training did not complete in time"
        
        # Step 4: Evaluate model
        response = await test_client.get(
            f"/api/v1/ml/models/{model_id}/metrics",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        metrics = response.json()
        assert "mae" in metrics  # Mean Absolute Error
        assert "rmse" in metrics  # Root Mean Square Error
        assert "mape" in metrics  # Mean Absolute Percentage Error
        assert metrics["mape"] < 20.0  # Less than 20% error
        
        # Step 5: Make predictions
        response = await test_client.post(
            f"/api/v1/ml/models/{model_id}/predict",
            json={
                "markets": ["SANAA_CENTRAL", "ADEN_PORT"],
                "start_date": "2023-06-01",
                "end_date": "2023-06-30",
                "include_confidence_intervals": True
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        predictions = response.json()["predictions"]
        
        assert len(predictions) > 0
        for pred in predictions:
            assert "market_id" in pred
            assert "date" in pred
            assert "predicted_price" in pred
            assert "lower_bound" in pred
            assert "upper_bound" in pred
            
            # Sanity checks
            assert pred["lower_bound"] < pred["predicted_price"] < pred["upper_bound"]
            assert pred["predicted_price"] > 0
        
        # Step 6: Deploy model for production use
        response = await test_client.post(
            f"/api/v1/ml/models/{model_id}/deploy",
            json={
                "deployment_name": "wheat-price-predictor-v1",
                "auto_retrain": True,
                "retrain_frequency_days": 30,
                "monitoring": {
                    "track_accuracy": True,
                    "alert_on_drift": True,
                    "drift_threshold": 0.15
                }
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        deployment = response.json()
        assert deployment["status"] == "deployed"
        assert "endpoint" in deployment