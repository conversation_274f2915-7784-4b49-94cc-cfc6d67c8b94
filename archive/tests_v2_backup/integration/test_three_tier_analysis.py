"""Integration tests for three-tier analysis."""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio

from src.application.commands.run_three_tier_analysis import (
    RunThreeTierAnalysisCommand, RunThreeTierAnalysisHandler
)
from src.application.services.analysis_orchestrator import AnalysisOrchestrator
from src.shared.container import Container


@pytest.fixture
def sample_market_data():
    """Create comprehensive sample market data."""
    np.random.seed(42)
    
    # Configuration
    n_markets = 5
    n_commodities = 3
    n_days = 365
    start_date = datetime(2023, 1, 1)
    
    # Generate data
    data = []
    for day in range(n_days):
        date = start_date + timedelta(days=day)
        
        for market_id in range(n_markets):
            market_name = f"market_{market_id}"
            
            for commodity_id in range(n_commodities):
                commodity_name = f"commodity_{commodity_id}"
                
                # Base price with trend and seasonality
                base_price = 100 + commodity_id * 20
                trend = day * 0.1
                seasonality = 10 * np.sin(2 * np.pi * day / 365)
                market_effect = market_id * 5
                
                # Add conflict effect
                conflict = 1 if np.random.random() < 0.1 else 0
                conflict_effect = conflict * np.random.uniform(5, 15)
                
                # Final price
                price = (base_price + trend + seasonality + 
                        market_effect + conflict_effect + 
                        np.random.normal(0, 5))
                
                data.append({
                    'date': date,
                    'market_id': market_name,
                    'commodity': commodity_name,
                    'price': price,
                    'quantity': 100 + np.random.normal(0, 10),
                    'conflict_events': conflict,
                    'rainfall': 50 + np.random.normal(0, 20),
                })
    
    return pd.DataFrame(data)


@pytest.fixture
def container():
    """Create test container."""
    container = Container()
    # Configure test database
    container.config.database.url.from_value("sqlite:///:memory:")
    container.config.cache.type.from_value("memory")
    return container


@pytest.mark.asyncio
class TestThreeTierIntegration:
    """Test three-tier analysis integration."""
    
    async def test_full_analysis_workflow(self, container, sample_market_data):
        """Test complete three-tier analysis workflow."""
        # Create command
        command = RunThreeTierAnalysisCommand(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            market_ids=['market_0', 'market_1', 'market_2'],
            commodity_ids=['commodity_0', 'commodity_1'],
            tier1_config={
                'model_type': 'fixed_effects',
                'entity_effects': True,
                'time_effects': True,
                'clustered_errors': 'market_id'
            },
            tier2_config={
                'max_lags': 4,
                'deterministic': 'ci',
                'test_thresholds': True
            },
            tier3_config={
                'n_factors': 3,
                'conflict_lags': 2,
                'bootstrap_iterations': 100
            },
            user_id='test_user'
        )
        
        # Mock data preparation service to return our sample data
        prep_service = container.data_preparation_service()
        prep_service.prepare_panel_data = lambda *args, **kwargs: sample_market_data
        
        # Create handler
        handler = RunThreeTierAnalysisHandler(
            unit_of_work=container.unit_of_work(),
            event_bus=container.event_bus(),
            data_preparation_service=prep_service,
            model_estimator_service=container.model_estimator_service(),
            analysis_repository=container.analysis_repository()
        )
        
        # Run analysis
        result = await handler.handle(command)
        
        assert result.analysis_id is not None
        assert result.status == "started"
        assert result.message == "Three-tier analysis started successfully"
    
    async def test_tier1_results_structure(self, container, sample_market_data):
        """Test tier 1 results structure."""
        orchestrator = AnalysisOrchestrator(
            model_estimator_service=container.model_estimator_service(),
            event_bus=container.event_bus()
        )
        
        # Run only tier 1
        tier1_config = {
            'model_type': 'pooled',
            'clustered_errors': 'market_id'
        }
        
        results = await orchestrator.run_tier1_analysis(
            panel_data=sample_market_data,
            config=tier1_config
        )
        
        assert 'pooled_model' in results
        assert 'coefficients' in results['pooled_model']
        assert 'diagnostics' in results['pooled_model']
        assert 'r_squared' in results['pooled_model']
    
    async def test_tier2_commodity_analysis(self, container, sample_market_data):
        """Test tier 2 commodity-specific analysis."""
        orchestrator = AnalysisOrchestrator(
            model_estimator_service=container.model_estimator_service(),
            event_bus=container.event_bus()
        )
        
        # Filter for one commodity
        commodity_data = sample_market_data[
            sample_market_data['commodity'] == 'commodity_0'
        ]
        
        # Pivot for VECM format
        price_matrix = commodity_data.pivot(
            index='date',
            columns='market_id',
            values='price'
        )
        
        results = await orchestrator.run_tier2_analysis(
            commodity_data={'commodity_0': price_matrix},
            config={'max_lags': 2, 'test_thresholds': False}
        )
        
        assert 'commodity_0' in results
        commodity_results = results['commodity_0']
        assert 'vecm' in commodity_results
        assert 'cointegration_rank' in commodity_results
        assert 'diagnostics' in commodity_results
    
    async def test_tier3_validation(self, container, sample_market_data):
        """Test tier 3 validation analysis."""
        orchestrator = AnalysisOrchestrator(
            model_estimator_service=container.model_estimator_service(),
            event_bus=container.event_bus()
        )
        
        # Mock tier 1 residuals
        residuals = pd.DataFrame(
            np.random.normal(0, 1, (100, 5)),
            columns=[f'market_{i}' for i in range(5)]
        )
        
        # Add conflict data
        conflict_data = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=100),
            'total_events': np.random.poisson(2, 100),
            'total_fatalities': np.random.poisson(5, 100)
        })
        
        results = await orchestrator.run_tier3_validation(
            residuals=residuals,
            conflict_data=conflict_data,
            original_data=sample_market_data,
            config={'n_factors': 2, 'conflict_lags': 1}
        )
        
        assert 'factor_analysis' in results
        assert 'pca_results' in results
        assert 'conflict_validation' in results
        assert 'cross_validation' in results
    
    async def test_error_handling(self, container):
        """Test error handling in analysis."""
        command = RunThreeTierAnalysisCommand(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2022, 1, 1),  # End before start
            market_ids=['market_0'],
            commodity_ids=['commodity_0'],
            tier1_config={},
            tier2_config={},
            tier3_config={},
            user_id='test_user'
        )
        
        handler = RunThreeTierAnalysisHandler(
            unit_of_work=container.unit_of_work(),
            event_bus=container.event_bus(),
            data_preparation_service=container.data_preparation_service(),
            model_estimator_service=container.model_estimator_service(),
            analysis_repository=container.analysis_repository()
        )
        
        with pytest.raises(ValueError, match="End date must be after start date"):
            await handler.handle(command)
    
    async def test_concurrent_analyses(self, container, sample_market_data):
        """Test running multiple analyses concurrently."""
        prep_service = container.data_preparation_service()
        prep_service.prepare_panel_data = lambda *args, **kwargs: sample_market_data
        
        handler = RunThreeTierAnalysisHandler(
            unit_of_work=container.unit_of_work(),
            event_bus=container.event_bus(),
            data_preparation_service=prep_service,
            model_estimator_service=container.model_estimator_service(),
            analysis_repository=container.analysis_repository()
        )
        
        # Create multiple commands
        commands = []
        for i in range(3):
            command = RunThreeTierAnalysisCommand(
                start_date=datetime(2023, 1, 1),
                end_date=datetime(2023, 12, 31),
                market_ids=[f'market_{i}'],
                commodity_ids=['commodity_0'],
                tier1_config={},
                tier2_config={},
                tier3_config={},
                user_id=f'user_{i}'
            )
            commands.append(command)
        
        # Run concurrently
        tasks = [handler.handle(cmd) for cmd in commands]
        results = await asyncio.gather(*tasks)
        
        # All should succeed
        assert len(results) == 3
        assert all(r.status == "started" for r in results)
        assert len(set(r.analysis_id for r in results)) == 3  # Unique IDs