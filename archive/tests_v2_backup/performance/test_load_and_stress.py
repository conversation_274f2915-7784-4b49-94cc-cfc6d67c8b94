"""Performance tests for load and stress testing."""

import asyncio
import pytest
import time
import psutil
import statistics
from datetime import datetime, timedelta
from typing import List, Dict, Any
from unittest.mock import patch, AsyncMock
from concurrent.futures import ThreadPoolExecutor
import httpx

from src.application.services.analysis_orchestrator import AnalysisOrchestrator
from src.application.services.data_preparation_service import DataPreparationService
from src.shared.container import Container


class PerformanceMetrics:
    """Helper class to collect and analyze performance metrics."""
    
    def __init__(self):
        self.response_times = []
        self.memory_usage = []
        self.cpu_usage = []
        self.error_count = 0
        self.success_count = 0
        self.start_time = None
        self.end_time = None
    
    def start_monitoring(self):
        """Start performance monitoring."""
        self.start_time = time.time()
        self.response_times = []
        self.memory_usage = []
        self.cpu_usage = []
        self.error_count = 0
        self.success_count = 0
    
    def record_request(self, response_time: float, success: bool = True):
        """Record a request's performance metrics."""
        self.response_times.append(response_time)
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
        
        # Record system metrics
        self.memory_usage.append(psutil.virtual_memory().percent)
        self.cpu_usage.append(psutil.cpu_percent())
    
    def stop_monitoring(self):
        """Stop monitoring and calculate final metrics."""
        self.end_time = time.time()
    
    def get_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        if not self.response_times:
            return {"error": "No data collected"}
        
        total_time = self.end_time - self.start_time if self.end_time else 0
        
        return {
            "total_requests": len(self.response_times),
            "successful_requests": self.success_count,
            "failed_requests": self.error_count,
            "success_rate": (self.success_count / len(self.response_times)) * 100,
            "total_duration_seconds": total_time,
            "requests_per_second": len(self.response_times) / total_time if total_time > 0 else 0,
            "response_times": {
                "min": min(self.response_times),
                "max": max(self.response_times),
                "mean": statistics.mean(self.response_times),
                "median": statistics.median(self.response_times),
                "p95": statistics.quantiles(self.response_times, n=20)[18] if len(self.response_times) > 20 else max(self.response_times),
                "p99": statistics.quantiles(self.response_times, n=100)[98] if len(self.response_times) > 100 else max(self.response_times)
            },
            "system_metrics": {
                "avg_memory_usage": statistics.mean(self.memory_usage) if self.memory_usage else 0,
                "max_memory_usage": max(self.memory_usage) if self.memory_usage else 0,
                "avg_cpu_usage": statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
                "max_cpu_usage": max(self.cpu_usage) if self.cpu_usage else 0
            }
        }


@pytest.fixture
def performance_container():
    """Create container optimized for performance testing."""
    container = Container()
    
    # Configure for performance testing
    container.config.database.url.from_value("postgresql://test:test@localhost:5432/perf_test_db")
    container.config.cache.type.from_value("redis")
    container.config.cache.redis_url.from_value("redis://localhost:6379/1")
    container.config.events.type.from_value("redis")
    container.config.logging.level.from_value("WARNING")  # Reduce logging overhead
    
    return container


@pytest.fixture
def sample_large_dataset():
    """Create a large sample dataset for performance testing."""
    markets = []
    prices = []
    
    # Generate 100 markets
    for i in range(100):
        market_id = f"MARKET_{i:03d}"
        markets.append({
            "market_id": market_id,
            "name": f"Market {i}",
            "latitude": 15.0 + (i % 10) * 0.1,
            "longitude": 44.0 + (i // 10) * 0.1,
            "governorate": f"Governorate_{i % 10}",
            "district": f"District_{i}"
        })
    
    # Generate 3 years of daily price data for 10 commodities across all markets
    commodities = ["WHEAT", "RICE", "OIL", "SUGAR", "FUEL_PETROL", "FUEL_DIESEL", "BEANS", "LENTILS", "MEAT", "MILK"]
    start_date = datetime(2021, 1, 1)
    
    for market in markets:
        for commodity in commodities:
            for day in range(1095):  # 3 years of daily data
                price_date = start_date + timedelta(days=day)
                base_price = {"WHEAT": 400, "RICE": 800, "OIL": 1200}.get(commodity, 500)
                # Add some realistic variation
                price_variation = 1 + (day % 30 - 15) * 0.01  # Monthly cycle
                market_factor = 1 + (int(market["market_id"].split("_")[1]) % 10) * 0.05  # Market-specific factor
                
                prices.append({
                    "market_id": market["market_id"],
                    "commodity": commodity,
                    "price": base_price * price_variation * market_factor,
                    "currency": "YER",
                    "unit": "kg",
                    "date": price_date.isoformat(),
                    "source": "WFP"
                })
    
    return {"markets": markets, "prices": prices}


class TestLoadTesting:
    """Load testing scenarios."""
    
    @pytest.mark.asyncio
    async def test_concurrent_analysis_requests(self, performance_container, sample_large_dataset):
        """Test system performance under concurrent analysis requests."""
        metrics = PerformanceMetrics()
        
        # Mock data sources to return large dataset
        with patch('src.infrastructure.persistence.repositories.postgres.market_repository.PostgresMarketRepository') as mock_market_repo, \
             patch('src.infrastructure.persistence.repositories.postgres.price_repository.PostgresPriceRepository') as mock_price_repo:
            
            mock_market_repo.return_value.get_all.return_value = sample_large_dataset["markets"]
            mock_price_repo.return_value.get_price_series.return_value = sample_large_dataset["prices"]
            
            orchestrator = performance_container.analysis_orchestrator()
            
            async def run_analysis(analysis_id: str):
                """Run a single analysis and measure performance."""
                start_time = time.time()
                try:
                    result = await orchestrator.run_full_analysis(
                        analysis_id=analysis_id,
                        markets=["MARKET_001", "MARKET_002", "MARKET_003"],
                        commodities=["WHEAT", "RICE"],
                        start_date=datetime(2021, 1, 1),
                        end_date=datetime(2023, 12, 31)
                    )
                    response_time = time.time() - start_time
                    metrics.record_request(response_time, success=True)
                    return result
                except Exception as e:
                    response_time = time.time() - start_time
                    metrics.record_request(response_time, success=False)
                    raise e
            
            # Start monitoring
            metrics.start_monitoring()
            
            # Run 20 concurrent analyses
            tasks = []
            for i in range(20):
                analysis_id = f"load_test_analysis_{i}"
                task = asyncio.create_task(run_analysis(analysis_id))
                tasks.append(task)
            
            # Wait for all analyses to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Stop monitoring
            metrics.stop_monitoring()
            
            # Analyze results
            summary = metrics.get_summary()
            
            # Performance assertions
            assert summary["success_rate"] >= 90  # At least 90% success rate
            assert summary["response_times"]["mean"] <= 30  # Average response time under 30 seconds
            assert summary["response_times"]["p95"] <= 60   # 95th percentile under 60 seconds
            assert summary["system_metrics"]["max_memory_usage"] <= 80  # Memory usage under 80%
            
            print(f"Load Test Summary: {summary}")
    
    @pytest.mark.asyncio
    async def test_data_ingestion_performance(self, performance_container, sample_large_dataset):
        """Test data ingestion performance with large datasets."""
        metrics = PerformanceMetrics()
        
        data_prep_service = performance_container.data_preparation_service()
        
        async def ingest_batch(batch_data):
            """Ingest a batch of data and measure performance."""
            start_time = time.time()
            try:
                result = await data_prep_service.ingest_price_data(batch_data)
                response_time = time.time() - start_time
                metrics.record_request(response_time, success=True)
                return result
            except Exception as e:
                response_time = time.time() - start_time
                metrics.record_request(response_time, success=False)
                raise e
        
        # Split large dataset into batches
        batch_size = 1000
        price_batches = []
        prices = sample_large_dataset["prices"]
        
        for i in range(0, len(prices), batch_size):
            batch = prices[i:i + batch_size]
            price_batches.append(batch)
        
        metrics.start_monitoring()
        
        # Process batches concurrently
        tasks = []
        for i, batch in enumerate(price_batches[:10]):  # Test with first 10 batches
            task = asyncio.create_task(ingest_batch(batch))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        metrics.stop_monitoring()
        
        summary = metrics.get_summary()
        
        # Performance assertions for data ingestion
        assert summary["success_rate"] >= 95  # Higher success rate expected for data ingestion
        assert summary["response_times"]["mean"] <= 10  # Average ingestion time under 10 seconds
        assert summary["requests_per_second"] >= 0.5   # At least 0.5 batches per second
        
        print(f"Data Ingestion Performance: {summary}")
    
    def test_api_endpoint_load(self, performance_container):
        """Test API endpoint performance under load."""
        from fastapi.testclient import TestClient
        from src.interfaces.api.rest.app import create_app
        
        app = create_app(performance_container)
        client = TestClient(app)
        
        # Create authenticated user
        user_data = {
            "username": "load_test_user",
            "email": "<EMAIL>",
            "password": "password123",
            "role": "analyst"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        login_data = {"username": "load_test_user", "password": "password123"}
        response = client.post("/api/v1/auth/login", json=login_data)
        token = response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        metrics = PerformanceMetrics()
        
        def make_api_request(endpoint: str):
            """Make an API request and measure performance."""
            start_time = time.time()
            try:
                response = client.get(endpoint, headers=headers)
                response_time = time.time() - start_time
                success = response.status_code == 200
                metrics.record_request(response_time, success)
                return response
            except Exception as e:
                response_time = time.time() - start_time
                metrics.record_request(response_time, success=False)
                raise e
        
        # Test endpoints
        endpoints = [
            "/api/v1/markets",
            "/api/v1/commodities",
            "/api/v1/prices",
            "/api/v1/health"
        ]
        
        metrics.start_monitoring()
        
        # Make concurrent requests to different endpoints
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = []
            for _ in range(200):  # 200 total requests
                endpoint = endpoints[len(futures) % len(endpoints)]
                future = executor.submit(make_api_request, endpoint)
                futures.append(future)
            
            # Wait for all requests to complete
            for future in futures:
                future.result()
        
        metrics.stop_monitoring()
        
        summary = metrics.get_summary()
        
        # API performance assertions
        assert summary["success_rate"] >= 95
        assert summary["response_times"]["mean"] <= 2.0  # Average response time under 2 seconds
        assert summary["response_times"]["p95"] <= 5.0   # 95th percentile under 5 seconds
        assert summary["requests_per_second"] >= 10      # At least 10 requests per second
        
        print(f"API Load Test Summary: {summary}")


class TestStressTesting:
    """Stress testing scenarios to find system limits."""
    
    @pytest.mark.asyncio
    async def test_memory_stress_large_dataset(self, performance_container):
        """Test system behavior with very large datasets."""
        # Create an extremely large dataset
        large_dataset = []
        for i in range(100000):  # 100k price observations
            large_dataset.append({
                "market_id": f"MARKET_{i % 100}",
                "commodity": f"COMMODITY_{i % 20}",
                "price": 100 + (i % 1000),
                "currency": "YER",
                "unit": "kg",
                "date": datetime(2020, 1, 1) + timedelta(days=i % 365),
                "source": "STRESS_TEST"
            })
        
        data_prep_service = performance_container.data_preparation_service()
        
        # Monitor memory usage during processing
        initial_memory = psutil.virtual_memory().percent
        
        start_time = time.time()
        
        try:
            # Process large dataset
            result = await data_prep_service.prepare_panel_data(large_dataset)
            
            processing_time = time.time() - start_time
            peak_memory = psutil.virtual_memory().percent
            memory_increase = peak_memory - initial_memory
            
            # Stress test assertions
            assert processing_time <= 120  # Should complete within 2 minutes
            assert memory_increase <= 50   # Memory increase should be reasonable
            assert result is not None
            
            print(f"Stress Test - Processing time: {processing_time:.2f}s, Memory increase: {memory_increase:.2f}%")
            
        except MemoryError:
            pytest.fail("System ran out of memory during stress test")
        except Exception as e:
            pytest.fail(f"Unexpected error during stress test: {e}")
    
    @pytest.mark.asyncio
    async def test_concurrent_user_stress(self, performance_container):
        """Test system with high concurrent user load."""
        from fastapi.testclient import TestClient
        from src.interfaces.api.rest.app import create_app
        
        app = create_app(performance_container)
        
        # Create multiple users
        user_count = 100
        users_created = 0
        
        def create_user_and_test(user_id: int):
            """Create a user and perform multiple operations."""
            client = TestClient(app)
            
            user_data = {
                "username": f"stress_user_{user_id}",
                "email": f"stress_{user_id}@example.com",
                "password": "password123",
                "role": "analyst"
            }
            
            try:
                # Register user
                response = client.post("/api/v1/auth/register", json=user_data)
                if response.status_code != 201:
                    return False
                
                # Login
                login_data = {"username": user_data["username"], "password": user_data["password"]}
                response = client.post("/api/v1/auth/login", json=login_data)
                if response.status_code != 200:
                    return False
                
                token = response.json()["access_token"]
                headers = {"Authorization": f"Bearer {token}"}
                
                # Perform multiple operations
                operations = [
                    lambda: client.get("/api/v1/markets", headers=headers),
                    lambda: client.get("/api/v1/commodities", headers=headers),
                    lambda: client.get("/api/v1/auth/profile", headers=headers),
                ]
                
                for operation in operations:
                    response = operation()
                    if response.status_code not in [200, 202]:
                        return False
                
                return True
                
            except Exception:
                return False
        
        # Run concurrent user operations
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            for i in range(user_count):
                future = executor.submit(create_user_and_test, i)
                futures.append(future)
            
            # Collect results
            successful_users = sum(1 for future in futures if future.result())
        
        success_rate = (successful_users / user_count) * 100
        
        # Stress test assertions
        assert success_rate >= 80  # At least 80% of users should succeed
        
        print(f"Concurrent User Stress Test - Success rate: {success_rate:.2f}%")
    
    @pytest.mark.asyncio
    async def test_database_connection_stress(self, performance_container):
        """Test database performance under connection stress."""
        # This test would verify database connection pooling and performance
        # For now, we'll simulate database stress
        
        connection_count = 50
        successful_connections = 0
        
        async def test_database_operation():
            """Simulate database operation."""
            try:
                # In a real test, this would use actual database connections
                await asyncio.sleep(0.1)  # Simulate database query time
                return True
            except Exception:
                return False
        
        # Create many concurrent database operations
        tasks = []
        for _ in range(connection_count):
            task = asyncio.create_task(test_database_operation())
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        successful_connections = sum(1 for result in results if result is True)
        
        success_rate = (successful_connections / connection_count) * 100
        
        # Database stress assertions
        assert success_rate >= 90  # Database should handle concurrent connections well
        
        print(f"Database Stress Test - Success rate: {success_rate:.2f}%")


class TestEnduranceTesting:
    """Endurance testing for long-running operations."""
    
    @pytest.mark.asyncio
    async def test_long_running_analysis_endurance(self, performance_container, sample_large_dataset):
        """Test system stability during long-running analyses."""
        # This test simulates a very long-running analysis
        # In production, some analyses might take hours
        
        metrics = PerformanceMetrics()
        
        orchestrator = performance_container.analysis_orchestrator()
        
        # Mock long-running operations
        async def simulate_long_analysis():
            """Simulate a long-running analysis."""
            start_time = time.time()
            
            # Simulate different phases of analysis
            phases = [
                ("Data preparation", 5),
                ("Tier 1 analysis", 10),
                ("Tier 2 analysis", 15),
                ("Tier 3 validation", 8),
                ("Results compilation", 3)
            ]
            
            for phase_name, duration in phases:
                await asyncio.sleep(duration)
                
                # Monitor system resources during long operation
                memory_usage = psutil.virtual_memory().percent
                cpu_usage = psutil.cpu_percent()
                
                # Ensure system remains stable
                assert memory_usage <= 80, f"Memory usage too high during {phase_name}: {memory_usage}%"
                assert cpu_usage <= 90, f"CPU usage too high during {phase_name}: {cpu_usage}%"
            
            total_time = time.time() - start_time
            return {"analysis_id": "endurance_test", "total_time": total_time}
        
        # Run long analysis
        result = await simulate_long_analysis()
        
        # Endurance test assertions
        assert result["total_time"] <= 45  # Should complete within expected time
        
        print(f"Endurance Test - Analysis completed in {result['total_time']:.2f} seconds")
    
    @pytest.mark.asyncio
    async def test_memory_leak_detection(self, performance_container):
        """Test for memory leaks during repeated operations."""
        initial_memory = psutil.virtual_memory().used
        memory_samples = []
        
        # Perform repeated operations
        for iteration in range(20):
            # Simulate typical operation
            await asyncio.sleep(0.5)
            
            # Sample memory usage
            current_memory = psutil.virtual_memory().used
            memory_samples.append(current_memory)
            
            # Check for memory growth trend
            if len(memory_samples) >= 5:
                recent_trend = statistics.mean(memory_samples[-5:]) - statistics.mean(memory_samples[-10:-5])
                
                # Allow some memory growth but flag excessive increase
                memory_increase_mb = recent_trend / (1024 * 1024)
                assert memory_increase_mb <= 50, f"Potential memory leak detected: {memory_increase_mb:.2f}MB increase"
        
        final_memory = psutil.virtual_memory().used
        total_increase_mb = (final_memory - initial_memory) / (1024 * 1024)
        
        print(f"Memory Leak Test - Total memory increase: {total_increase_mb:.2f}MB")
        
        # Final memory leak assertion
        assert total_increase_mb <= 100, f"Excessive memory usage: {total_increase_mb:.2f}MB"


@pytest.mark.performance
class TestPerformanceBenchmarks:
    """Performance benchmarks and SLA validation."""
    
    @pytest.mark.asyncio
    async def test_sla_compliance(self, performance_container):
        """Test compliance with Service Level Agreements."""
        sla_requirements = {
            "api_response_time_95th_percentile": 2.0,  # seconds
            "analysis_completion_time": 60.0,          # seconds
            "system_availability": 99.5,               # percentage
            "concurrent_users_supported": 100,
            "data_ingestion_rate": 1000                # records per second
        }
        
        # Test API response time SLA
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # Simulate API requests
        for _ in range(100):
            start_time = time.time()
            await asyncio.sleep(0.1)  # Simulate API processing
            response_time = time.time() - start_time
            metrics.record_request(response_time, success=True)
        
        metrics.stop_monitoring()
        summary = metrics.get_summary()
        
        # Validate SLA compliance
        assert summary["response_times"]["p95"] <= sla_requirements["api_response_time_95th_percentile"]
        assert summary["success_rate"] >= sla_requirements["system_availability"]
        
        print(f"SLA Compliance Test Results: {summary}")
    
    def test_resource_utilization_limits(self):
        """Test system resource utilization stays within limits."""
        resource_limits = {
            "max_memory_usage_percent": 80,
            "max_cpu_usage_percent": 85,
            "max_disk_usage_percent": 90
        }
        
        # Monitor current resource usage
        memory_usage = psutil.virtual_memory().percent
        cpu_usage = psutil.cpu_percent(interval=1)
        disk_usage = psutil.disk_usage('/').percent
        
        # Validate resource limits
        assert memory_usage <= resource_limits["max_memory_usage_percent"], f"Memory usage: {memory_usage}%"
        assert cpu_usage <= resource_limits["max_cpu_usage_percent"], f"CPU usage: {cpu_usage}%"
        assert disk_usage <= resource_limits["max_disk_usage_percent"], f"Disk usage: {disk_usage}%"
        
        print(f"Resource utilization - Memory: {memory_usage}%, CPU: {cpu_usage}%, Disk: {disk_usage}%")