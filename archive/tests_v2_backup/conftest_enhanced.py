"""Enhanced pytest configuration and fixtures for comprehensive testing."""

import asyncio
import pytest
import pytest_asyncio
import tempfile
import shutil
from datetime import datetime, timedelta
from typing import AsyncGenerator, Generator, Dict, List, Any
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch

import numpy as np
import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import (
    Commodity,
    Coordinates,
    MarketId,
    MarketType,
    Price,
)
from src.core.domain.conflict.entities import ConflictEvent
from src.core.domain.conflict.value_objects import ConflictEventId, EventType, Severity
from src.core.domain.auth.entities import User
from src.core.domain.auth.value_objects import UserId, Role as UserRole
from src.infrastructure.persistence.unit_of_work import PostgresUnitOfWork
from src.shared.container import Container


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_container() -> Container:
    """Create test container with in-memory services."""
    container = Container()
    
    # Configure for testing
    container.config.database.url.from_value("postgresql://test:test@localhost:5432/test_db")
    container.config.cache.type.from_value("memory")
    container.config.events.type.from_value("inmemory")
    container.config.logging.level.from_value("DEBUG")
    container.config.auth.jwt_secret.from_value("test-secret-key-for-testing-only")
    
    return container


@pytest.fixture
def temp_directory():
    """Create temporary directory for test files."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


# Market and Price Fixtures
@pytest.fixture
def sample_markets() -> List[Market]:
    """Create sample markets for testing."""
    return [
        Market(
            market_id=MarketId("SANAA_CENTRAL"),
            name="Sanaa Central Market",
            coordinates=Coordinates(latitude=15.3694, longitude=44.1910),
            market_type=MarketType.WHOLESALE,
            governorate="Sana'a",
            district="Al Tahrir",
            active_since=datetime(2015, 1, 1)
        ),
        Market(
            market_id=MarketId("ADEN_MAIN"),
            name="Aden Main Market",
            coordinates=Coordinates(latitude=12.7797, longitude=45.0365),
            market_type=MarketType.RETAIL,
            governorate="Aden",
            district="Crater",
            active_since=datetime(2015, 1, 1)
        ),
        Market(
            market_id=MarketId("HODEIDAH_PORT"),
            name="Hodeidah Port Market",
            coordinates=Coordinates(latitude=14.7978, longitude=42.9545),
            market_type=MarketType.WHOLESALE,
            governorate="Hodeidah",
            district="Al Mina",
            active_since=datetime(2015, 1, 1)
        ),
        Market(
            market_id=MarketId("TAIZ_CITY"),
            name="Taiz City Market",
            coordinates=Coordinates(latitude=13.5795, longitude=44.0209),
            market_type=MarketType.RETAIL,
            governorate="Taiz",
            district="Salh",
            active_since=datetime(2015, 1, 1)
        ),
        Market(
            market_id=MarketId("IBB_CENTRAL"),
            name="Ibb Central Market",
            coordinates=Coordinates(latitude=13.9667, longitude=44.1833),
            market_type=MarketType.WHOLESALE,
            governorate="Ibb",
            district="Ibb City",
            active_since=datetime(2015, 1, 1)
        )
    ]


@pytest.fixture
def sample_commodities() -> List[Commodity]:
    """Create sample commodities for testing."""
    return [
        Commodity(code="WHEAT", name="Wheat", category="cereals", standard_unit="kg"),
        Commodity(code="RICE", name="Rice", category="cereals", standard_unit="kg"),
        Commodity(code="OIL_VEG", name="Vegetable Oil", category="oils", standard_unit="liter"),
        Commodity(code="SUGAR", name="Sugar", category="sweeteners", standard_unit="kg"),
        Commodity(code="FUEL_PETROL", name="Petrol", category="fuel", standard_unit="liter"),
        Commodity(code="FUEL_DIESEL", name="Diesel", category="fuel", standard_unit="liter"),
        Commodity(code="BEANS", name="Beans", category="legumes", standard_unit="kg"),
        Commodity(code="LENTILS", name="Lentils", category="legumes", standard_unit="kg"),
        Commodity(code="MEAT_CHICKEN", name="Chicken", category="meat", standard_unit="kg"),
        Commodity(code="MILK", name="Milk", category="dairy", standard_unit="liter")
    ]


@pytest.fixture
def sample_price_observations(sample_markets, sample_commodities) -> List[PriceObservation]:
    """Create comprehensive sample price observations."""
    observations = []
    start_date = datetime(2020, 1, 1)
    
    # Generate 4 years of monthly data
    for month in range(48):
        observation_date = start_date + timedelta(days=30 * month)
        
        for market in sample_markets:
            for commodity in sample_commodities:
                # Create realistic price variations
                base_prices = {
                    "WHEAT": 400, "RICE": 800, "OIL_VEG": 1200, "SUGAR": 600,
                    "FUEL_PETROL": 500, "FUEL_DIESEL": 450, "BEANS": 700,
                    "LENTILS": 650, "MEAT_CHICKEN": 1500, "MILK": 300
                }
                
                base_price = base_prices.get(commodity.code, 500)
                
                # Market-specific multipliers
                market_multipliers = {
                    "SANAA_CENTRAL": 1.0,
                    "ADEN_MAIN": 1.1,
                    "HODEIDAH_PORT": 0.95,
                    "TAIZ_CITY": 1.05,
                    "IBB_CENTRAL": 0.98
                }
                
                market_factor = market_multipliers.get(market.market_id.value, 1.0)
                
                # Time trends and seasonal effects
                inflation_factor = 1 + (month * 0.015)  # 1.5% monthly inflation
                seasonal_factor = 1 + 0.1 * np.sin(2 * np.pi * month / 12)  # Seasonal variation
                
                # Random variation
                random_factor = 1 + np.random.normal(0, 0.05)  # 5% random variation
                
                final_price = base_price * market_factor * inflation_factor * seasonal_factor * random_factor
                
                observations.append(PriceObservation(
                    market_id=market.market_id,
                    commodity=commodity,
                    price=Price(amount=final_price, currency="YER", unit=commodity.standard_unit),
                    observed_date=observation_date,
                    source="WFP",
                    quality="standard",
                    observations_count=1
                ))
    
    return observations


# Conflict Event Fixtures
@pytest.fixture
def sample_conflict_events(sample_markets) -> List[ConflictEvent]:
    """Create sample conflict events for testing."""
    events = []
    start_date = datetime(2020, 1, 1)
    
    event_types = [EventType.VIOLENCE_AGAINST_CIVILIANS, EventType.BATTLES, EventType.EXPLOSIONS]
    severities = [Severity.LOW, Severity.MEDIUM, Severity.HIGH]
    
    for i in range(100):  # Generate 100 conflict events
        event_date = start_date + timedelta(days=i * 15)  # Every 15 days
        
        # Pick a random market location
        market = sample_markets[i % len(sample_markets)]
        
        # Add some spatial variation around the market
        lat_offset = np.random.normal(0, 0.1)  # ~11km standard deviation
        lon_offset = np.random.normal(0, 0.1)
        
        events.append(ConflictEvent(
            event_id=ConflictEventId(f"YEM{i:06d}"),
            event_date=event_date,
            event_type=event_types[i % len(event_types)],
            latitude=market.coordinates.latitude + lat_offset,
            longitude=market.coordinates.longitude + lon_offset,
            location_description=f"Near {market.name}",
            fatalities=np.random.poisson(2),  # Poisson-distributed fatalities
            severity=severities[i % len(severities)],
            source="ACLED",
            notes=f"Test conflict event {i}"
        ))
    
    return events


# User and Authentication Fixtures
@pytest.fixture
def sample_users() -> List[User]:
    """Create sample users for testing."""
    return [
        User(
            user_id=UserId("admin_001"),
            username="admin_user",
            email="<EMAIL>",
            password_hash="$2b$12$hash1",
            role=UserRole.ADMIN,
            is_active=True,
            created_at=datetime.now(),
            last_login=datetime.now()
        ),
        User(
            user_id=UserId("analyst_001"),
            username="lead_analyst",
            email="<EMAIL>",
            password_hash="$2b$12$hash2",
            role=UserRole.ANALYST,
            is_active=True,
            created_at=datetime.now(),
            last_login=datetime.now()
        ),
        User(
            user_id=UserId("researcher_001"),
            username="researcher",
            email="<EMAIL>",
            password_hash="$2b$12$hash3",
            role=UserRole.RESEARCHER,
            is_active=True,
            created_at=datetime.now(),
            last_login=None
        ),
        User(
            user_id=UserId("viewer_001"),
            username="policy_viewer",
            email="<EMAIL>",
            password_hash="$2b$12$hash4",
            role=UserRole.VIEWER,
            is_active=True,
            created_at=datetime.now(),
            last_login=datetime.now()
        )
    ]


# Data Analysis Fixtures
@pytest.fixture
def sample_panel_data(sample_markets, sample_commodities) -> pd.DataFrame:
    """Create sample panel data for econometric testing."""
    np.random.seed(42)  # For reproducible tests
    
    data = []
    start_date = datetime(2020, 1, 1)
    
    # Generate monthly panel data
    for month in range(48):  # 4 years
        date = start_date + timedelta(days=30 * month)
        
        for market in sample_markets:
            for commodity in sample_commodities:
                # Generate realistic price data with trends and seasonality
                base_price = {"WHEAT": 400, "RICE": 800, "OIL_VEG": 1200}.get(commodity.code, 500)
                
                # Market fixed effects
                market_effects = {
                    "SANAA_CENTRAL": 0.0,
                    "ADEN_MAIN": 0.1,
                    "HODEIDAH_PORT": -0.05,
                    "TAIZ_CITY": 0.08,
                    "IBB_CENTRAL": -0.03
                }
                
                # Time trend
                time_trend = month * 0.01
                
                # Seasonal effects
                seasonal = 0.1 * np.sin(2 * np.pi * month / 12)
                
                # Random shock
                shock = np.random.normal(0, 0.1)
                
                # Log price
                log_price = np.log(base_price) + market_effects.get(market.market_id.value, 0) + time_trend + seasonal + shock
                
                # Exchange rate (varying by region)
                if market.governorate in ["Sana'a", "Hodeidah", "Ibb"]:  # Houthi areas
                    exchange_rate = 535 + np.random.normal(0, 5)
                else:  # Government areas
                    exchange_rate = 1800 + month * 10 + np.random.normal(0, 50)
                
                # Conflict intensity (random)
                conflict_events = np.random.poisson(0.5)
                
                data.append({
                    "date": date,
                    "market_id": market.market_id.value,
                    "commodity": commodity.code,
                    "governorate": market.governorate,
                    "log_price_yer": log_price,
                    "price_yer": np.exp(log_price),
                    "price_usd": np.exp(log_price) / exchange_rate,
                    "exchange_rate": exchange_rate,
                    "conflict_events": conflict_events,
                    "latitude": market.coordinates.latitude,
                    "longitude": market.coordinates.longitude
                })
    
    return pd.DataFrame(data)


# Mock Service Fixtures
@pytest.fixture
async def mock_uow():
    """Create a mock unit of work."""
    uow = AsyncMock(spec=PostgresUnitOfWork)
    uow.__aenter__.return_value = uow
    uow.__aexit__.return_value = None
    
    # Mock repositories
    uow.markets = AsyncMock()
    uow.prices = AsyncMock()
    uow.conflicts = AsyncMock()
    uow.users = AsyncMock()
    
    return uow


@pytest.fixture
def mock_external_services():
    """Mock external data services."""
    with patch('src.infrastructure.external_services.wfp_client.WFPClient') as mock_wfp, \
         patch('src.infrastructure.external_services.hdx_client.HDXClient') as mock_hdx, \
         patch('src.infrastructure.external_services.acled_client.ACLEDClient') as mock_acled:
        
        # Setup WFP mock
        mock_wfp.return_value.get_price_data.return_value = [
            {
                "market": "Sanaa Central Market",
                "commodity": "Wheat",
                "price": 400.0,
                "currency": "YER",
                "date": "2023-01-01",
                "unit": "kg"
            }
        ]
        
        # Setup HDX mock
        mock_hdx.return_value.get_market_locations.return_value = [
            {
                "market_id": "SANAA_CENTRAL",
                "name": "Sanaa Central Market",
                "latitude": 15.3694,
                "longitude": 44.1910,
                "governorate": "Sana'a"
            }
        ]
        
        # Setup ACLED mock
        mock_acled.return_value.get_conflict_events.return_value = [
            {
                "event_id": "YEM123456",
                "event_date": "2023-01-01",
                "latitude": 15.3694,
                "longitude": 44.1910,
                "event_type": "Violence against civilians",
                "fatalities": 2
            }
        ]
        
        yield {
            'wfp': mock_wfp.return_value,
            'hdx': mock_hdx.return_value,
            'acled': mock_acled.return_value
        }


# Performance Testing Fixtures
@pytest.fixture
def performance_data():
    """Create large dataset for performance testing."""
    np.random.seed(123)
    
    # Generate larger dataset
    n_markets = 50
    n_commodities = 15
    n_months = 60  # 5 years
    
    data = []
    
    for market_id in range(n_markets):
        for commodity_id in range(n_commodities):
            for month in range(n_months):
                data.append({
                    "market_id": f"MARKET_{market_id:03d}",
                    "commodity": f"COMMODITY_{commodity_id:02d}",
                    "date": datetime(2019, 1, 1) + timedelta(days=30 * month),
                    "price": 100 + np.random.exponential(50),
                    "currency": "YER",
                    "unit": "kg"
                })
    
    return data


# Test Utilities
@pytest.fixture
def assert_response_time():
    """Utility for asserting response times."""
    def _assert_response_time(func, max_time_seconds=1.0):
        import time
        start_time = time.time()
        result = func()
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response_time <= max_time_seconds, f"Response time {response_time:.3f}s exceeded limit {max_time_seconds}s"
        
        return result
    
    return _assert_response_time


@pytest.fixture
def assert_memory_usage():
    """Utility for asserting memory usage."""
    def _assert_memory_usage(func, max_memory_mb=100):
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        result = func()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        assert memory_increase <= max_memory_mb, f"Memory increase {memory_increase:.2f}MB exceeded limit {max_memory_mb}MB"
        
        return result
    
    return _assert_memory_usage


# Database Testing Fixtures
@pytest.fixture
async def test_database_session():
    """Create test database session."""
    # This would create an actual test database session in a real implementation
    # For now, we'll return a mock
    mock_session = AsyncMock(spec=AsyncSession)
    return mock_session


@pytest.fixture
def database_cleanup():
    """Cleanup database after tests."""
    tables_to_cleanup = []
    
    yield tables_to_cleanup
    
    # Cleanup logic would go here
    # In a real implementation, this would clean up test data
    pass


# Integration Test Fixtures
@pytest.fixture
async def integration_test_client():
    """Create integration test client."""
    from fastapi.testclient import TestClient
    from src.interfaces.api.rest.app import create_app
    
    container = Container()
    container.config.database.url.from_value("postgresql://test:test@localhost:5432/integration_test_db")
    
    app = create_app(container)
    return TestClient(app)


# Async Test Helpers
@pytest.fixture
def async_test_helper():
    """Helper for running async tests."""
    def _run_async(coro):
        loop = asyncio.new_event_loop()
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    
    return _run_async


# Parametrized Test Data
@pytest.fixture(params=[
    {"currency": "YER", "expected_range": (300, 2000)},
    {"currency": "USD", "expected_range": (0.1, 2.0)},
])
def currency_test_cases(request):
    """Parametrized test cases for different currencies."""
    return request.param


@pytest.fixture(params=[
    {"model_type": "pooled", "expected_convergence": True},
    {"model_type": "fixed_effects", "expected_convergence": True},
    {"model_type": "random_effects", "expected_convergence": True},
])
def model_test_cases(request):
    """Parametrized test cases for different model types."""
    return request.param