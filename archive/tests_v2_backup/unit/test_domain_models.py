"""Unit tests for domain models."""

from datetime import datetime
from decimal import Decimal

import pytest

from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import (
    Commodity,
    Coordinates,
    MarketId,
    MarketPair,
    MarketType,
    Price,
)
from src.core.domain.shared.exceptions import BusinessRuleViolation, ValidationException


class TestValueObjects:
    """Test value objects."""
    
    def test_market_id_validation(self):
        """Test MarketId validation."""
        # Valid ID
        market_id = MarketId("SANAA_CENTRAL")
        assert market_id.value == "SANAA_CENTRAL"
        
        # Invalid - empty
        with pytest.raises(ValidationException):
            MarketId("")
        
        # Invalid - too long
        with pytest.raises(ValidationException):
            MarketId("A" * 51)
    
    def test_coordinates_validation(self):
        """Test Coordinates validation."""
        # Valid coordinates
        coords = Coordinates(latitude=15.3694, longitude=44.1910)
        assert coords.latitude == 15.3694
        assert coords.longitude == 44.1910
        
        # Invalid latitude
        with pytest.raises(ValidationException):
            Coordinates(latitude=91, longitude=0)
        
        # Invalid longitude
        with pytest.raises(ValidationException):
            Coordinates(latitude=0, longitude=181)
    
    def test_coordinates_distance(self):
        """Test distance calculation between coordinates."""
        coords1 = Coordinates(latitude=15.3694, longitude=44.1910)  # Sana'a
        coords2 = Coordinates(latitude=12.7855, longitude=45.0187)  # Aden
        
        distance = coords1.distance_to(coords2)
        assert 290 < distance < 310  # Approximately 300 km
    
    def test_price_validation(self):
        """Test Price validation."""
        # Valid price
        price = Price(amount=Decimal("100.50"), currency="YER", unit="kg")
        assert price.amount == Decimal("100.50")
        
        # Invalid - negative amount
        with pytest.raises(ValidationException):
            Price(amount=Decimal("-10"), currency="YER", unit="kg")
        
        # Invalid - no currency
        with pytest.raises(ValidationException):
            Price(amount=Decimal("100"), currency="", unit="kg")
    
    def test_price_conversion(self):
        """Test price currency conversion."""
        price = Price(amount=Decimal("1000"), currency="YER", unit="kg")
        
        # Convert to USD
        usd_price = price.to_usd(Decimal("500"))  # 500 YER = 1 USD
        assert usd_price.amount == Decimal("2")
        assert usd_price.currency == "USD"
        
        # Already USD
        usd_price2 = usd_price.to_usd(Decimal("500"))
        assert usd_price2 == usd_price
    
    def test_commodity_validation(self):
        """Test Commodity validation."""
        # Valid commodity
        commodity = Commodity(
            code="WHEAT_FLOUR",
            name="Wheat Flour",
            category="cereal",
            standard_unit="kg"
        )
        assert commodity.code == "WHEAT_FLOUR"
        
        # Invalid - missing required fields
        with pytest.raises(ValidationException):
            Commodity(code="", name="Test", category="test", standard_unit="kg")


class TestMarketEntity:
    """Test Market entity."""
    
    def test_market_creation(self, sample_market):
        """Test market creation."""
        assert sample_market.market_id.value == "TEST_MARKET"
        assert sample_market.is_active_at(datetime.utcnow())
        assert len(sample_market.events) == 1
        assert sample_market.events[0].event_name == "MarketCreatedEvent"
    
    def test_market_deactivation(self, sample_market):
        """Test market deactivation."""
        # Deactivate market
        sample_market.deactivate("Test reason")
        
        assert sample_market.active_until is not None
        assert not sample_market.is_active_at(datetime.utcnow())
        assert len(sample_market.events) == 2
        assert sample_market.events[1].event_name == "MarketDeactivatedEvent"
        
        # Cannot deactivate twice
        with pytest.raises(BusinessRuleViolation):
            sample_market.deactivate("Another reason")
    
    def test_market_active_period(self):
        """Test market active period checks."""
        market = Market(
            market_id=MarketId("TEST"),
            name="Test",
            coordinates=Coordinates(15, 44),
            market_type=MarketType.RETAIL,
            governorate="Test",
            district="Test",
            active_since=datetime(2020, 1, 1),
            active_until=datetime(2022, 12, 31)
        )
        
        # Before active period
        assert not market.is_active_at(datetime(2019, 12, 31))
        
        # During active period
        assert market.is_active_at(datetime(2021, 6, 15))
        
        # After active period
        assert not market.is_active_at(datetime(2023, 1, 1))
    
    def test_market_commodity_trading_rules(self, sample_commodity):
        """Test commodity trading business rules."""
        # Port market - only imported
        port_market = Market(
            market_id=MarketId("PORT"),
            name="Port Market",
            coordinates=Coordinates(15, 44),
            market_type=MarketType.PORT,
            governorate="Test",
            district="Test",
            active_since=datetime(2020, 1, 1)
        )
        
        imported_commodity = Commodity(
            code="RICE_IMPORTED",
            name="Rice (Imported)",
            category="imported",
            standard_unit="kg"
        )
        
        assert port_market.can_trade_commodity(imported_commodity)
        assert not port_market.can_trade_commodity(sample_commodity)


class TestPriceObservation:
    """Test PriceObservation entity."""
    
    def test_price_observation_creation(self, sample_price_observation):
        """Test price observation creation."""
        assert sample_price_observation.market_id.value == "TEST_MARKET"
        assert sample_price_observation.price.amount == 100.0
        assert sample_price_observation.observations_count == 5
    
    def test_price_observation_validation(self, sample_market, sample_commodity):
        """Test price observation validation."""
        # Future date
        with pytest.raises(ValidationException):
            PriceObservation(
                market_id=sample_market.market_id,
                commodity=sample_commodity,
                price=Price(amount=100, currency="YER", unit="kg"),
                observed_date=datetime(2030, 1, 1),
                source="TEST"
            )
        
        # Invalid observations count
        with pytest.raises(ValidationException):
            PriceObservation(
                market_id=sample_market.market_id,
                commodity=sample_commodity,
                price=Price(amount=100, currency="YER", unit="kg"),
                observed_date=datetime(2023, 1, 1),
                source="TEST",
                observations_count=0
            )
    
    def test_outlier_detection(self, sample_price_observation):
        """Test price outlier detection."""
        mean_price = Decimal("100")
        std_dev = Decimal("10")
        
        # Not an outlier
        assert not sample_price_observation.is_outlier(mean_price, std_dev, threshold=3.0)
        
        # Create outlier observation
        outlier = PriceObservation(
            market_id=sample_price_observation.market_id,
            commodity=sample_price_observation.commodity,
            price=Price(amount=Decimal("200"), currency="YER", unit="kg"),
            observed_date=datetime(2023, 1, 1),
            source="TEST"
        )
        
        assert outlier.is_outlier(mean_price, std_dev, threshold=3.0)
    
    def test_observation_merging(self, sample_price_observation):
        """Test merging price observations."""
        # Create another observation for same date
        other = PriceObservation(
            market_id=sample_price_observation.market_id,
            commodity=sample_price_observation.commodity,
            price=Price(amount=Decimal("120"), currency="YER", unit="kg"),
            observed_date=sample_price_observation.observed_date,
            source="OTHER",
            observations_count=3
        )
        
        # Merge
        merged = sample_price_observation.merge_with(other)
        
        # Check weighted average: (100*5 + 120*3) / 8 = 107.5
        expected_price = (Decimal("100") * 5 + Decimal("120") * 3) / 8
        assert merged.price.amount == expected_price
        assert merged.observations_count == 8
        assert "TEST,OTHER" in merged.source
        
        # Cannot merge different commodities
        different_commodity = PriceObservation(
            market_id=sample_price_observation.market_id,
            commodity=Commodity(
                code="DIFFERENT",
                name="Different",
                category="test",
                standard_unit="kg"
            ),
            price=Price(amount=100, currency="YER", unit="kg"),
            observed_date=sample_price_observation.observed_date,
            source="TEST"
        )
        
        with pytest.raises(BusinessRuleViolation):
            sample_price_observation.merge_with(different_commodity)