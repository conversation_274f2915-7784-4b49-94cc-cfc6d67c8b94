"""Unit tests for time series models."""

import pytest
import pandas as pd
import numpy as np

from src.core.models.time_series import VECMModel, ThresholdVECMModel
from src.core.models.interfaces import ModelSpecification


@pytest.fixture
def sample_time_series_data():
    """Create sample time series data for testing."""
    np.random.seed(42)
    n_periods = 100
    
    # Generate cointegrated series
    common_factor = np.cumsum(np.random.normal(0, 1, n_periods))
    
    data = pd.DataFrame({
        'date': pd.date_range('2020-01-01', periods=n_periods, freq='D'),
        'price_market1': common_factor + np.random.normal(0, 0.5, n_periods),
        'price_market2': common_factor * 1.2 + np.random.normal(0, 0.5, n_periods),
        'price_market3': common_factor * 0.8 + np.random.normal(0, 0.5, n_periods),
    })
    
    return data.set_index('date')


class TestVECMModel:
    """Test VECM model."""
    
    def test_initialization(self):
        """Test VECM initialization."""
        model = VECMModel(
            variables=['price_market1', 'price_market2'],
            n_lags=2,
            deterministic='ci'
        )
        
        assert model.variables == ['price_market1', 'price_market2']
        assert model.n_lags == 2
        assert model.deterministic == 'ci'
        assert model.rank is None
    
    def test_validate_data(self, sample_time_series_data):
        """Test data validation."""
        model = VECMModel(
            variables=['price_market1', 'price_market2'],
            n_lags=2
        )
        
        # Should not raise
        model.validate_data(sample_time_series_data)
        
        # Missing variable
        bad_data = sample_time_series_data.drop(columns=['price_market1'])
        with pytest.raises(ValueError, match="Missing variables"):
            model.validate_data(bad_data)
        
        # Insufficient observations
        short_data = sample_time_series_data.iloc[:5]
        with pytest.raises(ValueError, match="Insufficient observations"):
            model.validate_data(short_data)
    
    def test_test_cointegration_rank(self, sample_time_series_data):
        """Test cointegration rank testing."""
        model = VECMModel(
            variables=['price_market1', 'price_market2'],
            n_lags=2
        )
        
        result = model.test_cointegration_rank(sample_time_series_data)
        
        assert 'trace_stats' in result
        assert 'critical_values' in result
        assert 'selected_rank' in result
        assert isinstance(result['selected_rank'], int)
        assert result['selected_rank'] >= 0
    
    def test_get_model_info(self):
        """Test model info."""
        model = VECMModel(
            variables=['price_market1', 'price_market2'],
            n_lags=3,
            rank=1
        )
        
        info = model.get_model_info()
        
        assert info['name'] == "Vector Error Correction Model"
        assert info['type'] == "time_series"
        assert info['n_variables'] == 2
        assert info['n_lags'] == 3
        assert info['rank'] == 1


class TestThresholdVECMModel:
    """Test threshold VECM model."""
    
    def test_initialization(self):
        """Test threshold VECM initialization."""
        model = ThresholdVECMModel(
            variables=['price_market1', 'price_market2'],
            n_lags=2,
            n_thresholds=1,
            threshold_variable='price_spread'
        )
        
        assert model.n_thresholds == 1
        assert model.threshold_variable == 'price_spread'
        assert model.threshold_values is None
        assert model.delay == 1
    
    def test_estimate_thresholds(self, sample_time_series_data):
        """Test threshold estimation."""
        # Add price spread
        data = sample_time_series_data.copy()
        data['price_spread'] = data['price_market1'] - data['price_market2']
        
        model = ThresholdVECMModel(
            variables=['price_market1', 'price_market2'],
            n_lags=2,
            n_thresholds=1,
            threshold_variable='price_spread'
        )
        
        thresholds = model.estimate_thresholds(data)
        
        assert 'values' in thresholds
        assert 'ssr' in thresholds
        assert len(thresholds['values']) == 1
        assert isinstance(thresholds['values'][0], (int, float))
    
    def test_test_threshold_effects(self, sample_time_series_data):
        """Test threshold effects testing."""
        data = sample_time_series_data.copy()
        data['price_spread'] = data['price_market1'] - data['price_market2']
        
        model = ThresholdVECMModel(
            variables=['price_market1', 'price_market2'],
            n_lags=2,
            n_thresholds=1,
            threshold_variable='price_spread'
        )
        
        result = model.test_threshold_effects(data)
        
        assert 'sup_wald' in result
        assert 'p_value' in result
        assert 'critical_values' in result
        assert 'reject_null' in result
        assert isinstance(result['reject_null'], bool)
    
    def test_multiple_thresholds(self):
        """Test multiple threshold configuration."""
        model = ThresholdVECMModel(
            variables=['price_market1', 'price_market2', 'price_market3'],
            n_lags=2,
            n_thresholds=2,
            threshold_variable='volatility'
        )
        
        assert model.n_thresholds == 2
        info = model.get_model_info()
        assert info['n_thresholds'] == 2
        assert info['n_regimes'] == 3  # n_thresholds + 1
    
    def test_smooth_transition(self):
        """Test smooth transition configuration."""
        model = ThresholdVECMModel(
            variables=['price_market1', 'price_market2'],
            n_lags=2,
            n_thresholds=1,
            smooth_transition=True,
            gamma=10.0
        )
        
        assert model.smooth_transition is True
        assert model.gamma == 10.0
        
        info = model.get_model_info()
        assert 'smooth_transition' in info
        assert info['smooth_transition'] is True