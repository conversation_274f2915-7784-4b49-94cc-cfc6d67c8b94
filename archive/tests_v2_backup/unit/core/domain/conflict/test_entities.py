"""Unit tests for conflict entities."""

import pytest
from datetime import datetime, timedelta

from v2.src.core.domain.conflict.entities import ConflictEvent
from v2.src.core.domain.conflict.value_objects import (
    ConflictType, ConflictIntensity, ImpactRadius
)
from v2.src.core.domain.market.value_objects import Coordinates
from v2.src.core.domain.shared.exceptions import ValidationException


class TestConflictEvent:
    """Test ConflictEvent entity."""
    
    def test_create_valid_conflict_event(self):
        """Test creating a valid conflict event."""
        event = ConflictEvent(
            event_date=datetime(2023, 6, 15),
            location=Coordinates(15.3694, 44.1910),
            conflict_type=ConflictType.BATTLE,
            intensity=ConflictIntensity.MEDIUM,
            fatalities=15,
            actors=["Actor A", "Actor B"],
            description="Armed clash in city center",
            source="ACLED"
        )
        
        assert event.event_date == datetime(2023, 6, 15)
        assert event.location.latitude == 15.3694
        assert event.conflict_type == ConflictType.BATTLE
        assert event.intensity == ConflictIntensity.MEDIUM
        assert event.fatalities == 15
        assert len(event.actors) == 2
        assert event.impact_radius is not None
    
    def test_default_impact_radius(self):
        """Test that default impact radius is set based on conflict type."""
        battle_event = ConflictEvent(
            event_date=datetime(2023, 6, 15),
            location=Coordinates(15.3694, 44.1910),
            conflict_type=ConflictType.BATTLE,
            intensity=ConflictIntensity.HIGH,
            fatalities=50,
            actors=["Actor A"],
            description="Major battle",
            source="ACLED"
        )
        
        explosion_event = ConflictEvent(
            event_date=datetime(2023, 6, 15),
            location=Coordinates(15.3694, 44.1910),
            conflict_type=ConflictType.EXPLOSION,
            intensity=ConflictIntensity.LOW,
            fatalities=2,
            actors=["Unknown"],
            description="IED explosion",
            source="ACLED"
        )
        
        # Battle should have larger impact radius than explosion
        assert battle_event.impact_radius.immediate_km > explosion_event.impact_radius.immediate_km
        assert battle_event.impact_radius.marginal_km > explosion_event.impact_radius.marginal_km
    
    def test_conflict_event_validation(self):
        """Test conflict event validation rules."""
        base_args = {
            "location": Coordinates(15.3694, 44.1910),
            "conflict_type": ConflictType.BATTLE,
            "intensity": ConflictIntensity.LOW,
            "fatalities": 3,
            "actors": ["Actor A"],
            "description": "Test event",
            "source": "ACLED"
        }
        
        # Future date
        with pytest.raises(ValidationException, match="Event date cannot be in the future"):
            ConflictEvent(
                event_date=datetime.utcnow() + timedelta(days=1),
                **base_args
            )
        
        # Negative fatalities
        with pytest.raises(ValidationException, match="Fatalities cannot be negative"):
            ConflictEvent(
                event_date=datetime(2023, 6, 15),
                fatalities=-1,
                location=base_args["location"],
                conflict_type=base_args["conflict_type"],
                intensity=base_args["intensity"],
                actors=base_args["actors"],
                description=base_args["description"],
                source=base_args["source"]
            )
        
        # No actors
        with pytest.raises(ValidationException, match="At least one actor must be specified"):
            ConflictEvent(
                event_date=datetime(2023, 6, 15),
                actors=[],
                location=base_args["location"],
                conflict_type=base_args["conflict_type"],
                intensity=base_args["intensity"],
                fatalities=base_args["fatalities"],
                description=base_args["description"],
                source=base_args["source"]
            )
        
        # Empty description
        with pytest.raises(ValidationException, match="Description is required"):
            ConflictEvent(
                event_date=datetime(2023, 6, 15),
                description="",
                location=base_args["location"],
                conflict_type=base_args["conflict_type"],
                intensity=base_args["intensity"],
                fatalities=base_args["fatalities"],
                actors=base_args["actors"],
                source=base_args["source"]
            )
    
    def test_intensity_fatality_validation(self):
        """Test that intensity matches fatality count."""
        # Low intensity (1-5 fatalities) but high fatality count
        with pytest.raises(ValidationException, match="Intensity .* doesn't match fatalities count"):
            ConflictEvent(
                event_date=datetime(2023, 6, 15),
                location=Coordinates(15.3694, 44.1910),
                conflict_type=ConflictType.BATTLE,
                intensity=ConflictIntensity.LOW,
                fatalities=50,  # Should be HIGH intensity
                actors=["Actor A"],
                description="Test event",
                source="ACLED"
            )
    
    def test_distance_calculation(self):
        """Test distance calculation from conflict to point."""
        event = ConflictEvent(
            event_date=datetime(2023, 6, 15),
            location=Coordinates(15.3694, 44.1910),  # Sana'a
            conflict_type=ConflictType.BATTLE,
            intensity=ConflictIntensity.MEDIUM,
            fatalities=10,
            actors=["Actor A"],
            description="Test event",
            source="ACLED"
        )
        
        # Distance to same point
        assert event.distance_to_point(event.location) < 0.001
        
        # Distance to Aden
        aden = Coordinates(12.7855, 45.0187)
        distance = event.distance_to_point(aden)
        assert 290 < distance < 310  # Approximately 300km
    
    def test_impact_level_calculation(self):
        """Test impact level calculation based on distance."""
        event = ConflictEvent(
            event_date=datetime(2023, 6, 15),
            location=Coordinates(15.3694, 44.1910),
            conflict_type=ConflictType.BATTLE,
            intensity=ConflictIntensity.HIGH,
            fatalities=50,
            actors=["Actor A"],
            description="Major battle",
            source="ACLED",
            impact_radius=ImpactRadius(
                immediate_km=10,
                moderate_km=30,
                marginal_km=50
            )
        )
        
        # Test points at different distances
        same_location = event.location
        nearby = Coordinates(15.4, 44.2)  # ~5km away
        moderate_dist = Coordinates(15.6, 44.4)  # ~25km away
        far_dist = Coordinates(15.8, 44.6)  # ~45km away
        very_far = Coordinates(16.5, 45.5)  # >100km away
        
        assert event.get_impact_level(same_location) == "immediate"
        assert event.get_impact_level(nearby) == "immediate"
        assert event.get_impact_level(moderate_dist) == "moderate"
        assert event.get_impact_level(far_dist) == "marginal"
        assert event.get_impact_level(very_far) == "none"
    
    def test_impacts_area(self):
        """Test checking if conflict impacts an area."""
        event = ConflictEvent(
            event_date=datetime(2023, 6, 15),
            location=Coordinates(15.3694, 44.1910),
            conflict_type=ConflictType.EXPLOSION,
            intensity=ConflictIntensity.MEDIUM,
            fatalities=8,
            actors=["Unknown"],
            description="Explosion in market",
            source="ACLED",
            impact_radius=ImpactRadius(
                immediate_km=5,
                moderate_km=15,
                marginal_km=25
            )
        )
        
        # Area centered 20km away with 10km radius
        # Should overlap with marginal impact zone
        area_center = Coordinates(15.5, 44.3)
        assert event.impacts_area(area_center, radius_km=10)
        
        # Area centered 40km away with 5km radius
        # Should not overlap
        far_area = Coordinates(15.8, 44.8)
        assert not event.impacts_area(far_area, radius_km=5)