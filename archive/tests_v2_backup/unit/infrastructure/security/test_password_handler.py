"""Tests for password handling."""

import pytest

from src.infrastructure.security.password_handler import (
    PasswordHandler, hash_password, verify_password
)


class TestPasswordHandler:
    """Test password hashing and verification."""
    
    @pytest.fixture
    def password_handler(self):
        """Create password handler instance."""
        return PasswordHandler(rounds=10)  # Lower rounds for faster tests
    
    def test_hash_password(self, password_handler):
        """Test hashing a password."""
        password = "MySecurePassword123!"
        hashed = password_handler.hash_password(password)
        
        assert isinstance(hashed, str)
        assert hashed != password  # Should not be plain text
        assert hashed.startswith("$2b$")  # bcrypt format
        assert len(hashed) == 60  # bcrypt hash length
    
    def test_hash_different_passwords(self, password_handler):
        """Test that different passwords produce different hashes."""
        password1 = "Password123!"
        password2 = "DifferentPassword456!"
        
        hash1 = password_handler.hash_password(password1)
        hash2 = password_handler.hash_password(password2)
        
        assert hash1 != hash2
    
    def test_hash_same_password_different_salts(self, password_handler):
        """Test that same password produces different hashes (due to salt)."""
        password = "SamePassword789!"
        
        hash1 = password_handler.hash_password(password)
        hash2 = password_handler.hash_password(password)
        
        assert hash1 != hash2  # Different salts
    
    def test_verify_correct_password(self, password_handler):
        """Test verifying a correct password."""
        password = "CorrectPassword123!"
        hashed = password_handler.hash_password(password)
        
        assert password_handler.verify_password(password, hashed) is True
    
    def test_verify_incorrect_password(self, password_handler):
        """Test verifying an incorrect password."""
        password = "CorrectPassword123!"
        wrong_password = "WrongPassword456!"
        hashed = password_handler.hash_password(password)
        
        assert password_handler.verify_password(wrong_password, hashed) is False
    
    def test_verify_empty_password(self, password_handler):
        """Test verifying empty password."""
        password = "ActualPassword123!"
        hashed = password_handler.hash_password(password)
        
        assert password_handler.verify_password("", hashed) is False
    
    def test_verify_invalid_hash(self, password_handler):
        """Test verifying password against invalid hash."""
        password = "SomePassword123!"
        invalid_hash = "not-a-valid-bcrypt-hash"
        
        assert password_handler.verify_password(password, invalid_hash) is False
    
    def test_needs_rehash_same_rounds(self, password_handler):
        """Test checking if hash needs rehashing with same rounds."""
        password = "TestPassword123!"
        hashed = password_handler.hash_password(password)
        
        assert password_handler.needs_rehash(hashed) is False
    
    def test_needs_rehash_different_rounds(self):
        """Test checking if hash needs rehashing with different rounds."""
        handler_10 = PasswordHandler(rounds=10)
        handler_12 = PasswordHandler(rounds=12)
        
        password = "TestPassword123!"
        hashed = handler_10.hash_password(password)
        
        assert handler_12.needs_rehash(hashed) is True
    
    def test_generate_temp_password(self, password_handler):
        """Test generating temporary password."""
        temp_password = password_handler.generate_temp_password(16)
        
        assert isinstance(temp_password, str)
        assert len(temp_password) == 16
        
        # Test different lengths
        short_password = password_handler.generate_temp_password(8)
        long_password = password_handler.generate_temp_password(32)
        
        assert len(short_password) == 8
        assert len(long_password) == 32
        
        # Test randomness
        password1 = password_handler.generate_temp_password(16)
        password2 = password_handler.generate_temp_password(16)
        assert password1 != password2
    
    def test_validate_password_strength_valid(self, password_handler):
        """Test validating a strong password."""
        strong_password = "StrongP@ssw0rd!"
        
        is_valid, error = password_handler.validate_password_strength(strong_password)
        assert is_valid is True
        assert error is None
    
    def test_validate_password_strength_too_short(self, password_handler):
        """Test validating password that's too short."""
        short_password = "Sh0rt!"
        
        is_valid, error = password_handler.validate_password_strength(short_password)
        assert is_valid is False
        assert "at least 8 characters" in error
    
    def test_validate_password_strength_no_uppercase(self, password_handler):
        """Test validating password without uppercase."""
        password = "weakp@ssw0rd!"
        
        is_valid, error = password_handler.validate_password_strength(password)
        assert is_valid is False
        assert "uppercase letter" in error
    
    def test_validate_password_strength_no_lowercase(self, password_handler):
        """Test validating password without lowercase."""
        password = "WEAKP@SSW0RD!"
        
        is_valid, error = password_handler.validate_password_strength(password)
        assert is_valid is False
        assert "lowercase letter" in error
    
    def test_validate_password_strength_no_digit(self, password_handler):
        """Test validating password without digit."""
        password = "WeakP@ssword!"
        
        is_valid, error = password_handler.validate_password_strength(password)
        assert is_valid is False
        assert "digit" in error
    
    def test_validate_password_strength_no_special(self, password_handler):
        """Test validating password without special character."""
        password = "WeakPassw0rd"
        
        is_valid, error = password_handler.validate_password_strength(password)
        assert is_valid is False
        assert "special character" in error
    
    def test_validate_password_strength_custom_requirements(self, password_handler):
        """Test validating password with custom requirements."""
        password = "simple"
        
        # Disable all requirements except length
        is_valid, error = password_handler.validate_password_strength(
            password,
            min_length=5,
            require_uppercase=False,
            require_lowercase=False,
            require_digit=False,
            require_special=False
        )
        assert is_valid is True
        assert error is None
    
    def test_module_level_functions(self):
        """Test module-level convenience functions."""
        password = "TestPassword123!"
        
        # Test hash_password
        hashed = hash_password(password)
        assert isinstance(hashed, str)
        assert hashed.startswith("$2b$")
        
        # Test verify_password
        assert verify_password(password, hashed) is True
        assert verify_password("WrongPassword", hashed) is False