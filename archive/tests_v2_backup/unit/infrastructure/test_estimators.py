"""Unit tests for estimators."""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from src.infrastructure.estimators.implementations.panel_estimators import (
    PooledPanelEstimator, FixedEffectsEstimator, VECMEstimator
)
from src.core.models.panel import PooledPanelModel, FixedEffectsModel
from src.core.models.time_series import VECMModel
from src.core.models.interfaces import ModelSpecification, EstimationResult


@pytest.fixture
def panel_data():
    """Create sample panel data."""
    np.random.seed(42)
    n_entities = 10
    n_periods = 20
    
    data = []
    for i in range(n_entities):
        for t in range(n_periods):
            data.append({
                'entity_id': f'entity_{i}',
                'time_id': t,
                'y': 10 + 2 * i + 0.5 * t + np.random.normal(0, 1),
                'x1': 5 + 0.3 * i + 0.1 * t + np.random.normal(0, 0.5),
                'x2': 3 + 0.2 * i + 0.05 * t + np.random.normal(0, 0.3),
            })
    
    df = pd.DataFrame(data)
    return df.set_index(['entity_id', 'time_id'])


@pytest.fixture
def time_series_data():
    """Create sample time series data."""
    np.random.seed(42)
    n = 100
    
    # Generate cointegrated series
    common = np.cumsum(np.random.normal(0, 1, n))
    
    return pd.DataFrame({
        'y1': common + np.random.normal(0, 0.5, n),
        'y2': 1.5 * common + np.random.normal(0, 0.5, n),
        'y3': 0.8 * common + np.random.normal(0, 0.5, n),
    }, index=pd.date_range('2020-01-01', periods=n, freq='D'))


class TestPooledPanelEstimator:
    """Test pooled panel estimator."""
    
    def test_estimate(self, panel_data):
        """Test estimation."""
        spec = ModelSpecification(
            dependent_variable='y',
            independent_variables=['x1', 'x2'],
            fixed_effects=[],
            time_effects=False
        )
        
        model = PooledPanelModel(specification=spec)
        estimator = PooledPanelEstimator()
        
        result = estimator.estimate(model, panel_data)
        
        assert isinstance(result, EstimationResult)
        assert result.coefficients is not None
        assert 'x1' in result.coefficients
        assert 'x2' in result.coefficients
        assert result.standard_errors is not None
        assert result.p_values is not None
        assert result.r_squared > 0
        assert result.n_obs == len(panel_data)
    
    def test_predict(self, panel_data):
        """Test prediction."""
        spec = ModelSpecification(
            dependent_variable='y',
            independent_variables=['x1', 'x2']
        )
        
        model = PooledPanelModel(specification=spec)
        estimator = PooledPanelEstimator()
        
        # Estimate first
        result = estimator.estimate(model, panel_data)
        
        # Predict on new data
        new_data = panel_data.iloc[:10].copy()
        predictions = estimator.predict(model, result, new_data)
        
        assert len(predictions) == len(new_data)
        assert all(isinstance(p, (int, float)) for p in predictions)
    
    def test_robust_standard_errors(self, panel_data):
        """Test robust standard error calculation."""
        spec = ModelSpecification(
            dependent_variable='y',
            independent_variables=['x1', 'x2'],
            clustered_errors='entity_id'
        )
        
        model = PooledPanelModel(specification=spec)
        estimator = PooledPanelEstimator()
        
        result = estimator.estimate(model, panel_data)
        
        # Should have clustered standard errors
        assert result.standard_errors is not None
        assert result.vcov_type == 'clustered'


class TestFixedEffectsEstimator:
    """Test fixed effects estimator."""
    
    def test_entity_fixed_effects(self, panel_data):
        """Test entity fixed effects estimation."""
        spec = ModelSpecification(
            dependent_variable='y',
            independent_variables=['x1', 'x2'],
            fixed_effects=['entity_id']
        )
        
        model = FixedEffectsModel(
            specification=spec,
            entity_effects=True,
            time_effects=False
        )
        estimator = FixedEffectsEstimator()
        
        result = estimator.estimate(model, panel_data)
        
        assert isinstance(result, EstimationResult)
        assert result.coefficients is not None
        assert result.r_squared > 0
        assert result.r_squared_within > 0
        
        # Fixed effects should absorb entity-level variation
        assert 'entity_effects' in result.diagnostics
    
    def test_two_way_fixed_effects(self, panel_data):
        """Test two-way fixed effects."""
        spec = ModelSpecification(
            dependent_variable='y',
            independent_variables=['x1', 'x2'],
            fixed_effects=['entity_id'],
            time_effects=True
        )
        
        model = FixedEffectsModel(
            specification=spec,
            entity_effects=True,
            time_effects=True
        )
        estimator = FixedEffectsEstimator()
        
        result = estimator.estimate(model, panel_data)
        
        assert result.coefficients is not None
        assert 'time_effects' in result.diagnostics
        assert result.diagnostics['time_effects']['included'] is True


class TestVECMEstimator:
    """Test VECM estimator."""
    
    def test_estimate(self, time_series_data):
        """Test VECM estimation."""
        model = VECMModel(
            variables=['y1', 'y2'],
            n_lags=2,
            rank=1,
            deterministic='ci'
        )
        estimator = VECMEstimator()
        
        result = estimator.estimate(model, time_series_data)
        
        assert isinstance(result, EstimationResult)
        assert 'alpha' in result.coefficients
        assert 'beta' in result.coefficients
        assert result.coefficients['alpha'].shape[0] == 2  # n_variables
        assert result.coefficients['beta'].shape[1] == 1   # rank
        assert result.log_likelihood is not None
    
    def test_predict(self, time_series_data):
        """Test VECM prediction."""
        model = VECMModel(
            variables=['y1', 'y2'],
            n_lags=2,
            rank=1
        )
        estimator = VECMEstimator()
        
        # Estimate
        result = estimator.estimate(model, time_series_data)
        
        # Predict next period
        last_obs = time_series_data.iloc[-10:]
        predictions = estimator.predict(model, result, last_obs)
        
        assert len(predictions) == 1  # One-step ahead
        assert len(predictions[0]) == 2  # Two variables
    
    def test_impulse_response(self, time_series_data):
        """Test impulse response calculation."""
        model = VECMModel(
            variables=['y1', 'y2', 'y3'],
            n_lags=2,
            rank=1
        )
        estimator = VECMEstimator()
        
        result = estimator.estimate(model, time_series_data)
        
        # Should have impulse response in diagnostics
        assert 'impulse_response' in result.diagnostics
        irf = result.diagnostics['impulse_response']
        
        assert irf is not None
        assert 'periods' in irf
        assert 'responses' in irf


@pytest.mark.parametrize("estimator_class,model_data", [
    (PooledPanelEstimator, 'panel_data'),
    (FixedEffectsEstimator, 'panel_data'),
    (VECMEstimator, 'time_series_data')
])
def test_estimator_interface_compliance(estimator_class, model_data, request):
    """Test that estimators comply with interface."""
    estimator = estimator_class()
    data = request.getfixturevalue(model_data)
    
    # Check required methods
    assert hasattr(estimator, 'estimate')
    assert hasattr(estimator, 'predict')
    assert hasattr(estimator, 'diagnose')
    
    # All methods should be callable
    assert callable(estimator.estimate)
    assert callable(estimator.predict)
    assert callable(estimator.diagnose)