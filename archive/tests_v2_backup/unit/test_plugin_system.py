"""Test plugin system functionality."""

import pytest
from pathlib import Path

from src.shared.plugins.manager import Plugin<PERSON>anager
from src.shared.plugins.interfaces import PluginMetadata


class TestPluginSystem:
    """Test plugin discovery and loading."""
    
    def test_plugin_manager_initialization(self):
        """Test plugin manager can be initialized."""
        manager = PluginManager(plugin_dirs=[Path("plugins")])
        assert manager is not None
        assert manager.plugin_dirs == [Path("plugins")]
    
    def test_plugin_discovery(self):
        """Test plugin discovery finds expected plugins."""
        manager = PluginManager(plugin_dirs=[Path("plugins")])
        discovered = manager.discover_plugins()
        
        # Check structure
        assert "data_sources" in discovered
        assert "models" in discovered
        assert "outputs" in discovered
        
        # Check specific plugins
        assert "wfp_poc" in discovered["data_sources"]
        assert "world_bank" in discovered["data_sources"]
        assert "custom_vecm" in discovered["models"]
        assert "latex_report" in discovered["outputs"]
    
    def test_load_wfp_plugin(self):
        """Test loading WFP data source plugin."""
        manager = PluginManager(plugin_dirs=[Path("plugins")])
        
        # Load plugin
        plugin = manager.load_plugin("data_sources", "wfp_poc")
        
        # Check metadata
        assert plugin is not None
        metadata = plugin.metadata
        assert isinstance(metadata, PluginMetadata)
        assert metadata.name == "wfp_data_source"
        assert metadata.version == "1.0.0"
        assert metadata.author == "Yemen Market Integration Team"
        
        # Check required methods exist
        assert hasattr(plugin, "initialize")
        assert hasattr(plugin, "validate_config")
        assert hasattr(plugin, "fetch_price_data")
        assert hasattr(plugin, "fetch_market_metadata")
    
    def test_plugin_configuration(self):
        """Test plugin configuration validation."""
        manager = PluginManager(plugin_dirs=[Path("plugins")])
        plugin = manager.load_plugin("data_sources", "wfp_poc")
        
        # Valid config
        valid_config = {
            "data_path": "/tmp/test_data",
            "cache_enabled": True
        }
        assert plugin.validate_config(valid_config) == False  # Path doesn't exist
        
        # Invalid config (missing required field)
        invalid_config = {
            "cache_enabled": True
        }
        assert plugin.validate_config(invalid_config) == False
    
    def test_multiple_plugin_loading(self):
        """Test loading multiple plugins."""
        manager = PluginManager(plugin_dirs=[Path("plugins")])
        
        # Load all plugins
        loaded_count = manager.load_all_plugins()
        
        # Check counts
        assert loaded_count["data_sources"] >= 2  # WFP and World Bank
        assert loaded_count["models"] >= 1  # Custom VECM
        assert loaded_count["outputs"] >= 1  # LaTeX
        
        # List loaded plugins
        loaded = manager.list_plugins()
        assert len(loaded["data_sources"]) >= 2
        assert len(loaded["models"]) >= 1
        assert len(loaded["outputs"]) >= 1
    
    def test_plugin_lifecycle(self):
        """Test plugin load/unload lifecycle."""
        manager = PluginManager(plugin_dirs=[Path("plugins")])
        
        # Load plugin
        plugin = manager.load_plugin("outputs", "latex_report")
        assert plugin is not None
        
        # Check it's registered
        assert manager.get_plugin("outputs", "latex_report") is not None
        
        # Unload plugin
        manager.unload_plugin("outputs", "latex_report")
        
        # Check it's unregistered
        assert manager.get_plugin("outputs", "latex_report") is None
    
    def test_plugin_imports(self):
        """Test that plugin imports work correctly."""
        # Test WFP plugin imports
        from plugins.data_sources.wfp_poc import WFPDataSourcePlugin
        assert WFPDataSourcePlugin is not None
        
        # Test World Bank plugin imports
        from plugins.data_sources.world_bank import WorldBankDataPlugin
        assert WorldBankDataPlugin is not None
        
        # Test VECM plugin imports
        from plugins.models.custom_vecm import CustomVECMPlugin
        assert CustomVECMPlugin is not None
        
        # Test LaTeX plugin imports
        from plugins.outputs.latex_report import LaTeXReportPlugin
        assert LaTeXReportPlugin is not None