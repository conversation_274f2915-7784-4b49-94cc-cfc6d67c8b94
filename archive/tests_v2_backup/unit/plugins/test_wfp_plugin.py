"""
Unit tests for WFP data source plugin.
"""

import json
import tempfile
from datetime import datetime
from pathlib import Path

import pytest
import pandas as pd

from v2.plugins.data_sources.wfp_poc.plugin import WFPDataSourcePlugin
from v2.src.shared.plugins.interfaces import PluginMetadata


class TestWFPDataSourcePlugin:
    """Test suite for WFP data source plugin."""
    
    @pytest.fixture
    def temp_data_dir(self):
        """Create temporary directory with test data."""
        with tempfile.TemporaryDirectory() as tmpdir:
            data_dir = Path(tmpdir)
            
            # Create test CSV data
            csv_content = """date,market,commodity,commodity_name,price,unit,currency,price_type,admin1,admin2,latitude,longitude
2024-01-01,SAN001,WHEAT,Wheat,150,kg,YER,retail,Sana'a,Old City,15.3694,44.1910
2024-01-01,SAN001,RICE,Rice (imported),200,kg,YER,retail,Sana'a,Old City,15.3694,44.1910
2024-01-02,ADE001,WHEAT,Wheat,160,kg,YER,retail,Aden,Crater,12.7855,45.0187
2024-01-02,ADE001,RICE,Rice (imported),210,kg,YER,retail,Aden,Crater,12.7855,45.0187
2024-01-03,SAN001,WHEAT,Wheat,155,kg,YER,retail,Sana'a,Old City,15.3694,44.1910
"""
            price_file = data_dir / "wfp_food_prices.csv"
            price_file.write_text(csv_content)
            
            # Create market metadata
            markets = [
                {
                    "id": "SAN001",
                    "name": "Sana'a Central Market",
                    "admin1": "Sana'a",
                    "admin2": "Old City",
                    "type": "retail",
                    "latitude": 15.3694,
                    "longitude": 44.1910
                },
                {
                    "id": "ADE001",
                    "name": "Aden Main Market",
                    "admin1": "Aden",
                    "admin2": "Crater",
                    "type": "retail",
                    "latitude": 12.7855,
                    "longitude": 45.0187
                }
            ]
            market_file = data_dir / "markets.json"
            market_file.write_text(json.dumps(markets))
            
            # Create commodity metadata
            commodities = [
                {
                    "code": "WHEAT",
                    "name": "Wheat",
                    "category": "Cereals",
                    "unit": "kg"
                },
                {
                    "code": "RICE",
                    "name": "Rice (imported)",
                    "category": "Cereals",
                    "unit": "kg"
                }
            ]
            commodity_file = data_dir / "commodities.json"
            commodity_file.write_text(json.dumps(commodities))
            
            yield data_dir
    
    def test_plugin_metadata(self):
        """Test plugin metadata is correct."""
        plugin = WFPDataSourcePlugin()
        metadata = plugin.metadata
        
        assert isinstance(metadata, PluginMetadata)
        assert metadata.name == "wfp_data_source"
        assert metadata.version == "1.0.0"
        assert "WFP" in metadata.description
        assert "pandas" in metadata.dependencies[0]
    
    def test_initialize_plugin(self, temp_data_dir):
        """Test plugin initialization."""
        plugin = WFPDataSourcePlugin()
        
        config = {
            "data_path": str(temp_data_dir),
            "cache_enabled": True
        }
        
        plugin.initialize(config)
        
        assert plugin.config == config
        assert plugin.data_path == temp_data_dir
        assert len(plugin._market_metadata) == 2
        assert len(plugin._commodity_metadata) == 2
    
    def test_validate_config(self, temp_data_dir):
        """Test configuration validation."""
        plugin = WFPDataSourcePlugin()
        
        # Valid config
        valid_config = {
            "data_path": str(temp_data_dir),
            "cache_enabled": True
        }
        assert plugin.validate_config(valid_config) is True
        
        # Missing required field
        invalid_config = {
            "cache_enabled": True
        }
        assert plugin.validate_config(invalid_config) is False
        
        # Invalid data path
        invalid_config = {
            "data_path": "/non/existent/path"
        }
        assert plugin.validate_config(invalid_config) is False
        
        # Invalid type for optional field
        invalid_config = {
            "data_path": str(temp_data_dir),
            "cache_enabled": "yes"  # Should be boolean
        }
        assert plugin.validate_config(invalid_config) is False
    
    @pytest.mark.asyncio
    async def test_fetch_price_data(self, temp_data_dir):
        """Test fetching price data."""
        plugin = WFPDataSourcePlugin()
        plugin.initialize({"data_path": str(temp_data_dir)})
        
        # Fetch all data
        data = await plugin.fetch_price_data(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31)
        )
        
        assert len(data) == 5
        assert all(isinstance(d, dict) for d in data)
        assert all("price" in d for d in data)
        assert all("market_id" in d for d in data)
        assert all("commodity_code" in d for d in data)
    
    @pytest.mark.asyncio
    async def test_fetch_price_data_with_filters(self, temp_data_dir):
        """Test fetching price data with filters."""
        plugin = WFPDataSourcePlugin()
        plugin.initialize({"data_path": str(temp_data_dir)})
        
        # Filter by market
        data = await plugin.fetch_price_data(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31),
            markets=["SAN001"]
        )
        assert len(data) == 3
        assert all(d["market_id"] == "SAN001" for d in data)
        
        # Filter by commodity
        data = await plugin.fetch_price_data(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31),
            commodities=["WHEAT"]
        )
        assert len(data) == 3
        assert all(d["commodity_code"] == "WHEAT" for d in data)
        
        # Filter by date range
        data = await plugin.fetch_price_data(
            start_date=datetime(2024, 1, 2),
            end_date=datetime(2024, 1, 2)
        )
        assert len(data) == 2
        assert all("2024-01-02" in d["date"] for d in data)
    
    @pytest.mark.asyncio
    async def test_fetch_market_metadata(self, temp_data_dir):
        """Test fetching market metadata."""
        plugin = WFPDataSourcePlugin()
        plugin.initialize({"data_path": str(temp_data_dir)})
        
        markets = await plugin.fetch_market_metadata()
        
        assert len(markets) == 2
        assert markets[0]["market_id"] == "SAN001"
        assert markets[0]["market_name"] == "Sana'a Central Market"
        assert markets[1]["market_id"] == "ADE001"
    
    def test_validate_query(self):
        """Test query validation."""
        plugin = WFPDataSourcePlugin()
        
        # Valid queries
        assert plugin.validate_query({"data_type": "prices"}) is True
        assert plugin.validate_query({
            "data_type": "prices",
            "markets": ["SAN001"],
            "commodities": ["WHEAT"]
        }) is True
        
        # Invalid data type
        assert plugin.validate_query({"data_type": "invalid"}) is False
        
        # Invalid parameter types
        assert plugin.validate_query({
            "data_type": "prices",
            "markets": "SAN001"  # Should be list
        }) is False
    
    def test_transform_data(self, temp_data_dir):
        """Test data transformation to DataFrame."""
        plugin = WFPDataSourcePlugin()
        
        raw_data = [
            {
                "date": "2024-01-01T00:00:00",
                "market_id": "SAN001",
                "commodity_code": "WHEAT",
                "price": 150,
                "metadata": {
                    "admin1": "Sana'a",
                    "latitude": 15.3694
                }
            },
            {
                "date": "2024-01-02T00:00:00",
                "market_id": "ADE001",
                "commodity_code": "RICE",
                "price": 210,
                "metadata": {
                    "admin1": "Aden",
                    "latitude": 12.7855
                }
            }
        ]
        
        df = plugin.transform_data(raw_data)
        
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 2
        assert df['price'].dtype == 'float64'
        assert pd.api.types.is_datetime64_any_dtype(df['date'])
        assert 'admin1' in df.columns  # Metadata extracted
        assert 'latitude' in df.columns
    
    def test_get_available_markets(self, temp_data_dir):
        """Test getting available markets."""
        plugin = WFPDataSourcePlugin()
        plugin.initialize({"data_path": str(temp_data_dir)})
        
        markets = plugin.get_available_markets()
        assert markets == ["SAN001", "ADE001"]
    
    def test_get_available_commodities(self, temp_data_dir):
        """Test getting available commodities."""
        plugin = WFPDataSourcePlugin()
        plugin.initialize({"data_path": str(temp_data_dir)})
        
        commodities = plugin.get_available_commodities()
        assert commodities == ["WHEAT", "RICE"]
    
    def test_lifecycle_methods(self, capsys):
        """Test plugin lifecycle methods."""
        plugin = WFPDataSourcePlugin()
        
        # Test on_load
        plugin.on_load()
        captured = capsys.readouterr()
        assert "loaded" in captured.out
        
        # Test on_unload
        plugin.on_unload()
        captured = capsys.readouterr()
        assert "unloaded" in captured.out