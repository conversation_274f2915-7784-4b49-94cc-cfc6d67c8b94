"""Unit tests for Panel Builder Service."""

import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
import pandas as pd
import numpy as np

from v2.src.application.services.panel_builder_service import (
    PanelBuilderService, PanelConfiguration, PanelCoverage
)
from v2.src.core.domain.market.entities import Market, PriceObservation
from v2.src.core.domain.market.value_objects import (
    MarketId, Commodity, Price, Currency, Coordinates, MarketType
)
from v2.src.core.domain.conflict.entities import ConflictEvent
from v2.src.infrastructure.persistence.unit_of_work import UnitOfWork


@pytest.fixture
def mock_uow():
    """Create mock unit of work."""
    uow = AsyncMock(spec=UnitOfWork)
    uow.__aenter__.return_value = uow
    uow.__aexit__.return_value = None
    return uow


@pytest.fixture
def mock_spatial_service():
    """Create mock spatial service."""
    return MagicMock()


@pytest.fixture
def panel_config():
    """Create panel configuration."""
    return PanelConfiguration(
        start_date=datetime(2019, 1, 1),
        end_date=datetime(2019, 12, 31),
        frequency="M",
        min_coverage_pct=85.0,
        min_markets_per_commodity=20,
        min_commodities_per_market=15,
        interpolation_limit=2,
        seasonal_adjustment=True,
        include_spatial_features=True,
        include_conflict_data=True,
        include_exchange_rates=True
    )


@pytest.fixture
def panel_builder(mock_uow, mock_spatial_service):
    """Create panel builder service."""
    return PanelBuilderService(
        unit_of_work=mock_uow,
        spatial_service=mock_spatial_service,
        cache_enabled=False
    )


@pytest.fixture
def sample_markets():
    """Create sample markets."""
    markets = []
    for i in range(25):
        market = Market(
            market_id=MarketId(f"MARKET_{i}"),
            name=f"Market {i}",
            coordinates=Coordinates(
                latitude=15.0 + i * 0.1,
                longitude=44.0 + i * 0.1
            ),
            market_type=MarketType.RETAIL,
            governorate=f"Gov_{i % 5}",
            district=f"Dist_{i}",
            active_since=datetime(2018, 1, 1)
        )
        markets.append(market)
    return markets


@pytest.fixture
def sample_prices():
    """Create sample price observations."""
    prices = []
    markets = [f"MARKET_{i}" for i in range(25)]
    commodities = ["Wheat", "Rice (Imported)", "Sugar", "Oil (Vegetable)", "Salt"]
    
    for market_id in markets:
        for commodity in commodities:
            for month in range(1, 13):
                price = PriceObservation(
                    market_id=MarketId(market_id),
                    commodity=Commodity(name=commodity, category="food"),
                    price=Price(
                        amount=Decimal("100") + Decimal(str(np.random.normal(0, 10))),
                        currency=Currency.USD,
                        unit="kg"
                    ),
                    observed_date=datetime(2019, month, 15),
                    source="WFP"
                )
                prices.append(price)
    
    return prices


@pytest.fixture
def sample_conflicts():
    """Create sample conflict events."""
    conflicts = []
    for month in range(1, 13):
        for i in range(10):
            conflict = ConflictEvent(
                event_id=f"CONFLICT_{month}_{i}",
                event_date=datetime(2019, month, np.random.randint(1, 28)),
                event_type="battles" if i % 3 == 0 else "explosions",
                coordinates=Coordinates(
                    latitude=15.0 + np.random.normal(0, 0.5),
                    longitude=44.0 + np.random.normal(0, 0.5)
                ),
                fatalities=np.random.randint(0, 10)
            )
            conflicts.append(conflict)
    
    return conflicts


class TestPanelBuilderService:
    """Test Panel Builder Service functionality."""
    
    @pytest.mark.asyncio
    async def test_create_balanced_panel_basic(
        self,
        panel_builder,
        panel_config,
        mock_uow,
        sample_markets,
        sample_prices
    ):
        """Test basic balanced panel creation."""
        # Setup mocks
        price_repo = AsyncMock()
        market_repo = AsyncMock()
        
        # Mock commodity coverage
        commodity_coverage = {
            "Wheat": {"coverage_pct": 90.0, "n_markets": 22},
            "Rice (Imported)": {"coverage_pct": 88.0, "n_markets": 21},
            "Sugar": {"coverage_pct": 92.0, "n_markets": 23},
            "Oil (Vegetable)": {"coverage_pct": 87.0, "n_markets": 20},
            "Salt": {"coverage_pct": 85.0, "n_markets": 20}
        }
        price_repo.get_commodity_coverage.return_value = commodity_coverage
        
        # Mock market coverage
        market_coverage = {}
        for i in range(25):
            market_coverage[f"MARKET_{i}"] = {
                "coverage_pct": 85.0 + i * 0.5,
                "n_commodities": 15 if i < 5 else 16
            }
        price_repo.get_market_coverage.return_value = market_coverage
        
        # Mock price data
        price_repo.get_prices_for_period.return_value = sample_prices
        
        # Mock market data
        market_repo.get_by_ids.return_value = sample_markets
        
        mock_uow.get_repository.side_effect = lambda repo_type: {
            "PriceRepository": price_repo,
            "MarketRepository": market_repo
        }.get(repo_type.__name__, AsyncMock())
        
        # Execute
        panel = await panel_builder.create_balanced_panel(panel_config)
        
        # Verify
        assert isinstance(panel, pd.DataFrame)
        assert not panel.empty
        assert "market_id" in panel.columns
        assert "commodity" in panel.columns
        assert "date" in panel.columns
        assert "price" in panel.columns
        
        # Check dimensions
        assert panel["market_id"].nunique() >= 20  # At least 20 markets
        assert panel["commodity"].nunique() == 5   # All commodities
        assert panel["date"].nunique() == 12       # 12 months
    
    @pytest.mark.asyncio
    async def test_coverage_calculation(self, panel_builder):
        """Test panel coverage calculation."""
        # Create test panel
        panel = pd.DataFrame({
            "market_id": ["M1", "M1", "M2", "M2"] * 3,
            "commodity": ["Wheat", "Rice", "Wheat", "Rice"] * 3,
            "date": pd.date_range("2019-01-01", periods=12, freq="M"),
            "year_month": pd.date_range("2019-01-01", periods=12, freq="M").to_period("M"),
            "price": [100, 200, 150, None, 110, 210, 160, 250, 120, None, 170, 260]
        })
        
        # Calculate coverage
        coverage = panel_builder._calculate_coverage(panel)
        
        # Verify
        assert isinstance(coverage, PanelCoverage)
        assert coverage.total_observations == 12
        assert coverage.markets_count == 2
        assert coverage.commodities_count == 2
        assert coverage.time_periods_count == 12
        assert coverage.missing_observations == 2
        assert coverage.coverage_percentage == pytest.approx(83.33, rel=0.01)
    
    def test_handle_missing_data(self, panel_builder, panel_config):
        """Test missing data handling."""
        # Create panel with missing data
        panel = pd.DataFrame({
            "market_id": ["M1"] * 12 + ["M2"] * 12,
            "commodity": ["Wheat"] * 12 + ["Rice"] * 12,
            "date": list(pd.date_range("2019-01-01", periods=12, freq="M")) * 2,
            "month": list(range(1, 13)) * 2,
            "price": [100, None, None, 130, 140, None, 160, 170, 180, None, 200, 210] * 2
        })
        
        # Handle missing data
        result = panel_builder._handle_missing_data(panel, panel_config)
        
        # Verify interpolation
        assert result["price"].isna().sum() < panel["price"].isna().sum()
        
        # Check interpolation limits
        # Should not interpolate more than 2 consecutive missing values
        m1_wheat = result[(result["market_id"] == "M1") & (result["commodity"] == "Wheat")]
        assert pd.notna(m1_wheat.iloc[1]["price"])  # First missing interpolated
        assert pd.notna(m1_wheat.iloc[2]["price"])  # Second missing interpolated
    
    def test_add_temporal_features(self, panel_builder, panel_config):
        """Test temporal feature addition."""
        # Create simple panel
        panel = pd.DataFrame({
            "market_id": ["M1"] * 12,
            "commodity": ["Wheat"] * 12,
            "date": pd.date_range("2019-01-01", periods=12, freq="M"),
            "month": range(1, 13),
            "price": range(100, 112)
        })
        
        # Add temporal features
        result = panel_builder._add_temporal_features(panel, panel_config)
        
        # Verify features
        assert "price_lag1" in result.columns
        assert "price_lag2" in result.columns
        assert "price_lag3" in result.columns
        assert "price_diff" in result.columns
        assert "price_pct_change" in result.columns
        assert "price_ma3" in result.columns
        assert "price_volatility" in result.columns
        assert "time_trend" in result.columns
        assert "quarter" in result.columns
        assert "is_ramadan" in result.columns
        
        # Check lag values
        assert pd.isna(result.iloc[0]["price_lag1"])
        assert result.iloc[1]["price_lag1"] == 100
        assert result.iloc[2]["price_lag1"] == 101
        
        # Check moving average
        assert result.iloc[2]["price_ma3"] == pytest.approx(101.0, rel=0.01)
    
    @pytest.mark.asyncio
    async def test_add_spatial_features(self, panel_builder):
        """Test spatial feature addition."""
        # Create panel with coordinates
        panel = pd.DataFrame({
            "market_id": ["M1", "M2", "M3"] * 4,
            "commodity": ["Wheat"] * 12,
            "date": list(pd.date_range("2019-01-01", periods=4, freq="M")) * 3,
            "latitude": [15.0, 15.5, 16.0] * 4,
            "longitude": [44.0, 44.5, 45.0] * 4,
            "price": range(100, 112)
        })
        
        # Add spatial features
        result = await panel_builder._add_spatial_features(panel)
        
        # Verify features
        assert "nearest_market_distance" in result.columns
        assert "avg_distance_5_nearest" in result.columns
        
        # Check distance calculations
        assert result[result["market_id"] == "M1"]["nearest_market_distance"].iloc[0] > 0
    
    @pytest.mark.asyncio
    async def test_integrate_conflict_data(
        self,
        panel_builder,
        panel_config,
        mock_uow,
        sample_conflicts
    ):
        """Test conflict data integration."""
        # Create panel
        panel = pd.DataFrame({
            "market_id": ["M1", "M2"] * 12,
            "date": list(pd.date_range("2019-01-01", periods=12, freq="M")) * 2,
            "year_month": list(pd.date_range("2019-01-01", periods=12, freq="M").to_period("M")) * 2,
            "latitude": [15.0, 15.5] * 12,
            "longitude": [44.0, 44.5] * 12
        })
        
        # Setup mock
        conflict_repo = AsyncMock()
        conflict_repo.get_events_for_period.return_value = sample_conflicts
        mock_uow.get_repository.return_value = conflict_repo
        
        # Integrate conflict data
        result = await panel_builder._integrate_conflict_data(panel, panel_config)
        
        # Verify
        assert "events_total" in result.columns
        assert "events_battles" in result.columns
        assert "fatalities_total" in result.columns
        assert "conflict_intensity" in result.columns
        assert "conflict_ma3" in result.columns
        
        # Check values are non-negative
        assert (result["events_total"] >= 0).all()
        assert (result["fatalities_total"] >= 0).all()
    
    def test_calculate_haversine_distance(self, panel_builder):
        """Test Haversine distance calculation."""
        # Test with known coordinates
        # Sana'a to Aden (approximately 320 km)
        lat1 = np.array([15.3694])  # Sana'a
        lon1 = np.array([44.1910])
        lat2 = 12.7855  # Aden
        lon2 = 45.0187
        
        distance = panel_builder._calculate_haversine_distance(lat1, lon1, lat2, lon2)
        
        # Should be approximately 320 km
        assert 300 < distance[0] < 340
    
    @pytest.mark.asyncio
    async def test_create_model_specific_panels(self, panel_builder):
        """Test creation of model-specific panels."""
        # Create base panel
        base_panel = pd.DataFrame({
            "market_id": ["M1", "M2", "M3"] * 12,
            "commodity": ["Wheat", "Rice (Imported)", "Sugar"] * 12,
            "date": list(pd.date_range("2019-01-01", periods=12, freq="M")) * 3,
            "price_usd": range(100, 136),
            "latitude": [15.0, 15.5, 16.0] * 12,
            "longitude": [44.0, 44.5, 45.0] * 12,
            "implied_exchange_rate": [500 + i * 10 for i in range(36)]
        })
        
        # Create model-specific panels
        panels = await panel_builder.create_model_specific_panels(base_panel)
        
        # Verify
        assert isinstance(panels, dict)
        assert "price_transmission" in panels
        assert "exchange_passthrough" in panels
        assert "threshold_coint" in panels
        assert "spatial" in panels
        
        # Check price transmission panel
        if not panels["price_transmission"].empty:
            assert "market1_id" in panels["price_transmission"].columns
            assert "market2_id" in panels["price_transmission"].columns
            assert "price_ratio" in panels["price_transmission"].columns
    
    @pytest.mark.asyncio
    async def test_validate_against_v1(self, panel_builder, tmp_path):
        """Test V1 validation functionality."""
        # Create V1 panel file
        v1_panel = pd.DataFrame({
            "market": ["M1", "M2"] * 6,
            "commodity": ["Wheat", "Rice"] * 6,
            "date": list(pd.date_range("2019-01-01", periods=6, freq="M")) * 2,
            "price": range(100, 112)
        })
        v1_path = tmp_path / "v1_panel.parquet"
        v1_panel.to_parquet(v1_path)
        
        # Create V2 panel
        v2_panel = pd.DataFrame({
            "market_id": ["M1", "M2"] * 6,
            "commodity": ["Wheat", "Rice"] * 6,
            "date": list(pd.date_range("2019-01-01", periods=6, freq="M")) * 2,
            "price": range(100, 112),
            "price_lag1": [None] + list(range(100, 111))
        })
        
        # Validate
        validation = await panel_builder.validate_against_v1(v2_panel, v1_path)
        
        # Verify
        assert "dimension_match" in validation
        assert "coverage_comparison" in validation
        assert "feature_comparison" in validation
        assert validation["dimension_match"]["v1_shape"] == (12, 4)
        assert validation["dimension_match"]["v2_shape"] == (12, 5)
    
    def test_panel_configuration_defaults(self):
        """Test panel configuration default values."""
        config = PanelConfiguration(
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2019, 12, 31)
        )
        
        assert config.frequency == "M"
        assert config.min_coverage_pct == 85.0
        assert config.min_markets_per_commodity == 20
        assert config.min_commodities_per_market == 15
        assert config.interpolation_limit == 2
        assert config.seasonal_adjustment is True
        assert config.include_spatial_features is True
        assert config.include_conflict_data is True
        assert config.include_exchange_rates is True
    
    @pytest.mark.asyncio
    async def test_cache_functionality(self, mock_uow, mock_spatial_service):
        """Test caching functionality."""
        # Create panel builder with cache
        panel_builder = PanelBuilderService(
            unit_of_work=mock_uow,
            spatial_service=mock_spatial_service,
            cache_enabled=True
        )
        
        assert panel_builder._cache is not None
        assert isinstance(panel_builder._cache, dict)