"""Performance benchmarks for v2 implementation."""

import asyncio
import time
from datetime import datetime, timedelta
from typing import List
from uuid import uuid4

import pytest
from memory_profiler import profile

from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import (
    Commodity,
    Coordinates,
    MarketId,
    MarketType,
    Price,
)


class TestDomainPerformance:
    """Benchmark domain model performance."""
    
    def test_entity_creation_speed(self, benchmark):
        """Benchmark entity creation speed."""
        def create_market():
            return Market(
                market_id=MarketId(f"MARKET_{uuid4().hex[:8]}"),
                name="Test Market",
                coordinates=Coordinates(15.0, 44.0),
                market_type=MarketType.RETAIL,
                governorate="Test",
                district="Test",
                active_since=datetime.utcnow()
            )
        
        # Should create at least 10,000 per second
        result = benchmark(create_market)
        assert result is not None
    
    def test_value_object_validation_speed(self, benchmark):
        """Benchmark value object validation speed."""
        def validate_price():
            return Price(
                amount=1234.56,
                currency="YER",
                unit="kg"
            )
        
        # Should validate at least 50,000 per second
        result = benchmark(validate_price)
        assert result is not None
    
    def test_event_handling_speed(self, benchmark):
        """Benchmark domain event handling."""
        market = Market(
            market_id=MarketId("TEST"),
            name="Test",
            coordinates=Coordinates(15.0, 44.0),
            market_type=MarketType.RETAIL,
            governorate="Test",
            district="Test",
            active_since=datetime.utcnow()
        )
        
        def process_events():
            market.clear_events()
            market.deactivate("Test")
            events = market.events
            return len(events)
        
        # Should handle at least 100,000 events per second
        result = benchmark(process_events)
        assert result == 1


class TestRepositoryPerformance:
    """Benchmark repository performance."""
    
    @pytest.mark.asyncio
    async def test_batch_insert_performance(self, test_container, benchmark_async):
        """Benchmark batch insert performance."""
        # Create test data
        observations = []
        commodity = Commodity(
            code="TEST_COMMODITY",
            name="Test",
            category="test",
            standard_unit="kg"
        )
        
        for i in range(10000):
            observations.append(
                PriceObservation(
                    market_id=MarketId("TEST_MARKET"),
                    commodity=commodity,
                    price=Price(amount=100 + i, currency="YER", unit="kg"),
                    observed_date=datetime.utcnow().date(),
                    source="BENCHMARK",
                    observations_count=1
                )
            )
        
        async def batch_insert():
            async with test_container.unit_of_work() as uow:
                await uow.prices.save_batch(observations)
                await uow.commit()
        
        # Should insert 10,000 records in under 5 seconds
        start = time.time()
        await benchmark_async(batch_insert)
        duration = time.time() - start
        assert duration < 5.0
    
    @pytest.mark.asyncio
    async def test_query_performance(self, test_container, setup_large_dataset, benchmark_async):
        """Benchmark query performance."""
        async def complex_query():
            async with test_container.unit_of_work() as uow:
                # Complex aggregation query
                results = await uow.prices.aggregate(
                    start_date=datetime(2023, 1, 1),
                    end_date=datetime(2023, 12, 31),
                    group_by=["market_id", "commodity_code", "month"],
                    aggregations=["avg", "min", "max", "count"]
                )
                return len(results)
        
        # Should complete complex aggregation in under 1 second
        start = time.time()
        result = await benchmark_async(complex_query)
        duration = time.time() - start
        assert duration < 1.0
        assert result > 0


class TestAPIPerformance:
    """Benchmark API endpoint performance."""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, test_client, setup_test_data):
        """Test handling concurrent API requests."""
        async def make_request():
            response = await test_client.get("/api/v1/markets")
            return response.status_code
        
        # Make 100 concurrent requests
        start = time.time()
        tasks = [make_request() for _ in range(100)]
        results = await asyncio.gather(*tasks)
        duration = time.time() - start
        
        # All should succeed
        assert all(status == 200 for status in results)
        
        # Should handle 100 requests in under 2 seconds
        assert duration < 2.0
        
        # Calculate requests per second
        rps = len(results) / duration
        assert rps > 50  # At least 50 requests per second
    
    @pytest.mark.asyncio
    async def test_large_response_handling(self, test_client):
        """Test handling large API responses."""
        # Request 10,000 price records
        start = time.time()
        response = await test_client.get(
            "/api/v1/prices",
            params={"limit": 10000}
        )
        duration = time.time() - start
        
        assert response.status_code == 200
        
        # Should stream large response in under 3 seconds
        assert duration < 3.0
        
        # Check response size
        data = response.json()
        assert len(data["items"]) <= 10000


class TestAnalysisPerformance:
    """Benchmark analysis algorithm performance."""
    
    @pytest.mark.asyncio
    async def test_price_transmission_speed(self, test_container, large_price_dataset):
        """Benchmark price transmission analysis speed."""
        from src.core.domain.market.services import PriceTransmissionService
        
        service = PriceTransmissionService()
        source_prices, target_prices = large_price_dataset
        
        market_pair = MarketPair(
            source=MarketId("SOURCE"),
            target=MarketId("TARGET"),
            distance_km=100.0
        )
        
        start = time.time()
        metrics = service.calculate_transmission(
            source_prices=source_prices,
            target_prices=target_prices,
            market_pair=market_pair
        )
        duration = time.time() - start
        
        # Should analyze 1 year of daily data in under 0.5 seconds
        assert duration < 0.5
        assert metrics.correlation is not None
    
    @pytest.mark.asyncio
    async def test_market_integration_analysis_speed(self, test_container, integration_dataset):
        """Benchmark market integration analysis speed."""
        from src.core.domain.market.services import (
            MarketIntegrationService,
            PriceTransmissionService
        )
        
        transmission_service = PriceTransmissionService()
        service = MarketIntegrationService(transmission_service)
        
        markets, price_observations = integration_dataset
        
        start = time.time()
        metrics = service.analyze_integration(
            markets=markets,
            price_observations=price_observations,
            distance_threshold=200.0,
            correlation_threshold=0.7
        )
        duration = time.time() - start
        
        # Should analyze 20 markets in under 10 seconds
        assert duration < 10.0
        assert metrics.integration_score >= 0


class TestMemoryUsage:
    """Test memory usage patterns."""
    
    @profile
    def test_large_dataset_memory_usage(self):
        """Profile memory usage with large datasets."""
        # Create 100,000 price observations
        observations = []
        commodity = Commodity(
            code="TEST",
            name="Test",
            category="test",
            standard_unit="kg"
        )
        
        for i in range(100000):
            observations.append(
                PriceObservation(
                    market_id=MarketId("TEST"),
                    commodity=commodity,
                    price=Price(amount=100 + i % 1000, currency="YER", unit="kg"),
                    observed_date=(datetime.utcnow() - timedelta(days=i % 365)).date(),
                    source="TEST",
                    observations_count=1
                )
            )
        
        # Memory usage should be reasonable
        # Each observation should use less than 1KB
        import sys
        total_size = sys.getsizeof(observations)
        per_item_size = total_size / len(observations)
        assert per_item_size < 1024  # Less than 1KB per item


class TestConcurrencyPerformance:
    """Test concurrent operation performance."""
    
    @pytest.mark.asyncio
    async def test_concurrent_writes(self, test_container):
        """Test concurrent write performance."""
        async def write_batch(batch_id: int):
            observations = []
            commodity = Commodity(
                code=f"COMMODITY_{batch_id}",
                name=f"Test {batch_id}",
                category="test",
                standard_unit="kg"
            )
            
            for i in range(100):
                observations.append(
                    PriceObservation(
                        market_id=MarketId(f"MARKET_{batch_id}"),
                        commodity=commodity,
                        price=Price(amount=100 + i, currency="YER", unit="kg"),
                        observed_date=datetime.utcnow().date(),
                        source="CONCURRENT_TEST",
                        observations_count=1
                    )
                )
            
            async with test_container.unit_of_work() as uow:
                await uow.prices.save_batch(observations)
                await uow.commit()
        
        # Run 10 concurrent write operations
        start = time.time()
        tasks = [write_batch(i) for i in range(10)]
        await asyncio.gather(*tasks)
        duration = time.time() - start
        
        # Should complete 10 concurrent batches in under 5 seconds
        assert duration < 5.0
        
        # Verify all data was written
        async with test_container.unit_of_work() as uow:
            count = await uow.prices.count(source="CONCURRENT_TEST")
            assert count == 1000  # 10 batches × 100 observations


# Fixtures for performance testing

@pytest.fixture
def benchmark_async(benchmark):
    """Async benchmark fixture."""
    def _benchmark(coro):
        return benchmark(lambda: asyncio.run(coro()))
    return _benchmark


@pytest.fixture
def large_price_dataset():
    """Create large price dataset for benchmarking."""
    commodity = Commodity(
        code="WHEAT_FLOUR",
        name="Wheat Flour",
        category="cereal",
        standard_unit="kg"
    )
    
    source_prices = []
    target_prices = []
    
    # Create 365 days of price data
    base_date = datetime(2023, 1, 1)
    
    for i in range(365):
        date = base_date + timedelta(days=i)
        
        # Source market
        source_prices.append(
            PriceObservation(
                market_id=MarketId("SOURCE"),
                commodity=commodity,
                price=Price(
                    amount=1000 + (i % 50),
                    currency="YER",
                    unit="kg"
                ),
                observed_date=date.date(),
                source="BENCHMARK"
            )
        )
        
        # Target market (correlated)
        target_prices.append(
            PriceObservation(
                market_id=MarketId("TARGET"),
                commodity=commodity,
                price=Price(
                    amount=1050 + (i % 50),
                    currency="YER",
                    unit="kg"
                ),
                observed_date=date.date(),
                source="BENCHMARK"
            )
        )
    
    return source_prices, target_prices


@pytest.fixture
def integration_dataset():
    """Create market integration dataset for benchmarking."""
    # Create 20 markets
    markets = []
    for i in range(20):
        markets.append(
            Market(
                market_id=MarketId(f"MARKET_{i}"),
                name=f"Market {i}",
                coordinates=Coordinates(15.0 + i * 0.1, 44.0 + i * 0.1),
                market_type=MarketType.RETAIL,
                governorate=f"Gov{i % 5}",
                district=f"District{i}",
                active_since=datetime(2020, 1, 1)
            )
        )
    
    # Create price observations
    price_observations = {}
    commodity = Commodity(
        code="TEST",
        name="Test",
        category="test",
        standard_unit="kg"
    )
    
    for market in markets:
        observations = []
        for day in range(30):
            observations.append(
                PriceObservation(
                    market_id=market.market_id,
                    commodity=commodity,
                    price=Price(
                        amount=1000 + (day * 10),
                        currency="YER",
                        unit="kg"
                    ),
                    observed_date=(datetime.utcnow() - timedelta(days=day)).date(),
                    source="BENCHMARK"
                )
            )
        price_observations[market.market_id] = observations
    
    return markets, price_observations


@pytest.fixture
async def setup_large_dataset(test_container):
    """Set up large dataset for query benchmarking."""
    # Would create 1 million+ records for realistic benchmarking
    pass