"""Tests for price dynamics visualization module.

This module contains comprehensive tests for the price dynamics visualization tools,
ensuring robust visualization capabilities according to World Bank econometric standards.
"""

import pytest
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import geopandas as gpd
from shapely.geometry import Polygon
from datetime import datetime

from yemen_market.visualization.price_dynamics import PriceDynamicsVisualizer


class TestPriceDynamicsVisualizer:
    """Test suite for PriceDynamicsVisualizer class."""

    @pytest.fixture
    def setup_and_teardown(self):
        """Setup and teardown for each test."""
        # Store original plt functions to restore after test
        original_show = plt.show
        original_savefig = plt.savefig
        original_style_use = plt.style.use

        # Replace with mock functions
        plt.show = MagicMock()
        plt.savefig = MagicMock()
        plt.style.use = MagicMock()

        yield

        # Restore original functions
        plt.show = original_show
        plt.savefig = original_savefig
        plt.style.use = original_style_use
        plt.close('all')  # Close all figures

    @pytest.fixture
    def visualizer(self, setup_and_teardown):
        """Create a PriceDynamicsVisualizer instance for testing."""
        return PriceDynamicsVisualizer(style='seaborn-v0_8-whitegrid', figsize=(10, 6), dpi=72)

    @pytest.fixture
    def sample_price_data(self):
        """Create sample price data for testing."""
        # Create date range
        dates = pd.date_range('2020-01-01', periods=24, freq='M')

        # Create markets and commodities
        markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Mukalla']
        commodities = ['Wheat', 'Rice', 'Sugar', 'Oil']

        # Create price data
        data = []

        np.random.seed(42)  # For reproducibility

        for date in dates:
            for market in markets:
                for commodity in commodities:
                    # Base price
                    base_price = {
                        'Wheat': 500,
                        'Rice': 650,
                        'Sugar': 350,
                        'Oil': 750
                    }[commodity]

                    # Market effect (some markets have higher prices)
                    market_effect = {
                        'Sana\'a': 1.0,
                        'Aden': 0.9,
                        'Taiz': 1.1,
                        'Hodeidah': 0.95,
                        'Mukalla': 1.05
                    }[market]

                    # Time trend and seasonality
                    time_idx = (date - pd.to_datetime('2020-01-01')).days / 30  # Months
                    trend = 1.0 + 0.02 * time_idx  # 2% monthly increase
                    seasonality = 1.0 + 0.1 * np.sin(time_idx * np.pi / 6)  # Annual cycle

                    # Random variation
                    noise = np.random.normal(1.0, 0.05)

                    # Calculate price
                    price = base_price * market_effect * trend * seasonality * noise

                    # Control zone assignment
                    if market in ['Sana\'a', 'Hodeidah']:
                        control_zone = 'Houthi'
                    elif market in ['Aden', 'Mukalla']:
                        control_zone = 'Government'
                    else:
                        control_zone = 'Contested'

                    # Add coordinates
                    coordinates = {
                        'Sana\'a': (44.2, 15.3),
                        'Aden': (45.0, 12.8),
                        'Taiz': (44.0, 13.6),
                        'Hodeidah': (42.9, 14.8),
                        'Mukalla': (49.1, 14.5)
                    }

                    longitude, latitude = coordinates[market]

                    # Create row
                    data.append({
                        'date': date,
                        'year_month': date.strftime('%Y-%m'),
                        'market_name': market,
                        'commodity': commodity,
                        'price': price,
                        'control_zone': control_zone,
                        'longitude': longitude,
                        'latitude': latitude,
                        'district_pcode': f"YE{markets.index(market):02d}"
                    })

        # Create DataFrame
        df = pd.DataFrame(data)

        # Add exchange rate data (USD to YER)
        exchange_data = []
        base_rate = 250  # YER per USD

        for date in dates:
            time_idx = (date - pd.to_datetime('2020-01-01')).days / 30

            # Different rates for different control zones
            for zone in ['Houthi', 'Government']:
                # Time trend
                if zone == 'Houthi':
                    trend = 1.0 + 0.03 * time_idx  # 3% monthly increase
                else:
                    trend = 1.0 + 0.025 * time_idx  # 2.5% monthly increase

                # Add some randomness
                noise = np.random.normal(1.0, 0.02)

                # Calculate rate
                rate = base_rate * trend * noise

                exchange_data.append({
                    'date': date,
                    'control_zone': zone,
                    'exchange_rate': rate
                })

        exchange_df = pd.DataFrame(exchange_data)

        return df, exchange_df

    @pytest.fixture
    def sample_gdf(self):
        """Create a sample GeoDataFrame for testing."""
        # Create simple geometries for testing
        geometries = [
            Polygon([(43, 14), (45, 14), (45, 16), (43, 16)]),  # Sana'a area
            Polygon([(44, 12), (46, 12), (46, 14), (44, 14)]),  # Aden area
            Polygon([(43, 13), (45, 13), (45, 15), (43, 15)]),  # Taiz area
            Polygon([(42, 14), (44, 14), (44, 16), (42, 16)]),  # Hodeidah area
            Polygon([(48, 14), (50, 14), (50, 16), (48, 16)])   # Mukalla area
        ]

        # Create GeoDataFrame
        data = {
            'pcode': [f"YE{i:02d}" for i in range(5)],
            'name': ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Mukalla'],
            'geometry': geometries
        }

        return gpd.GeoDataFrame(data, geometry='geometry')

    # Tests for initialization
    def test_initialization(self, visualizer):
        """Test the initialization of PriceDynamicsVisualizer."""
        assert visualizer.style == 'seaborn-v0_8-whitegrid'
        assert visualizer.figsize == (10, 6)
        assert visualizer.dpi == 72

        # Check color palettes
        assert 'Houthi' in visualizer.control_zone_colors
        assert 'Wheat' in visualizer.commodity_colors

        # Check style was set
        plt.style.use.assert_called_once_with('seaborn-v0_8-whitegrid')

    # Tests for plot_price_series
    def test_plot_price_series_basic(self, visualizer, sample_price_data):
        """Test basic functionality of plot_price_series."""
        price_data, _ = sample_price_data

        # Call the function
        fig = visualizer.plot_price_series(price_data, commodity='Wheat')

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_price_series_with_markets(self, visualizer, sample_price_data):
        """Test plot_price_series with specific markets."""
        price_data, _ = sample_price_data

        # Call with specific markets
        fig = visualizer.plot_price_series(
            price_data,
            commodity='Rice',
            markets=['Sana\'a', 'Aden']
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_price_series_with_date_range(self, visualizer, sample_price_data):
        """Test plot_price_series with date range filters."""
        price_data, _ = sample_price_data

        # Call with date range
        fig = visualizer.plot_price_series(
            price_data,
            commodity='Sugar',
            start_date='2020-06-01',
            end_date='2021-06-01'
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_price_series_without_events(self, visualizer, sample_price_data):
        """Test plot_price_series without event markers."""
        price_data, _ = sample_price_data

        # Call without events
        fig = visualizer.plot_price_series(
            price_data,
            commodity='Oil',
            show_events=False
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_price_series_empty_data(self, visualizer):
        """Test plot_price_series with empty data."""
        # Create empty DataFrame with correct columns
        empty_data = pd.DataFrame(columns=[
            'date', 'market_name', 'commodity', 'price'
        ])

        # Call function - should handle empty data gracefully
        fig = visualizer.plot_price_series(empty_data, commodity='Wheat')

        # Function should still return a figure or None for empty data
        assert fig is None or isinstance(fig, plt.Figure)

    # Tests for plot_exchange_differential
    def test_plot_exchange_differential_basic(self, visualizer, sample_price_data):
        """Test basic functionality of plot_exchange_differential."""
        _, exchange_data = sample_price_data

        # Call the function
        fig = visualizer.plot_exchange_differential(exchange_data)

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_exchange_differential_without_ma(self, visualizer, sample_price_data):
        """Test exchange differential plotting without moving average."""
        _, exchange_data = sample_price_data

        # Call without moving average
        fig = visualizer.plot_exchange_differential(
            exchange_data,
            show_ma=False
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_exchange_differential_custom_ma(self, visualizer, sample_price_data):
        """Test exchange differential plotting with custom MA window."""
        _, exchange_data = sample_price_data

        # Call with custom MA window
        fig = visualizer.plot_exchange_differential(
            exchange_data,
            show_ma=True,
            ma_window=10
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_exchange_differential_missing_zone(self, visualizer, sample_price_data):
        """Test exchange differential with missing control zone."""
        _, exchange_data = sample_price_data

        # Remove one zone
        filtered_data = exchange_data[exchange_data['control_zone'] != 'Houthi']

        # Call function - should handle missing zone
        fig = visualizer.plot_exchange_differential(filtered_data)

        # Function should still return a figure
        assert isinstance(fig, plt.Figure)

    # Tests for plot_spatial_price_map
    def test_plot_spatial_price_map_basic(self, visualizer, sample_price_data, sample_gdf):
        """Test basic functionality of spatial price map."""
        price_data, _ = sample_price_data

        # Call the function
        fig = visualizer.plot_spatial_price_map(
            price_data,
            boundaries=sample_gdf,
            commodity='Wheat',
            date='2020-01'
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_spatial_price_map_without_markets(self, visualizer, sample_price_data, sample_gdf):
        """Test spatial price map without market locations."""
        price_data, _ = sample_price_data

        # Call without showing markets
        fig = visualizer.plot_spatial_price_map(
            price_data,
            boundaries=sample_gdf,
            commodity='Rice',
            date='2020-06',
            show_markets=False
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_spatial_price_map_empty_data(self, visualizer, sample_price_data, sample_gdf):
        """Test spatial price map with no data for given parameters."""
        price_data, _ = sample_price_data

        # Call with a date that doesn't exist in the data
        fig = visualizer.plot_spatial_price_map(
            price_data,
            boundaries=sample_gdf,
            commodity='Wheat',
            date='2025-01'  # Future date not in dataset
        )

        # Function should return None for no data
        assert fig is None

    # Tests for plot_price_convergence
    def test_plot_price_convergence_basic(self, visualizer, sample_price_data):
        """Test basic functionality of price convergence plotting."""
        price_data, _ = sample_price_data

        # Call the function
        fig = visualizer.plot_price_convergence(
            price_data,
            commodity='Wheat',
            reference_market='Sana\'a',
            comparison_markets=['Aden', 'Taiz']
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_price_convergence_single_market(self, visualizer, sample_price_data):
        """Test price convergence with a single comparison market."""
        price_data, _ = sample_price_data

        # Call with single comparison market
        fig = visualizer.plot_price_convergence(
            price_data,
            commodity='Rice',
            reference_market='Hodeidah',
            comparison_markets=['Mukalla']
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_price_convergence_missing_market(self, visualizer, sample_price_data):
        """Test price convergence with missing markets."""
        price_data, _ = sample_price_data

        # Call with a market not in the data
        fig = visualizer.plot_price_convergence(
            price_data,
            commodity='Sugar',
            reference_market='Sana\'a',
            comparison_markets=['NonExistentMarket']
        )

        # Function should still return a figure
        assert isinstance(fig, plt.Figure)

    # Tests for plot_structural_breaks
    def test_plot_structural_breaks_basic(self, visualizer, sample_price_data):
        """Test basic functionality of structural breaks plotting."""
        _, exchange_data = sample_price_data

        # Aggregate to single time series
        ts_data = exchange_data[exchange_data['control_zone'] == 'Government'].groupby('date')['exchange_rate'].mean().reset_index()

        # Call the function
        fig = visualizer.plot_structural_breaks(ts_data)

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_structural_breaks_with_breakpoints(self, visualizer, sample_price_data):
        """Test structural breaks with known breakpoints."""
        _, exchange_data = sample_price_data

        # Aggregate to single time series
        ts_data = exchange_data[exchange_data['control_zone'] == 'Houthi'].groupby('date')['exchange_rate'].mean().reset_index()

        # Call with breakpoints
        fig = visualizer.plot_structural_breaks(
            ts_data,
            breakpoints=['2020-06-01', '2021-01-01']
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    def test_plot_structural_breaks_custom_series(self, visualizer, sample_price_data):
        """Test structural breaks with custom series name."""
        price_data, _ = sample_price_data

        # Create price index series
        wheat_data = price_data[price_data['commodity'] == 'Wheat'].groupby('date')['price'].mean().reset_index()
        wheat_data.rename(columns={'price': 'wheat_price'}, inplace=True)

        # Call with custom series name
        fig = visualizer.plot_structural_breaks(
            wheat_data,
            series_name='wheat_price'
        )

        # Check result is a figure
        assert isinstance(fig, plt.Figure)

    # Tests for _add_event_markers helper method
    def test_add_event_markers(self, visualizer):
        """Test the _add_event_markers helper method."""
        # Create a figure and axes
        fig, ax = plt.subplots()

        # Create date range
        start_date = pd.to_datetime('2019-01-01')
        end_date = pd.to_datetime('2022-01-01')

        # Call the method
        visualizer._add_event_markers(ax, start_date, end_date)

        # Method should add vertical lines without error
        # Not much to assert here as it's a visual helper
        assert True

    # Tests for save_figure method
    def test_save_figure(self, visualizer):
        """Test the save_figure method."""
        # Create a simple figure
        fig, ax = plt.subplots()
        ax.plot([1, 2, 3], [1, 2, 3])

        with tempfile.TemporaryDirectory() as tmpdirname:
            save_path = Path(tmpdirname) / "test_figure"

            # Call the method
            visualizer.save_figure(fig, save_path, formats=['png', 'pdf'])

            # savefig should be called twice (once for each format)
            assert plt.savefig.call_count == 2

    def test_save_figure_single_format(self, visualizer):
        """Test save_figure with a single format."""
        # Create a simple figure
        fig, ax = plt.subplots()
        ax.plot([1, 2, 3], [1, 2, 3])

        with tempfile.TemporaryDirectory() as tmpdirname:
            save_path = Path(tmpdirname) / "test_figure"

            # Call with single format
            visualizer.save_figure(fig, save_path, formats=['png'])

            # savefig should be called once
            assert plt.savefig.call_count == 1
