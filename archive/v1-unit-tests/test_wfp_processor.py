"""Unit tests for WFP data processor."""

from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch, mock_open
import json

import numpy as np
import pandas as pd
import pytest

from yemen_market.data.wfp_processor import WFPProcessor


@pytest.fixture
def sample_wfp_data():
    """Create sample WFP data for testing."""
    dates = pd.date_range('2023-01-01', periods=12, freq='M')
    
    data = []
    # Create data for multiple markets and commodities
    for date in dates:
        for market, gov in [("Sana'a City", "Sana'a"), ("Aden City", "Aden"), ("Taizz City", "Taizz")]:
            for commodity in ["Wheat", "Rice", "Sugar"]:
                price_local = np.random.uniform(500, 1500)
                exchange_rate = 1500 if gov == "Sana'a" else 1000  # Different rates by zone
                price_usd = price_local / exchange_rate
                
                data.append({
                    'date': date,
                    'admin1': gov,
                    'admin2': market,
                    'market': market,
                    'latitude': np.random.uniform(12, 16),
                    'longitude': np.random.uniform(43, 46),
                    'category': 'Food',
                    'commodity': commodity,
                    'unit': 'KG',
                    'priceflag': 'actual',
                    'pricetype': 'Retail',
                    'currency': 'YER',
                    'price': price_local,
                    'usdprice': price_usd
                })
    
    return pd.DataFrame(data)


@pytest.fixture
def wfp_processor():
    """Create WFP processor instance."""
    return WFPProcessor(
        commodities=['Wheat', 'Rice', 'Sugar'],
        start_date='2023-01-01',
        end_date='2023-12-31'
    )


class TestWFPProcessor:
    """Test WFP processor functionality."""
    
    def test_initialization(self):
        """Test processor initialization."""
        processor = WFPProcessor()
        
        assert processor.commodities is not None
        assert processor.start_date is not None
        assert processor.end_date is not None
    
    def test_process_raw_data(self, wfp_processor, sample_wfp_data):
        """Test raw data processing."""
        processed = wfp_processor.process_raw_data(sample_wfp_data)
        
        # Check column renaming
        assert 'governorate' in processed.columns
        assert 'market_name' in processed.columns
        assert 'price_local' in processed.columns
        assert 'price_usd' in processed.columns
        
        # Check date conversion
        assert pd.api.types.is_datetime64_any_dtype(processed['date'])
        
        # Check year_month column
        assert 'year_month' in processed.columns
        assert processed['year_month'].dtype == 'period[M]'
        
        # Check data filtering
        assert len(processed) > 0
        assert processed['date'].min() >= wfp_processor.start_date
        assert processed['date'].max() <= wfp_processor.end_date
    
    def test_extract_exchange_rates(self, wfp_processor, sample_wfp_data):
        """Test exchange rate extraction."""
        processed = wfp_processor.process_raw_data(sample_wfp_data)
        exchange_rates = wfp_processor.extract_exchange_rates(processed)
        
        # Check output structure
        assert 'exchange_rate' in exchange_rates.columns
        assert 'control_zone' in exchange_rates.columns
        assert 'n_observations' in exchange_rates.columns
        
        # Check zone classification
        assert set(exchange_rates['control_zone'].unique()).issubset(
            {'Houthi', 'Government', 'Contested'}
        )
        
        # Check Sana'a is classified as Houthi
        sanaa_data = exchange_rates[exchange_rates['governorate'] == "Sana'a"]
        if not sanaa_data.empty:
            assert sanaa_data['control_zone'].iloc[0] == 'Houthi'
        
        # Check Aden is classified as Government
        aden_data = exchange_rates[exchange_rates['governorate'] == "Aden"]
        if not aden_data.empty:
            assert aden_data['control_zone'].iloc[0] == 'Government'
    
    def test_calculate_price_indices(self, wfp_processor, sample_wfp_data):
        """Test price index calculation."""
        processed = wfp_processor.process_raw_data(sample_wfp_data)
        price_data, composite_index = wfp_processor.calculate_price_indices(processed)
        
        # Check price index calculation
        assert 'price_index' in price_data.columns
        assert 'base_price' in price_data.columns
        
        # Check composite index
        assert 'composite_index' in composite_index.columns
        assert len(composite_index) > 0
        
        # Price indices should be around 100 for base period
        base_period = price_data['year_month'].min()
        base_indices = price_data[price_data['year_month'] == base_period]['price_index']
        assert np.abs(base_indices.mean() - 100) < 25  # Allow variance due to random test data
    
    def test_create_market_panel(self, wfp_processor, sample_wfp_data):
        """Test panel dataset creation."""
        processed = wfp_processor.process_raw_data(sample_wfp_data)
        panel = wfp_processor.create_market_panel(processed)
        
        # Check panel structure
        required_cols = ['year_month', 'governorate', 'market_name', 
                         'exchange_rate', 'control_zone', 'composite_index']
        for col in required_cols:
            assert col in panel.columns
        
        # Check time variables
        assert 'year' in panel.columns
        assert 'month' in panel.columns
        assert 'quarter' in panel.columns
        
        # Check exchange rate differential
        if 'exchange_diff_pct' in panel.columns:
            assert panel['exchange_diff_pct'].notna().any()
    
    def test_handle_missing_data_interpolate(self, wfp_processor):
        """Test missing data handling with interpolation."""
        # Create data with missing values
        dates = pd.date_range('2023-01-01', periods=6, freq='M')
        data = pd.DataFrame({
            'year_month': pd.PeriodIndex(dates, freq='M'),
            'market_name': ['Market A'] * 6,
            'exchange_rate': [100, np.nan, np.nan, 130, 140, 150],
            'composite_index': [100, 105, np.nan, 115, 120, 125]
        })
        
        result = wfp_processor.handle_missing_data(data, method='interpolate')
        
        # Check interpolation worked
        assert result['exchange_rate'].notna().all()
        assert result['composite_index'].notna().all()
        
        # Check interpolated values are reasonable
        assert 100 < result['exchange_rate'].iloc[1] < 130
        assert 105 < result['composite_index'].iloc[2] < 115
    
    def test_handle_missing_data_drop(self, wfp_processor):
        """Test missing data handling by dropping."""
        # Create data with varying missing rates
        data = pd.DataFrame({
            'market_name': ['Market A'] * 10 + ['Market B'] * 10,
            'exchange_rate': [100] * 5 + [np.nan] * 5 + [200] * 8 + [np.nan] * 2,
            'year_month': pd.PeriodIndex(pd.date_range('2023-01-01', periods=20, freq='M'), freq='M')
        })
        
        result = wfp_processor.handle_missing_data(data, method='drop')
        
        # Market A should be dropped (50% missing), Market B kept (20% missing)
        assert 'Market A' not in result['market_name'].values
        assert 'Market B' in result['market_name'].values
    
    def test_generate_summary_statistics(self, wfp_processor, sample_wfp_data):
        """Test summary statistics generation."""
        processed = wfp_processor.process_raw_data(sample_wfp_data)
        panel = wfp_processor.create_market_panel(processed)
        
        summary = wfp_processor.generate_summary_statistics(panel)
        
        # Check summary structure
        assert 'Category' in summary.columns
        assert 'Metric' in summary.columns
        assert 'Value' in summary.columns
        
        # Check key metrics are included
        metrics = summary['Metric'].values
        assert 'Total observations' in metrics
        assert 'Number of markets' in metrics
        assert 'Time period' in metrics
    
    def test_process_wfp_data_integration(self, wfp_processor, sample_wfp_data, tmp_path, monkeypatch):
        """Test full processing pipeline."""
        # Monkey patch the data directories
        monkeypatch.setattr('yemen_market.data.wfp_processor.PROCESSED_DATA_DIR', tmp_path / 'processed')
        monkeypatch.setattr('yemen_market.data.wfp_processor.INTERIM_DATA_DIR', tmp_path / 'interim')
        
        # Run full pipeline
        panel, exchange_rates, price_indices = wfp_processor.process_wfp_data(sample_wfp_data)
        
        # Check outputs
        assert isinstance(panel, pd.DataFrame)
        assert isinstance(exchange_rates, pd.DataFrame)
        assert isinstance(price_indices, pd.DataFrame)
        
        # Check files were saved
        assert (tmp_path / 'processed' / 'wfp_market_panel.parquet').exists()
        assert (tmp_path / 'interim' / 'exchange_rates.parquet').exists()
        assert (tmp_path / 'interim' / 'price_indices.parquet').exists()
        assert (tmp_path / 'processed' / 'wfp_summary_stats.csv').exists()
    
    def test_zone_classification(self, wfp_processor):
        """Test governorate to control zone classification."""
        # Test Houthi governorates
        assert "Sana'a" in wfp_processor.HOUTHI_GOVERNORATES
        assert "Sa'ada" in wfp_processor.HOUTHI_GOVERNORATES
        
        # Test Government governorates
        assert "Aden" in wfp_processor.GOVERNMENT_GOVERNORATES
        assert "Hadramaut" in wfp_processor.GOVERNMENT_GOVERNORATES
        
        # Test classification logic
        data = pd.DataFrame({
            'governorate': ["Sana'a", "Aden", "Taizz"],
            'exchange_rate': [1500, 1000, 1200]
        })
        
        data['control_zone'] = data['governorate'].apply(
            lambda x: 'Houthi' if x in wfp_processor.HOUTHI_GOVERNORATES 
            else 'Government' if x in wfp_processor.GOVERNMENT_GOVERNORATES 
            else 'Contested'
        )
        
        assert data[data['governorate'] == "Sana'a"]['control_zone'].iloc[0] == 'Houthi'
        assert data[data['governorate'] == "Aden"]['control_zone'].iloc[0] == 'Government'
        assert data[data['governorate'] == "Taizz"]['control_zone'].iloc[0] == 'Contested'
    
    # Enhanced functionality tests
    def test_governorate_name_standardization(self, wfp_processor):
        """Test governorate name mappings for pcode standardization."""
        # Test all mappings
        assert wfp_processor.GOVERNORATE_MAPPINGS["Al Dhale'e"] == "Ad Dale'"
        assert wfp_processor.GOVERNORATE_MAPPINGS["Al Hudaydah"] == "Al Hodeidah"
        assert wfp_processor.GOVERNORATE_MAPPINGS["Amanat Al Asimah"] == "Sana'a City"
        assert wfp_processor.GOVERNORATE_MAPPINGS["Hadramaut"] == "Hadramawt"
        assert wfp_processor.GOVERNORATE_MAPPINGS["Sa'ada"] == "Sa'dah"
        assert wfp_processor.GOVERNORATE_MAPPINGS["Taizz"] == "Ta'iz"
    
    @patch('pandas.read_excel')
    def test_load_pcode_mappings_success(self, mock_read_excel, wfp_processor):
        """Test successful pcode loading."""
        gov_pcodes = pd.DataFrame({
            'Eng_Name': ['Ad Dale\'', 'Ta\'iz', 'Hadramawt', 'Al Hodeidah', 'Sa\'dah'],
            'Gov_Pcode': ['YE01', 'YE02', 'YE03', 'YE04', 'YE05']
        })
        dist_pcodes = pd.DataFrame({
            'Gov_Name': ['Ad Dale\''] * 2,
            'Eng_Name': ['District1', 'District2'],
            'Dis_Pcode': ['YE0101', 'YE0102']
        })
        
        mock_read_excel.side_effect = [gov_pcodes, dist_pcodes]
        
        # Manually call _load_pcode_mappings
        wfp_processor._load_pcode_mappings()
        
        assert wfp_processor.gov_pcodes is not None
        assert len(wfp_processor.gov_to_pcode) == 5
        assert wfp_processor.gov_to_pcode['Ad Dale\''] == 'YE01'
        assert mock_read_excel.call_count == 2
    
    def test_analyze_commodity_coverage(self, wfp_processor):
        """Test commodity coverage analysis."""
        # Create data where Wheat appears in all 3 markets, Rice in 2, and Rare Item in 1
        df = pd.DataFrame({
            'commodity': (['Wheat'] * 9 +  # 3 markets x 3 observations
                          ['Rice'] * 6 +   # 2 markets x 3 observations  
                          ['Rare Item'] * 2),  # 1 market x 2 observations
            'market_id': (['Market_A', 'Market_B', 'Market_C'] * 3 +
                          ['Market_A', 'Market_B'] * 3 +
                          ['Market_A'] * 2)
        })
        
        coverage = wfp_processor._analyze_commodity_coverage(df)
        
        assert 'Wheat' in coverage
        assert 'Rice' in coverage
        assert 'Rare Item' in coverage
        assert coverage['Wheat'] > coverage['Rice']
        assert coverage['Rice'] > coverage['Rare Item']
    
    def test_process_price_data_method(self, wfp_processor, tmp_path):
        """Test the enhanced process_price_data method."""
        # Create test CSV file
        test_data = pd.DataFrame({
            'date': ['2023-01-15', '2023-02-15', '2023-03-15'],
            'admin1': ['Al Dhale\'e', 'Taizz', 'Hadramaut'],
            'admin2': ['District1', 'District2', 'District3'],
            'market': ['Market A', 'Market B', 'Market C'],
            'commodity': ['Wheat', 'Rice (imported)', 'Exchange rate (unofficial)'],
            'price': [100, 200, 500],
            'usdprice': [0.2, 0.4, 1.0],
            'latitude': [14.0, 14.5, 15.0],
            'longitude': [44.0, 44.5, 45.0],
            'unit': ['KG', 'KG', 'LCU'],
            'currency': ['YER', 'YER', 'YER'],
            'pricetype': ['Retail', 'Retail', 'Retail']
        })
        
        test_csv = tmp_path / "test_wfp.csv"
        test_data.to_csv(test_csv, index=False)
        
        with patch.object(wfp_processor, '_load_pcode_mappings'):
            commodity_df, exchange_df = wfp_processor.process_price_data(str(test_csv))
        
        # Check results
        assert not commodity_df.empty
        assert 'governorate' in commodity_df.columns
        assert 'market_id' in commodity_df.columns
        
        # Check exchange rates were separated
        if not exchange_df.empty:
            assert 'rate_differential' in exchange_df.columns
            assert 'rate_premium' in exchange_df.columns
    
    def test_create_smart_panels(self, wfp_processor):
        """Test smart panel creation that respects commodity availability."""
        # Create test data with different commodities in different markets
        # Create dates on 15th of month to match smart panel generation
        dates = (
            [pd.Timestamp('2023-01-15'), pd.Timestamp('2023-02-15'), pd.Timestamp('2023-03-15')] +
            [pd.Timestamp('2023-01-15'), pd.Timestamp('2023-02-15'), pd.Timestamp('2023-03-15')] +
            [pd.Timestamp('2023-01-15'), pd.Timestamp('2023-02-15')] +
            [pd.Timestamp('2023-01-15'), pd.Timestamp('2023-02-15')]
        )
        
        commodity_df = pd.DataFrame({
            'commodity': ['Wheat'] * 6 + ['Rice'] * 4,
            'market_id': ['Market_A'] * 3 + ['Market_B'] * 3 + ['Market_A'] * 2 + ['Market_C'] * 2,
            'date': dates,
            'governorate': ['Aden'] * 10,
            'district': ['District1'] * 10,
            'market_name': ['Market A'] * 3 + ['Market B'] * 3 + ['Market A'] * 2 + ['Market C'] * 2,
            'lat': [14.0] * 10,
            'lon': [44.0] * 10,
            'price_usd': [1.0] * 10,
            'price_local': [500] * 10,
            'gov_pcode': ['YE09'] * 10
        })
        
        wfp_processor.commodities = ['Wheat', 'Rice']
        panel = wfp_processor.create_smart_panels(commodity_df)
        
        # Check that panel respects commodity-market availability
        assert not panel.empty
        wheat_markets = panel[panel['commodity'] == 'Wheat']['market_id'].unique()
        rice_markets = panel[panel['commodity'] == 'Rice']['market_id'].unique()
        
        # Wheat should be in 2 markets, Rice in 2 markets
        assert len(wheat_markets) == 2
        assert len(rice_markets) == 2
        
        # Check coverage calculation
        assert 'price_usd' in panel.columns
        coverage = panel['price_usd'].notna().sum() / len(panel) * 100
        assert coverage > 0
    
    @patch('pandas.DataFrame.to_parquet')
    @patch('builtins.open', new_callable=mock_open)
    @patch('json.dump')
    def test_save_enhanced_summary_stats(self, mock_json_dump, mock_file, mock_to_parquet, wfp_processor, tmp_path):
        """Test enhanced summary statistics saving."""
        # Create test dataframes
        commodity_df = pd.DataFrame({
            'commodity': ['Wheat'],
            'price_local': [100],
            'price_usd': [0.2],
            'market_id': ['Market_A'],
            'governorate': ['Aden']
        })
        exchange_df = pd.DataFrame({'exchange_rate': [500]})
        panel_df = pd.DataFrame({
            'commodity': ['Wheat'], 
            'price_usd': [1.0],
            'market_id': ['Market_A']
        })
        
        wfp_processor.commodities = ['Wheat']
        
        # Mock the directories
        with patch('yemen_market.data.wfp_processor.PROCESSED_DATA_DIR', tmp_path):
            wfp_processor._save_summary_stats(commodity_df, exchange_df, panel_df)
        
        # Verify JSON dump was called
        assert mock_json_dump.called
        
        # Check JSON summary content
        summary_call_args = mock_json_dump.call_args[0][0]
        assert 'processing_date' in summary_call_args
        assert 'date_range' in summary_call_args
        assert 'observations' in summary_call_args
        assert 'coverage' in summary_call_args
        
        # Check specific values
        assert summary_call_args['commodities']['selected'] == 1
        assert summary_call_args['commodities']['list'] == ['Wheat']
        assert 'overall' in summary_call_args['coverage']