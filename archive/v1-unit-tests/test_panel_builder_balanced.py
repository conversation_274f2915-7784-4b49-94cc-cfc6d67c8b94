"""Unit tests for PanelBuilder balanced panel methods."""

import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime

from yemen_market.data.panel_builder import PanelBuilder


class TestPanelBuilderBalanced(unittest.TestCase):
    """Test balanced panel methods in PanelBuilder."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.builder = PanelBuilder()
        
        # Create sample data
        self.sample_data = self._create_sample_data()
        self.sample_conflict = self._create_sample_conflict()
        self.sample_zones = self._create_sample_zones()
    
    def _create_sample_data(self):
        """Create sample WFP price data."""
        dates = pd.date_range('2019-01-01', '2019-03-01', freq='MS')
        markets = ['Aden', 'Sana\'a', 'Taiz']
        commodities = ['Wheat', 'Rice', 'Sugar']
        
        data = []
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    data.append({
                        'date': date,
                        'market': market,
                        'commodity': commodity,
                        'price': np.random.uniform(100, 200),
                        'usdprice': np.random.uniform(1, 3),
                        'admin1': f'{market}_Gov',
                        'admin2': f'{market}_District',
                        'latitude': np.random.uniform(12, 16),
                        'longitude': np.random.uniform(42, 46),
                        'market_id': f'{market}_Gov_{market}'
                    })
        
        return pd.DataFrame(data)
    
    def _create_sample_conflict(self):
        """Create sample conflict data."""
        dates = pd.date_range('2019-01-01', '2019-03-01', freq='MS')
        markets = ['Aden_Gov_Aden', 'Sana\'a_Gov_Sana\'a', 'Taiz_Gov_Taiz']
        
        data = []
        for date in dates:
            for market in markets:
                data.append({
                    'market_id': market,
                    'year_month': date.to_period('M'),
                    'n_events': np.random.randint(0, 10),
                    'n_battles': np.random.randint(0, 5),
                    'n_explosions': np.random.randint(0, 3),
                    'n_violence_civilians': np.random.randint(0, 2),
                    'total_fatalities': np.random.randint(0, 20),
                    'avg_distance_km': np.random.uniform(0, 50),
                    'conflict_intensity': np.random.choice(['none', 'low', 'medium', 'high']),
                    'conflict_ma3': np.random.uniform(0, 5)
                })
        
        return pd.DataFrame(data)
    
    def _create_sample_zones(self):
        """Create sample control zone data."""
        markets = ['Aden', 'Sana\'a', 'Taiz']
        
        data = []
        for market in markets:
            data.append({
                'market_name': market,
                'control_zone': np.random.choice(['IRG', 'DFA']),
                'zone_changed': 0,
                'date': pd.to_datetime('2019-03-01'),
                'lat': np.random.uniform(12, 16),
                'lon': np.random.uniform(42, 46)
            })
        
        return pd.DataFrame(data)
    
    @patch('yemen_market.data.panel_builder.pd.read_csv')
    def test_create_core_balanced_panel(self, mock_read_csv):
        """Test create_core_balanced_panel method."""
        # Mock the CSV read to return our sample data
        mock_read_csv.return_value = self.sample_data
        
        # Test the method
        result = self.builder.create_core_balanced_panel(
            min_coverage_pct=50.0,  # Lower threshold for test data
            min_markets=2,
            min_commodities=2
        )
        
        # Verify result
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('market', result.columns)
        self.assertIn('commodity', result.columns)
        self.assertIn('price', result.columns)
        
        # Check balance
        obs_per_entity = result.groupby(['market', 'commodity']).size()
        self.assertEqual(obs_per_entity.nunique(), 1, "Panel should be perfectly balanced")
    
    @patch('yemen_market.data.panel_builder.pd.read_parquet')
    def test_integrate_panel_data(self, mock_read_parquet):
        """Test integrate_panel_data method."""
        # Convert year_month to string for conflict data to avoid PeriodDtype issues
        conflict_data = self.sample_conflict.copy()
        conflict_data['year_month'] = conflict_data['year_month'].astype(str)
        
        # Mock the parquet reads
        mock_read_parquet.side_effect = [
            conflict_data,  # conflict data
            self.sample_zones,     # control zones
            self.sample_zones.rename(columns={'market_name': 'market'})  # geographic data
        ]
        
        # Create base panel
        base_panel = self.sample_data.copy()
        base_panel['year_month'] = base_panel['date'].dt.to_period('M')
        
        # Test the method
        result = self.builder.integrate_panel_data(base_panel)
        
        # Verify result
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('events_total', result.columns)
        self.assertIn('control_zone', result.columns)
        self.assertIn('distance_to_capital_km', result.columns)
        
        # Check that conflict data was integrated
        self.assertGreater(result['events_total'].sum(), 0)
    
    def test_validate_balanced_panel(self):
        """Test validate_balanced_panel method."""
        # Create a balanced panel
        panel = self.sample_data.copy()
        panel['events_total'] = np.random.randint(0, 10, len(panel))
        panel['control_zone'] = np.random.choice(['IRG', 'DFA'], len(panel))
        
        # Test the method
        result = self.builder.validate_balanced_panel(panel)
        
        # Verify result
        self.assertIsInstance(result, dict)
        self.assertIn('is_balanced', result)
        self.assertIn('missing_data', result)
        self.assertIn('date_range', result)
    
    @patch('yemen_market.data.panel_builder.Path.mkdir')
    @patch('pandas.DataFrame.to_parquet')
    @patch('pandas.DataFrame.to_csv')
    @patch('builtins.open', create=True)
    def test_save_balanced_panels(self, mock_open, mock_to_csv, mock_to_parquet, mock_mkdir):
        """Test save_balanced_panels method."""
        # Create test panel
        panel = self.sample_data.copy()
        panel['events_total'] = 5
        
        # Mock file operations
        mock_file = MagicMock()
        mock_open.return_value.__enter__.return_value = mock_file
        
        # Test the method
        result = self.builder.save_balanced_panels(panel)
        
        # Verify result
        self.assertIsInstance(result, dict)
        self.assertIn('raw', result)
        self.assertIn('filled', result)
        
        # Verify parquet files were saved (3 if integrated panel with conflict data)
        self.assertGreaterEqual(mock_to_parquet.call_count, 2)  # at least raw and filled
        
        # Verify CSV was saved if panel has conflict data
        if 'events_total' in panel.columns:
            self.assertGreaterEqual(mock_to_csv.call_count, 1)
    
    @patch('yemen_market.data.panel_builder.pd.read_parquet')
    def test_load_balanced_panel(self, mock_read_parquet):
        """Test load_balanced_panel method."""
        # Mock the parquet read
        mock_read_parquet.return_value = self.sample_data
        
        # Test loading filled panel
        result = self.builder.load_balanced_panel(panel_type='filled')
        
        # Verify result
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), len(self.sample_data))
        mock_read_parquet.assert_called_once()
        
        # Test loading raw panel
        mock_read_parquet.reset_mock()
        result = self.builder.load_balanced_panel(panel_type='raw')
        mock_read_parquet.assert_called_once()
    
    @patch('yemen_market.data.panel_builder.pd.read_parquet')
    def test_load_integrated_panel(self, mock_read_parquet):
        """Test load_integrated_panel method."""
        # Mock the parquet read
        integrated_panel = self.sample_data.copy()
        integrated_panel['events_total'] = 5
        integrated_panel['control_zone'] = 'IRG'
        mock_read_parquet.return_value = integrated_panel
        
        # Test the method
        result = self.builder.load_integrated_panel()
        
        # Verify result
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('events_total', result.columns)
        self.assertIn('control_zone', result.columns)
        mock_read_parquet.assert_called_once()
    
    def test_haversine_distance_calculation(self):
        """Test that geographic distance calculation works correctly."""
        # Create panel with coordinates
        panel = pd.DataFrame({
            'market': ['Aden'],
            'commodity': ['Wheat'],
            'latitude': [12.7855],  # Aden coordinates
            'longitude': [45.0187]
        })
        
        # Add geographic features
        result = self.builder._add_geographic_features(panel)
        
        # Check that distance was calculated
        self.assertIn('distance_to_capital_km', result.columns)
        self.assertGreater(result['distance_to_capital_km'].iloc[0], 0)
        self.assertLess(result['distance_to_capital_km'].iloc[0], 1000)  # Reasonable distance
    
    def test_conflict_integration_with_missing_data(self):
        """Test that conflict integration handles missing data correctly."""
        # Create panel without matching conflict data
        panel = pd.DataFrame({
            'market': ['Unknown_Market'],
            'commodity': ['Wheat'],
            'admin1': ['Unknown_Gov'],
            'year_month': pd.to_datetime('2019-01').to_period('M')
        })
        
        # Create empty conflict data with all required columns
        conflict = pd.DataFrame({
            'market_id': [],
            'year_month': [],
            'n_events': [],
            'n_battles': [],
            'n_explosions': [],
            'n_violence_civilians': [],
            'total_fatalities': [],
            'avg_distance_km': [],
            'conflict_intensity': [],
            'conflict_ma3': []
        })
        
        # Mock the conflict data read
        with patch('yemen_market.data.panel_builder.pd.read_parquet', return_value=conflict):
            result = self.builder._integrate_conflict_data(panel)
        
        # Check that missing conflict data is filled with zeros
        self.assertIn('events_total', result.columns)
        self.assertEqual(result['events_total'].iloc[0], 0)


if __name__ == '__main__':
    unittest.main()