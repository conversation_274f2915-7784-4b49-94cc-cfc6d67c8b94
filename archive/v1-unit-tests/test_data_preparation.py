"""Tests for the data preparation module."""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import tempfile
import json

from yemen_market.features.data_preparation import (
    generate_data_quality_report,
    winsorize_prices,
    test_panel_stationarity,
    define_conflict_regimes,
    add_econometric_features,
    validate_for_modeling,
    save_prepared_data
)


@pytest.fixture
def sample_panel():
    """Create a sample panel dataset for testing."""
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=50, freq='M')
    markets = ['Aden', 'Sana\'a', 'Taiz']
    commodities = ['Wheat', 'Rice']
    
    data = []
    for date in dates:
        for market in markets:
            for commodity in commodities:
                price = np.random.uniform(100, 500) if commodity == 'Wheat' else np.random.uniform(200, 800)
                # Add some extreme values
                if np.random.random() < 0.02:
                    price *= 10  # Extreme high
                elif np.random.random() < 0.02:
                    price *= 0.1  # Extreme low
                    
                data.append({
                    'date': date,
                    'market': market,
                    'commodity': commodity,
                    'price': price,
                    'usdprice': price / 560,  # YER to USD
                    'events_total': np.random.poisson(5) if np.random.random() < 0.3 else 0
                })
    
    df = pd.DataFrame(data)
    # Add some missing values
    df.loc[df.sample(frac=0.05).index, 'price'] = np.nan
    return df


class TestDataQualityReport:
    """Test data quality report generation."""
    
    def test_generate_report_basic(self, sample_panel):
        """Test basic report generation."""
        report = generate_data_quality_report(sample_panel)
        
        assert 'total_observations' in report
        assert 'date_range' in report
        assert 'n_markets' in report
        assert 'n_commodities' in report
        assert 'price_coverage' in report
        
        assert report['n_markets'] == 3
        assert report['n_commodities'] == 2
        
    def test_report_with_conflict_data(self, sample_panel):
        """Test report with conflict data."""
        report = generate_data_quality_report(sample_panel)
        
        assert 'conflict_coverage' in report
        assert 'total_conflict_events' in report
        assert report['total_conflict_events'] > 0
        
    def test_report_extreme_values(self, sample_panel):
        """Test extreme value reporting."""
        report = generate_data_quality_report(sample_panel)
        
        assert 'price_quantiles' in report
        assert '1%' in report['price_quantiles']
        assert '99%' in report['price_quantiles']
        assert 'extreme_low_prices' in report
        assert 'extreme_high_prices' in report


class TestWinsorization:
    """Test price winsorization."""
    
    def test_basic_winsorization(self, sample_panel):
        """Test basic winsorization functionality."""
        df_winsorized = winsorize_prices(sample_panel)
        
        assert 'price_winsorized' in df_winsorized.columns
        assert 'usdprice_winsorized' in df_winsorized.columns
        
        # Check that extreme values are capped
        for commodity in df_winsorized['commodity'].unique():
            commodity_data = df_winsorized[df_winsorized['commodity'] == commodity]
            original = commodity_data['price'].dropna()
            winsorized = commodity_data['price_winsorized'].dropna()
            
            assert winsorized.min() >= original.quantile(0.01)
            assert winsorized.max() <= original.quantile(0.99)
    
    def test_custom_limits(self, sample_panel):
        """Test winsorization with custom limits."""
        df_winsorized = winsorize_prices(sample_panel, limits=(0.05, 0.05))
        
        for commodity in df_winsorized['commodity'].unique():
            commodity_data = df_winsorized[df_winsorized['commodity'] == commodity]
            original = commodity_data['price'].dropna()
            winsorized = commodity_data['price_winsorized'].dropna()
            
            assert winsorized.min() >= original.quantile(0.05)
            assert winsorized.max() <= original.quantile(0.95)


class TestStationarity:
    """Test stationarity testing."""
    
    def test_stationarity_analysis(self, sample_panel):
        """Test panel stationarity testing."""
        results, summary = test_panel_stationarity(sample_panel, min_obs=10)
        
        assert isinstance(results, dict)
        assert isinstance(summary, dict)
        
        if 'total_tested' in summary:
            assert summary['total_tested'] > 0
            assert 'stationary_count' in summary
            assert 'stationary_pct' in summary
            
            # Check individual results
            for key, result in results.items():
                assert 'stationary' in result
                assert 'adf_stat' in result
                assert 'p_value' in result
                assert 'n_obs' in result


class TestConflictRegimes:
    """Test conflict regime definition."""
    
    def test_define_regimes(self, sample_panel):
        """Test conflict regime categorization."""
        df_regimes = define_conflict_regimes(sample_panel)
        
        assert 'conflict_regime' in df_regimes.columns
        assert 'high_conflict' in df_regimes.columns
        assert 'any_conflict' in df_regimes.columns
        
        # Check regime categories
        regimes = df_regimes['conflict_regime'].unique()
        expected_regimes = {'no_conflict', 'low', 'medium', 'high'}
        assert set(regimes).issubset(expected_regimes)
        
        # Check binary indicators
        assert df_regimes['high_conflict'].isin([0, 1]).all()
        assert df_regimes['any_conflict'].isin([0, 1]).all()
        
        # Check consistency
        no_conflict_mask = df_regimes['conflict_regime'] == 'no_conflict'
        assert (df_regimes.loc[no_conflict_mask, 'any_conflict'] == 0).all()


class TestEconometricFeatures:
    """Test econometric feature engineering."""
    
    def test_add_features(self, sample_panel):
        """Test adding econometric features."""
        df_features = add_econometric_features(sample_panel)
        
        # Check log transformations
        assert 'log_price' in df_features.columns
        assert 'log_usdprice' in df_features.columns
        
        # Check time features
        assert 'time_trend' in df_features.columns
        assert 'month' in df_features.columns
        assert 'quarter' in df_features.columns
        assert 'year' in df_features.columns
        
        # Check entity identifier
        assert 'entity_id' in df_features.columns
        
        # Check lagged conflict
        assert 'events_total_lag1' in df_features.columns
        assert 'events_total_lag2' in df_features.columns
        
        # Verify log transformation handles zeros
        assert not df_features['log_price'].isna().all()
        assert (df_features['log_price'] > -10).all()  # No extreme negative values


class TestValidation:
    """Test panel validation."""
    
    def test_valid_panel(self, sample_panel):
        """Test validation of a valid panel."""
        df_features = add_econometric_features(sample_panel)
        validation = validate_for_modeling(df_features, strict=False)
        
        assert isinstance(validation, dict)
        assert 'valid' in validation
        assert 'errors' in validation
        assert 'warnings' in validation
        
    def test_missing_columns(self):
        """Test validation with missing columns."""
        df = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'market': 'Aden',
            # Missing commodity and price columns
        })
        
        validation = validate_for_modeling(df)
        assert not validation['valid']
        assert len(validation['errors']) > 0
        
    def test_strict_validation(self, sample_panel):
        """Test strict validation mode."""
        # Add many missing values
        df = sample_panel.copy()
        df.loc[df.sample(frac=0.10).index, 'price'] = np.nan
        
        validation_relaxed = validate_for_modeling(df, strict=False)
        validation_strict = validate_for_modeling(df, strict=True)
        
        # Strict mode should be more restrictive
        if validation_relaxed['valid']:
            assert len(validation_strict['errors']) >= len(validation_relaxed['errors'])


class TestSaveData:
    """Test data saving functionality."""
    
    def test_save_prepared_data(self, sample_panel):
        """Test saving prepared data."""
        with tempfile.TemporaryDirectory() as tmpdir:
            output_dir = Path(tmpdir)
            
            df_prepared = add_econometric_features(
                define_conflict_regimes(
                    winsorize_prices(sample_panel)
                )
            )
            
            save_prepared_data(df_prepared, output_dir)
            
            # Check files were created
            assert (output_dir / "panel_prepared_for_modeling.parquet").exists()
            assert (output_dir / "panel_prepared_for_modeling.csv").exists()
            assert (output_dir / "preparation_metadata.json").exists()
            
            # Check metadata content
            with open(output_dir / "preparation_metadata.json", 'r') as f:
                metadata = json.load(f)
                
            assert 'preparation_date' in metadata
            assert 'n_observations' in metadata
            assert 'n_markets' in metadata
            assert 'n_commodities' in metadata
            assert 'columns' in metadata
            
            # Verify data can be reloaded
            df_reloaded = pd.read_parquet(output_dir / "panel_prepared_for_modeling.parquet")
            assert len(df_reloaded) == len(df_prepared)
            assert set(df_reloaded.columns) == set(df_prepared.columns)