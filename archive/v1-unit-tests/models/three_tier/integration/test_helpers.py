"""Helper functions for three-tier integration tests."""

import pandas as pd
import numpy as np


def create_sample_data_with_both_naming_conventions(dates, markets, commodities):
    """Create sample data with both column naming conventions.
    
    This helper creates data that works with both the old (governorate/usd_price)
    and new (market/price) naming conventions by including both sets of columns.
    """
    data = []
    for date in dates:
        for market in markets:
            for commodity in commodities:
                # Base price with market and commodity effects
                base_price = 100
                market_effect = hash(market) % 20 - 10
                commodity_effect = hash(commodity) % 15 - 7.5
                time_trend = (dates.get_loc(date) / len(dates)) * 20
                seasonal = 10 * np.sin(2 * np.pi * dates.get_loc(date) / 52)
                noise = np.random.randn() * 5
                
                price = base_price + market_effect + commodity_effect + time_trend + seasonal + noise
                
                data.append({
                    'date': date,
                    # Include both naming conventions
                    'market': market,
                    'governorate': market,  # Duplicate for compatibility
                    'commodity': commodity,
                    'price': max(price, 10),  # Ensure positive prices
                    'usd_price': max(price, 10)  # Duplicate for compatibility
                })
    
    return pd.DataFrame(data)


def create_conflict_data_with_both_naming_conventions(major_dates, minor_dates):
    """Create conflict data with both column naming conventions."""
    conflicts = []
    
    # Major conflicts
    for date in major_dates:
        market = np.random.choice(['Sana\'a', 'Aden', 'Taiz'])
        conflicts.append({
            'date': date,
            'market': market,
            'governorate': market,  # Duplicate for compatibility
            'fatalities': np.random.randint(20, 100),
            'event_type': 'Battle'
        })
    
    # Minor conflicts
    for date in minor_dates:
        market = np.random.choice(['Hodeidah', 'Ibb'])
        conflicts.append({
            'date': date,
            'market': market,
            'governorate': market,  # Duplicate for compatibility
            'fatalities': np.random.randint(1, 20),
            'event_type': 'Violence'
        })
    
    return pd.DataFrame(conflicts)