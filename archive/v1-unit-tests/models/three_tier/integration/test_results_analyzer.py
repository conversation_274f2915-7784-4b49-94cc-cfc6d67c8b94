"""Tests for the ResultsAnalyzer class."""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import tempfile
import json

from yemen_market.models.three_tier.integration.results_analyzer import ResultsAnalyzer
from yemen_market.models.three_tier.common import ResultsContainer


@pytest.fixture
def sample_panel_data():
    """Create sample panel data for testing."""
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=24, freq='M')
    markets = ['Aden', 'Sana\'a', 'Taiz', 'Hodeidah']
    commodities = ['Wheat', 'Rice', 'Oil (Vegetable)']
    
    data = []
    for date in dates:
        for market in markets:
            for commodity in commodities:
                base_price = {'Wheat': 300, 'Rice': 500, 'Oil (Vegetable)': 1000}[commodity]
                # Add market effects
                market_effect = {'Aden': 1.0, 'Sana\'a': 1.1, 'Taiz': 1.05, 'Hodeidah': 0.95}[market]
                # Add time trend
                time_effect = 1 + (date.toordinal() - dates[0].toordinal()) / 365 * 0.1
                # Add conflict effect
                conflict_intensity = np.random.poisson(3)
                conflict_effect = 1 + conflict_intensity * 0.02
                
                price = base_price * market_effect * time_effect * conflict_effect * np.random.uniform(0.9, 1.1)
                
                data.append({
                    'date': date,
                    'market': market,
                    'commodity': commodity,
                    'price': price,
                    'log_price': np.log(price),
                    'events_total': conflict_intensity,
                    'high_conflict': 1 if conflict_intensity > 5 else 0,
                    'conflict_regime': 'high' if conflict_intensity > 5 else 'medium' if conflict_intensity > 2 else 'low' if conflict_intensity > 0 else 'no_conflict',
                    'zone_DFA': 'IRG' if market in ['Aden', 'Taiz'] else 'SPC',
                    'year': date.year,
                    'price_change_pct': np.random.normal(0, 0.05)
                })
    
    return pd.DataFrame(data)


@pytest.fixture
def sample_results_dir():
    """Create a temporary results directory with sample results."""
    with tempfile.TemporaryDirectory() as tmpdir:
        results_dir = Path(tmpdir)
        
        # Create tier directories
        (results_dir / "tier1").mkdir(parents=True)
        (results_dir / "tier2").mkdir(parents=True)
        (results_dir / "tier3").mkdir(parents=True)
        
        # Create sample Tier 1 results
        tier1_results = {
            "coefficients": {
                "events_total": 0.015,
                "high_conflict": 0.12,
                "control_zone_DFA": 0.08,
                "time_trend": 0.005
            },
            "standard_errors": {
                "events_total": 0.003,
                "high_conflict": 0.04,
                "control_zone_DFA": 0.03,
                "time_trend": 0.001
            },
            "statistics": {
                "n_observations": 1000,
                "r_squared": 0.75,
                "f_statistic": 45.2
            }
        }
        
        with open(results_dir / "tier1" / "tier1_results.json", 'w') as f:
            json.dump(tier1_results, f)
        
        # Create sample Tier 2 results
        commodities = ['Wheat', 'Rice', 'Oil (Vegetable)']
        for commodity in commodities:
            if commodity == 'Oil (Vegetable)':
                # Error case
                result = {"error": "Insufficient data for analysis"}
            else:
                result = {
                    "threshold_value": np.random.uniform(300, 500),
                    "n_observations": np.random.randint(100, 200),
                    "r_squared": np.random.uniform(0.6, 0.8),
                    "integration_level": "high" if commodity == 'Wheat' else "medium"
                }
            
            with open(results_dir / "tier2" / f"{commodity}_results.json", 'w') as f:
                json.dump(result, f)
        
        # Create sample Tier 3 results
        tier3_results = {
            "static_factors": {
                "n_factors": 3,
                "cumulative_variance": [0.45, 0.68, 0.82, 0.91],
                "factor_loadings": [[0.8, 0.6, 0.4], [0.7, 0.5, 0.3]]
            }
        }
        
        with open(results_dir / "tier3" / "tier3_results.json", 'w') as f:
            json.dump(tier3_results, f)
        
        yield results_dir


class TestResultsAnalyzerInit:
    """Test ResultsAnalyzer initialization and loading."""
    
    def test_init(self, sample_results_dir):
        """Test basic initialization."""
        analyzer = ResultsAnalyzer(sample_results_dir)
        assert analyzer.results_dir == sample_results_dir
        assert analyzer.tier1_results is None
        assert isinstance(analyzer.tier2_results, dict)
        assert analyzer.tier3_results is None
    
    def test_load_results(self, sample_results_dir):
        """Test loading results from directory."""
        analyzer = ResultsAnalyzer(sample_results_dir)
        analyzer.load_results()
        
        # Check Tier 1 loaded
        assert analyzer.tier1_results is not None
        assert 'coefficients' in analyzer.tier1_results
        
        # Check Tier 2 loaded
        assert len(analyzer.tier2_results) == 3
        assert 'Wheat' in analyzer.tier2_results
        assert 'Rice' in analyzer.tier2_results
        
        # Check Tier 3 loaded
        assert analyzer.tier3_results is not None
        assert 'static_factors' in analyzer.tier3_results


class TestCoefficientExtraction:
    """Test coefficient extraction methods."""
    
    def test_extract_tier1_from_saved(self, sample_results_dir):
        """Test extracting coefficients from saved results."""
        analyzer = ResultsAnalyzer(sample_results_dir)
        analyzer.load_results()
        
        coefficients = analyzer.extract_tier1_coefficients()
        
        assert 'coefficients' in coefficients
        assert 'standard_errors' in coefficients
        assert 'statistics' in coefficients
        assert 'p_values' in coefficients
        assert 'significance' in coefficients
        
        # Check specific coefficients
        assert coefficients['coefficients']['events_total'] == 0.015
        assert coefficients['standard_errors']['events_total'] == 0.003
        
        # Check p-values calculated
        assert all(0 <= p <= 1 for p in coefficients['p_values'].values())
        
        # Check significance stars
        assert coefficients['significance']['events_total'] == "***"  # Should be highly significant
    
    def test_extract_tier1_from_model_object(self, sample_results_dir):
        """Test extracting from a model object."""
        analyzer = ResultsAnalyzer(sample_results_dir)
        
        # Create mock ResultsContainer
        container = ResultsContainer(
            tier='tier1',
            commodity=None,
            model_type='pooled_panel'
        )
        container.parameters = {'events_total': 0.02, 'time_trend': 0.003}
        container.standard_errors = {'events_total': 0.005, 'time_trend': 0.001}
        container.statistics = {'n_observations': 500}
        
        # Create mock model object
        class MockModel:
            def __init__(self):
                self.results = container
        
        coefficients = analyzer.extract_tier1_coefficients(model_object=MockModel())
        
        assert coefficients['coefficients']['events_total'] == 0.02
        assert coefficients['standard_errors']['events_total'] == 0.005


class TestConflictAnalysis:
    """Test conflict impact analysis."""
    
    def test_analyze_conflict_impact(self, sample_results_dir):
        """Test analyzing conflict impact from coefficients."""
        analyzer = ResultsAnalyzer(sample_results_dir)
        analyzer.load_results()
        
        coefficients = analyzer.extract_tier1_coefficients()
        impact = analyzer.analyze_conflict_impact(coefficients)
        
        assert 'impact_per_event' in impact
        assert 'monthly_impact' in impact
        assert 'high_conflict_premium' in impact
        assert 'control_zone_effect' in impact
        assert 'monthly_trend' in impact
        assert 'annual_trend' in impact
        
        # Check calculations
        assert impact['impact_per_event'] == 1.5  # 0.015 * 100
        assert impact['monthly_impact'] == 36.0  # 1.5 * 24
        assert impact['annual_trend'] == 6.0  # 0.005 * 100 * 12


class TestTier2Results:
    """Test Tier 2 results extraction."""
    
    def test_extract_tier2_results(self, sample_results_dir):
        """Test extracting Tier 2 commodity results."""
        analyzer = ResultsAnalyzer(sample_results_dir)
        analyzer.load_results()
        
        summary = analyzer.extract_tier2_results()
        
        assert len(summary) == 3
        assert 'Wheat' in summary
        assert 'Rice' in summary
        assert 'Oil (Vegetable)' in summary
        
        # Check successful commodity
        assert summary['Wheat']['status'] == 'completed'
        assert summary['Wheat']['has_threshold'] is True
        assert 'threshold_value' in summary['Wheat']
        
        # Check error commodity
        assert summary['Oil (Vegetable)']['status'] == 'error'
        assert 'error_message' in summary['Oil (Vegetable)']


class TestMarketIntegration:
    """Test market integration analysis."""
    
    def test_analyze_market_integration(self, sample_panel_data):
        """Test market integration pattern analysis."""
        analyzer = ResultsAnalyzer(Path("."))  # Dummy path
        
        integration = analyzer.analyze_market_integration(sample_panel_data)
        
        assert 'correlation_matrix' in integration
        assert 'high_integration_pairs' in integration
        assert 'integration_summary' in integration
        
        # Check correlation matrix
        assert isinstance(integration['correlation_matrix'], dict)
        
        # Check summary statistics
        summary = integration['integration_summary']
        assert 'n_markets' in summary
        assert 'mean_correlation' in summary
        assert 'n_high_integration_pairs' in summary
        
        assert summary['n_markets'] == 4  # 4 markets in sample data
    
    def test_zone_correlations(self, sample_panel_data):
        """Test cross-zone correlation analysis."""
        analyzer = ResultsAnalyzer(Path("."))
        
        integration = analyzer.analyze_market_integration(sample_panel_data)
        
        assert 'zone_correlations' in integration
        zone_corr = integration['zone_correlations']
        assert 'IRG' in zone_corr
        assert 'SPC' in zone_corr


class TestPolicyInsights:
    """Test policy insights generation."""
    
    def test_generate_policy_insights(self, sample_panel_data):
        """Test generating policy insights."""
        analyzer = ResultsAnalyzer(Path("."))
        
        insights = analyzer.generate_policy_insights(sample_panel_data)
        
        assert 'price_volatility_by_conflict' in insights
        assert 'market_accessibility' in insights
        assert 'essential_commodities_trends' in insights
        assert 'geographic_disparities' in insights
        assert 'key_recommendations' in insights
        
        # Check volatility analysis
        volatility = insights['price_volatility_by_conflict']
        assert 'Wheat' in volatility
        
        # Check market accessibility
        accessibility = insights['market_accessibility']
        assert 'most_accessible' in accessibility
        assert 'least_accessible' in accessibility
        assert 'mean_accessibility' in accessibility
        
        # Check recommendations
        recommendations = insights['key_recommendations']
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        assert all(isinstance(r, str) for r in recommendations)


class TestReportSaving:
    """Test report saving functionality."""
    
    def test_save_analysis_report(self, sample_results_dir):
        """Test saving analysis report."""
        analyzer = ResultsAnalyzer(sample_results_dir)
        analyzer.load_results()
        
        report_path = sample_results_dir / "test_report.json"
        analyzer.save_analysis_report(report_path)
        
        assert report_path.exists()
        
        with open(report_path, 'r') as f:
            report = json.load(f)
        
        assert 'analysis_date' in report
        assert 'results_directory' in report
        assert 'tier1_available' in report
        assert 'tier2_commodities_analyzed' in report
        assert 'tier3_available' in report
        
        assert report['tier1_available'] is True
        assert report['tier2_commodities_analyzed'] == 3
        assert report['tier3_available'] is True