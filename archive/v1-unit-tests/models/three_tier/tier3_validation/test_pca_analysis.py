"""Test suite for PCA-based market integration analysis.

Tests the PCA analysis tools for market integration validation.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from yemen_market.models.three_tier.tier3_validation.pca_analysis import PCAMarketIntegration


class TestPCAMarketIntegration:
    """Test PCA market integration analyzer."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample panel data for testing."""
        np.random.seed(42)
        
        # Generate correlated price series
        dates = pd.date_range('2020-01-01', periods=100, freq='W')
        markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah']
        commodities = ['wheat', 'rice', 'sugar']
        
        # Create common factor
        common_trend = np.cumsum(np.random.randn(100)) + 100
        
        data = []
        for i, date in enumerate(dates):
            for market in markets:
                for commodity in commodities:
                    # Price = common trend + market effect + commodity effect + noise
                    market_weight = np.random.uniform(0.7, 1.3)
                    commodity_weight = np.random.uniform(0.8, 1.2)
                    noise = np.random.randn() * 5
                    
                    price = common_trend[i] * market_weight * commodity_weight + noise
                    
                    data.append({
                        'date': date,
                        'governorate': market,
                        'commodity': commodity,
                        'usd_price': price
                    })
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def analyzer(self):
        """Create PCA analyzer instance."""
        config = {
            'standardize': True,
            'min_periods': 26,
            'window_size': 52
        }
        return PCAMarketIntegration(config)
    
    def test_initialization(self, analyzer):
        """Test analyzer initialization."""
        assert analyzer.standardize is True
        assert analyzer.min_periods == 26
        assert analyzer.window_size == 52
        assert hasattr(analyzer, 'panel_handler')
    
    def test_analyze_integration_strength(self, analyzer, sample_data):
        """Test overall integration strength analysis."""
        results = analyzer.analyze_integration_strength(sample_data)
        
        # Check result structure
        assert 'pc1_variance_explained' in results
        assert 'cumulative_variance_3pc' in results
        assert 'effective_n_factors' in results
        assert 'integration_level' in results
        assert 'pc1_loadings' in results
        assert 'eigenvalues' in results
        
        # Check values are reasonable
        assert 0 <= results['pc1_variance_explained'] <= 1
        assert results['cumulative_variance_3pc'] >= results['pc1_variance_explained']
        assert results['effective_n_factors'] >= 1
        assert results['integration_level'] in ['Very High', 'High', 'Moderate', 'Low', 'Very Low']
        
        # Check loadings
        assert isinstance(results['pc1_loadings'], pd.Series)
        assert len(results['pc1_loadings']) == results['n_series']
    
    def test_integration_level_interpretation(self, analyzer):
        """Test integration level interpretation logic."""
        assert analyzer._interpret_integration(0.75) == 'Very High'
        assert analyzer._interpret_integration(0.55) == 'High'
        assert analyzer._interpret_integration(0.35) == 'Moderate'
        assert analyzer._interpret_integration(0.25) == 'Low'
        assert analyzer._interpret_integration(0.15) == 'Very Low'
    
    def test_rolling_pca_analysis(self, analyzer, sample_data):
        """Test rolling PCA analysis."""
        results = analyzer.rolling_pca_analysis(sample_data)
        
        assert isinstance(results, pd.DataFrame)
        assert 'pc1_variance' in results.columns
        assert 'pc2_variance' in results.columns
        assert 'cumvar_2pc' in results.columns
        assert 'effective_factors' in results.columns
        
        # Check rolling calculations
        assert len(results) <= len(sample_data['date'].unique()) - analyzer.window_size + 1
        
        # Check trend calculation
        if len(results) > 12:
            assert 'integration_trend' in results.columns
            assert 'integration_change' in results.columns
            assert 'regime_change' in results.columns
    
    def test_hierarchical_pca(self, analyzer, sample_data):
        """Test hierarchical PCA clustering."""
        results = analyzer.hierarchical_pca(sample_data)
        
        # Check result structure
        assert 'n_clusters' in results
        assert 'market_groups' in results
        assert 'cluster_sizes' in results
        assert 'linkage_matrix' in results
        assert 'distance_matrix' in results
        assert 'cluster_stats' in results
        assert 'pca_loadings' in results
        
        # Check clustering results
        assert results['n_clusters'] >= 1
        assert isinstance(results['market_groups'], dict)
        assert sum(results['cluster_sizes'].values()) <= len(sample_data['governorate'].unique()) * len(sample_data['commodity'].unique())
        
        # Check distance matrix
        assert isinstance(results['distance_matrix'], pd.DataFrame)
        assert results['distance_matrix'].shape[0] == results['distance_matrix'].shape[1]
    
    def test_commodity_specific_pca(self, analyzer, sample_data):
        """Test commodity-specific PCA analysis."""
        results = analyzer.commodity_specific_pca(sample_data)
        
        # Should have results for each commodity
        commodities = sample_data['commodity'].unique()
        assert len(results) <= len(commodities)
        
        # Check each commodity result
        for commodity, commodity_results in results.items():
            assert 'pc1_variance' in commodity_results
            assert 'cumvar_3pc' in commodity_results
            assert 'n_markets' in commodity_results
            assert 'n_periods' in commodity_results
            assert 'integration_level' in commodity_results
            assert 'market_loadings' in commodity_results
            assert 'price_volatility' in commodity_results
            assert 'missing_data_pct' in commodity_results
            
            # Check values
            assert 0 <= commodity_results['pc1_variance'] <= 1
            assert commodity_results['n_markets'] <= len(sample_data['governorate'].unique())
    
    def test_spatial_pca_analysis_without_distances(self, analyzer, sample_data):
        """Test spatial PCA analysis without distance matrix."""
        results = analyzer.spatial_pca_analysis(sample_data)
        
        assert 'market_loadings' in results
        assert 'loading_variance' in results
        assert 'n_markets' in results
        
        # Check market loadings
        assert isinstance(results['market_loadings'], dict)
        assert len(results['market_loadings']) <= len(sample_data['governorate'].unique())
    
    def test_spatial_pca_analysis_with_distances(self, analyzer, sample_data):
        """Test spatial PCA analysis with distance matrix."""
        # Create mock distance matrix
        markets = sample_data['governorate'].unique()
        n_markets = len(markets)
        distances = pd.DataFrame(
            np.random.uniform(0, 500, (n_markets, n_markets)),
            index=markets,
            columns=markets
        )
        np.fill_diagonal(distances.values, 0)
        
        results = analyzer.spatial_pca_analysis(sample_data, distance_matrix=distances)
        
        assert 'spatial_correlation' in results
        assert 'correlation' in results['spatial_correlation']
        assert 'p_value' in results['spatial_correlation']
        assert 'n_pairs' in results['spatial_correlation']
    
    def test_prepare_data(self, analyzer):
        """Test data preparation method."""
        # Create data with missing values
        data = pd.DataFrame({
            'A': [1, 2, np.nan, 4, 5, np.nan, 7],
            'B': [10, np.nan, np.nan, 40, 50, 60, 70],
            'C': [np.nan] * 7  # All missing
        })
        
        prepared = analyzer._prepare_data(data)
        
        # Should drop column C (too many missing)
        assert 'C' not in prepared.columns
        
        # Should fill missing values
        assert prepared.isna().sum().sum() == 0
    
    def test_calculate_effective_factors(self, analyzer):
        """Test effective factor calculation."""
        # Test with uniform distribution (maximum entropy)
        uniform_var = np.array([0.25, 0.25, 0.25, 0.25])
        n_effective = analyzer._calculate_effective_factors(uniform_var)
        assert np.isclose(n_effective, 4.0, rtol=0.01)
        
        # Test with single dominant factor
        dominant_var = np.array([0.9, 0.05, 0.03, 0.02])
        n_effective = analyzer._calculate_effective_factors(dominant_var)
        assert n_effective < 2.0
    
    def test_analyze_clusters(self, analyzer):
        """Test cluster analysis method."""
        # Create mock data and clusters
        data = pd.DataFrame(
            np.random.randn(100, 6),
            columns=[f'Series_{i}' for i in range(6)]
        )
        clusters = np.array([1, 1, 1, 2, 2, 2])
        
        cluster_stats = analyzer._analyze_clusters(data, clusters)
        
        assert isinstance(cluster_stats, pd.DataFrame)
        assert 'cluster_id' in cluster_stats.columns
        assert 'n_series' in cluster_stats.columns
        assert 'avg_correlation' in cluster_stats.columns
        assert 'price_volatility' in cluster_stats.columns
        assert 'price_level' in cluster_stats.columns
        assert len(cluster_stats) == 2  # Two clusters
    
    def test_generate_integration_report(self, analyzer, sample_data):
        """Test comprehensive integration report generation."""
        report = analyzer.generate_integration_report(sample_data)
        
        # Check report structure
        assert hasattr(report, 'tier')
        assert report.tier == 'tier3_validation'
        assert report.model_type == 'pca_integration_analysis'
        
        # Check tier-specific results
        assert 'overall_integration' in report.tier_specific
        assert 'commodity_integration' in report.tier_specific
        assert 'market_clusters' in report.tier_specific
        assert 'n_clusters' in report.tier_specific
        
        # Check metadata
        assert report.metadata['n_observations'] == len(sample_data)
        assert report.metadata['n_markets'] == sample_data['governorate'].nunique()
        assert report.metadata['n_commodities'] == sample_data['commodity'].nunique()
        
        # Check coefficients
        assert 'Overall_Integration_PC1' in report.coefficients
    
    def test_missing_data_handling(self, analyzer):
        """Test handling of datasets with missing values."""
        # Create data with systematic missing pattern
        dates = pd.date_range('2020-01-01', periods=50, freq='W')
        markets = ['Sana\'a', 'Aden']
        commodities = ['wheat', 'rice']
        
        # Create proper panel data
        data_list = []
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    data_list.append({
                        'date': date,
                        'governorate': market,
                        'commodity': commodity,
                        'usd_price': np.random.randn() + 100
                    })
        
        data = pd.DataFrame(data_list)
        
        # Introduce 40% missing values
        missing_idx = np.random.choice(data.index, size=int(0.4 * len(data)), replace=False)
        data.loc[missing_idx, 'usd_price'] = np.nan
        
        # Should still work with missing data
        results = analyzer.analyze_integration_strength(data)
        
        assert 'pc1_variance_explained' in results
        assert results['n_series'] > 0
    
    def test_edge_cases(self, analyzer):
        """Test edge cases and error handling."""
        # Minimal data
        minimal_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'governorate': 'Sana\'a',
            'commodity': 'wheat',
            'usd_price': np.random.randn(10) + 100
        })
        
        # Should handle single series
        results = analyzer.analyze_integration_strength(minimal_data)
        assert results['n_series'] == 1
        
        # Empty data after filtering
        empty_data = minimal_data.copy()
        empty_data['usd_price'] = np.nan
        
        # Should handle gracefully
        results = analyzer.analyze_integration_strength(empty_data)
        assert results['n_series'] >= 0


class TestPCAIntegrationWithMocks:
    """Test PCA integration with mocked dependencies."""
    
    @pytest.fixture
    def analyzer(self):
        """Create analyzer with mocked panel handler."""
        analyzer = PCAMarketIntegration()
        analyzer.panel_handler = Mock()
        return analyzer
    
    def test_analyze_integration_with_mock(self, analyzer):
        """Test integration analysis with mocked wide matrix."""
        # Mock wide matrix creation
        mock_wide = pd.DataFrame(
            np.random.randn(100, 10),
            columns=[f'Market_{i}' for i in range(10)]
        )
        analyzer.panel_handler.create_wide_matrix.return_value = mock_wide
        
        # Mock data
        mock_data = pd.DataFrame({'dummy': [1]})
        
        results = analyzer.analyze_integration_strength(mock_data)
        
        assert results['n_series'] == 10
        assert results['n_periods'] == 100
        assert 'pc1_variance_explained' in results
        
    def test_rolling_pca_with_mock(self, analyzer):
        """Test rolling PCA with mocked data."""
        # Create time series for rolling analysis
        dates = pd.date_range('2020-01-01', periods=200, freq='W')
        mock_wide = pd.DataFrame(
            np.random.randn(200, 5),
            index=dates,
            columns=[f'Series_{i}' for i in range(5)]
        )
        analyzer.panel_handler.create_wide_matrix.return_value = mock_wide
        
        mock_data = pd.DataFrame({'dummy': [1]})
        analyzer.window_size = 52
        
        results = analyzer.rolling_pca_analysis(mock_data)
        
        assert isinstance(results, pd.DataFrame)
        assert len(results) == 200 - 52 + 1
        assert results.index.equals(dates[51:])  # Windows end at these dates