"""Test suite for conflict validation in Tier 3.

Tests the conflict impact validation on market integration patterns.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock

from yemen_market.models.three_tier.tier3_validation.conflict_validation import (
    ConflictIntegrationValidator
)
from yemen_market.models.three_tier.core.base_model import TierType


class TestConflictIntegrationValidator:
    """Test conflict integration validator."""
    
    @pytest.fixture
    def price_data(self):
        """Create sample price panel data."""
        np.random.seed(42)
        
        dates = pd.date_range('2020-01-01', periods=200, freq='W')
        markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah']
        commodities = ['wheat', 'rice']
        
        data = []
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    price = 100 + np.random.randn() * 10
                    
                    # Add shock for specific periods (simulate conflict impact)
                    if date >= pd.Timestamp('2020-06-01') and date <= pd.Timestamp('2020-07-01') and market == 'Sana\'a':
                        price *= 1.5  # 50% price increase
                    
                    data.append({
                        'date': date,
                        'governorate': market,
                        'commodity': commodity,
                        'usd_price': price
                    })
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def conflict_data(self):
        """Create sample conflict data."""
        conflict_events = [
            {'date': pd.Timestamp('2020-05-25'), 'governorate': 'Sana\'a', 'fatalities': 50, 'event_type': 'Battle'},
            {'date': pd.Timestamp('2020-06-10'), 'governorate': 'Sana\'a', 'fatalities': 30, 'event_type': 'Explosion'},
            {'date': pd.Timestamp('2020-08-15'), 'governorate': 'Aden', 'fatalities': 20, 'event_type': 'Battle'},
            {'date': pd.Timestamp('2020-10-01'), 'governorate': 'Taiz', 'fatalities': 15, 'event_type': 'Violence'},
            {'date': pd.Timestamp('2020-03-01'), 'governorate': 'Hodeidah', 'fatalities': 5, 'event_type': 'Protest'},
        ]
        
        return pd.DataFrame(conflict_events)
    
    @pytest.fixture
    def integration_scores(self):
        """Create sample integration scores over time."""
        dates = pd.date_range('2020-01-01', periods=200, freq='W')
        
        # Create integration scores with dip during conflict
        scores = []
        for i, date in enumerate(dates):
            base_score = 0.7 + 0.1 * np.sin(i / 20)  # Baseline with some variation
            
            # Reduce integration during conflict period
            if date >= pd.Timestamp('2020-05-15') and date <= pd.Timestamp('2020-07-15'):
                base_score *= 0.8
            
            scores.append({
                'pc1_variance': base_score + np.random.randn() * 0.02,
                'pc2_variance': 0.15 + np.random.randn() * 0.01,
                'effective_factors': 2.5 + np.random.randn() * 0.1
            })
        
        return pd.DataFrame(scores, index=dates)
    
    @pytest.fixture
    def validator(self):
        """Create validator instance."""
        config = {
            'conflict_threshold': 10,
            'window_before': 30,
            'window_after': 30,
            'min_markets_affected': 2
        }
        return ConflictIntegrationValidator(config)
    
    def test_initialization(self, validator):
        """Test validator initialization."""
        assert validator.tier == TierType.TIER3_VALIDATION
        assert validator.conflict_threshold == 10
        assert validator.window_before == 30
        assert validator.window_after == 30
        assert validator.min_markets_affected == 2
        assert not validator.is_fitted
    
    def test_validate_data_with_prices(self, validator, price_data):
        """Test data validation with price data."""
        assert validator.validate_data(price_data) is True
        assert validator.has_conflict_data is False
    
    def test_validate_data_with_conflict(self, validator, price_data):
        """Test data validation with embedded conflict data."""
        # Add conflict columns to price data
        price_data['fatalities'] = 0
        price_data.loc[price_data.index[:10], 'fatalities'] = 10
        price_data['event_type'] = 'None'
        
        assert validator.validate_data(price_data) is True
        assert validator.has_conflict_data is True
    
    def test_validate_data_missing_columns(self, validator):
        """Test validation with missing required columns."""
        invalid_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'price': np.random.randn(10)
        })
        
        assert validator.validate_data(invalid_data) is False
    
    def test_fit_with_all_data(self, validator, price_data, conflict_data, integration_scores):
        """Test fitting with all data provided."""
        validator.fit(price_data, conflict_data=conflict_data, integration_scores=integration_scores)
        
        assert validator.is_fitted
        assert validator.results is not None
        
        # Check tier-specific results
        assert 'event_study' in validator.results.tier_specific
        assert 'structural_breaks' in validator.results.tier_specific
        assert 'spatial_impact' in validator.results.tier_specific
        assert 'granger_causality' in validator.results.tier_specific
    
    def test_extract_conflict_data(self, validator, price_data):
        """Test conflict data extraction from combined dataset."""
        # Add conflict info to price data
        price_data['fatalities'] = 0
        price_data['event_type'] = 'None'
        
        # Add some conflict events
        conflict_indices = [10, 50, 100]
        for idx in conflict_indices:
            price_data.loc[price_data.index[idx], 'fatalities'] = 20
            price_data.loc[price_data.index[idx], 'event_type'] = 'Battle'
        
        conflict_df = validator._extract_conflict_data(price_data)
        
        assert isinstance(conflict_df, pd.DataFrame)
        assert len(conflict_df) <= len(conflict_indices)
        assert 'date' in conflict_df.columns
        assert 'governorate' in conflict_df.columns
        assert 'fatalities' in conflict_df.columns
    
    def test_compute_integration_scores(self, validator, price_data):
        """Test integration score computation."""
        with patch('yemen_market.models.three_tier.tier3_validation.conflict_validation.PCAMarketIntegration') as mock_pca:
            mock_analyzer = Mock()
            mock_scores = pd.DataFrame({
                'pc1_variance': np.random.uniform(0.5, 0.8, 100)
            }, index=pd.date_range('2020-01-01', periods=100, freq='W'))
            mock_analyzer.rolling_pca_analysis.return_value = mock_scores
            mock_pca.return_value = mock_analyzer
            
            scores = validator._compute_integration_scores(price_data)
            
            assert isinstance(scores, pd.DataFrame)
            assert 'pc1_variance' in scores.columns
    
    def test_event_study_analysis(self, validator, conflict_data, integration_scores):
        """Test event study analysis around conflicts."""
        validator.conflict_data = conflict_data
        validator.market_integration_scores = integration_scores
        validator.conflict_threshold = 10
        
        results = validator._event_study_analysis()
        
        assert 'n_events_analyzed' in results
        assert 'event_details' in results
        
        if results['n_events_analyzed'] > 0:
            assert 'n_significant_changes' in results
            assert 'avg_integration_change' in results
            assert 'pct_events_reducing_integration' in results
            
            # Check event details structure
            event_df = results['event_details']
            assert 'event_date' in event_df.columns
            assert 'integration_before' in event_df.columns
            assert 'integration_after' in event_df.columns
            assert 'integration_change' in event_df.columns
            assert 'p_value' in event_df.columns
            assert 'significant' in event_df.columns
    
    def test_structural_break_analysis(self, validator, conflict_data, integration_scores):
        """Test structural break detection."""
        validator.conflict_data = conflict_data
        validator.market_integration_scores = integration_scores
        validator.conflict_threshold = 10
        
        results = validator._structural_break_analysis()
        
        if 'n_breaks_tested' in results:
            assert results['n_breaks_tested'] >= 0
            assert 'n_significant_breaks' in results
            
            if results['n_breaks_tested'] > 0:
                assert 'break_details' in results
                break_df = results['break_details']
                assert 'break_date' in break_df.columns
                assert 'mean_before' in break_df.columns
                assert 'mean_after' in break_df.columns
                assert 'p_value' in break_df.columns
    
    def test_spatial_impact_analysis(self, validator, price_data, conflict_data):
        """Test spatial impact analysis."""
        validator.data = price_data
        validator.conflict_data = conflict_data
        
        results = validator._spatial_impact_analysis()
        
        assert 'directly_affected_markets' in results
        assert 'n_directly_affected' in results
        assert 'n_total_markets' in results
        assert 'pct_markets_affected' in results
        
        # Check volatility comparison if available
        if 'avg_volatility_affected' in results:
            assert 'avg_volatility_unaffected' in results
            assert 'volatility_ratio' in results
            assert 'volatility_test_pvalue' in results
            assert 'higher_volatility_in_conflict' in results
    
    def test_granger_causality_tests(self, validator, conflict_data, integration_scores):
        """Test Granger causality analysis."""
        validator.conflict_data = conflict_data
        validator.market_integration_scores = integration_scores
        
        # Mock grangercausalitytests to avoid statsmodels dependency
        with patch('yemen_market.models.three_tier.tier3_validation.conflict_validation.grangercausalitytests') as mock_granger:
            # Create mock results
            mock_results = {}
            for lag in range(1, 5):
                mock_results[lag] = [{'ssr_ftest': (2.5, 0.03)}]
            
            mock_granger.return_value = mock_results
            
            results = validator._granger_causality_tests()
            
            if results:  # May be empty if insufficient data
                assert 'conflict_causes_integration' in results or 'integration_causes_conflict' in results
                
                # Check for significant relationships
                if 'significant_relationships' in results:
                    assert isinstance(results['significant_relationships'], list)
    
    def test_predict_not_applicable(self, validator):
        """Test that predict method returns empty series."""
        validator.is_fitted = True
        
        predictions = validator.predict()
        
        assert isinstance(predictions, pd.Series)
        assert len(predictions) == 0
    
    def test_get_conflict_impact_summary(self, validator, price_data, conflict_data, integration_scores):
        """Test conflict impact summary generation."""
        validator.fit(price_data, conflict_data=conflict_data, integration_scores=integration_scores)
        
        if 'event_study' in validator.results.tier_specific:
            event_details = validator.results.tier_specific['event_study'].get('event_details')
            if event_details is not None and not event_details.empty:
                summary = validator.get_conflict_impact_summary()
                
                assert isinstance(summary, pd.DataFrame)
                if not summary.empty:
                    assert 'total_fatalities' in summary.columns
                    assert 'avg_integration_impact' in summary.columns
                    assert 'n_significant_events' in summary.columns
    
    def test_fit_without_conflict_data(self, validator, price_data):
        """Test fitting without conflict data."""
        # Should work but skip Granger causality
        validator.fit(price_data)
        
        assert validator.is_fitted
        assert 'granger_causality' not in validator.results.tier_specific
    
    def test_summary_statistics(self, validator, price_data, conflict_data):
        """Test summary statistics in results."""
        validator.fit(price_data, conflict_data=conflict_data)
        
        results = validator.results
        
        # Check metadata
        assert results.metadata['n_observations'] > 0
        assert results.metadata['n_conflict_events'] == len(conflict_data)
        assert results.metadata['total_fatalities'] == conflict_data['fatalities'].sum()
        
        # Check coefficients
        if 'avg_conflict_impact' in results.coefficients:
            assert isinstance(results.coefficients['avg_conflict_impact'], (int, float))


class TestConflictValidationEdgeCases:
    """Test edge cases and error handling."""
    
    @pytest.fixture
    def validator(self):
        """Create validator instance."""
        return ConflictIntegrationValidator()
    
    def test_empty_conflict_data(self, validator):
        """Test handling of empty conflict data."""
        price_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'governorate': 'Sana\'a',
            'commodity': 'wheat',
            'usd_price': np.random.randn(10) + 100
        })
        
        empty_conflicts = pd.DataFrame(columns=['date', 'governorate', 'fatalities'])
        
        validator.fit(price_data, conflict_data=empty_conflicts)
        
        assert validator.is_fitted
        # Should handle empty conflict data gracefully
    
    def test_no_significant_conflicts(self, validator):
        """Test when no conflicts meet threshold."""
        price_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=100),
            'governorate': 'Sana\'a',
            'commodity': 'wheat',
            'usd_price': np.random.randn(100) + 100
        })
        
        # All conflicts below threshold
        conflict_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=5),
            'governorate': 'Sana\'a',
            'fatalities': [1, 2, 3, 4, 5],
            'event_type': 'Minor'
        })
        
        validator.conflict_threshold = 10  # Higher than any event
        validator.fit(price_data, conflict_data=conflict_data)
        
        assert validator.is_fitted
        
        # Event study should handle no major conflicts
        event_results = validator.results.tier_specific.get('event_study', {})
        assert event_results.get('n_events_analyzed', 0) == 0
    
    def test_misaligned_time_periods(self, validator):
        """Test when price and conflict data have different time ranges."""
        # Price data: 2020
        price_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=52, freq='W'),
            'governorate': 'Sana\'a',
            'commodity': 'wheat',
            'usd_price': np.random.randn(52) + 100
        })
        
        # Conflict data: 2021 (no overlap)
        conflict_data = pd.DataFrame({
            'date': pd.date_range('2021-01-01', periods=10),
            'governorate': 'Sana\'a',
            'fatalities': np.random.randint(10, 50, 10),
            'event_type': 'Battle'
        })
        
        validator.fit(price_data, conflict_data=conflict_data)
        
        assert validator.is_fitted
        # Should handle misaligned periods without crashing