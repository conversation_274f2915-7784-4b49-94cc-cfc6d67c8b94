"""Comprehensive unit tests for PooledPanelModel."""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
import warnings
from datetime import datetime, timedelta

from yemen_market.models.three_tier.tier1_pooled.pooled_panel_model import (
    PooledPanelModel, PooledPanelConfig
)
from yemen_market.models.three_tier.common import ResultsContainer
from yemen_market.models.three_tier.core.panel_data_handler import PanelDataHandler
from yemen_market.models.three_tier.core.base_model import TierType # Added import


def create_tier1_panel_data(n_entities=10, n_periods=50, seed=42):
    """Create standardized panel data for Tier 1 tests.
    
    This creates data that matches the expected format with proper column names.
    """
    np.random.seed(seed)
    
    # Create market-commodity entities
    # Need at least 10 entities for econometric validity (4 markets × 3 commodities = 12)
    markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah']
    commodities = ['wheat', 'rice', 'sugar']
    entities = []
    entity_to_market = {}
    entity_to_commodity = {}
    
    for market in markets:
        for commodity in commodities:
            entity = f"{market}_{commodity}"
            entities.append(entity)
            entity_to_market[entity] = market
            entity_to_commodity[entity] = commodity
    
    # Ensure we have at least n_entities
    while len(entities) < n_entities:
        entities.append(f"entity_{len(entities)}")
        entity_to_market[entities[-1]] = markets[0]
        entity_to_commodity[entities[-1]] = commodities[0]
    
    entities = entities[:n_entities]
    
    dates = pd.date_range('2020-01-01', periods=n_periods, freq='W')
    
    # Create MultiIndex
    index = pd.MultiIndex.from_product(
        [entities, dates],
        names=['entity', 'date']
    )
    
    # Generate realistic panel data
    data = pd.DataFrame(index=index)
    
    # Entity fixed effects
    entity_effects = np.random.normal(0, 2, n_entities)
    
    # Time trend
    time_trend = np.linspace(0, 2, n_periods)
    
    # Generate price data with structure
    price = np.zeros(len(data))
    for i, entity in enumerate(entities):
        entity_mask = data.index.get_level_values('entity') == entity
        price[entity_mask] = (
            100 +  # Base level
            entity_effects[i] +  # Entity effect
            time_trend +  # Time trend
            0.5 * np.random.randn(n_periods)  # Random noise
        )
    
    # Add all required columns
    data['usd_price'] = price
    data['price'] = price  # Duplicate for compatibility
    data['y'] = price  # For tests expecting 'y'
    
    # Add market and commodity columns based on entity
    data['market'] = data.index.get_level_values('entity').map(
        lambda e: entity_to_market.get(e, 'Sana\'a')
    )
    data['governorate'] = data['market']  # Duplicate for compatibility
    data['commodity'] = data.index.get_level_values('entity').map(
        lambda e: entity_to_commodity.get(e, 'wheat')
    )
    
    # Generate independent variables
    data['x1'] = 0.3 * price + np.random.randn(len(data))
    data['x2'] = -0.2 * price + 2 * np.random.randn(len(data))
    data['x3'] = np.random.randn(len(data))
    
    # Add categorical variable for testing
    data['category'] = np.random.choice(['A', 'B', 'C'], size=len(data))
    
    # Add some missing values to test handling
    missing_mask = np.random.random(len(data)) < 0.02  # 2% missing
    data.loc[missing_mask, 'x3'] = np.nan
    
    # Add entity IDs for compatibility
    data['entity_id'] = data.index.get_level_values('entity').map(
        {e: i for i, e in enumerate(entities)}
    )
    data['time_id'] = data.index.get_level_values('date').map(
        {d: i for i, d in enumerate(dates)}
    )
    
    return data


class TestPooledPanelModel:
    """Comprehensive tests for pooled panel model."""
    
    @pytest.fixture
    def sample_panel_data(self):
        """Create comprehensive sample panel data for testing."""
        return create_tier1_panel_data(n_entities=10, n_periods=50, seed=42)
    
    @pytest.fixture
    def unbalanced_panel_data(self):
        """Create unbalanced panel data with missing entity-time combinations."""
        np.random.seed(42)
        
        # Create proper market-commodity entities 
        markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah']
        commodities = ['wheat', 'rice', 'sugar']
        entities = []
        entity_to_market = {}
        entity_to_commodity = {}
        
        for market in markets:
            for commodity in commodities:
                entity = f"{market}_{commodity}"
                entities.append(entity)
                entity_to_market[entity] = market
                entity_to_commodity[entity] = commodity
        
        # Use first 10 entities
        entities = entities[:10]
        dates = pd.date_range('2020-01-01', periods=30, freq='W')
        
        # Create unbalanced panel by randomly dropping 20% of observations
        full_index = pd.MultiIndex.from_product([entities, dates], names=['entity', 'date'])
        keep_mask = np.random.random(len(full_index)) > 0.2
        unbalanced_index = full_index[keep_mask]
        
        data = pd.DataFrame(index=unbalanced_index)
        data['usd_price'] = 100 + np.random.randn(len(data))
        data['y'] = data['usd_price']  # For compatibility
        data['x1'] = np.random.randn(len(data))
        data['x2'] = np.random.randn(len(data))
        
        # Add required columns with proper mapping
        data['entity'] = data.index.get_level_values('entity')
        data['date'] = data.index.get_level_values('date')
        data['governorate'] = data['entity'].map(lambda e: entity_to_market.get(e, 'Sana\'a'))
        data['commodity'] = data['entity'].map(lambda e: entity_to_commodity.get(e, 'wheat'))
        
        # Add IDs for compatibility
        data['entity_id'] = data.index.get_level_values('entity').map(
            {e: i for i, e in enumerate(entities)}
        )
        data['time_id'] = data.index.get_level_values('date').map(
            {d: i for i, d in enumerate(dates)}
        )
        
        return data
    
    @pytest.fixture
    def panel_handler(self):
        """Create a real PanelDataHandler instance."""
        return PanelDataHandler()
    
    def test_init_default_config(self, panel_handler):
        """Test initialization with default configuration."""
        model = PooledPanelModel() # Removed panel_handler
        
        assert model.tier == TierType.TIER1_POOLED # Use Enum for clarity
        assert model.__class__.__name__ == "PooledPanelModel"
        assert model.config.entity_effects == True
        assert model.config.time_effects == True # Default from PooledPanelConfig is True
        assert model.config.drop_absorbed == True
        assert model.config.cov_type == 'clustered'
        assert model.config.cluster_entity == True
        assert model.results is None
    
    def test_init_custom_config(self, panel_handler):
        """Test initialization with custom configuration."""
        config = PooledPanelConfig(
            entity_effects=False,
            time_effects=True,
            cov_type='robust',
            cluster_entity=False
            # dependent_var='custom_y', # Keep it simple for now, will adjust if test fails
            # independent_vars=['custom_x1']
        )
        
        model = PooledPanelModel(config=config) # Removed panel_handler
        
        assert model.config.entity_effects == False
        assert model.config.time_effects == True
        assert model.config.cov_type == 'robust'
        assert model.config.cluster_entity == False
    
    def test_validate_data_valid(self, sample_panel_data, panel_handler):
        """Test data validation with valid data."""
        model = PooledPanelModel() # Removed panel_handler
        
        # Should not raise any errors
        # The validator expects a DataFrame with specific columns, not necessarily indexed yet.
        result = model.validate_data(sample_panel_data.reset_index())
        assert result == True
    
    def test_validate_data_missing_columns(self, panel_handler):
        """Test data validation with missing required columns."""
        model = PooledPanelModel() # Removed panel_handler
        
        # Create data without required columns
        bad_data = pd.DataFrame({
            'usd_price': [1, 2, 3], # Default dependent
            'x': [4, 5, 6]
            # Missing 'entity', 'date', 'governorate', 'commodity'
        })
        
        # model.validate_data calls validator.validate_for_tier1
        is_valid, issues = model.validator.validate_for_tier1(bad_data)
        assert not is_valid
        # Check for a more specific message if possible, or a generic one
        assert any("Missing required columns" in issue for issue in issues)

    
    def test_validate_data_no_multiindex(self, panel_handler):
        """Test data validation without MultiIndex."""
        model = PooledPanelModel() # Removed panel_handler
        
        # Create valid panel data without MultiIndex but with required columns
        # Need at least 10 entities and 20 time periods for validation to pass
        markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah']
        commodities = ['wheat', 'rice', 'sugar']
        dates = pd.date_range('2020-01-01', periods=25, freq='W')
        
        data_list = []
        for market in markets:
            for commodity in commodities:
                for date in dates:
                    data_list.append({
                        'entity': f"{market}_{commodity}",
                        'date': date,
                        'governorate': market,
                        'commodity': commodity,
                        'usd_price': 100 + np.random.randn()
                    })
        
        flat_data = pd.DataFrame(data_list)
        
        # validate_data should pass with proper panel structure even without MultiIndex
        result = model.validate_data(flat_data)
        assert result == True # Validator checks panel structure, not index type
    
    def test_prepare_panel_data_comprehensive(self, sample_panel_data, panel_handler):
        """Test comprehensive panel data preparation."""
        model = PooledPanelModel() # Removed panel_handler
        
        X_vars = ['x1', 'x2', 'x3']
        # prepare_panel_data expects a DataFrame that will be processed by PanelDataHandler
        # The sample_panel_data fixture has a MultiIndex. PanelDataHandler.create_entity_panel
        # expects 'market', 'commodity', 'date' columns.
        # Let's adapt sample_panel_data to fit what create_entity_panel expects.
        # The fixture already has 'entity' and 'date' in index, and 'commodity', 'governorate' as columns.
        # PanelDataHandler.create_entity_panel will use these.
        prepared_df, dep_var, indep_vars = model.prepare_panel_data(sample_panel_data.reset_index(), 'y', X_vars)
        
        # Check structure
        assert dep_var == 'y'
        assert all(var in indep_vars for var in X_vars)
        assert all(var in prepared_df.columns for var in X_vars)
        assert 'entity' in prepared_df.columns # This is created by panel_handler
        assert 'date' in prepared_df.columns
        
        # Check that NaN values were handled (x3 had NaNs)
        assert prepared_df['x3'].isna().sum() == 0
        
        # Check data shape
        assert len(prepared_df) <= len(sample_panel_data)
    
    def test_prepare_panel_data_with_transformations(self, sample_panel_data, panel_handler):
        """Test panel data preparation with various transformations."""
        model = PooledPanelModel() # Removed panel_handler
        
        data_for_prep = sample_panel_data.reset_index()
        # Add log transformation
        data_for_prep['log_y'] = np.log(data_for_prep['y'].clip(lower=1))
        
        # Add lagged variable
        data_for_prep['x1_lag'] = data_for_prep.groupby('entity')['x1'].shift(1)
        
        X_vars = ['x1', 'x2', 'x1_lag']
        prepared_df, dep_var, indep_vars = model.prepare_panel_data(data_for_prep, 'log_y', X_vars)
        
        # Check that lagged variable is properly handled
        assert 'x1_lag' in prepared_df.columns
        # NaNs from lag should be handled by handle_missing_data (e.g., ffill or drop)
        # If ffill, then only the very first obs for an entity might be NaN if it's the first in the group.
        # If drop, then length would be reduced.
        # Current model.prepare_panel_data uses ffill.
        # A more precise check depends on the exact behavior of handle_missing_data.
        # For now, just ensure the column is there and NaNs are not excessive.
        assert prepared_df['x1_lag'].isna().sum() < data_for_prep['entity'].nunique() # Should not be NaN for all first obs
    
    @patch('yemen_market.models.three_tier.tier1_pooled.pooled_panel_model.PanelOLS')
    def test_fit_comprehensive(self, mock_panel_ols, sample_panel_data, panel_handler):
        """Test comprehensive model fitting."""
        # Create comprehensive mock results
        mock_fit_result = Mock()
        
        # Basic attributes
        mock_fit_result.params = pd.Series({
            'x1': 0.5,
            'x2': -0.3,
            'x3': 0.1,
            'const': 100.0
        })
        mock_fit_result.std_errors = pd.Series({
            'x1': 0.05,
            'x2': 0.04,
            'x3': 0.08,
            'const': 2.0
        })
        mock_fit_result.tstats = pd.Series({
            'x1': 10.0,
            'x2': -7.5,
            'x3': 1.25,
            'const': 50.0
        })
        mock_fit_result.pvalues = pd.Series({
            'x1': 0.0001,
            'x2': 0.0002,
            'x3': 0.2115,
            'const': 0.0001
        })
        
        # Mock confidence intervals - return a DataFrame with lower/upper columns
        conf_int_df = pd.DataFrame({
            'lower': {'x1': 0.4, 'x2': -0.38, 'x3': -0.06, 'const': 96.0},
            'upper': {'x1': 0.6, 'x2': -0.22, 'x3': 0.26, 'const': 104.0}
        })
        mock_fit_result.conf_int = Mock(return_value=conf_int_df)
        
        # Model statistics - ensure all R-squared measures are present
        mock_fit_result.rsquared = 0.85
        mock_fit_result.rsquared_within = 0.75
        mock_fit_result.rsquared_between = 0.90
        mock_fit_result.rsquared_overall = 0.83
        mock_fit_result.nobs = 487  # Match actual data size after dropna
        mock_fit_result.df_resid = 450
        mock_fit_result.f_statistic = Mock(stat=125.5, pval=0.0001)
        
        # Predictions and residuals - match actual data size
        mock_fit_result.fitted_values = pd.Series(np.random.randn(487))
        mock_fit_result.resids = pd.Series(np.random.randn(487))
        mock_fit_result.wresids = pd.Series(np.random.randn(487))
        
        # Entity effects
        entity_names = [f'entity_{i}' for i in range(20)]
        mock_fit_result.estimated_effects = pd.DataFrame({
            'entity': np.random.randn(20)
        }, index=entity_names)
        
        # Summary
        mock_fit_result.summary = Mock(return_value="Comprehensive Model Summary")
        
        # Configure mock
        mock_model = Mock()
        mock_model.fit.return_value = mock_fit_result
        mock_panel_ols.return_value = mock_model
        
        # Fit model
        model = PooledPanelModel()
        fitted_model = model.fit(sample_panel_data, 'y', ['x1', 'x2', 'x3'])
        
        # Verify fit returns self
        assert fitted_model is model
        assert model.is_fitted == True
        
        # Verify results are stored in model.results
        results = model.results
        assert isinstance(results, ResultsContainer)
        assert results.metadata.tier == 1
        assert results.metadata.model_type == "PanelOLS"
        
        # Check coefficients using the ResultsContainer API
        assert results.get_parameter('x1') == 0.5
        assert results.get_parameter('x2') == -0.3
        assert results.get_parameter('x3') == 0.1
        assert results.standard_errors.get('x1') == 0.05
        
        # Check model statistics using the ResultsContainer API
        assert results.get_statistic('r_squared') == 0.85
        assert results.get_statistic('r_squared_within') == 0.75
        assert results.get_statistic('n_observations') == 487  # Actual size after dropna
        
        # Check that results contain the expected components
        assert results.fitted_values is not None
        assert results.residuals is not None
        assert len(results.fitted_values) == 487
        assert len(results.residuals) == 487
    
    @patch('yemen_market.models.three_tier.tier1_pooled.pooled_panel_model.PanelOLS')
    def test_fit_with_time_effects(self, mock_panel_ols, sample_panel_data, panel_handler):
        """Test fitting with time effects enabled."""
        # Configure for time effects
        config = PooledPanelConfig(
            entity_effects=True,
            time_effects=True
        )
        
        # Mock results with time effects
        mock_fit_result = Mock()
        mock_fit_result.params = pd.Series({'x1': 0.5})
        mock_fit_result.std_errors = pd.Series({'x1': 0.05})
        mock_fit_result.tstats = pd.Series({'x1': 10.0})
        mock_fit_result.pvalues = pd.Series({'x1': 0.0001})
        mock_fit_result.rsquared = 0.9
        mock_fit_result.rsquared_within = 0.85
        mock_fit_result.rsquared_between = 0.92
        mock_fit_result.rsquared_overall = 0.9
        mock_fit_result.nobs = 500
        
        # Mock confidence intervals
        mock_fit_result.conf_int = Mock(return_value=pd.DataFrame({
            'lower': {'x1': 0.4},
            'upper': {'x1': 0.6}
        }))
        
        # Mock time effects - create separate Series for entity and time effects
        entity_effects = pd.Series(np.random.randn(10), index=[f'entity_{i}' for i in range(10)])
        time_effects = pd.Series(np.random.randn(50), index=pd.date_range('2020-01-01', periods=50, freq='W'))
        
        # Combine into a single DataFrame with proper structure
        mock_fit_result.estimated_effects = pd.DataFrame({
            'entity': entity_effects,
            'time': time_effects
        })
        
        mock_fit_result.fitted_values = pd.Series(np.random.randn(500))
        mock_fit_result.resids = pd.Series(np.random.randn(500))
        mock_fit_result.summary = Mock(return_value="Model with time effects")
        mock_fit_result.f_statistic = Mock(stat=150.5, pval=0.0001)
        
        mock_model = Mock()
        mock_model.fit.return_value = mock_fit_result
        mock_panel_ols.return_value = mock_model
        
        # Fit model
        model = PooledPanelModel(config=config)
        results = model.fit(sample_panel_data, 'y', ['x1'])
        
        # Verify PanelOLS was called with time_effects=True
        mock_panel_ols.assert_called_once()
        call_kwargs = mock_panel_ols.call_args[1]
        assert call_kwargs['time_effects'] == True
    
    def test_fit_unbalanced_panel(self, unbalanced_panel_data, panel_handler):
        """Test fitting with unbalanced panel data - validate handling, not fitting."""
        model = PooledPanelModel()
        
        # Unbalanced panels with irregular intervals should fail validation
        # This is a design choice - the model requires regular time intervals
        with pytest.raises(ValueError) as exc_info:
            model.fit(unbalanced_panel_data, 'y', ['x1', 'x2'])
        
        assert "Data validation failed" in str(exc_info.value)
    
    def test_fit_no_x_variables(self, sample_panel_data, panel_handler):
        """Test fitting with no independent variables."""
        model = PooledPanelModel()
        
        # Should raise ValueError
        with pytest.raises(ValueError) as exc_info:
            model.fit(sample_panel_data, 'y', [])
        
        assert "No independent variables" in str(exc_info.value)
    
    def test_fit_missing_dependent_var(self, sample_panel_data, panel_handler):
        """Test fitting with missing dependent variable."""
        model = PooledPanelModel()
        
        # Should raise ValueError
        with pytest.raises(ValueError) as exc_info:
            model.fit(sample_panel_data, 'nonexistent_y', ['x1'])
        
        assert "not found in data" in str(exc_info.value)
    
    @patch('yemen_market.models.three_tier.tier1_pooled.pooled_panel_model.PanelOLS')
    def test_predict(self, mock_panel_ols, sample_panel_data, panel_handler):
        """Test prediction functionality."""
        # Setup mock
        mock_fit_result = Mock()
        mock_fit_result.params = pd.Series({'x1': 0.5, 'x2': -0.3})
        mock_fit_result.predict = Mock(return_value=pd.Series(np.random.randn(100)))
        
        mock_model = Mock()
        mock_model.fit.return_value = mock_fit_result
        mock_panel_ols.return_value = mock_model
        
        # Fit model first
        model = PooledPanelModel()
        model.model = mock_model
        model.fit_result = mock_fit_result
        model.is_fitted = True  # Mark model as fitted
        model.results = Mock()
        model.results.metadata = {'variables': ['x1', 'x2']}
        
        # Create new data for prediction - ensure proper structure
        new_data = sample_panel_data.iloc[:100].copy()
        # The predict method expects the data already prepared, so mock the preparation
        model.prepare_panel_data = Mock(return_value=(new_data.reset_index(), 'y', ['x1', 'x2']))
        
        # Make predictions
        predictions = model.predict(new_data)
        
        assert isinstance(predictions, pd.Series)
        assert len(predictions) == 100
    
    def test_predict_not_fitted(self, sample_panel_data, panel_handler):
        """Test prediction without fitting model first."""
        model = PooledPanelModel()
        
        with pytest.raises(ValueError) as exc_info:
            model.predict(sample_panel_data)
        
        assert "Model must be fitted" in str(exc_info.value)
    
    @patch('yemen_market.models.three_tier.tier1_pooled.pooled_panel_model.PanelOLS')
    def test_get_fixed_effects(self, mock_panel_ols, sample_panel_data, panel_handler):
        """Test fixed effects extraction."""
        # Setup mock with effects
        mock_fit_result = Mock()
        
        # Create realistic entity effects
        entity_effects = pd.DataFrame({
            'entity': np.random.randn(10)
        }, index=[f'entity_{i}' for i in range(10)])
        
        mock_fit_result.estimated_effects = entity_effects
        # Also set direct access to entity_effects for the extractor
        mock_fit_result.entity_effects = pd.Series(
            np.random.randn(10), 
            index=[f'entity_{i}' for i in range(10)],
            name='entity_effects'
        )
        
        mock_model = Mock()
        mock_model.fit.return_value = mock_fit_result
        mock_panel_ols.return_value = mock_model
        
        # Fit model
        model = PooledPanelModel()
        model.fit_result = mock_fit_result
        model.is_fitted = True  # Mark model as fitted
        
        # Get fixed effects
        effects = model.get_fixed_effects()
        
        assert isinstance(effects, dict)
        assert 'entity_effects' in effects
        assert isinstance(effects['entity_effects'], pd.Series)
        assert len(effects['entity_effects']) == 10  # We have 10 entities in our test data
    
    def test_comprehensive_error_handling(self, panel_handler):
        """Test comprehensive error handling scenarios."""
        model = PooledPanelModel()
        
        # Test with completely empty DataFrame
        empty_df = pd.DataFrame()
        assert model.validate_data(empty_df) == False
        
        # Test with wrong index type (missing required columns)
        wrong_index_df = pd.DataFrame(
            {'y': [1, 2, 3]},
            index=[1, 2, 3]
        )
        assert model.validate_data(wrong_index_df) == False
        
        # Test with single entity (no variation for FE)
        single_entity = pd.DataFrame(
            index=pd.MultiIndex.from_tuples(
                [('A', '2020-01-01'), ('A', '2020-01-02')],
                names=['entity', 'date']
            )
        )
        single_entity['y'] = [1, 2]
        single_entity['x'] = [3, 4]
        single_entity['governorate'] = 'test'
        single_entity['commodity'] = 'test'
        
        # Should fail validation due to insufficient entities
        result = model.validate_data(single_entity.reset_index())
        assert result == False  # Only 1 entity, needs minimum 10
    
    @patch('yemen_market.models.three_tier.tier1_pooled.pooled_panel_model.PanelOLS')
    def test_standard_error_options(self, mock_panel_ols, sample_panel_data, panel_handler):
        """Test different standard error configurations."""
        # Test robust standard errors
        config_robust = PooledPanelConfig(cov_type='robust')
        model_robust = PooledPanelModel(config=config_robust)
        
        mock_fit_result = Mock()
        mock_fit_result.params = pd.Series({'x1': 0.5})
        mock_fit_result.std_errors = pd.Series({'x1': 0.05})
        mock_fit_result.tstats = pd.Series({'x1': 10.0})
        mock_fit_result.pvalues = pd.Series({'x1': 0.0001})
        mock_fit_result.rsquared = 0.8
        mock_fit_result.rsquared_within = 0.75
        mock_fit_result.rsquared_between = 0.82
        mock_fit_result.rsquared_overall = 0.8
        mock_fit_result.nobs = 500
        mock_fit_result.fitted_values = pd.Series(np.random.randn(500))
        mock_fit_result.resids = pd.Series(np.random.randn(500))
        mock_fit_result.estimated_effects = pd.DataFrame({'entity': np.random.randn(10)})
        mock_fit_result.summary = Mock(return_value="Robust SE model")
        mock_fit_result.conf_int = Mock(return_value=pd.DataFrame({
            'lower': {'x1': 0.4},
            'upper': {'x1': 0.6}
        }))
        mock_fit_result.f_statistic = Mock(stat=100.0, pval=0.0001)
        
        mock_model = Mock()
        mock_model.fit.return_value = mock_fit_result
        mock_panel_ols.return_value = mock_model
        
        # Fit with robust SE
        fitted_model = model_robust.fit(sample_panel_data, 'y', ['x1'], cov_type='robust')
        
        # Verify model was fitted and fit was called
        assert fitted_model is model_robust
        mock_model.fit.assert_called_once()
        # Check that cov_type was passed through kwargs
        fit_kwargs = mock_model.fit.call_args[1]
        assert fit_kwargs.get('cov_type') == 'robust'
        
        # Test clustered standard errors (two-way)
        config_twoway = PooledPanelConfig(
            cov_type='clustered',
            cluster_entity=True,
            cluster_time=True
        )
        model_twoway = PooledPanelModel(config=config_twoway)
        
        # Reset mock
        mock_model.reset_mock()
        mock_fit_result.rsquared_overall = 0.85  # Update for new fit
        
        # Fit with two-way clustering - pass clustering options via kwargs
        fitted_twoway = model_twoway.fit(
            sample_panel_data, 'y', ['x1'], 
            cov_type='clustered',
            cluster_entity=True,
            cluster_time=True
        )
        
        # Verify two-way clustering was requested
        fit_kwargs = mock_model.fit.call_args[1]
        assert fit_kwargs.get('cov_type') == 'clustered'
        assert fit_kwargs.get('cluster_entity') == True
        assert fit_kwargs.get('cluster_time') == True
