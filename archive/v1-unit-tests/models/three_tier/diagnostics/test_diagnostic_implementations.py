"""
Unit tests for diagnostic test implementations.
"""
import pytest
import pandas as pd
import numpy as np
import statsmodels.api as sm # For generating test data

from yemen_market.models.three_tier.diagnostics.test_implementations import (
    wooldridge_serial_correlation,
    pesaran_cd_test,
    ips_unit_root_test,
    PanelInfo
)

# Fixtures for common panel data structures can be added here later.

class TestWooldridgeSerialCorrelation:
    """
    Tests for the Wooldridge serial correlation test.
    """

    def test_no_serial_correlation(self):
        """
        Test with data that should ideally show no serial correlation.
        H0: No serial correlation. Expect p-value > 0.05.
        """
        N = 10  # Number of entities
        T = 20  # Number of time periods

        # Create MultiIndex
        idx = pd.MultiIndex.from_product([range(N), range(T)], names=['entity', 'time'])

        # Generate random residuals (white noise)
        np.random.seed(42)
        residuals_arr = np.random.normal(0, 1, size=N*T)
        residuals = pd.Series(residuals_arr, index=idx, name='resid')

        panel_info: PanelInfo = {
            "entity_id": "entity",
            "time_id": "time",
            "entities": list(range(N)),
            "time_periods": list(range(T)),
            "is_balanced": True,
            "N": N,
            "T_max": T,
            "T_min": T,
            "T_avg": T,
            "nobs": N * T,
        }

        stat, p_value, recommendation = wooldridge_serial_correlation(residuals, panel_info)

        assert isinstance(stat, float), "Statistic should be a float."
        assert isinstance(p_value, float), "P-value should be a float."
        assert isinstance(recommendation, str), "Recommendation should be a string."

        # For purely random residuals, we expect to not reject H0 (no serial correlation)
        # However, with small samples or by chance, this might fail.
        # This is an illustrative test; true validation needs comparison with established packages.
        print(f"Wooldridge (No Corr): stat={stat:.4f}, p-value={p_value:.4f}")
        assert p_value > 0.05, f"Expected no serial correlation (p > 0.05), got p={p_value:.4f}"
        assert "No significant evidence of serial correlation" in recommendation

    def test_with_serial_correlation(self):
        """
        Test with data that has AR(1) serial correlation.
        H0: No serial correlation. Expect p-value < 0.05.
        """
        N = 10
        T = 50 # Longer T to better detect AR(1)
        rho = 0.8 # AR(1) coefficient

        idx = pd.MultiIndex.from_product([range(N), range(T)], names=['entity', 'time'])

        # Generate AR(1) residuals for each entity
        np.random.seed(123)
        residuals_list = []
        for i in range(N):
            ar1_process = np.zeros(T)
            ar1_process[0] = np.random.normal(0, 1) / np.sqrt(1 - rho**2) # Stationary start
            for t in range(1, T):
                ar1_process[t] = rho * ar1_process[t-1] + np.random.normal(0, 1)
            residuals_list.extend(ar1_process)

        residuals = pd.Series(residuals_list, index=idx, name='resid')

        panel_info: PanelInfo = {
            "entity_id": "entity",
            "time_id": "time",
            "entities": list(range(N)),
            "time_periods": list(range(T)),
            "is_balanced": True,
            "N": N,
            "T_max": T,
            "T_min": T,
            "T_avg": T,
            "nobs": N * T,
        }

        stat, p_value, recommendation = wooldridge_serial_correlation(residuals, panel_info)

        assert isinstance(stat, float)
        assert isinstance(p_value, float)
        assert isinstance(recommendation, str)

        print(f"Wooldridge (With Corr): stat={stat:.4f}, p-value={p_value:.4f}")
        assert p_value <= 0.05, f"Expected serial correlation (p <= 0.05), got p={p_value:.4f}"
        assert "evidence of serial correlation" in recommendation.lower()

    def test_insufficient_data(self):
        """Test with insufficient data (e.g., T too small after lagging)."""
        N = 5
        T = 1 # Only 1 time period, so lagging will result in empty data
        idx = pd.MultiIndex.from_product([range(N), range(T)], names=['entity', 'time'])
        residuals = pd.Series(np.random.normal(0, 1, size=N*T), index=idx, name='resid')

        panel_info: PanelInfo = {"entity_id": "entity", "time_id": "time", "N": N, "nobs": N*T} # Simplified

        stat, p_value, recommendation = wooldridge_serial_correlation(residuals, panel_info)

        assert np.isnan(stat), "Statistic should be NaN for insufficient data."
        assert np.isnan(p_value), "P-value should be NaN for insufficient data."
        assert "Not enough data" in recommendation or "Not enough observations" in recommendation


class TestPesaranCD:
    """Tests for Pesaran CD test."""

    def test_no_cross_sectional_dependence(self):
        """Test with data that has no cross-sectional dependence."""
        N = 10
        T = 30
        idx = pd.MultiIndex.from_product([range(N), range(T)], names=['entity', 'time'])

        np.random.seed(42)
        # Independent residuals for each entity
        residuals_arr = np.random.normal(0, 1, size=N*T)
        residuals = pd.Series(residuals_arr, index=idx, name='resid')

        panel_info: PanelInfo = {
            "entity_id": "entity", "time_id": "time", "N": N, "nobs": N*T,
            "entities": list(range(N)), "time_periods": list(range(T)),
            "is_balanced": True, "T_max": T, "T_min": T, "T_avg": T
        }

        stat, p_value, recommendation = pesaran_cd_test(residuals, panel_info)
        print(f"Pesaran CD (No Dep): stat={stat:.4f}, p-value={p_value:.4f}")
        assert p_value > 0.05, f"Expected no CS dependence (p > 0.05), got p={p_value:.4f}"
        assert "No significant evidence of cross-sectional dependence" in recommendation

    def test_with_cross_sectional_dependence(self):
        """Test with data that has cross-sectional dependence (common factor)."""
        N = 10
        T = 30
        idx = pd.MultiIndex.from_product([range(N), range(T)], names=['entity', 'time'])

        np.random.seed(123)
        common_factor = np.random.normal(0, 1, size=T)
        residuals_list = []
        for i in range(N):
            # Each entity's residuals are its own noise + common factor
            entity_error = np.random.normal(0, 0.5, size=T) # Smaller idiosyncratic variance
            residuals_list.extend(entity_error + common_factor)

        residuals = pd.Series(residuals_list, index=idx, name='resid')

        panel_info: PanelInfo = {
            "entity_id": "entity", "time_id": "time", "N": N, "nobs": N*T,
            "entities": list(range(N)), "time_periods": list(range(T)),
            "is_balanced": True, "T_max": T, "T_min": T, "T_avg": T
        }

        stat, p_value, recommendation = pesaran_cd_test(residuals, panel_info)
        print(f"Pesaran CD (With Dep): stat={stat:.4f}, p-value={p_value:.4f}")
        assert p_value <= 0.05, f"Expected CS dependence (p <= 0.05), got p={p_value:.4f}"
        assert "evidence of cross-sectional dependence" in recommendation.lower()

    def test_unbalanced_panel_pesaran_cd(self):
        """Test Pesaran CD with an unbalanced panel."""
        N = 5
        T_max = 20
        entities = [f"entity_{i}" for i in range(N)]

        # Create an unbalanced panel
        all_indices = []
        all_residuals = []
        np.random.seed(777)
        common_factor = np.random.normal(0, 1, size=T_max)

        for i, entity_name in enumerate(entities):
            current_T = T_max - i * 2 # Make it unbalanced (20, 18, 16, 14, 12)
            times = range(current_T)
            entity_indices = [(entity_name, t) for t in times]
            all_indices.extend(entity_indices)

            # Simulate some dependence
            entity_residuals = np.random.normal(0, 0.7, size=current_T) + 0.5 * common_factor[:current_T]
            all_residuals.extend(entity_residuals)

        multi_idx = pd.MultiIndex.from_tuples(all_indices, names=['entity', 'time'])
        residuals = pd.Series(all_residuals, index=multi_idx, name='resid')

        panel_info: PanelInfo = {
            "entity_id": "entity", "time_id": "time", "N": N, "nobs": len(residuals),
            "entities": entities, "time_periods": list(range(T_max)), # Max time periods
            "is_balanced": False, "T_max": T_max, "T_min": T_max - (N-1)*2, "T_avg": np.mean([T_max - i*2 for i in range(N)])
        }

        stat, p_value, recommendation = pesaran_cd_test(residuals, panel_info)
        print(f"Pesaran CD (Unbalanced, With Dep): stat={stat:.4f}, p-value={p_value:.4f}")
        # Expect dependence due to common_factor
        assert p_value <= 0.05, f"Expected CS dependence (p <= 0.05) for unbalanced panel, got p={p_value:.4f}"
        assert "evidence of cross-sectional dependence" in recommendation.lower()


class TestIPanelUnitRoot:
    """Tests for IPS Panel Unit Root test using linearmodels."""

    def test_all_series_stationary(self):
        """Test with all panel series being stationary (AR(1) with rho < 1)."""
        N = 5
        T = 50
        rho_stationary = 0.5
        idx = pd.MultiIndex.from_product([range(N), range(T)], names=['entity', 'time'])

        np.random.seed(101)
        series_list = []
        for i in range(N):
            ar_process = np.zeros(T)
            ar_process[0] = np.random.normal(0, 1) / np.sqrt(1 - rho_stationary**2)
            for t in range(1, T):
                ar_process[t] = rho_stationary * ar_process[t-1] + np.random.normal(0, 1)
            series_list.extend(ar_process)

        series_data = pd.Series(series_list, index=idx, name='price') # Name it 'price' or similar

        panel_info: PanelInfo = {
            "entity_id": "entity", "time_id": "time", "N": N, "nobs": N*T,
            "entities": list(range(N)), "time_periods": list(range(T)),
            "is_balanced": True, "T_max": T, "T_min": T, "T_avg": T
        }

        stat, p_value, recommendation = ips_unit_root_test(series_data, panel_info)
        print(f"IPS (All Stationary): stat={stat:.4f}, p-value={p_value:.4f}")
        # H0: all series have unit root. We expect to reject H0.
        assert p_value <= 0.05, f"Expected stationarity (p <= 0.05), got p={p_value:.4f}"
        assert "evidence against unit roots" in recommendation.lower()

    def test_all_series_unit_root(self):
        """Test with all panel series having a unit root (random walks)."""
        N = 5
        T = 50
        idx = pd.MultiIndex.from_product([range(N), range(T)], names=['entity', 'time'])

        np.random.seed(202)
        series_list = []
        for i in range(N):
            # Random walk
            rw_process = np.cumsum(np.random.normal(0, 1, size=T))
            series_list.extend(rw_process)

        series_data = pd.Series(series_list, index=idx, name='price')

        panel_info: PanelInfo = {
            "entity_id": "entity", "time_id": "time", "N": N, "nobs": N*T,
            "entities": list(range(N)), "time_periods": list(range(T)),
            "is_balanced": True, "T_max": T, "T_min": T, "T_avg": T
        }

        stat, p_value, recommendation = ips_unit_root_test(series_data, panel_info)
        print(f"IPS (All Unit Root): stat={stat:.4f}, p-value={p_value:.4f}")
        # H0: all series have unit root. We expect to not reject H0.
        assert p_value > 0.05, f"Expected unit roots (p > 0.05), got p={p_value:.4f}"
        assert "Cannot reject unit roots" in recommendation

    def test_mixed_series_ips(self):
        """Test with a mix of stationary and unit root series."""
        N_stationary = 3
        N_unit_root = 2
        N = N_stationary + N_unit_root
        T = 50
        rho_stationary = 0.5

        entity_names = [f"entity_{i}" for i in range(N)]
        idx = pd.MultiIndex.from_product([entity_names, range(T)], names=['entity', 'time'])

        np.random.seed(303)
        series_list = []
        # Stationary series
        for i in range(N_stationary):
            ar_process = np.zeros(T)
            ar_process[0] = np.random.normal(0, 1) / np.sqrt(1 - rho_stationary**2)
            for t in range(1, T):
                ar_process[t] = rho_stationary * ar_process[t-1] + np.random.normal(0, 1)
            series_list.extend(ar_process)
        # Unit root series
        for i in range(N_unit_root):
            rw_process = np.cumsum(np.random.normal(0, 1, size=T))
            series_list.extend(rw_process)

        series_data = pd.Series(series_list, index=idx, name='price')

        panel_info: PanelInfo = {
            "entity_id": "entity", "time_id": "time", "N": N, "nobs": N*T,
            "entities": entity_names, "time_periods": list(range(T)),
            "is_balanced": True, "T_max": T, "T_min": T, "T_avg": T
        }

        stat, p_value, recommendation = ips_unit_root_test(series_data, panel_info)
        print(f"IPS (Mixed Series): stat={stat:.4f}, p-value={p_value:.4f}")
        # H0: all series have unit root. Ha: some are stationary. We expect to reject H0.
        assert p_value <= 0.05, f"Expected rejection of all unit roots (p <= 0.05) for mixed panel, got p={p_value:.4f}"
        assert "evidence against unit roots" in recommendation.lower()

# To run these tests (from the project root directory):
# Ensure pytest is installed: pip install pytest
# Command: pytest tests/unit/models/three_tier/diagnostics/test_diagnostic_implementations.py
