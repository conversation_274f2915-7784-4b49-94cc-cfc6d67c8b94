"""Comprehensive tests for tier2_commodity module exports and integration."""

import pytest
import yemen_market.models.three_tier.tier2_commodity as t2c
from yemen_market.models.three_tier.core.results_container import ResultsContainer


class TestTier2CommodityModule:
    """Test tier2_commodity module functionality."""
    
    def test_module_exports(self):
        """Test that the module exports all expected symbols."""
        expected_exports = {
            'CommodityExtractor',
            'CommodityExtractorConfig',
            'CommoditySpecificModel',
            'CommodityModelConfig',
            'ThresholdVECM',
            'ThresholdVECMConfig',
            'CointegrationTestSuite',
            'CointegrationTestConfig'
        }
        
        # Check all exports are present
        for export in expected_exports:
            assert hasattr(t2c, export), f"Missing export: {export}"
        
        # Check __all__ matches expected
        assert set(t2c.__all__) == expected_exports
    
    def test_class_imports(self):
        """Test that all classes can be imported and instantiated."""
        # Test CommodityExtractor
        extractor_config = t2c.CommodityExtractorConfig()
        extractor = t2c.CommodityExtractor(extractor_config)
        assert extractor.config == extractor_config
        
        # Test CommoditySpecificModel - requires commodity parameter
        model_config = t2c.CommodityModelConfig()
        model = t2c.CommoditySpecificModel('wheat', model_config)
        assert model.config == model_config
        assert model.commodity == 'wheat'
        
        # Test ThresholdVECM - can accept commodity parameter
        vecm_config = t2c.ThresholdVECMConfig()
        vecm = t2c.ThresholdVECM(commodity='wheat', config=vecm_config)
        assert vecm.config == vecm_config
        assert vecm.commodity == 'wheat'
        
        # Test CointegrationTestSuite
        test_config = t2c.CointegrationTestConfig()
        test_suite = t2c.CointegrationTestSuite(test_config)
        assert test_suite.config == test_config
    
    def test_config_classes(self):
        """Test configuration classes with custom parameters."""
        # Test CommodityExtractorConfig - use correct field names
        extractor_config = t2c.CommodityExtractorConfig(
            min_markets=10,
            min_periods=100
        )
        assert extractor_config.min_markets == 10
        assert extractor_config.min_periods == 100
        
        # Test CommodityModelConfig - use correct parameters
        model_config = t2c.CommodityModelConfig(
            model_type='random_effects',
            cov_type='kernel'
        )
        assert model_config.model_type == 'random_effects'
        assert model_config.cov_type == 'kernel'
        
        # Test ThresholdVECMConfig - use correct field names
        vecm_config = t2c.ThresholdVECMConfig(
            n_lags=3,
            min_obs_per_regime=30,
            bootstrap_reps=500
        )
        assert vecm_config.n_lags == 3
        assert vecm_config.min_obs_per_regime == 30
        assert vecm_config.bootstrap_reps == 500
        
        # Test CointegrationTestConfig - use correct field names
        test_config = t2c.CointegrationTestConfig(
            max_lags=4,
            significance_level=0.01
        )
        assert test_config.max_lags == 4
        assert test_config.significance_level == 0.01
    
    def test_module_integration(self):
        """Test that module components work together."""
        import pandas as pd
        import numpy as np
        
        # Create sample data
        dates = pd.date_range('2020-01-01', periods=100, freq='W')
        data = pd.DataFrame({
            'date': dates,
            'market': 'market_0',
            'commodity': 'wheat',
            'price': 100 + np.random.normal(0, 10, 100)
        })
        
        # Test extractor can process data
        extractor = t2c.CommodityExtractor(t2c.CommodityExtractorConfig())
        
        # Verify extractor has required methods
        assert hasattr(extractor, 'extract_all_commodities')
        assert hasattr(extractor, 'validate_commodity_data')
        assert hasattr(extractor, 'prepare_for_vecm')
    
    def test_results_container_compatibility(self):
        """Test that all models return ResultsContainer objects."""
        # Test that models are designed to return ResultsContainer
        model = t2c.CommoditySpecificModel('wheat', t2c.CommodityModelConfig())
        vecm = t2c.ThresholdVECM(commodity='wheat', config=t2c.ThresholdVECMConfig())
        
        # Check fit methods exist and have correct signature
        import inspect
        
        # Check CommoditySpecificModel.fit
        model_sig = inspect.signature(model.fit)
        assert 'panel_df' in model_sig.parameters
        assert model_sig.return_annotation == ResultsContainer or str(model_sig.return_annotation).endswith('ResultsContainer')
        
        # Check ThresholdVECM.fit  
        vecm_sig = inspect.signature(vecm.fit)
        assert 'df' in vecm_sig.parameters  # First parameter is 'df', not 'price_data'
        assert vecm_sig.return_annotation == ResultsContainer or str(vecm_sig.return_annotation).endswith('ResultsContainer')
    
    def test_error_handling(self):
        """Test error handling in module classes."""
        # Test invalid model type
        with pytest.raises(ValueError):
            t2c.CommodityModelConfig(model_type='invalid')
        
        # Test invalid cov_type (was standard_errors)
        with pytest.raises(ValueError):
            t2c.CommodityModelConfig(cov_type='invalid')
        
        # Test invalid trend (was deterministic_trend)
        with pytest.raises(ValueError):
            t2c.CointegrationTestConfig(trend='invalid')
    
    def test_module_docstrings(self):
        """Test that module and classes have proper documentation."""
        # Check module docstring
        assert t2c.__doc__ is not None
        assert 'Tier 2' in t2c.__doc__
        
        # Check class docstrings
        assert t2c.CommodityExtractor.__doc__ is not None
        assert t2c.CommoditySpecificModel.__doc__ is not None
        assert t2c.ThresholdVECM.__doc__ is not None
        assert t2c.CointegrationTestSuite.__doc__ is not None
    
    def test_logging_integration(self):
        """Test that module uses enhanced logging."""
        # Module should bind logging context
        import yemen_market.utils.logging as logging
        
        # Check that module uses logging
        assert hasattr(t2c, '__name__')
        
        # Instantiate classes and verify they don't raise logging errors
        extractor = t2c.CommodityExtractor(t2c.CommodityExtractorConfig())
        model = t2c.CommoditySpecificModel('wheat', t2c.CommodityModelConfig())
        vecm = t2c.ThresholdVECM(commodity='wheat', config=t2c.ThresholdVECMConfig())
        suite = t2c.CointegrationTestSuite(t2c.CointegrationTestConfig())
