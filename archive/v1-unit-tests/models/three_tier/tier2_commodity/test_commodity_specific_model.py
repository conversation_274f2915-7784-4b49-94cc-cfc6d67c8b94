"""Comprehensive tests for CommoditySpecificModel."""

import numpy as np
import pandas as pd
import pytest
from unittest.mock import Mock, patch, MagicMock
from linearmodels.panel import PanelOLS, RandomEffects
from linearmodels.panel.results import PanelResults

from yemen_market.models.three_tier.tier2_commodity.commodity_specific_model import (
    CommoditySpecificModel, CommodityModelConfig
)
from yemen_market.models.three_tier.common import ResultsContainer


class TestCommoditySpecificModel:
    """Test commodity-specific panel regression models."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample commodity panel data."""
        np.random.seed(42)
        n_markets = 5
        n_periods = 50
        
        dates = pd.date_range('2020-01-01', periods=n_periods, freq='W')
        markets = [f'market_{i}' for i in range(n_markets)]
        
        data = []
        for date in dates:
            for market in markets:
                price = 100 + np.random.normal(0, 10)
                volume = 1000 + np.random.normal(0, 100)
                conflict = np.random.poisson(2)
                control = np.random.choice([0, 1])
                
                data.append({
                    'date': date,
                    'market': market,
                    'price': price,
                    'volume': volume,
                    'conflict_events': conflict,
                    'govt_controlled': control,
                    'log_price': np.log(price),
                    'log_volume': np.log(volume)
                })
        
        df = pd.DataFrame(data)
        df['commodity'] = 'wheat'
        return df
    
    @pytest.fixture
    def model(self):
        """Create model instance with default config."""
        config = CommodityModelConfig()
        return CommoditySpecificModel(commodity='wheat', config=config)
    
    @pytest.fixture
    def mock_panel_results(self):
        """Create mock panel regression results."""
        results = Mock(spec=PanelResults)
        results.params = pd.Series({
            'log_volume': 0.5,
            'conflict_events': -0.1,
            'govt_controlled': 0.2
        })
        results.std_errors = pd.Series({
            'log_volume': 0.1,
            'conflict_events': 0.05,
            'govt_controlled': 0.08
        })
        results.pvalues = pd.Series({
            'log_volume': 0.000,
            'conflict_events': 0.046,
            'govt_controlled': 0.013
        })
        results.rsquared = 0.75
        results.rsquared_between = 0.80
        results.rsquared_within = 0.70
        results.entity_effects = pd.Series({
            'market_0': 0.1,
            'market_1': -0.1,
            'market_2': 0.0,
            'market_3': 0.05,
            'market_4': -0.05
        })
        results.time_effects = pd.Series(
            np.random.normal(0, 0.02, 50),
            index=pd.date_range('2020-01-01', periods=50, freq='W')
        )
        results.nobs = 250
        results.entity_info = Mock(nentity=5)
        results.time_info = Mock(ntime=50)
        results.resids = pd.Series(np.random.normal(0, 0.1, 250))
        results.fitted_values = pd.Series(np.random.normal(4.6, 0.5, 250))
        
        # Add model info
        results.model = Mock()
        results.model._method = 'fixed_effects'
        results.model._cov_type = 'clustered'
        results.model._debiased = False
        
        return results
    
    def test_prepare_data_basic(self, model, sample_data):
        """Test basic data preparation."""
        prepared = model.prepare_data(sample_data)
        
        # Check structure
        assert isinstance(prepared.index, pd.MultiIndex)
        assert prepared.index.names == ['market', 'date']
        
        # Check columns
        assert 'price' in prepared.columns
        assert 'log_price' in prepared.columns
        
    def test_prepare_data_with_exog(self, model, sample_data):
        """Test data preparation with exogenous variables."""
        exog_vars = ['conflict_events', 'govt_controlled']
        prepared = model.prepare_data(sample_data, exog_vars=exog_vars)
        
        # Check all required columns exist
        for var in exog_vars:
            assert var in prepared.columns
        
        # No duplicate columns
        assert len(prepared.columns) == len(set(prepared.columns))
    
    def test_prepare_data_missing_columns(self, model, sample_data):
        """Test handling of missing columns."""
        # Remove required column
        sample_data = sample_data.drop('price', axis=1)
        
        with pytest.raises(KeyError):
            model.prepare_data(sample_data)
    
    @patch('yemen_market.models.three_tier.tier2_commodity.commodity_specific_model.PanelOLS')
    def test_fit_fixed_effects(self, mock_panel_ols, model, sample_data, mock_panel_results):
        """Test fitting with fixed effects."""
        # Setup mock
        mock_model = Mock()
        mock_model.fit.return_value = mock_panel_results
        mock_panel_ols.return_value = mock_model
        
        # First prepare the data
        prepared_data = model.prepare_data(sample_data, exog_vars=['log_volume', 'conflict_events'])
        
        # Fit model
        results = model.fit(
            prepared_data,
            dependent_var='log_price',
            exog_vars=['log_volume', 'conflict_events']
        )
        
        # Verify call
        mock_panel_ols.assert_called_once()
        call_args = mock_panel_ols.call_args
        
        # Check dependent variable
        assert call_args[0][0].name == 'log_price'
        
        # Check model was fit
        mock_model.fit.assert_called_once()
        
        # Check results
        assert results is not None
    
    @patch('yemen_market.models.three_tier.tier2_commodity.commodity_specific_model.RandomEffects')
    def test_fit_random_effects(self, mock_random_effects, sample_data, mock_panel_results):
        """Test fitting with random effects."""
        # Setup config and model
        config = CommodityModelConfig(
            model_type='random_effects',
            cov_type='clustered'
        )
        model = CommoditySpecificModel(commodity='wheat', config=config)
        
        # Setup mock
        mock_model = Mock()
        mock_model.fit.return_value = mock_panel_results
        mock_random_effects.return_value = mock_model
        
        # First prepare the data
        prepared_data = model.prepare_data(sample_data)
        
        # Fit model
        results = model.fit(prepared_data)
        
        # Verify random effects was used
        mock_random_effects.assert_called_once()
        mock_model.fit.assert_called_once()
    
    def test_fit_different_standard_errors(self, model, sample_data):
        """Test different standard error specifications."""
        se_options = ['robust', 'clustered', 'kernel']
        
        for se_type in se_options:
            # Create model with specific SE type
            config = CommodityModelConfig(cov_type=se_type)
            model = CommoditySpecificModel(commodity='wheat', config=config)
            
            with patch('yemen_market.models.three_tier.tier2_commodity.commodity_specific_model.PanelOLS') as mock_ols:
                mock_model = Mock()
                mock_model.fit.return_value = Mock(spec=PanelResults)
                mock_ols.return_value = mock_model
                
                # First prepare the data
                prepared_data = model.prepare_data(sample_data)
                model.fit(prepared_data)
                
                # Check fit was called with correct cov_type
                fit_kwargs = mock_model.fit.call_args[1]
                assert 'cov_type' in fit_kwargs
    
    def test_extract_residuals(self, model, sample_data, mock_panel_results):
        """Test residual extraction after fitting."""
        # Mock the fit process
        with patch('yemen_market.models.three_tier.tier2_commodity.commodity_specific_model.PanelOLS') as mock_ols:
            mock_model = Mock()
            mock_model.fit.return_value = mock_panel_results
            mock_ols.return_value = mock_model
            
            # Prepare and fit
            prepared_data = model.prepare_data(sample_data)
            model.fit(prepared_data)
            
            # Check that model has fit_result
            assert hasattr(model, 'fit_result')
            assert model.fit_result is not None
    
    def test_compute_diagnostics(self, model, sample_data, mock_panel_results):
        """Test diagnostic computation after fitting."""
        # Mock the fit process
        with patch('yemen_market.models.three_tier.tier2_commodity.commodity_specific_model.PanelOLS') as mock_ols:
            mock_model = Mock()
            mock_model.fit.return_value = mock_panel_results
            mock_ols.return_value = mock_model
            
            # Prepare and fit
            prepared_data = model.prepare_data(sample_data)
            model.fit(prepared_data)
            
            # Check that diagnostics were computed
            assert hasattr(model, 'residual_diagnostics')
            assert isinstance(model.residual_diagnostics, dict)
    
    def test_invalid_model_type(self):
        """Test invalid model type raises error."""
        with pytest.raises(ValueError, match="model_type must be"):
            config = CommodityModelConfig(model_type='invalid')
    
    def test_no_commodity_column(self, model):
        """Test handling data without commodity column."""
        data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'market': ['A'] * 10,
            'price': np.random.normal(100, 10, 10)
        })
        
        # Should still work but with warning
        prepared = model.prepare_data(data)
        assert isinstance(prepared, pd.DataFrame)
    
    def test_post_estimation_analysis(self, model, sample_data):
        """Test post-estimation analysis features."""
        with patch('yemen_market.models.three_tier.tier2_commodity.commodity_specific_model.PanelOLS') as mock_ols:
            # Setup mock with more detailed results
            mock_results = Mock(spec=PanelResults)
            mock_results.params = pd.Series({'conflict_events': -0.15})
            mock_results.std_errors = pd.Series({'conflict_events': 0.05})
            mock_results.pvalues = pd.Series({'conflict_events': 0.003})
            mock_results.rsquared = 0.82
            mock_results.nobs = 250
            
            mock_model = Mock()
            mock_model.fit.return_value = mock_results
            mock_ols.return_value = mock_model
            
            # Prepare and fit
            prepared_data = model.prepare_data(sample_data, exog_vars=['conflict_events'])
            results = model.fit(prepared_data, exog_vars=['conflict_events'])
            
            # Verify results exist
            assert results is not None
    
    def test_convergence_issues(self, model, sample_data):
        """Test handling of convergence issues."""
        with patch('yemen_market.models.three_tier.tier2_commodity.commodity_specific_model.PanelOLS') as mock_ols:
            # Setup mock to raise convergence error
            mock_model = Mock()
            mock_model.fit.side_effect = Exception("Convergence failed")
            mock_ols.return_value = mock_model
            
            # Prepare data first
            prepared_data = model.prepare_data(sample_data)
            
            # Should raise with informative message
            with pytest.raises(Exception, match="Convergence"):
                model.fit(prepared_data)
    
    def test_multicollinearity_detection(self, model):
        """Test detection of multicollinearity."""
        # Create data with perfect multicollinearity
        n = 100
        data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=n),
            'market': ['A'] * n,
            'price': np.random.normal(100, 10, n),
            'x1': np.random.normal(0, 1, n)
        })
        data['x2'] = data['x1'] * 2  # Perfect collinearity
        data['log_price'] = np.log(data['price'])
        
        with patch('yemen_market.models.three_tier.tier2_commodity.commodity_specific_model.PanelOLS') as mock_ols:
            # Mock should detect high condition number
            mock_results = Mock(spec=PanelResults)
            mock_results.condition_number = 1e10  # Very high
            
            mock_model = Mock()
            mock_model.fit.return_value = mock_results
            mock_ols.return_value = mock_model
            
            # Prepare and fit model
            prepared_data = model.prepare_data(data, exog_vars=['x1', 'x2'])
            results = model.fit(prepared_data, exog_vars=['x1', 'x2'])
            
            # Check results exist
            assert results is not None