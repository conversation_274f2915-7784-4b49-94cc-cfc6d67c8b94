"""Comprehensive tests for PanelDataHandler."""

import numpy as np
import pandas as pd
import pytest
from unittest.mock import Mock, patch

from yemen_market.models.three_tier.core.panel_data_handler import (
    PanelDataHandler, PanelDataConfig
)


class TestPanelDataHandler:
    """Test panel data handling and transformation."""
    
    @pytest.fixture
    def sample_3d_data(self):
        """Create sample 3D panel data (market × commodity × time)."""
        np.random.seed(42)
        
        markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah']
        commodities = ['wheat', 'rice', 'sugar']
        dates = pd.date_range('2020-01-01', periods=52, freq='W')
        
        data = []
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    price = 100 + np.random.normal(0, 10)
                    volume = 1000 + np.random.normal(0, 100)
                    
                    data.append({
                        'date': date,
                        'market': market,
                        'commodity': commodity,
                        'price': price,
                        'volume': volume,
                        'conflict_events': np.random.poisson(2),
                        'govt_controlled': np.random.choice([0, 1])
                    })
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def handler(self):
        """Create handler with default config."""
        config = PanelDataConfig()
        return PanelDataHandler(config)
    
    def test_validate_structure(self, handler, sample_3d_data):
        """Test validation of 3D panel structure."""
        # Should validate successfully
        is_valid, messages = handler.validate_structure(sample_3d_data)
        assert is_valid
        assert len(messages) == 0
        
        # Test missing required column
        bad_data = sample_3d_data.drop('price', axis=1)
        is_valid, messages = handler.validate_structure(bad_data)
        assert not is_valid
        assert any('price' in msg for msg in messages)
    
    def test_create_entity_id(self, handler, sample_3d_data):
        """Test entity ID creation from market-commodity pairs."""
        result = handler.create_entity_id(sample_3d_data)
        
        # Check entity_id created
        assert 'entity_id' in result.columns
        
        # Check format
        entity_ids = result['entity_id'].unique()
        assert all('_' in eid for eid in entity_ids)
        
        # Check uniqueness per market-commodity
        grouped = result.groupby(['market', 'commodity'])['entity_id'].nunique()
        assert all(grouped == 1)
    
    def test_transform_to_2d(self, handler, sample_3d_data):
        """Test transformation from 3D to 2D panel."""
        # Add entity_id first
        data_with_entity = handler.create_entity_id(sample_3d_data)
        
        # Transform to 2D
        panel_2d = handler.transform_to_2d(data_with_entity)
        
        # Check structure
        assert isinstance(panel_2d.index, pd.MultiIndex)
        assert panel_2d.index.names == ['entity_id', 'date']
        
        # Check data preserved
        assert len(panel_2d) == len(sample_3d_data)
        assert set(panel_2d.columns) >= {'price', 'volume', 'market', 'commodity'}
    
    def test_check_balance(self, handler, sample_3d_data):
        """Test panel balance checking."""
        # Create balanced panel
        balanced = handler.transform_to_2d(
            handler.create_entity_id(sample_3d_data)
        )
        
        balance_info = handler.check_balance(balanced)
        
        assert 'is_balanced' in balance_info
        assert 'n_entities' in balance_info
        assert 'n_periods' in balance_info
        assert 'missing_observations' in balance_info
        
        # Should be balanced
        assert balance_info['is_balanced'] == True
        assert balance_info['missing_observations'] == 0
    
    def test_handle_unbalanced_panel(self, handler, sample_3d_data):
        """Test handling of unbalanced panels."""
        # Create unbalanced by removing some observations
        unbalanced = sample_3d_data.iloc[10:]  # Remove first 10
        
        # Transform
        panel = handler.transform_to_2d(
            handler.create_entity_id(unbalanced)
        )
        
        # Check balance
        balance_info = handler.check_balance(panel)
        assert not balance_info['is_balanced']
        assert balance_info['missing_observations'] > 0
        
        # Test balancing
        balanced = handler.balance_panel(panel, method='drop')
        balance_check = handler.check_balance(balanced)
        assert balance_check['is_balanced']
    
    def test_add_time_features(self, handler, sample_3d_data):
        """Test addition of time-based features."""
        panel = handler.transform_to_2d(
            handler.create_entity_id(sample_3d_data)
        )
        
        # Add time features
        with_features = handler.add_time_features(panel)
        
        # Check new columns
        expected_features = ['year', 'month', 'week', 'quarter']
        for feature in expected_features:
            assert feature in with_features.columns
        
        # Verify values
        assert with_features['year'].nunique() >= 1
        assert with_features['month'].nunique() >= 1
        assert 1 <= with_features['week'].max() <= 53
        assert 1 <= with_features['quarter'].max() <= 4
    
    def test_add_lags(self, handler, sample_3d_data):
        """Test lag feature creation."""
        panel = handler.transform_to_2d(
            handler.create_entity_id(sample_3d_data)
        )
        
        # Add lags
        with_lags = handler.add_lags(
            panel,
            variables=['price', 'volume'],
            lags=[1, 2, 4]
        )
        
        # Check lag columns created
        for var in ['price', 'volume']:
            for lag in [1, 2, 4]:
                assert f'{var}_lag_{lag}' in with_lags.columns
        
        # Verify lag values
        # First observation of each entity should have NaN lags
        for entity in with_lags.index.get_level_values(0).unique():
            entity_data = with_lags.loc[entity]
            assert pd.isna(entity_data.iloc[0]['price_lag_1'])
    
    def test_add_differences(self, handler, sample_3d_data):
        """Test difference feature creation."""
        panel = handler.transform_to_2d(
            handler.create_entity_id(sample_3d_data)
        )
        
        # Add differences
        with_diffs = handler.add_differences(
            panel,
            variables=['price'],
            periods=[1, 4]
        )
        
        # Check difference columns
        assert 'price_diff_1' in with_diffs.columns
        assert 'price_diff_4' in with_diffs.columns
        
        # Verify differences
        for entity in with_diffs.index.get_level_values(0).unique():
            entity_data = with_diffs.loc[entity]
            # Check 1-period difference
            if len(entity_data) > 1:
                expected_diff = entity_data.iloc[1]['price'] - entity_data.iloc[0]['price']
                actual_diff = entity_data.iloc[1]['price_diff_1']
                assert np.isclose(actual_diff, expected_diff, rtol=1e-10)
    
    def test_compute_summary_stats(self, handler, sample_3d_data):
        """Test summary statistics computation."""
        panel = handler.transform_to_2d(
            handler.create_entity_id(sample_3d_data)
        )
        
        stats = handler.compute_summary_stats(panel)
        
        # Check structure
        assert 'overall' in stats
        assert 'between' in stats
        assert 'within' in stats
        
        # Check statistics included
        for stat_type in ['overall', 'between', 'within']:
            assert 'mean' in stats[stat_type]
            assert 'std' in stats[stat_type]
            assert 'min' in stats[stat_type]
            assert 'max' in stats[stat_type]
    
    def test_export_to_linearmodels_format(self, handler, sample_3d_data):
        """Test export to linearmodels-compatible format."""
        panel = handler.transform_to_2d(
            handler.create_entity_id(sample_3d_data)
        )
        
        # Export for linearmodels
        lm_data = handler.export_to_linearmodels(panel)
        
        # Check format
        assert isinstance(lm_data.index, pd.MultiIndex)
        assert lm_data.index.names == ['entity_id', 'date']
        
        # Check no missing values in index
        assert not lm_data.index.has_duplicates
        assert not any(pd.isna(lm_data.index.get_level_values(0)))
        assert not any(pd.isna(lm_data.index.get_level_values(1)))
    
    def test_handle_missing_data(self, handler):
        """Test missing data handling strategies."""
        # Create data with missing values
        data = pd.DataFrame({
            'entity_id': ['A', 'A', 'A', 'B', 'B', 'B'],
            'date': pd.date_range('2020-01-01', periods=3).tolist() * 2,
            'price': [100, np.nan, 102, 200, 201, np.nan],
            'volume': [1000, 1100, np.nan, 2000, np.nan, 2200]
        })
        panel = data.set_index(['entity_id', 'date'])
        
        # Test different strategies
        # Forward fill
        filled_ff = handler.handle_missing_data(panel, method='ffill')
        assert filled_ff.loc['A', '2020-01-02']['price'] == 100
        
        # Interpolation
        filled_interp = handler.handle_missing_data(panel, method='interpolate')
        assert filled_interp.loc['A', '2020-01-02']['price'] == 101  # Linear interpolation
        
        # Drop
        filled_drop = handler.handle_missing_data(panel, method='drop')
        assert len(filled_drop) < len(panel)
    
    def test_validation_with_config(self):
        """Test validation with different configurations."""
        # Strict config
        strict_config = PanelDataConfig(
            min_entities=10,
            min_periods=100,
            max_missing_pct=0.05
        )
        strict_handler = PanelDataHandler(strict_config)
        
        # Small dataset should fail strict validation
        small_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'market': 'A',
            'commodity': 'wheat',
            'price': np.random.normal(100, 10, 10)
        })
        
        is_valid, messages = strict_handler.validate_structure(small_data)
        assert not is_valid  # Should fail due to insufficient data
    
    def test_entity_aggregation(self, handler, sample_3d_data):
        """Test aggregation across entities."""
        panel = handler.transform_to_2d(
            handler.create_entity_id(sample_3d_data)
        )
        
        # Aggregate by market
        market_avg = handler.aggregate_by_dimension(
            panel,
            dimension='market',
            agg_func='mean'
        )
        
        # Check results
        markets = sample_3d_data['market'].unique()
        assert len(market_avg) == len(markets) * panel.index.get_level_values(1).nunique()
        
        # Aggregate by commodity
        commodity_avg = handler.aggregate_by_dimension(
            panel,
            dimension='commodity',
            agg_func='mean'
        )
        
        commodities = sample_3d_data['commodity'].unique()
        assert len(commodity_avg) == len(commodities) * panel.index.get_level_values(1).nunique()
    
    def test_panel_info_report(self, handler, sample_3d_data):
        """Test comprehensive panel information report."""
        panel = handler.transform_to_2d(
            handler.create_entity_id(sample_3d_data)
        )
        
        info = handler.get_panel_info(panel)
        
        # Check all info included
        expected_keys = [
            'n_entities', 'n_periods', 'n_observations',
            'is_balanced', 'date_range', 'entities',
            'missing_pattern', 'summary_stats'
        ]
        
        for key in expected_keys:
            assert key in info
        
        # Verify counts
        assert info['n_entities'] == len(panel.index.get_level_values(0).unique())
        assert info['n_periods'] == len(panel.index.get_level_values(1).unique())
        assert info['n_observations'] == len(panel)