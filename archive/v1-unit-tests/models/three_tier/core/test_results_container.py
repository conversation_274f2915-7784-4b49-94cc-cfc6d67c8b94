"""Comprehensive tests for ResultsContainer."""

import numpy as np
import pandas as pd
import pytest
import json
import copy
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch, mock_open

from yemen_market.models.three_tier.core.results_container import ResultsContainer


class TestResultsContainer:
    """Test results container for storing and managing analysis outputs."""

    @pytest.fixture
    def sample_results(self):
        """Create sample results for testing."""
        return {
            'coefficients': {
                'price_lag1': 0.85,
                'conflict': -0.12,
                'intercept': 2.5
            },
            'standard_errors': {
                'price_lag1': 0.05,
                'conflict': 0.03,
                'intercept': 0.8
            },
            'fit_statistics': {
                'r_squared': 0.75,
                'adj_r_squared': 0.74,
                'f_statistic': 45.2,
                'f_pvalue': 0.0001,
                'nobs': 1000
            }
        }

    @pytest.fixture
    def sample_diagnostics(self):
        """Create sample diagnostics for testing."""
        return {
            'normality_test': {
                'statistic': 2.45,
                'p_value': 0.29,
                'passed': True
            },
            'heteroskedasticity_test': {
                'statistic': 8.12,
                'p_value': 0.087,
                'passed': True
            },
            'autocorrelation_test': {
                'durbin_watson': 1.98,
                'passed': True
            }
        }

    @pytest.fixture
    def sample_metadata(self):
        """Create sample metadata for testing."""
        return {
            'estimation_time': 2.5,
            'convergence_achieved': True,
            'n_iterations': 15,
            'method': 'fixed_effects',
            'software_version': '0.1.0'
        }

    def test_initialization(self, sample_results, sample_diagnostics, sample_metadata):
        """Test ResultsContainer initialization."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='PooledPanel',
            results=sample_results,
            diagnostics=sample_diagnostics,
            metadata=sample_metadata
        )

        assert container.commodity == 'wheat'
        assert container.model_type == 'PooledPanel'
        assert container.results == sample_results
        assert container.diagnostics == sample_diagnostics
        assert container.metadata == sample_metadata
        assert isinstance(container.timestamp, datetime)

    def test_initialization_minimal(self):
        """Test initialization with minimal parameters."""
        container = ResultsContainer(
            commodity='rice',
            model_type='ThresholdVECM',
            results={'test': 'value'}
        )

        assert container.commodity == 'rice'
        assert container.model_type == 'ThresholdVECM'
        assert container.results == {'test': 'value'}
        assert container.diagnostics == {}
        assert container.metadata == {}

    def test_get_coefficient(self, sample_results):
        """Test coefficient retrieval."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results
        )

        # Test existing coefficient
        assert container.get_coefficient('price_lag1') == 0.85
        assert container.get_coefficient('conflict') == -0.12

        # Test non-existent coefficient
        assert container.get_coefficient('nonexistent') is None
        assert container.get_coefficient('nonexistent', default=-999) == -999

    def test_get_standard_error(self, sample_results):
        """Test standard error retrieval."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results
        )

        # Test existing SE
        assert container.get_standard_error('price_lag1') == 0.05
        assert container.get_standard_error('conflict') == 0.03

        # Test non-existent SE
        assert container.get_standard_error('nonexistent') is None

    def test_get_t_statistic(self, sample_results):
        """Test t-statistic calculation."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results
        )

        # Calculate t-stat
        t_stat = container.get_t_statistic('price_lag1')
        expected = 0.85 / 0.05  # coef / se
        assert np.isclose(t_stat, expected)

        # Test with missing data
        assert container.get_t_statistic('nonexistent') is None

    def test_get_p_value(self, sample_results):
        """Test p-value calculation from t-statistic."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results
        )

        # Add degrees of freedom to metadata
        container.metadata['degrees_of_freedom'] = 995

        # Get p-value
        p_value = container.get_p_value('price_lag1')
        assert 0 <= p_value <= 1

        # High t-stat should have low p-value
        assert p_value < 0.001

    def test_summary_dict(self, sample_results, sample_diagnostics):
        """Test summary dictionary generation."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='PooledPanel',
            results=sample_results,
            diagnostics=sample_diagnostics
        )

        summary = container.summary_dict()

        # Check structure
        assert 'commodity' in summary
        assert 'model_type' in summary
        assert 'key_results' in summary
        assert 'diagnostics_passed' in summary

        # Check key results
        assert summary['key_results']['r_squared'] == 0.75
        assert summary['key_results']['n_observations'] == 1000

        # Check diagnostics
        assert summary['diagnostics_passed']['normality'] == True
        assert summary['diagnostics_passed']['heteroskedasticity'] == True

    def test_to_dict(self, sample_results, sample_diagnostics, sample_metadata):
        """Test full dictionary export."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results,
            diagnostics=sample_diagnostics,
            metadata=sample_metadata
        )

        full_dict = container.to_dict()

        # Check all components included
        assert full_dict['commodity'] == 'wheat'
        assert full_dict['model_type'] == 'Test'
        assert full_dict['results'] == sample_results
        assert full_dict['diagnostics'] == sample_diagnostics
        assert full_dict['metadata'] == sample_metadata
        assert 'timestamp' in full_dict

    def test_to_json(self, sample_results):
        """Test JSON serialization."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results
        )

        json_str = container.to_json()

        # Should be valid JSON
        parsed = json.loads(json_str)
        assert parsed['commodity'] == 'wheat'
        assert parsed['results']['coefficients']['price_lag1'] == 0.85

    def test_save_and_load(self, tmp_path, sample_results, sample_diagnostics):
        """Test saving to and loading from file."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results,
            diagnostics=sample_diagnostics
        )

        # Save to file
        filepath = tmp_path / 'test_results.json'
        container.save(filepath)

        # Check file exists
        assert filepath.exists()

        # Load from file
        loaded = ResultsContainer.load(filepath)

        # Check contents match
        assert loaded.commodity == container.commodity
        assert loaded.model_type == container.model_type
        assert loaded.results == container.results
        assert loaded.diagnostics == container.diagnostics

    def test_merge_containers(self, sample_results):
        """Test merging multiple containers."""
        container1 = ResultsContainer(
            commodity='wheat',
            model_type='Model1',
            results={'test1': 'value1'}
        )

        container2 = ResultsContainer(
            commodity='wheat',
            model_type='Model2',
            results={'test2': 'value2'}
        )

        # Merge containers
        merged = ResultsContainer.merge(
            [container1, container2],
            commodity='wheat',
            model_type='Combined'
        )

        # Check merged results
        assert merged.commodity == 'wheat'
        assert merged.model_type == 'Combined'
        assert 'Model1' in merged.results
        assert 'Model2' in merged.results
        assert merged.results['Model1'] == {'test1': 'value1'}
        assert merged.results['Model2'] == {'test2': 'value2'}

    def test_filter_significant_results(self, sample_results):
        """Test filtering for significant coefficients."""
        # Add p-values to results
        sample_results['p_values'] = {
            'price_lag1': 0.001,
            'conflict': 0.045,
            'intercept': 0.15
        }

        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results
        )

        # Filter at 5% level
        significant = container.filter_significant(alpha=0.05)

        # Should include price_lag1 and conflict, not intercept
        assert 'price_lag1' in significant
        assert 'conflict' in significant
        assert 'intercept' not in significant

    def test_compare_models(self, sample_results):
        """Test model comparison functionality."""
        # Create two models with different R-squared
        results1 = copy.deepcopy(sample_results)
        results1['fit_statistics']['r_squared'] = 0.75
        results1['fit_statistics']['aic'] = 1000

        results2 = copy.deepcopy(sample_results)
        results2['fit_statistics']['r_squared'] = 0.82
        results2['fit_statistics']['aic'] = 980

        container1 = ResultsContainer(
            commodity='wheat',
            model_type='Model1',
            results=results1
        )

        container2 = ResultsContainer(
            commodity='wheat',
            model_type='Model2',
            results=results2
        )

        # Compare models
        comparison = ResultsContainer.compare([container1, container2])

        # Check comparison results
        assert comparison['best_r_squared'] == 'Model2'
        assert comparison['best_aic'] == 'Model2'
        assert comparison['r_squared_values'] == {'Model1': 0.75, 'Model2': 0.82}

    def test_format_table(self, sample_results):
        """Test formatted table output."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results
        )

        # Format as table
        table = container.format_table()

        # Check it's a DataFrame
        assert isinstance(table, pd.DataFrame)

        # Check columns
        assert 'Coefficient' in table.columns
        assert 'Std. Error' in table.columns
        assert 't-statistic' in table.columns

        # Check rows
        assert 'price_lag1' in table.index
        assert 'conflict' in table.index

    def test_export_latex(self, sample_results):
        """Test LaTeX export for paper tables."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results
        )

        # Export to LaTeX
        latex = container.to_latex()

        # Check LaTeX formatting
        assert '\\begin{tabular}' in latex
        assert '\\end{tabular}' in latex
        assert 'price\\_lag1' in latex  # Escaped underscore
        assert '0.85' in latex  # Coefficient value

    def test_validation(self):
        """Test input validation."""
        # Test invalid commodity type
        with pytest.raises(TypeError):
            ResultsContainer(
                commodity=123,  # Should be string
                model_type='Test',
                results={}
            )

        # Test invalid results type
        with pytest.raises(TypeError):
            ResultsContainer(
                commodity='wheat',
                model_type='Test',
                results='not a dict'  # Should be dict
            )

    def test_nested_results_access(self):
        """Test accessing deeply nested results."""
        nested_results = {
            'regime1': {
                'coefficients': {
                    'var1': 0.5,
                    'var2': 0.3
                },
                'diagnostics': {
                    'test1': {'stat': 2.5, 'pval': 0.05}
                }
            },
            'regime2': {
                'coefficients': {
                    'var1': 0.7,
                    'var2': 0.1
                }
            }
        }

        container = ResultsContainer(
            commodity='wheat',
            model_type='ThresholdModel',
            results=nested_results
        )

        # Test nested access method
        assert container.get_nested('regime1.coefficients.var1') == 0.5
        assert container.get_nested('regime1.diagnostics.test1.pval') == 0.05
        assert container.get_nested('nonexistent.path') is None

    def test_summary_statistics(self, sample_results):
        """Test computation of summary statistics."""
        container = ResultsContainer(
            commodity='wheat',
            model_type='Test',
            results=sample_results
        )

        stats = container.compute_summary_stats()

        # Check basic stats
        assert 'n_coefficients' in stats
        assert stats['n_coefficients'] == 3

        assert 'mean_coefficient' in stats
        assert 'max_coefficient' in stats
        assert 'min_coefficient' in stats