"""Unit tests for ACAPS areas of control processor."""

import json
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import MagicMock, patch, mock_open

import pandas as pd
import geopandas as gpd
import numpy as np
import pytest
from shapely.geometry import Point, Polygon

from yemen_market.data.acaps_processor import ACAPSProcessor


class TestACAPSProcessor:
    """Test suite for ACAPS control zone processor."""
    
    @pytest.fixture
    def processor(self, tmp_path):
        """Create ACAPS processor instance with temp directories."""
        acaps_dir = tmp_path / "raw" / "acaps"
        output_dir = tmp_path / "processed" / "control_zones"
        return ACAPSProcessor(acaps_dir=acaps_dir, output_dir=output_dir)
    
    @pytest.fixture
    def sample_geodataframe(self):
        """Create sample GeoDataFrame with control zone data."""
        data = {
            'ADM0_EN': ['Yemen'] * 5,
            'ADM1_EN': ['<PERSON><PERSON>\'a', '<PERSON><PERSON>\'a', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
            'ADM2_EN': ['<PERSON>', '<PERSON>', 
                       '<PERSON><PERSON><PERSON>', '<PERSON>ar', 'Al Mukalla'],
            'ADM1_PCODE': ['YE01', 'YE01', 'YE02', 'YE03', 'YE04'],
            'ADM2_PCODE': ['YE0101', 'YE0102', 'YE0201', 'YE0301', 'YE0401'],
            'irg_or_dfa': ['Houthi', 'Houthi', 'Government', 'Contested', 'Government'],
            'geometry': [Point(44.2, 15.3), Point(44.3, 15.4), Point(45.0, 12.8),
                        Point(44.0, 13.6), Point(49.1, 14.5)]
        }
        return gpd.GeoDataFrame(data, crs='EPSG:4326')
    
    @pytest.fixture
    def sample_dataframe(self):
        """Create sample DataFrame with control zone data."""
        return pd.DataFrame({
            'ADM1_EN': ['Sana\'a', 'Sana\'a', 'Aden', 'Taiz', 'Hadramaut'],
            'ADM2_EN': ['Al Haymah Ad Dakhiliyah', 'Al Haymah Al Kharijiyah',
                       'Crater', 'Al Mudhaffar', 'Al Mukalla'],
            'irg_or_dfa': ['Houthi', 'Houthi', 'Government', 'Contested', 'Government']
        })
    
    @pytest.fixture
    def sample_time_series_data(self):
        """Create sample time series data for testing."""
        dates = pd.date_range('2023-01-01', periods=6, freq='2W')
        data = []
        
        for date in dates:
            data.extend([
                {'date': date, 'governorate': 'Sana\'a', 'district': 'Al Haymah',
                 'control_zone': 'houthi'},
                {'date': date, 'governorate': 'Aden', 'district': 'Crater',
                 'control_zone': 'government'},
                {'date': date, 'governorate': 'Taiz', 'district': 'Al Mudhaffar',
                 'control_zone': 'contested' if date < dates[3] else 'government'}
            ])
        
        return pd.DataFrame(data)
    
    def test_init(self, processor):
        """Test processor initialization."""
        assert processor.acaps_dir.exists()
        assert processor.output_dir.exists()
        assert processor.interim_dir.exists()
    
    def test_extract_date_from_filename(self, processor):
        """Test date extraction from various filename formats."""
        # Test YYYYMMDD format
        assert processor._extract_date_from_filename(
            "20240513 Yemen Analysis Hub - Areas of control.zip"
        ) == "2024-05-13"
        
        # Test YYYY-MM-DD format
        assert processor._extract_date_from_filename(
            "2024-05-13_yemen_control.zip"
        ) == "2024-05-13"
        
        # Test no date
        assert processor._extract_date_from_filename(
            "yemen_control_zones.zip"
        ) is None
    
    def test_standardize_control_zones(self, processor):
        """Test control zone name standardization."""
        # Test Houthi variations
        assert processor.standardize_control_zones("Houthi") == "houthi"
        assert processor.standardize_control_zones("Ansar Allah") == "houthi"
        assert processor.standardize_control_zones("SPC") == "houthi"
        assert processor.standardize_control_zones("DFA") == "houthi"
        
        # Test Government variations
        assert processor.standardize_control_zones("Government") == "government"
        assert processor.standardize_control_zones("IRG") == "government"
        assert processor.standardize_control_zones("GOY") == "government"
        
        # Test contested
        assert processor.standardize_control_zones("Contested") == "contested"
        assert processor.standardize_control_zones("Disputed") == "contested"
        
        # Test STC
        assert processor.standardize_control_zones("STC") == "stc"
        assert processor.standardize_control_zones("Southern Transitional Council") == "stc"
        
        # Test unknown/other
        assert processor.standardize_control_zones("Unknown Actor") == "other"
        assert processor.standardize_control_zones(None) == "unknown"
        assert processor.standardize_control_zones(np.nan) == "unknown"
    
    def test_standardize_columns(self, processor, sample_geodataframe):
        """Test column name standardization."""
        # Test with standard columns
        result = processor._standardize_columns(sample_geodataframe)
        assert 'governorate' in result.columns
        assert 'district' in result.columns
        assert 'control_zone' in result.columns
        
        # Test with different case
        df = sample_geodataframe.copy()
        df.columns = [c.lower() for c in df.columns]
        result = processor._standardize_columns(df)
        assert 'governorate' in result.columns
    
    def test_extract_control_data_shapefile(self, processor, tmp_path, 
                                           sample_geodataframe):
        """Test extracting control data from shapefile in zip."""
        # Create a temporary shapefile in a zip
        zip_path = tmp_path / "20240513_control.zip"
        
        with zipfile.ZipFile(zip_path, 'w') as zip_ref:
            # Create temporary shapefile
            shp_dir = tmp_path / "temp_shp"
            shp_dir.mkdir()
            shp_path = shp_dir / "control.shp"
            
            sample_geodataframe.to_file(shp_path)
            
            # Add all shapefile components to zip
            for file in shp_dir.glob("control.*"):
                zip_ref.write(file, file.name)
        
        # Test extraction
        with patch('geopandas.read_file', return_value=sample_geodataframe):
            result = processor.extract_control_data(zip_path)
            
            assert isinstance(result, gpd.GeoDataFrame)
            assert 'control_zone' in result.columns
            assert 'date' in result.columns
            assert result['date'].iloc[0] == pd.Timestamp('2024-05-13')
    
    def test_extract_control_data_excel(self, processor, tmp_path,
                                       sample_dataframe):
        """Test extracting control data from Excel in zip."""
        zip_path = tmp_path / "20240513_control.zip"
        
        with zipfile.ZipFile(zip_path, 'w') as zip_ref:
            # Create Excel file in memory
            excel_path = tmp_path / "control.xlsx"
            sample_dataframe.to_excel(excel_path, index=False)
            zip_ref.write(excel_path, "control.xlsx")
        
        # Test extraction
        with patch('pandas.read_excel', return_value=sample_dataframe):
            result = processor.extract_control_data(zip_path)
            
            assert isinstance(result, pd.DataFrame)
            assert 'control_zone' in result.columns
    
    def test_extract_control_data_no_valid_files(self, processor, tmp_path):
        """Test handling of zip with no valid files."""
        zip_path = tmp_path / "invalid.zip"
        
        with zipfile.ZipFile(zip_path, 'w') as zip_ref:
            zip_ref.writestr("readme.txt", "No data here")
        
        with pytest.raises(ValueError, match="No shapefile or Excel file found"):
            processor.extract_control_data(zip_path)
    
    def test_create_control_time_series(self, processor, sample_time_series_data):
        """Test creating time series of control changes."""
        result = processor.create_control_time_series(sample_time_series_data)
        
        assert not result.empty
        assert 'district_id' in result.columns
        assert 'control_changed' in result.columns
        assert 'previous_control' in result.columns
        assert 'transition_type' in result.columns
        
        # Check that Taiz shows a control change
        taiz_data = result[result['district'] == 'Al Mudhaffar']
        assert taiz_data['control_changed'].any()
        assert 'contested_to_government' in taiz_data['transition_type'].values
    
    def test_create_control_time_series_empty(self, processor):
        """Test handling empty data for time series."""
        result = processor.create_control_time_series(pd.DataFrame())
        assert result.empty
    
    def test_create_control_time_series_missing_columns(self, processor):
        """Test handling missing columns for time series."""
        df = pd.DataFrame({'date': ['2024-01-01'], 'district': ['Test']})
        result = processor.create_control_time_series(df)
        assert result.empty
    
    def test_align_to_monthly(self, processor, sample_time_series_data):
        """Test aligning bi-weekly data to monthly frequency."""
        # Create time series first
        time_series = processor.create_control_time_series(sample_time_series_data)
        
        # Align to monthly
        result = processor.align_to_monthly(time_series)
        
        assert not result.empty
        assert 'control_changed' in result.columns
        assert 'n_changes' in result.columns
        
        # Check monthly frequency
        assert result['date'].dt.day.unique()[0] == 1  # First day of month
        
        # Check that changes are aggregated
        assert result.groupby(['governorate', 'district'])['date'].count().max() <= 3
    
    def test_align_to_monthly_empty(self, processor):
        """Test handling empty data for monthly alignment."""
        result = processor.align_to_monthly(pd.DataFrame())
        assert result.empty
    
    def test_process_all_files(self, processor, tmp_path, sample_geodataframe):
        """Test processing multiple ACAPS files."""
        # Create multiple test files
        dates = ['20240101', '20240215', '20240401']
        for date in dates:
            zip_path = processor.acaps_dir / f"{date}_areas_control.zip"
            
            with zipfile.ZipFile(zip_path, 'w') as zip_ref:
                # Add a dummy file
                zip_ref.writestr("control.csv", "dummy")
        
        # Mock the extract method
        with patch.object(processor, 'extract_control_data') as mock_extract:
            # Return sample data with different dates
            def side_effect(zip_path):
                date_str = processor._extract_date_from_filename(zip_path.name)
                df = sample_geodataframe.copy()
                # Standardize columns like the real method does
                df = processor._standardize_columns(df)
                df['date'] = pd.to_datetime(date_str)
                return df
            
            mock_extract.side_effect = side_effect
            
            # Process with date filter
            result = processor.process_all_files(
                start_date='2024-02-01',
                end_date='2024-04-30'
            )
            
            assert not result.empty
            assert len(result['date'].unique()) == 2  # Only Feb and Apr
            assert 'control_zone' in result.columns
            assert 'control_zone_raw' in result.columns
            assert result['control_zone'].isin(['houthi', 'government', 'contested']).all()
    
    def test_process_all_files_no_files(self, processor):
        """Test handling when no ACAPS files found."""
        result = processor.process_all_files()
        assert result.empty
    
    def test_save_processed_data(self, processor, sample_time_series_data):
        """Test saving processed data to disk."""
        # Create test data
        control_data = sample_time_series_data
        time_series = processor.create_control_time_series(control_data)
        monthly_data = processor.align_to_monthly(time_series)
        
        # Save data
        saved_files = processor.save_processed_data(
            control_data, time_series, monthly_data
        )
        
        # Check files were created
        assert saved_files['raw'].exists()
        assert saved_files['raw_csv'].exists()
        assert saved_files['time_series'].exists()
        assert saved_files['monthly'].exists()
        assert saved_files['monthly_csv'].exists()
        assert saved_files['metadata'].exists()
        
        # Check metadata content
        with open(saved_files['metadata']) as f:
            metadata = json.load(f)
        
        assert 'processed_date' in metadata
        assert 'n_districts' in metadata
        assert metadata['n_districts'] == 3
        assert 'control_zones' in metadata
    
    def test_save_processed_data_empty(self, processor):
        """Test saving empty data."""
        saved_files = processor.save_processed_data(
            pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
        )
        
        # Only metadata should be saved for empty data
        assert 'metadata' in saved_files
        assert saved_files['metadata'].exists()
    
    def test_generate_summary_stats(self, processor, sample_time_series_data):
        """Test generating summary statistics."""
        # Create monthly data
        time_series = processor.create_control_time_series(sample_time_series_data)
        monthly_data = processor.align_to_monthly(time_series)
        
        # Generate stats
        summary = processor.generate_summary_stats(monthly_data)
        
        assert not summary.empty
        assert 'metric' in summary.columns
        assert 'value' in summary.columns
        
        # Check specific metrics
        metrics = summary.set_index('metric')['value']
        assert metrics['total_districts'] > 0
        assert metrics['total_months'] > 0
    
    def test_generate_summary_stats_empty(self, processor):
        """Test generating stats for empty data."""
        summary = processor.generate_summary_stats(pd.DataFrame())
        assert summary.empty
    
    def test_integration_workflow(self, processor, tmp_path, sample_geodataframe):
        """Test complete processing workflow."""
        # Create test file
        zip_path = processor.acaps_dir / "20240513_areas_control.zip"
        
        with zipfile.ZipFile(zip_path, 'w') as zip_ref:
            zip_ref.writestr("control.csv", "dummy")
        
        # Mock extraction to return standardized data
        standardized_gdf = sample_geodataframe.copy()
        standardized_gdf = processor._standardize_columns(standardized_gdf)
        standardized_gdf['date'] = pd.Timestamp('2024-05-13')
        
        with patch.object(processor, 'extract_control_data',
                         return_value=standardized_gdf):
            # Process files
            control_data = processor.process_all_files()
            assert not control_data.empty
            
            # Verify required columns exist after processing
            assert 'governorate' in control_data.columns
            assert 'district' in control_data.columns  
            assert 'control_zone' in control_data.columns
            assert 'date' in control_data.columns
            
            # Create time series
            time_series = processor.create_control_time_series(control_data)
            assert not time_series.empty
            
            # Align to monthly
            monthly_data = processor.align_to_monthly(time_series)
            assert not monthly_data.empty
            
            # Save all data
            saved_files = processor.save_processed_data(
                control_data, time_series, monthly_data
            )
            assert len(saved_files) > 0
            
            # Generate stats
            summary = processor.generate_summary_stats(monthly_data)
            assert not summary.empty