"""
Tests for the enhanced logging system.
"""

import pytest
from pathlib import Path
import tempfile
import json
import time
from unittest.mock import Mock, patch

from yemen_market.utils.logging import (
    EconometricLogger, LogConfig,
    info, debug, warning, error,
    bind, unbind, context, timer,
    log_metric, log_param, log_data_shape
)


@pytest.fixture
def temp_log_dir():
    """Create a temporary directory for logs."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def test_logger(temp_log_dir):
    """Create a test logger instance."""
    config = LogConfig(
        log_dir=temp_log_dir,
        console_level="DEBUG",
        file_level="DEBUG",
        track_experiments=False  # Disable for tests
    )
    return EconometricLogger(config)


class TestBasicLogging:
    """Test basic logging functionality."""

    def test_log_levels(self, test_logger, capsys):
        """Test different log levels."""
        test_logger.debug("Debug message")
        test_logger.info("Info message")
        test_logger.warning("Warning message")
        test_logger.error("Error message")

        # Check that messages appear in console output
        captured = capsys.readouterr()
        assert "Debug message" in captured.out or "Debug message" in captured.err
        assert "Info message" in captured.out or "Info message" in captured.err
        assert "Warning message" in captured.out or "Warning message" in captured.err
        assert "Error message" in captured.out or "Error message" in captured.err

    def test_log_files_created(self, test_logger, temp_log_dir):
        """Test that log files are created."""
        test_logger.info("Test message")

        # Check that log file exists
        log_files = list(temp_log_dir.glob("yemen_market_*.log"))
        assert len(log_files) >= 1

    def test_structured_logging(self, test_logger, temp_log_dir):
        """Test structured logging with extra data."""
        test_logger.info("Structured message", extra_data={"key": "value", "number": 42})

        # Read log file and check for structured data
        log_files = list(temp_log_dir.glob("yemen_market_*.log"))
        if log_files:
            with open(log_files[0], 'r') as f:
                content = f.read()
                # The log should contain our structured data
                assert "extra_data" in content or "key" in content


class TestContextManagement:
    """Test context binding and management."""

    def test_bind_unbind(self, test_logger):
        """Test binding and unbinding context."""
        test_logger.bind(user="test_user", session="12345")
        assert test_logger._context["user"] == "test_user"
        assert test_logger._context["session"] == "12345"

        test_logger.unbind("user")
        assert "user" not in test_logger._context
        assert test_logger._context["session"] == "12345"

    def test_context_manager(self, test_logger):
        """Test temporary context."""
        test_logger.bind(permanent="value")

        with test_logger.context(temporary="temp_value"):
            assert test_logger._context["permanent"] == "value"
            assert test_logger._context["temporary"] == "temp_value"

        # After context, temporary should be gone
        assert test_logger._context["permanent"] == "value"
        assert "temporary" not in test_logger._context


class TestPerformanceTracking:
    """Test performance tracking features."""

    def test_timer_context_manager(self, test_logger):
        """Test timer context manager."""
        with test_logger.timer("test_operation"):
            time.sleep(0.1)  # Simulate work

        # Check that timer was logged
        assert "test_operation" not in test_logger._timers

    def test_manual_timer(self, test_logger):
        """Test manual timer start/stop."""
        test_logger.start_timer("manual_timer")
        time.sleep(0.1)
        duration = test_logger.stop_timer("manual_timer")

        assert duration >= 0.1
        assert "manual_timer" not in test_logger._timers

    def test_timer_not_found(self, test_logger):
        """Test stopping non-existent timer."""
        duration = test_logger.stop_timer("non_existent")
        assert duration == 0.0


class TestExperimentTracking:
    """Test experiment tracking features."""

    def test_log_metric(self, test_logger, capsys):
        """Test metric logging."""
        test_logger.log_metric("accuracy", 0.95, step=10)

        captured = capsys.readouterr()
        assert "accuracy" in captured.out or "accuracy" in captured.err
        assert "0.95" in captured.out or "0.95" in captured.err

    def test_log_param(self, test_logger, capsys):
        """Test parameter logging."""
        test_logger.log_param("learning_rate", 0.01)

        captured = capsys.readouterr()
        assert "learning_rate" in captured.out or "learning_rate" in captured.err
        assert "0.01" in captured.out or "0.01" in captured.err

    def test_log_model_performance(self, test_logger, capsys):
        """Test model performance logging."""
        metrics = {"rmse": 0.123, "r2": 0.89}
        test_logger.log_model_performance("test_model", metrics)

        captured = capsys.readouterr()
        assert "test_model" in captured.out or "test_model" in captured.err
        assert "rmse" in captured.out or "rmse" in captured.err


class TestDataLogging:
    """Test data-related logging features."""

    def test_log_data_shape_dataframe(self, test_logger, capsys):
        """Test logging DataFrame shape."""
        import pandas as pd
        import numpy as np

        df = pd.DataFrame(np.random.randn(100, 5))
        test_logger.log_data_shape("test_df", df)

        captured = capsys.readouterr()
        output = captured.out + captured.err
        assert "test_df" in output
        assert "(100, 5)" in output
        assert "DataFrame" in output

    def test_log_data_shape_array(self, test_logger, capsys):
        """Test logging numpy array shape."""
        import numpy as np

        arr = np.random.randn(50, 10)
        test_logger.log_data_shape("test_array", arr)

        captured = capsys.readouterr()
        output = captured.out + captured.err
        assert "test_array" in output
        assert "(50, 10)" in output

    def test_log_data_shape_list(self, test_logger, capsys):
        """Test logging list length."""
        lst = [1, 2, 3, 4, 5]
        test_logger.log_data_shape("test_list", lst)

        captured = capsys.readouterr()
        output = captured.out + captured.err
        assert "test_list" in output
        assert "(5,)" in output


class TestDecorators:
    """Test logging decorators."""

    def test_log_execution_decorator(self, test_logger):
        """Test execution logging decorator."""

        @test_logger.log_execution(level="INFO")
        def sample_function(x, y):
            return x + y

        result = sample_function(2, 3)
        assert result == 5

        # Function name should not be in timers after completion
        assert "sample_function" not in test_logger._timers

    def test_log_execution_with_exception(self, test_logger):
        """Test execution logging with exception."""

        @test_logger.log_execution(level="INFO")
        def failing_function():
            raise ValueError("Test error")

        with pytest.raises(ValueError):
            failing_function()


class TestConfiguration:
    """Test logger configuration options."""

    def test_custom_configuration(self, temp_log_dir):
        """Test creating logger with custom configuration."""
        config = LogConfig(
            log_dir=temp_log_dir / "custom",
            console_level="WARNING",
            file_level="TRACE",
            rotation="10 MB",
            retention="3 days",
            compression="zip"
        )

        logger = EconometricLogger(config)
        assert logger.config.console_level == "WARNING"
        assert logger.config.rotation == "10 MB"
        assert (temp_log_dir / "custom").exists()

    def test_default_configuration(self):
        """Test default configuration values."""
        config = LogConfig()
        assert config.console_level == "INFO"
        assert config.file_level == "DEBUG"
        assert config.rotation == "100 MB"
        assert config.retention == "30 days"


class TestErrorHandling:
    """Test error handling and exception logging."""

    def test_exception_logging(self, test_logger, capsys):
        """Test exception logging with traceback."""
        try:
            1 / 0
        except ZeroDivisionError:
            test_logger.exception("Division by zero occurred")

        captured = capsys.readouterr()
        output = captured.out + captured.err
        assert "Division by zero occurred" in output
        assert "ZeroDivisionError" in output

    def test_artifact_not_found(self, test_logger, capsys):
        """Test logging non-existent artifact."""
        test_logger.log_artifact("non_existent_file.txt")

        captured = capsys.readouterr()
        output = captured.out + captured.err
        assert "Artifact not found" in output


# Integration test
def test_full_workflow(temp_log_dir):
    """Test a complete logging workflow."""
    config = LogConfig(
        log_dir=temp_log_dir,
        console_level="INFO",
        track_experiments=False
    )
    logger = EconometricLogger(config)

    # Simulate a complete analysis workflow
    logger.bind(analysis="test_analysis", version="1.0")

    with logger.context(phase="data_loading"):
        logger.info("Loading data")
        with logger.timer("load_time"):
            time.sleep(0.05)

    with logger.context(phase="modeling"):
        logger.log_param("model_type", "linear_regression")
        logger.log_metric("rmse", 0.25)
        logger.log_model_performance("lr_model", {"r2": 0.85, "mae": 0.20})

    logger.info("Analysis complete")

    # Verify log files were created
    assert len(list(temp_log_dir.glob("*.log"))) >= 1
