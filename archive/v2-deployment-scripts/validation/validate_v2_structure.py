#!/usr/bin/env python3
"""
V2 Structure Validation Script

This script validates that the V2 three-tier econometric analysis implementation
has been properly wired up by checking:

1. File structure and organization
2. Import relationships 
3. Key class definitions
4. Method signatures
5. Dependency flow

This validation doesn't require running the code, just analyzing the structure.
"""

import ast
import sys
from pathlib import Path
from typing import Dict, List, Set


def analyze_python_file(file_path: Path) -> Dict:
    """Analyze a Python file and extract key information."""
    if not file_path.exists():
        return {"error": "File does not exist"}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        info = {
            "classes": [],
            "functions": [],
            "imports": [],
            "async_methods": [],
            "error": None
        }
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                info["classes"].append(node.name)
            elif isinstance(node, ast.FunctionDef):
                info["functions"].append(node.name)
            elif isinstance(node, ast.AsyncFunctionDef):
                info["async_methods"].append(node.name)
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    info["imports"].append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        info["imports"].append(f"{node.module}.{alias.name}")
        
        return info
        
    except Exception as e:
        return {"error": str(e)}


def validate_file_structure():
    """Validate that all required files exist."""
    print("🔍 Validating file structure...")
    
    src_path = Path("src")
    required_files = [
        # Core domain
        "core/domain/market/entities.py",
        "core/domain/market/repositories.py", 
        "core/domain/market/value_objects.py",
        
        # Core models
        "core/models/__init__.py",
        "core/models/interfaces/model.py",
        "core/models/interfaces/estimator.py",
        "core/models/panel/pooled_panel.py",
        "core/models/panel/fixed_effects.py",
        
        # Infrastructure
        "infrastructure/estimators/standard_errors.py",
        "infrastructure/diagnostics/panel_diagnostics.py",
        
        # Application
        "application/services/three_tier_analysis_service.py",
        "application/commands/run_three_tier_analysis.py",
        "application/analysis_tiers/tier1_runner.py",
        "application/analysis_tiers/tier2_runner.py",  
        "application/analysis_tiers/tier3_runner.py",
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        full_path = src_path / file_path
        if full_path.exists():
            existing_files.append(file_path)
        else:
            missing_files.append(file_path)
    
    print(f"  ✅ Found {len(existing_files)} / {len(required_files)} required files")
    
    if missing_files:
        print("  ⚠️  Missing files:")
        for file in missing_files:
            print(f"    - {file}")
    
    return len(missing_files) == 0


def validate_three_tier_service():
    """Validate the main three-tier analysis service."""
    print("🔍 Validating ThreeTierAnalysisService...")
    
    service_path = Path("src/application/services/three_tier_analysis_service.py")
    info = analyze_python_file(service_path)
    
    if info.get("error"):
        print(f"  ❌ Error analyzing service: {info['error']}")
        return False
    
    # Check for required class
    if "ThreeTierAnalysisService" not in info["classes"]:
        print("  ❌ ThreeTierAnalysisService class not found")
        return False
    
    print("  ✅ ThreeTierAnalysisService class found")
    
    # Check for required methods
    required_methods = ["run_analysis", "__init__"]
    for method in required_methods:
        if method in info["async_methods"] or method in info["functions"]:
            print(f"    ✅ Method {method} found")
        else:
            print(f"    ❌ Method {method} missing")
            return False
    
    # Check imports
    expected_imports = [
        "datetime",
        "MarketRepository", 
        "PriceRepository",
        "Tier1Runner",
        "Tier2Runner", 
        "Tier3Runner"
    ]
    
    import_check = all(
        any(expected in imp for imp in info["imports"]) 
        for expected in expected_imports
    )
    
    if import_check:
        print("  ✅ Required imports present")
    else:
        print("  ⚠️  Some imports may be missing")
    
    return True


def validate_tier_runners():
    """Validate tier runner implementations."""
    print("🔍 Validating tier runners...")
    
    tiers = [
        ("tier1_runner.py", "Tier1Runner"),
        ("tier2_runner.py", "Tier2Runner"), 
        ("tier3_runner.py", "Tier3Runner")
    ]
    
    all_valid = True
    
    for filename, classname in tiers:
        runner_path = Path(f"src/application/analysis_tiers/{filename}")
        info = analyze_python_file(runner_path)
        
        if info.get("error"):
            print(f"  ❌ Error analyzing {filename}: {info['error']}")
            all_valid = False
            continue
        
        if classname not in info["classes"]:
            print(f"  ❌ {classname} class not found in {filename}")
            all_valid = False
            continue
        
        print(f"  ✅ {classname} class found")
        
        # Check for run method
        if "run" in info["async_methods"]:
            print(f"    ✅ async run method found in {classname}")
        else:
            print(f"    ❌ async run method missing in {classname}")
            all_valid = False
    
    return all_valid


def validate_command_handler():
    """Validate command and handler structure."""
    print("🔍 Validating command handler...")
    
    command_path = Path("src/application/commands/run_three_tier_analysis.py")
    info = analyze_python_file(command_path)
    
    if info.get("error"):
        print(f"  ❌ Error analyzing command: {info['error']}")
        return False
    
    required_classes = ["RunThreeTierAnalysisCommand", "RunThreeTierAnalysisHandler"]
    
    for cls in required_classes:
        if cls in info["classes"]:
            print(f"  ✅ {cls} class found")
        else:
            print(f"  ❌ {cls} class missing")
            return False
    
    # Check handler has handle method
    if "handle" in info["async_methods"]:
        print("  ✅ async handle method found")
    else:
        print("  ❌ async handle method missing")
        return False
    
    return True


def validate_domain_entities():
    """Validate domain entity structure."""
    print("🔍 Validating domain entities...")
    
    entities_path = Path("src/core/domain/market/entities.py")
    info = analyze_python_file(entities_path)
    
    if info.get("error"):
        print(f"  ❌ Error analyzing entities: {info['error']}")
        return False
    
    required_classes = ["Market", "PriceObservation", "PanelData"]
    
    for cls in required_classes:
        if cls in info["classes"]:
            print(f"  ✅ {cls} class found")
        else:
            print(f"  ❌ {cls} class missing")
            return False
    
    return True


def validate_integration_flow():
    """Validate that the integration flow is properly structured."""
    print("🔍 Validating integration flow...")
    
    # Check that ThreeTierAnalysisService imports tier runners
    service_path = Path("src/application/services/three_tier_analysis_service.py")
    
    if not service_path.exists():
        print("  ❌ Service file missing")
        return False
    
    with open(service_path, 'r') as f:
        content = f.read()
    
    integration_checks = [
        ("Tier1Runner", "Tier 1 runner import"),
        ("Tier2Runner", "Tier 2 runner import"), 
        ("Tier3Runner", "Tier 3 runner import"),
        ("MarketRepository", "Market repository import"),
        ("PriceRepository", "Price repository import"),
        ("run_analysis", "Main analysis method"),
    ]
    
    all_checks_pass = True
    
    for check, description in integration_checks:
        if check in content:
            print(f"  ✅ {description} found")
        else:
            print(f"  ❌ {description} missing")
            all_checks_pass = False
    
    return all_checks_pass


def main():
    """Run validation."""
    print("=" * 60)
    print("YEMEN MARKET INTEGRATION - V2 STRUCTURE VALIDATION")
    print("=" * 60)
    print("Validating three-tier econometric analysis V2 implementation...")
    print()
    
    validations = [
        validate_file_structure,
        validate_domain_entities,
        validate_three_tier_service,
        validate_tier_runners,
        validate_command_handler,
        validate_integration_flow
    ]
    
    passed = 0
    total = len(validations)
    
    for validation in validations:
        if validation():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"VALIDATION RESULTS: {passed}/{total} checks passed")
    
    if passed == total:
        print("✅ ALL VALIDATIONS PASSED!")
        print()
        print("🎯 V2 Three-Tier Analysis Implementation Status:")
        print("  ✅ File structure is complete")
        print("  ✅ Core components are properly defined")
        print("  ✅ Integration points are wired correctly")
        print("  ✅ Command and service layers are connected")
        print()
        print("🚀 The V2 implementation is ready for:")
        print("  1. Unit testing with proper mocks")
        print("  2. Integration testing with real data")
        print("  3. Performance benchmarking")
        print("  4. Production deployment")
        success = True
    else:
        print("❌ VALIDATION FAILED!")
        print("Fix the implementation issues before proceeding.")
        success = False
    
    print("=" * 60)
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)