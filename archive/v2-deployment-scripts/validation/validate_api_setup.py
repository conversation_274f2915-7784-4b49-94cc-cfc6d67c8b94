#!/usr/bin/env python3
"""
Validation script to check if all API components are properly set up.
"""

import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """Test all critical imports."""
    print("Testing imports...")
    
    try:
        # Test basic FastAPI import
        from fastapi import FastAPI
        print("✓ FastAPI imported successfully")
        
        # Test container (need to handle relative import)
        try:
            from src.shared.container import Container
            print("✓ Container imported successfully")
        except ImportError:
            try:
                import sys
                import os
                sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
                from shared.container import Container
                print("✓ Container imported successfully")
            except ImportError as e:
                print(f"✗ Container import error: {e}")
                return False
        
        # Test three-tier service
        try:
            from src.application.services import ThreeTierAnalysisService
            print("✓ ThreeTierAnalysisService imported successfully")
        except ImportError:
            try:
                from application.services import ThreeTierAnalysisService
                print("✓ ThreeTierAnalysisService imported successfully")
            except ImportError as e:
                print(f"✗ ThreeTierAnalysisService import error: {e}")
                return False
        
        # Test analysis schemas
        try:
            from src.interfaces.api.rest.schemas.analysis import (
                ThreeTierAnalysisRequest,
                TierAnalysisRequest,
                AnalysisResponse,
                TierResultsResponse,
                AnalysisStatus,
                TierType
            )
            print("✓ Analysis schemas imported successfully")
        except ImportError:
            try:
                from interfaces.api.rest.schemas.analysis import (
                    ThreeTierAnalysisRequest,
                    TierAnalysisRequest,
                    AnalysisResponse,
                    TierResultsResponse,
                    AnalysisStatus,
                    TierType
                )
                print("✓ Analysis schemas imported successfully")
            except ImportError as e:
                print(f"✗ Analysis schemas import error: {e}")
                return False
        
        # Test analysis routes
        try:
            from src.interfaces.api.rest.routes.analysis import router
            print("✓ Analysis routes imported successfully")
        except ImportError:
            try:
                from interfaces.api.rest.routes.analysis import router
                print("✓ Analysis routes imported successfully")
            except ImportError as e:
                print(f"✗ Analysis routes import error: {e}")
                return False
        
        # Test the app
        try:
            from src.interfaces.api.rest.app import app
            print("✓ FastAPI app imported successfully")
        except ImportError:
            try:
                from interfaces.api.rest.app import app
                print("✓ FastAPI app imported successfully")
            except ImportError as e:
                print(f"✗ FastAPI app import error: {e}")
                return False
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def test_container_configuration():
    """Test container configuration."""
    print("\nTesting container configuration...")
    
    try:
        from shared.container import Container
        
        # Check if three_tier_analysis_service is configured
        if hasattr(Container, 'three_tier_analysis_service'):
            print("✓ three_tier_analysis_service configured in container")
        else:
            print("✗ three_tier_analysis_service not found in container")
            return False
        
        # Check if other required services are configured
        required_services = [
            'analysis_orchestrator',
            'model_estimator_service',
            'event_bus'
        ]
        
        for service in required_services:
            if hasattr(Container, service):
                print(f"✓ {service} configured in container")
            else:
                print(f"✗ {service} not found in container")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Container configuration error: {e}")
        return False


def test_schema_validation():
    """Test schema validation."""
    print("\nTesting schema validation...")
    
    try:
        from interfaces.api.rest.schemas.analysis import (
            ThreeTierAnalysisRequest,
            TierAnalysisRequest,
            AnalysisStatus,
            TierType
        )
        from datetime import date
        
        # Test ThreeTierAnalysisRequest
        request_data = {
            "name": "Test Analysis",
            "start_date": date.today(),
            "end_date": date.today(),
            "confidence_level": 0.95
        }
        
        try:
            request = ThreeTierAnalysisRequest(**request_data)
            print("✗ ThreeTierAnalysisRequest should have failed validation (end_date <= start_date)")
            return False
        except ValueError:
            print("✓ ThreeTierAnalysisRequest validation working correctly")
        
        # Test valid request
        request_data["end_date"] = date.today().replace(year=date.today().year + 1)
        request = ThreeTierAnalysisRequest(**request_data)
        print("✓ ThreeTierAnalysisRequest accepts valid data")
        
        # Test TierAnalysisRequest
        tier_request = TierAnalysisRequest(**request_data)
        print("✓ TierAnalysisRequest working correctly")
        
        # Test enums
        status = AnalysisStatus.PENDING
        tier = TierType.TIER1
        print("✓ Enum classes working correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Schema validation error: {e}")
        return False


def test_endpoint_structure():
    """Test endpoint structure."""
    print("\nTesting endpoint structure...")
    
    try:
        from interfaces.api.rest.routes.analysis import router
        
        # Get routes from the router
        routes = [route.path for route in router.routes]
        
        expected_routes = [
            "/three-tier",
            "/tier1", 
            "/tier2",
            "/tier3",
            "/{analysis_id}/status",
            "/{analysis_id}/results"
        ]
        
        for expected_route in expected_routes:
            if any(expected_route in route for route in routes):
                print(f"✓ Route {expected_route} found")
            else:
                print(f"✗ Route {expected_route} not found")
                print(f"  Available routes: {routes}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Endpoint structure error: {e}")
        return False


def main():
    """Run all validation tests."""
    print("Yemen Market Integration V2 API Setup Validation")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_container_configuration,
        test_schema_validation,
        test_endpoint_structure
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All validation tests passed! API setup is correct.")
        print("\nYou can now:")
        print("1. Run 'python start_api_server.py' to start the API server")
        print("2. Run 'python test_api_endpoints.py' to test the endpoints")
        return True
    else:
        print("✗ Some validation tests failed. Please fix the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)