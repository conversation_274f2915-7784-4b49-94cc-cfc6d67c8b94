#!/usr/bin/env python3
"""
V2 System Import Validation Script

This script validates that all critical V2 components can be imported successfully,
confirming that the system is functional and ready for use.

All imports should pass if the V2 system is properly configured.
"""

import sys
from typing import List, Tuple, Dict, Any

def test_critical_imports() -> Dict[str, Any]:
    """Test all critical V2 system imports."""
    
    results = {
        "passed": 0,
        "failed": 0,
        "details": []
    }
    
    critical_imports = [
        # Core Domain Layer
        ("src.core.domain.market.entities", "Market Domain Entities"),
        ("src.core.domain.conflict.entities", "Conflict Domain Entities"), 
        ("src.core.domain.geography.entities", "Geography Domain Entities"),
        ("src.core.domain.auth", "Authentication Domain"),
        
        # Core Models
        ("src.core.models.interfaces", "Model Interfaces"),
        ("src.core.models.panel", "Panel Models"),
        ("src.core.models.time_series", "Time Series Models"),
        ("src.core.models.validation", "Validation Models"),
        
        # Infrastructure Layer
        ("src.infrastructure.estimators", "Statistical Estimators"),
        ("src.infrastructure.diagnostics", "Diagnostic Tests"),
        ("src.infrastructure.persistence", "Database Persistence"),
        ("src.infrastructure.external_services", "External API Clients"),
        ("src.infrastructure.caching", "Caching Services"),
        ("src.infrastructure.security", "Security Services"),
        ("src.infrastructure.processors", "Data Processors"),
        
        # Application Layer - Core Services
        ("src.application.services.three_tier_analysis_service", "Three Tier Analysis Service"),
        ("src.application.services.analysis_orchestrator", "Analysis Orchestrator"),
        ("src.application.services.model_estimator_service", "Model Estimator Service"),
        ("src.application.services.data_preparation_service", "Data Preparation Service"),
        
        # Application Layer - Analysis Tiers
        ("src.application.analysis_tiers.tier1_runner", "Tier 1 Analysis Runner"),
        ("src.application.analysis_tiers.tier2_runner", "Tier 2 Analysis Runner"),
        ("src.application.analysis_tiers.tier3_runner", "Tier 3 Analysis Runner"),
        
        # Application Layer - Commands & Queries
        ("src.application.commands", "Command Handlers"),
        ("src.application.queries", "Query Handlers"),
        
        # Interface Layer
        ("src.interfaces.api.rest", "REST API"),
        ("src.interfaces.cli", "CLI Interface"),
        
        # Shared Infrastructure
        ("src.shared.container", "Dependency Injection Container"),
        ("src.shared.plugins", "Plugin System"),
    ]
    
    print("🔍 V2 System Import Validation")
    print("=" * 60)
    
    for module_path, description in critical_imports:
        try:
            exec(f"import {module_path}")
            results["passed"] += 1
            results["details"].append(("✅", description, "OK"))
            print(f"✅ {description:<40} OK")
            
        except ImportError as e:
            results["failed"] += 1
            results["details"].append(("❌", description, str(e)))
            print(f"❌ {description:<40} FAILED: {str(e)}")
            
        except Exception as e:
            results["failed"] += 1
            results["details"].append(("⚠️", description, str(e)))
            print(f"⚠️ {description:<40} ERROR: {str(e)}")
    
    return results

def test_key_functionality() -> Dict[str, Any]:
    """Test key V2 functionality beyond just imports."""
    
    results = {
        "passed": 0,
        "failed": 0,
        "details": []
    }
    
    print("\n🧪 Functional Tests")
    print("=" * 60)
    
    # Test 1: Container instantiation
    try:
        from src.shared.container import Container
        container = Container()
        results["passed"] += 1
        results["details"].append(("✅", "Container Instantiation", "OK"))
        print(f"✅ Container Instantiation{'':30} OK")
    except Exception as e:
        results["failed"] += 1
        results["details"].append(("❌", "Container Instantiation", str(e)))
        print(f"❌ Container Instantiation{'':30} FAILED: {str(e)}")
    
    # Test 2: Three Tier Analysis Service instantiation
    try:
        from src.application.services.three_tier_analysis_service import ThreeTierAnalysisService
        # We can't fully instantiate without dependencies, but we can check the class
        assert hasattr(ThreeTierAnalysisService, 'run_analysis')
        results["passed"] += 1
        results["details"].append(("✅", "ThreeTierAnalysisService Class", "OK"))
        print(f"✅ ThreeTierAnalysisService Class{'':23} OK")
    except Exception as e:
        results["failed"] += 1
        results["details"].append(("❌", "ThreeTierAnalysisService Class", str(e)))
        print(f"❌ ThreeTierAnalysisService Class{'':23} FAILED: {str(e)}")
    
    # Test 3: Domain Entity Creation
    try:
        from src.core.domain.market.entities import Market
        from src.core.domain.market.value_objects import MarketId, Coordinates, MarketType
        from datetime import datetime
        
        # Create a market entity to test dataclass structure
        market = Market(
            market_id=MarketId("TEST001"),
            name="Test Market",
            coordinates=Coordinates(latitude=15.3694, longitude=44.1910),
            market_type=MarketType.RETAIL,
            governorate="Sana'a",
            district="Sana'a City",
            active_since=datetime(2020, 1, 1)  # Use past date
        )
        assert market.market_id.value == "TEST001"
        assert market.name == "Test Market"
        results["passed"] += 1
        results["details"].append(("✅", "Domain Entity Creation", "OK"))
        print(f"✅ Domain Entity Creation{'':28} OK")
    except Exception as e:
        results["failed"] += 1
        results["details"].append(("❌", "Domain Entity Creation", str(e)))
        print(f"❌ Domain Entity Creation{'':28} FAILED: {str(e)}")
    
    return results

def main():
    """Run all validation tests."""
    
    print("🚀 V2 System Validation")
    print("This script validates that the V2 system is functional after import fixes.")
    print()
    
    # Test imports
    import_results = test_critical_imports()
    
    # Test functionality
    functional_results = test_key_functionality()
    
    # Summary
    total_passed = import_results["passed"] + functional_results["passed"]
    total_failed = import_results["failed"] + functional_results["failed"]
    
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    print(f"Import Tests:      {import_results['passed']} passed, {import_results['failed']} failed")
    print(f"Functional Tests:  {functional_results['passed']} passed, {functional_results['failed']} failed")
    print(f"TOTAL:            {total_passed} passed, {total_failed} failed")
    
    if total_failed == 0:
        print("\n🎉 SUCCESS: V2 system is fully functional!")
        print("✅ All critical components can be imported and instantiated")
        print("✅ No circular dependencies detected")
        print("✅ Domain entities work correctly")
        print("✅ Application services are available")
        print("✅ Infrastructure layer is accessible")
        
        print("\n🚀 Next Steps:")
        print("1. Run integration tests: pytest tests/integration/")
        print("2. Start the API server: python -m src.interfaces.api.rest.app")
        print("3. Use the CLI: python -m src.interfaces.cli.app --help")
        print("4. Begin data analysis workflows")
        
        return True
    else:
        print(f"\n❌ VALIDATION FAILED: {total_failed} issues detected")
        print("\nFailed Tests:")
        all_details = import_results["details"] + functional_results["details"]
        for status, description, error in all_details:
            if status in ["❌", "⚠️"]:
                print(f"  {status} {description}: {error}")
        
        print("\n🔧 Recommended Actions:")
        print("1. Check error messages above for specific issues")
        print("2. Ensure all dependencies are installed: pip install -r requirements.txt")
        print("3. Verify Python path includes project root")
        print("4. Check for any remaining circular imports")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)