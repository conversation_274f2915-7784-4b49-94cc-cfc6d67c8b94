#!/bin/bash
# Master Deployment Orchestrator for Yemen Market Integration V2
# Coordinates all deployment phases with comprehensive validation and monitoring

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ID=$(date +%Y%m%d-%H%M%S)
DEPLOYMENT_LOG="/tmp/deployment-$DEPLOYMENT_ID.log"

# Default configuration
NAMESPACE="yemen-market-v2"
V1_NAMESPACE="yemen-market"
CLUSTER_NAME="${CLUSTER_NAME:-yemen-market-prod}"
ENVIRONMENT="${ENVIRONMENT:-production}"
V2_IMAGE_TAG="${V2_IMAGE_TAG:-v2.1.0}"
DRY_RUN="${DRY_RUN:-false}"
SKIP_MONITORING="${SKIP_MONITORING:-false}"
FORCE_PROCEED="${FORCE_PROCEED:-false}"

# Function definitions
log_info() {
    local message="[INFO $(date '+%H:%M:%S')] $1"
    echo -e "${GREEN}$message${NC}"
    echo "$message" >> "$DEPLOYMENT_LOG"
}

log_warn() {
    local message="[WARN $(date '+%H:%M:%S')] $1"
    echo -e "${YELLOW}$message${NC}"
    echo "$message" >> "$DEPLOYMENT_LOG"
}

log_error() {
    local message="[ERROR $(date '+%H:%M:%S')] $1"
    echo -e "${RED}$message${NC}"
    echo "$message" >> "$DEPLOYMENT_LOG"
}

log_step() {
    local message="[STEP $(date '+%H:%M:%S')] $1"
    echo -e "${BLUE}$message${NC}"
    echo "$message" >> "$DEPLOYMENT_LOG"
}

log_success() {
    local message="[SUCCESS $(date '+%H:%M:%S')] $1"
    echo -e "${CYAN}$message${NC}"
    echo "$message" >> "$DEPLOYMENT_LOG"
}

log_phase() {
    local message="[PHASE $(date '+%H:%M:%S')] $1"
    echo -e "${MAGENTA}=== $message ===${NC}"
    echo "=== $message ===" >> "$DEPLOYMENT_LOG"
}

print_banner() {
    echo -e "${MAGENTA}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    Yemen Market Integration V2 Production Deployment        ║
║                                                              ║
║    Zero-downtime blue-green deployment with automated       ║
║    traffic migration and rollback capabilities              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Yemen Market Integration V2 Production Deployment Orchestrator

OPTIONS:
    -e, --environment ENV       Target environment (production|staging) [default: production]
    -t, --tag TAG              V2 image tag to deploy [default: v2.1.0]
    -c, --cluster CLUSTER      Kubernetes cluster name [default: yemen-market-prod]
    -d, --dry-run              Perform dry run without actual deployment
    -s, --skip-monitoring      Skip monitoring setup (if already configured)
    -f, --force                Force proceed without confirmation
    -h, --help                 Show this help message

ENVIRONMENT VARIABLES:
    CLUSTER_NAME               Kubernetes cluster name
    V2_IMAGE_TAG              Docker image tag for V2
    SLACK_WEBHOOK             Slack webhook URL for notifications
    PAGERDUTY_SERVICE_KEY     PagerDuty service key for critical alerts

EXAMPLES:
    # Standard production deployment
    $0 --environment production --tag v2.1.0

    # Dry run to validate configuration
    $0 --dry-run

    # Quick deployment skipping monitoring setup
    $0 --skip-monitoring --force

    # Staging deployment
    $0 --environment staging --tag v2.1.0-rc1

EOF
}

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -t|--tag)
                V2_IMAGE_TAG="$2"
                shift 2
                ;;
            -c|--cluster)
                CLUSTER_NAME="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN="true"
                shift
                ;;
            -s|--skip-monitoring)
                SKIP_MONITORING="true"
                shift
                ;;
            -f|--force)
                FORCE_PROCEED="true"
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

validate_configuration() {
    log_step "Validating deployment configuration..."
    
    local validation_errors=()
    
    # Check required environment variables
    if [ -z "${SLACK_WEBHOOK:-}" ]; then
        validation_errors+=("SLACK_WEBHOOK environment variable not set")
    fi
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(production|staging)$ ]]; then
        validation_errors+=("Invalid environment: $ENVIRONMENT (must be production or staging)")
    fi
    
    # Check image tag format
    if [[ ! "$V2_IMAGE_TAG" =~ ^v[0-9]+\.[0-9]+\.[0-9]+(-.*)?$ ]]; then
        validation_errors+=("Invalid image tag format: $V2_IMAGE_TAG (expected: vX.Y.Z or vX.Y.Z-suffix)")
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        validation_errors+=("Cannot connect to Kubernetes cluster")
    fi
    
    # Validate V1 is running
    if ! kubectl get namespace "$V1_NAMESPACE" &> /dev/null; then
        validation_errors+=("V1 namespace $V1_NAMESPACE not found")
    fi
    
    # Check V1 deployments
    if ! kubectl get deployment yemen-market-api -n "$V1_NAMESPACE" &> /dev/null; then
        validation_errors+=("V1 API deployment not found")
    fi
    
    # Check required scripts exist
    local required_scripts=(
        "deployment-monitoring.sh"
        "production-deployment.sh"
        "rollback-automation.sh"
    )
    
    for script in "${required_scripts[@]}"; do
        if [ ! -f "$SCRIPT_DIR/$script" ]; then
            validation_errors+=("Required script not found: $script")
        fi
    done
    
    # Report validation results
    if [ ${#validation_errors[@]} -gt 0 ]; then
        log_error "Configuration validation failed:"
        for error in "${validation_errors[@]}"; do
            log_error "  - $error"
        done
        exit 1
    fi
    
    log_success "Configuration validation passed"
}

show_deployment_summary() {
    log_step "Deployment Summary"
    
    cat << EOF

╔════════════════════════════════════════════════════════════════╗
║                    DEPLOYMENT CONFIGURATION                    ║
╠════════════════════════════════════════════════════════════════╣
║ Deployment ID:    $DEPLOYMENT_ID                    ║
║ Environment:      $ENVIRONMENT                               ║
║ Cluster:          $CLUSTER_NAME                    ║
║ V2 Image Tag:     $V2_IMAGE_TAG                          ║
║ Target Namespace: $NAMESPACE                    ║
║ V1 Namespace:     $V1_NAMESPACE                           ║
║ Dry Run:          $DRY_RUN                                   ║
║ Skip Monitoring:  $SKIP_MONITORING                                   ║
╚════════════════════════════════════════════════════════════════╝

DEPLOYMENT PHASES:
  1. 🔧 Pre-deployment validation and setup
  2. 📊 Monitoring and alerting configuration
  3. 🚀 V2 infrastructure and application deployment
  4. 🔄 Gradual traffic migration (10% → 100%)
  5. ✅ Post-deployment validation and handover

ESTIMATED DURATION: 2.5 hours
ROLLBACK TIME: < 5 minutes (automated)

EOF

    if [ "$FORCE_PROCEED" != "true" ]; then
        echo -n "Proceed with this deployment? (yes/no): "
        read -r confirmation
        if [[ ! "$confirmation" =~ ^[Yy]([Ee][Ss])?$ ]]; then
            log_info "Deployment cancelled by user"
            exit 0
        fi
    fi
}

execute_phase() {
    local phase_name="$1"
    local phase_script="$2"
    local phase_args="${3:-}"
    
    log_phase "$phase_name"
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "DRY RUN: Would execute $phase_script $phase_args"
        sleep 2
        return 0
    fi
    
    local start_time=$(date +%s)
    
    # Execute the phase script
    if [ -f "$SCRIPT_DIR/$phase_script" ]; then
        log_info "Executing: $phase_script $phase_args"
        
        # Run script with timeout and error handling
        if timeout 3600 bash "$SCRIPT_DIR/$phase_script" $phase_args; then
            local duration=$(($(date +%s) - start_time))
            log_success "$phase_name completed successfully in ${duration}s"
        else
            local exit_code=$?
            log_error "$phase_name failed with exit code $exit_code"
            
            # Trigger automatic rollback on critical failures
            if [[ "$phase_name" =~ (Infrastructure|Application|Traffic) ]]; then
                log_error "Critical phase failed - triggering automatic rollback"
                execute_emergency_rollback
            fi
            
            exit $exit_code
        fi
    else
        log_error "Phase script not found: $phase_script"
        exit 1
    fi
}

execute_pre_deployment_checks() {
    log_phase "Pre-deployment Validation and Setup"
    
    # Cluster health check
    log_step "Verifying cluster health..."
    local unhealthy_nodes=$(kubectl get nodes --no-headers | grep -v " Ready " | wc -l)
    if [ "$unhealthy_nodes" -gt 0 ]; then
        log_error "$unhealthy_nodes nodes are not ready"
        exit 1
    fi
    log_success "All cluster nodes are healthy"
    
    # Resource capacity check
    log_step "Checking cluster capacity..."
    local total_cpu=$(kubectl describe nodes | grep "cpu:" | awk '{sum += $2} END {print sum}')
    local total_memory=$(kubectl describe nodes | grep "memory:" | awk '{sum += $2} END {print sum}')
    log_info "Available resources: ${total_cpu} CPU cores, ${total_memory} memory"
    
    # V1 health verification
    log_step "Verifying V1 system health..."
    if kubectl exec -n "$V1_NAMESPACE" deployment/yemen-market-api -- curl -f http://localhost:8000/health &> /dev/null; then
        log_success "V1 system is healthy"
    else
        log_error "V1 system health check failed"
        exit 1
    fi
    
    # External dependencies check
    log_step "Checking external dependencies..."
    local external_services=(
        "https://api.worldbank.org/v2/country"
        "https://data.humdata.org/api/3/action/status_show"
    )
    
    for service in "${external_services[@]}"; do
        if curl -f "$service" &> /dev/null; then
            log_info "✅ $service is accessible"
        else
            log_warn "⚠️  $service is not accessible"
        fi
    done
    
    log_success "Pre-deployment checks completed"
}

execute_monitoring_setup() {
    if [ "$SKIP_MONITORING" = "true" ]; then
        log_info "Skipping monitoring setup as requested"
        return 0
    fi
    
    execute_phase "Monitoring and Alerting Setup" "deployment-monitoring.sh"
    
    # Verify monitoring is working
    log_step "Verifying monitoring setup..."
    sleep 30  # Allow time for services to start
    
    if kubectl get pods -n monitoring | grep -q Running; then
        log_success "Monitoring services are running"
    else
        log_error "Monitoring services failed to start"
        exit 1
    fi
}

execute_v2_deployment() {
    execute_phase "V2 Infrastructure and Application Deployment" "production-deployment.sh"
    
    # Additional validation after deployment
    log_step "Validating V2 deployment..."
    
    # Wait for pods to be ready
    if kubectl wait --for=condition=ready pod -l app=yemen-market-api -n "$NAMESPACE" --timeout=600s; then
        log_success "V2 API pods are ready"
    else
        log_error "V2 API pods failed to become ready"
        execute_emergency_rollback
        exit 1
    fi
    
    # Run smoke tests
    log_step "Running extended smoke tests..."
    if kubectl exec -n "$NAMESPACE" deployment/yemen-market-api -- \
        curl -f http://localhost:8000/health &> /dev/null; then
        log_success "V2 health check passed"
    else
        log_error "V2 health check failed"
        execute_emergency_rollback
        exit 1
    fi
}

monitor_traffic_migration() {
    log_step "Monitoring traffic migration progress..."
    
    local start_time=$(date +%s)
    local max_duration=3600  # 1 hour max for traffic migration
    
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        # Check if migration is complete
        local v2_weight=$(kubectl get ingress yemen-market-canary-ingress -n "$NAMESPACE" \
            -o jsonpath='{.metadata.annotations.nginx\.ingress\.kubernetes\.io/canary-weight}' 2>/dev/null || echo "0")
        
        if [ "$v2_weight" = "100" ]; then
            log_success "Traffic migration completed - 100% traffic on V2"
            break
        fi
        
        # Check for timeout
        if [ $elapsed -gt $max_duration ]; then
            log_error "Traffic migration timed out after $elapsed seconds"
            execute_emergency_rollback
            exit 1
        fi
        
        log_info "Traffic migration in progress: ${v2_weight}% to V2 (elapsed: ${elapsed}s)"
        sleep 30
    done
}

execute_post_deployment_validation() {
    log_phase "Post-deployment Validation"
    
    # Comprehensive system validation
    log_step "Running comprehensive validation suite..."
    
    # API endpoint testing
    local api_endpoints=(
        "/health"
        "/ready"
        "/api/v2/markets"
        "/api/v2/commodities"
        "/metrics"
    )
    
    for endpoint in "${api_endpoints[@]}"; do
        if kubectl exec -n "$NAMESPACE" deployment/yemen-market-api -- \
            curl -f "http://localhost:8000$endpoint" &> /dev/null; then
            log_info "✅ $endpoint is responding"
        else
            log_error "❌ $endpoint is not responding"
        fi
    done
    
    # Performance validation
    log_step "Validating performance metrics..."
    sleep 60  # Allow metrics to accumulate
    
    # Database connectivity test
    log_step "Testing database connectivity..."
    if kubectl exec -n "$NAMESPACE" deployment/yemen-market-api -- \
        python -c "import psycopg2; conn = psycopg2.connect(os.environ['DATABASE_URL']); print('DB OK')" &> /dev/null; then
        log_success "Database connectivity verified"
    else
        log_error "Database connectivity failed"
    fi
    
    # External service integration test
    log_step "Testing external service integration..."
    if kubectl exec -n "$NAMESPACE" deployment/yemen-market-api -- \
        curl -f https://api.worldbank.org/v2/country/YEM &> /dev/null; then
        log_success "External service integration verified"
    else
        log_warn "External service integration test failed (may be temporary)"
    fi
    
    log_success "Post-deployment validation completed"
}

execute_emergency_rollback() {
    log_error "🚨 EXECUTING EMERGENCY ROLLBACK 🚨"
    
    # Use the rollback automation script
    if [ -f "$SCRIPT_DIR/rollback-automation.sh" ]; then
        bash "$SCRIPT_DIR/rollback-automation.sh" full
    else
        # Manual emergency rollback
        log_error "Rollback script not found - executing manual rollback"
        
        # Immediate traffic rollback
        kubectl patch ingress yemen-market-canary-ingress -n "$NAMESPACE" -p \
            '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary-weight":"0"}}}' || true
        
        # Scale down V2
        kubectl scale deployment yemen-market-api --replicas=0 -n "$NAMESPACE" || true
        kubectl scale deployment yemen-market-worker --replicas=0 -n "$NAMESPACE" || true
        
        # Scale up V1
        kubectl scale deployment yemen-market-api --replicas=3 -n "$V1_NAMESPACE" || true
        kubectl scale deployment yemen-market-worker --replicas=5 -n "$V1_NAMESPACE" || true
    fi
}

generate_final_report() {
    log_phase "Generating Deployment Report"
    
    local report_file="/tmp/deployment-report-$DEPLOYMENT_ID.md"
    local deployment_end_time=$(date)
    local deployment_duration=$(( $(date +%s) - $(stat -c %Y "$DEPLOYMENT_LOG" 2>/dev/null || echo $(date +%s)) ))
    
    cat > "$report_file" << EOF
# Yemen Market Integration V2 Production Deployment Report

**Deployment ID:** $DEPLOYMENT_ID
**Start Time:** $(head -1 "$DEPLOYMENT_LOG" | grep -o '\[[0-9:]*\]' | tr -d '[]')
**End Time:** $(date '+%H:%M:%S')
**Duration:** ${deployment_duration} seconds
**Environment:** $ENVIRONMENT
**V2 Image Tag:** $V2_IMAGE_TAG
**Cluster:** $CLUSTER_NAME

## Deployment Status: ✅ SUCCESSFUL

### Phases Completed
- [x] Pre-deployment validation and setup
- [x] Monitoring and alerting configuration  
- [x] V2 infrastructure and application deployment
- [x] Gradual traffic migration (0% → 100%)
- [x] Post-deployment validation

### Current System State
- **V2 Status:** Active (handling 100% traffic)
- **V1 Status:** Scaled down (preserved for rollback)
- **Database:** V2 database with migrated data
- **Monitoring:** Active with full alerting

### Performance Metrics
\`\`\`
$(kubectl top pods -n "$NAMESPACE" 2>/dev/null || echo "Metrics not available")
\`\`\`

### Access Information
- **API Endpoint:** https://api.yemen-market.prod.com
- **Grafana:** https://grafana.yemen-market.prod.com
- **Prometheus:** https://prometheus.yemen-market.prod.com

### Next Steps
1. Monitor system performance for 24 hours
2. Conduct post-deployment review meeting
3. Update documentation with lessons learned
4. Schedule V1 cleanup after 7-day grace period

### Team Handover
- **Monitoring:** Platform team
- **Incident Response:** On-call rotation
- **Data Operations:** Data team
- **Development:** Development team

---
*Report generated by deployment orchestrator on $deployment_end_time*
EOF
    
    log_info "Deployment report saved to: $report_file"
    
    # Display key information
    cat << EOF

╔════════════════════════════════════════════════════════════════╗
║                    DEPLOYMENT COMPLETED                        ║
╠════════════════════════════════════════════════════════════════╣
║ Status:           ✅ SUCCESSFUL                                ║
║ Deployment ID:    $DEPLOYMENT_ID                    ║
║ Duration:         ${deployment_duration} seconds                           ║
║ V2 Status:        Active (100% traffic)                       ║
║ V1 Status:        Preserved for rollback                      ║
╚════════════════════════════════════════════════════════════════╝

🎉 Yemen Market Integration V2 is now live in production!

📊 Monitor at: https://grafana.yemen-market.prod.com
🔄 Rollback available: ./scripts/rollback-automation.sh
📋 Full report: $report_file

EOF
}

cleanup_deployment() {
    log_step "Cleaning up deployment artifacts..."
    
    # Clean up temporary files (but preserve logs)
    find /tmp -name "prometheus-values-*" -delete 2>/dev/null || true
    
    # Archive deployment log
    if [ -f "$DEPLOYMENT_LOG" ]; then
        local archive_dir="/var/log/deployments"
        mkdir -p "$archive_dir" 2>/dev/null || true
        cp "$DEPLOYMENT_LOG" "$archive_dir/" 2>/dev/null || true
    fi
}

# Signal handlers
handle_interrupt() {
    log_error "Deployment interrupted by user"
    log_warn "Consider running rollback if deployment was in progress"
    exit 130
}

handle_error() {
    local exit_code=$?
    log_error "Deployment failed with exit code $exit_code"
    log_warn "Check logs and consider rollback if necessary"
    exit $exit_code
}

# Set up signal handling
trap handle_interrupt INT TERM
trap handle_error ERR

main() {
    # Initialize deployment
    print_banner
    log_info "Starting Yemen Market Integration V2 deployment orchestration"
    log_info "Deployment ID: $DEPLOYMENT_ID"
    log_info "Log file: $DEPLOYMENT_LOG"
    
    # Parse command line arguments
    parse_arguments "$@"
    
    # Validate configuration
    validate_configuration
    
    # Show deployment summary and get confirmation
    show_deployment_summary
    
    # Execute deployment phases
    execute_pre_deployment_checks
    execute_monitoring_setup
    execute_v2_deployment
    monitor_traffic_migration
    execute_post_deployment_validation
    
    # Generate final report and cleanup
    generate_final_report
    cleanup_deployment
    
    log_success "🎉 Yemen Market Integration V2 deployment completed successfully!"
    log_info "Deployment ID: $DEPLOYMENT_ID"
    log_info "Duration: $(( $(date +%s) - $(stat -c %Y "$DEPLOYMENT_LOG" 2>/dev/null || echo $(date +%s)) )) seconds"
}

# Execute main function with all arguments
main "$@"