#!/bin/bash
# Production deployment script for Yemen Market Integration v2

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="yemen-market-v2"
CLUSTER_NAME="${CLUSTER_NAME:-yemen-market-prod}"
REGION="${AWS_REGION:-us-east-1}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check aws cli
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed"
        exit 1
    fi
    
    # Check helm
    if ! command -v helm &> /dev/null; then
        log_error "Helm is not installed"
        exit 1
    fi
    
    log_info "All requirements satisfied"
}

setup_cluster_access() {
    log_info "Setting up cluster access..."
    aws eks update-kubeconfig --name "$CLUSTER_NAME" --region "$REGION"
    
    # Verify connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to cluster"
        exit 1
    fi
    
    log_info "Connected to cluster: $CLUSTER_NAME"
}

create_namespace() {
    log_info "Creating namespace..."
    kubectl apply -f kubernetes/namespace.yaml
    
    # Label namespace
    kubectl label namespace "$NAMESPACE" \
        environment="$ENVIRONMENT" \
        managed-by="deploy-script" \
        --overwrite
}

setup_secrets() {
    log_info "Setting up secrets..."
    
    # Check if secrets already exist
    if kubectl get secret yemen-market-secrets -n "$NAMESPACE" &> /dev/null; then
        log_warn "Secrets already exist. Skipping creation."
        log_warn "To update secrets, run: ./scripts/update-secrets.sh"
    else
        log_info "Creating secrets from environment variables..."
        
        # Validate required environment variables
        required_vars=(
            "DB_PASSWORD"
            "JWT_SECRET"
            "API_KEY"
            "REDIS_PASSWORD"
        )
        
        for var in "${required_vars[@]}"; do
            if [ -z "${!var:-}" ]; then
                log_error "Required environment variable $var is not set"
                exit 1
            fi
        done
        
        # Create secret
        kubectl create secret generic yemen-market-secrets \
            --from-literal=DB_PASSWORD="$DB_PASSWORD" \
            --from-literal=JWT_SECRET="$JWT_SECRET" \
            --from-literal=API_KEY="$API_KEY" \
            --from-literal=REDIS_PASSWORD="$REDIS_PASSWORD" \
            --from-literal=DATABASE_URL="************************************************************/yemen_market_v2" \
            --from-literal=REDIS_URL="redis://:$REDIS_PASSWORD@redis-service:6379/0" \
            -n "$NAMESPACE"
    fi
}

deploy_infrastructure() {
    log_info "Deploying infrastructure components..."
    
    # Deploy in order
    local components=(
        "configmap"
        "postgres"
        "redis"
    )
    
    for component in "${components[@]}"; do
        log_info "Deploying $component..."
        kubectl apply -f "kubernetes/$component.yaml"
        
        # Wait for readiness
        case $component in
            "postgres")
                kubectl wait --for=condition=ready pod \
                    -l app=postgres -n "$NAMESPACE" \
                    --timeout=300s
                ;;
            "redis")
                kubectl wait --for=condition=ready pod \
                    -l app=redis -n "$NAMESPACE" \
                    --timeout=120s
                ;;
        esac
    done
}

run_database_migrations() {
    log_info "Running database migrations..."
    
    # Create migration job
    cat <<EOF | kubectl apply -f -
apiVersion: batch/v1
kind: Job
metadata:
  name: database-migration-$(date +%s)
  namespace: $NAMESPACE
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: migrate
        image: yemen-market-v2:latest
        command: ["python", "-m", "alembic", "upgrade", "head"]
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yemen-market-secrets
              key: DATABASE_URL
EOF
    
    # Wait for migration to complete
    log_info "Waiting for migrations to complete..."
    kubectl wait --for=condition=complete job \
        -l job-name=database-migration -n "$NAMESPACE" \
        --timeout=300s
}

deploy_application() {
    log_info "Deploying application components..."
    
    # Deploy API and workers
    kubectl apply -f kubernetes/api-deployment.yaml
    kubectl apply -f kubernetes/worker-deployment.yaml
    
    # Wait for rollout
    log_info "Waiting for API deployment..."
    kubectl rollout status deployment/yemen-market-api -n "$NAMESPACE" --timeout=600s
    
    log_info "Waiting for worker deployment..."
    kubectl rollout status deployment/yemen-market-worker -n "$NAMESPACE" --timeout=600s
}

deploy_ingress() {
    log_info "Deploying ingress..."
    
    # Deploy ingress controller if not exists
    if ! kubectl get namespace ingress-nginx &> /dev/null; then
        log_info "Installing NGINX ingress controller..."
        helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
        helm repo update
        helm install ingress-nginx ingress-nginx/ingress-nginx \
            --create-namespace \
            --namespace ingress-nginx \
            --set controller.service.annotations."service\.beta\.kubernetes\.io/aws-load-balancer-type"="nlb"
    fi
    
    # Deploy application ingress
    kubectl apply -f kubernetes/ingress.yaml
}

deploy_monitoring() {
    log_info "Deploying monitoring stack..."
    
    kubectl apply -f kubernetes/monitoring.yaml
    
    # Wait for monitoring components
    log_info "Waiting for Prometheus..."
    kubectl wait --for=condition=ready pod \
        -l app=prometheus -n "$NAMESPACE" \
        --timeout=300s
    
    log_info "Waiting for Grafana..."
    kubectl wait --for=condition=ready pod \
        -l app=grafana -n "$NAMESPACE" \
        --timeout=300s
}

setup_autoscaling() {
    log_info "Setting up autoscaling..."
    
    # Install metrics server if not exists
    if ! kubectl get deployment metrics-server -n kube-system &> /dev/null; then
        log_info "Installing metrics server..."
        kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
    fi
    
    # Apply HPA configurations (already in deployment files)
    log_info "Autoscaling configured in deployment manifests"
}

verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check pod status
    log_info "Checking pod status..."
    kubectl get pods -n "$NAMESPACE"
    
    # Check services
    log_info "Checking services..."
    kubectl get svc -n "$NAMESPACE"
    
    # Get ingress URL
    log_info "Getting ingress URL..."
    INGRESS_URL=$(kubectl get ingress yemen-market-ingress -n "$NAMESPACE" \
        -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
    
    if [ -n "$INGRESS_URL" ]; then
        log_info "Application URL: https://$INGRESS_URL"
        
        # Wait for DNS propagation
        log_info "Waiting for DNS propagation..."
        sleep 30
        
        # Health check
        log_info "Running health check..."
        if curl -f -k "https://$INGRESS_URL/health" &> /dev/null; then
            log_info "Health check passed!"
        else
            log_warn "Health check failed. The application might still be starting up."
        fi
    else
        log_warn "Ingress URL not yet available. Check back later."
    fi
}

post_deployment_tasks() {
    log_info "Running post-deployment tasks..."
    
    # Create sample data if in staging
    if [ "$ENVIRONMENT" = "staging" ]; then
        log_info "Loading sample data for staging environment..."
        kubectl exec -it deployment/yemen-market-api -n "$NAMESPACE" -- \
            python -m src.infrastructure.scripts.load_sample_data
    fi
    
    # Configure backups
    log_info "Configuring database backups..."
    kubectl apply -f kubernetes/backup-cronjob.yaml
    
    # Setup alerts
    log_info "Configuring alerts..."
    # This would configure alertmanager or similar
}

print_summary() {
    echo
    echo "======================================"
    echo "Deployment Summary"
    echo "======================================"
    echo "Cluster: $CLUSTER_NAME"
    echo "Namespace: $NAMESPACE"
    echo "Environment: $ENVIRONMENT"
    echo
    echo "Next steps:"
    echo "1. Update DNS records to point to the load balancer"
    echo "2. Configure SSL certificates (cert-manager recommended)"
    echo "3. Set up monitoring alerts"
    echo "4. Configure backup retention policies"
    echo "5. Review and adjust resource limits based on load"
    echo
    echo "Useful commands:"
    echo "- View logs: kubectl logs -f deployment/yemen-market-api -n $NAMESPACE"
    echo "- Scale API: kubectl scale deployment yemen-market-api --replicas=5 -n $NAMESPACE"
    echo "- Access database: kubectl exec -it statefulset/postgres -n $NAMESPACE -- psql -U yemen_market"
    echo "======================================"
}

# Main execution
main() {
    log_info "Starting Yemen Market Integration v2 deployment..."
    
    check_requirements
    setup_cluster_access
    create_namespace
    setup_secrets
    deploy_infrastructure
    run_database_migrations
    deploy_application
    deploy_ingress
    deploy_monitoring
    setup_autoscaling
    verify_deployment
    post_deployment_tasks
    print_summary
    
    log_info "Deployment completed successfully!"
}

# Run main function
main "$@"