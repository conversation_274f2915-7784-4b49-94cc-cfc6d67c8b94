#!/bin/bash
# Automated Rollback System for Yemen Market Integration V2
# Provides comprehensive rollback capabilities with data preservation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="yemen-market-v2"
V1_NAMESPACE="yemen-market"
CLUSTER_NAME="${CLUSTER_NAME:-yemen-market-prod}"
ROLLBACK_ID=$(date +%Y%m%d-%H%M%S)
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

# Rollback types
ROLLBACK_TYPE="${1:-traffic}"  # traffic, application, full

# Function definitions
log_info() {
    echo -e "${GREEN}[INFO $(date '+%H:%M:%S')]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN $(date '+%H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR $(date '+%H:%M:%S')]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP $(date '+%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS $(date '+%H:%M:%S')]${NC} $1"
}

notify_slack() {
    local message="$1"
    local color="${2:-#ff6600}"  # Default orange for rollback
    
    if [ -n "$SLACK_WEBHOOK" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK" || true
    fi
}

check_prerequisites() {
    log_step "Checking rollback prerequisites..."
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check V1 availability
    if ! kubectl get namespace "$V1_NAMESPACE" &> /dev/null; then
        log_error "V1 namespace $V1_NAMESPACE not found"
        exit 1
    fi
    
    # Check if V1 deployments exist
    if ! kubectl get deployment yemen-market-api -n "$V1_NAMESPACE" &> /dev/null; then
        log_error "V1 API deployment not found"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

backup_v2_data() {
    log_step "Creating backup of V2 data before rollback..."
    
    # Create backup job
    kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: pre-rollback-backup-$ROLLBACK_ID
  namespace: $NAMESPACE
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: backup
        image: postgres:15
        command:
        - /bin/bash
        - -c
        - |
          set -e
          echo "Creating V2 data backup..."
          pg_dump \$DATABASE_URL > /backup/v2-backup-$ROLLBACK_ID.sql
          echo "Backup completed successfully"
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yemen-market-secrets
              key: DATABASE_URL
        volumeMounts:
        - name: backup-storage
          mountPath: /backup
      volumes:
      - name: backup-storage
        persistentVolumeClaim:
          claimName: backup-pvc
EOF
    
    # Wait for backup completion
    if kubectl wait --for=condition=complete job "pre-rollback-backup-$ROLLBACK_ID" -n "$NAMESPACE" --timeout=600s; then
        log_success "V2 data backup completed"
    else
        log_error "V2 data backup failed"
        kubectl logs job/"pre-rollback-backup-$ROLLBACK_ID" -n "$NAMESPACE"
        return 1
    fi
}

rollback_traffic() {
    log_step "Rolling back traffic to V1..."
    
    # Get current traffic distribution
    local current_weight=$(kubectl get ingress yemen-market-canary-ingress -n "$NAMESPACE" \
        -o jsonpath='{.metadata.annotations.nginx\.ingress\.kubernetes\.io/canary-weight}' 2>/dev/null || echo "0")
    
    if [ "$current_weight" != "0" ]; then
        log_info "Current V2 traffic weight: $current_weight%"
        
        # Immediate traffic rollback to V1
        kubectl patch ingress yemen-market-canary-ingress -n "$NAMESPACE" -p \
            '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary-weight":"0"}}}'
        
        # Wait for traffic to stabilize
        sleep 30
        
        # Verify traffic is going to V1
        local v1_requests=$(kubectl exec -n "$V1_NAMESPACE" deployment/yemen-market-api -- \
            curl -s http://localhost:8000/metrics | grep http_requests_total | head -1 | awk '{print $2}' || echo "0")
        
        log_success "Traffic rolled back to V1. Current V1 requests: $v1_requests"
    else
        log_info "Traffic is already on V1"
    fi
}

rollback_application() {
    log_step "Rolling back V2 application to safe state..."
    
    # Scale down V2 deployments
    log_info "Scaling down V2 API deployment..."
    kubectl scale deployment yemen-market-api --replicas=0 -n "$NAMESPACE"
    
    log_info "Scaling down V2 worker deployment..."
    kubectl scale deployment yemen-market-worker --replicas=0 -n "$NAMESPACE"
    
    # Wait for scale down
    kubectl wait --for=jsonpath='{.status.replicas}'=0 deployment yemen-market-api -n "$NAMESPACE" --timeout=300s
    kubectl wait --for=jsonpath='{.status.replicas}'=0 deployment yemen-market-worker -n "$NAMESPACE" --timeout=300s
    
    log_success "V2 application scaled down"
}

restore_v1_full_capacity() {
    log_step "Restoring V1 to full production capacity..."
    
    # Check current V1 state
    local api_replicas=$(kubectl get deployment yemen-market-api -n "$V1_NAMESPACE" \
        -o jsonpath='{.status.replicas}' 2>/dev/null || echo "0")
    local worker_replicas=$(kubectl get deployment yemen-market-worker -n "$V1_NAMESPACE" \
        -o jsonpath='{.status.replicas}' 2>/dev/null || echo "0")
    
    log_info "Current V1 state - API: $api_replicas replicas, Workers: $worker_replicas replicas"
    
    # Scale up V1 to production capacity
    if [ "$api_replicas" -lt 3 ]; then
        log_info "Scaling up V1 API to production capacity..."
        kubectl scale deployment yemen-market-api --replicas=3 -n "$V1_NAMESPACE"
    fi
    
    if [ "$worker_replicas" -lt 5 ]; then
        log_info "Scaling up V1 workers to production capacity..."
        kubectl scale deployment yemen-market-worker --replicas=5 -n "$V1_NAMESPACE"
    fi
    
    # Wait for V1 to be ready
    kubectl rollout status deployment/yemen-market-api -n "$V1_NAMESPACE" --timeout=300s
    kubectl rollout status deployment/yemen-market-worker -n "$V1_NAMESPACE" --timeout=300s
    
    # Verify V1 health
    if kubectl exec -n "$V1_NAMESPACE" deployment/yemen-market-api -- \
        curl -f http://localhost:8000/health &> /dev/null; then
        log_success "V1 restored to full production capacity and is healthy"
    else
        log_error "V1 health check failed after scaling up"
        return 1
    fi
}

update_dns_and_routing() {
    log_step "Updating DNS and routing to point to V1..."
    
    # Update main ingress to point back to V1
    kubectl patch ingress yemen-market-ingress -n "$V1_NAMESPACE" -p \
        '{"spec":{"rules":[{"host":"api.yemen-market.prod.com","http":{"paths":[{"path":"/","pathType":"Prefix","backend":{"service":{"name":"yemen-market-api","port":{"number":8000}}}}]}}]}}'
    
    # Remove or disable canary ingress
    if kubectl get ingress yemen-market-canary-ingress -n "$NAMESPACE" &> /dev/null; then
        kubectl delete ingress yemen-market-canary-ingress -n "$NAMESPACE"
    fi
    
    # Wait for DNS propagation
    log_info "Waiting for DNS/routing changes to propagate..."
    sleep 60
    
    log_success "DNS and routing updated to V1"
}

preserve_v2_data() {
    log_step "Preserving V2 data for potential future migration..."
    
    # Create data export for future use
    kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: v2-data-export-$ROLLBACK_ID
  namespace: $NAMESPACE
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: export
        image: yemen-market-v2:latest
        command:
        - /bin/bash
        - -c
        - |
          set -e
          echo "Exporting V2 data for preservation..."
          python -m tools.migration.data_transformer export-v2 \
            --output-file /exports/v2-data-export-$ROLLBACK_ID.json
          echo "V2 data export completed"
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yemen-market-secrets
              key: DATABASE_URL
        volumeMounts:
        - name: export-storage
          mountPath: /exports
      volumes:
      - name: export-storage
        persistentVolumeClaim:
          claimName: backup-pvc
EOF
    
    # Don't wait for this - it can run in background
    log_info "V2 data export job started (running in background)"
}

verify_rollback() {
    log_step "Verifying rollback completion..."
    
    # Check V1 is responding
    local v1_health="UNKNOWN"
    if kubectl exec -n "$V1_NAMESPACE" deployment/yemen-market-api -- \
        curl -f http://localhost:8000/health &> /dev/null; then
        v1_health="HEALTHY"
    else
        v1_health="UNHEALTHY"
    fi
    
    # Check V2 is scaled down
    local v2_api_replicas=$(kubectl get deployment yemen-market-api -n "$NAMESPACE" \
        -o jsonpath='{.status.replicas}' 2>/dev/null || echo "0")
    local v2_worker_replicas=$(kubectl get deployment yemen-market-worker -n "$NAMESPACE" \
        -o jsonpath='{.status.replicas}' 2>/dev/null || echo "0")
    
    # Check traffic routing
    local traffic_to_v2=$(kubectl get ingress yemen-market-canary-ingress -n "$NAMESPACE" \
        -o jsonpath='{.metadata.annotations.nginx\.ingress\.kubernetes\.io/canary-weight}' 2>/dev/null || echo "0")
    
    log_info "Rollback verification results:"
    log_info "  - V1 Health: $v1_health"
    log_info "  - V2 API Replicas: $v2_api_replicas"
    log_info "  - V2 Worker Replicas: $v2_worker_replicas"
    log_info "  - Traffic to V2: $traffic_to_v2%"
    
    if [ "$v1_health" = "HEALTHY" ] && [ "$v2_api_replicas" = "0" ] && [ "$traffic_to_v2" = "0" ]; then
        log_success "Rollback verification passed"
        return 0
    else
        log_error "Rollback verification failed"
        return 1
    fi
}

mark_rollback_complete() {
    log_step "Marking rollback as complete..."
    
    # Update namespace labels
    kubectl label namespace "$NAMESPACE" rollback-status="completed" --overwrite
    kubectl label namespace "$NAMESPACE" rollback-id="$ROLLBACK_ID" --overwrite
    kubectl label namespace "$NAMESPACE" rollback-timestamp="$(date -u +%Y-%m-%dT%H:%M:%SZ)" --overwrite
    
    kubectl label namespace "$V1_NAMESPACE" deployment-status="active" --overwrite
    kubectl label namespace "$V1_NAMESPACE" restored-at="$(date -u +%Y-%m-%dT%H:%M:%SZ)" --overwrite
    
    log_success "Rollback marked as complete"
}

create_rollback_report() {
    log_step "Creating rollback report..."
    
    local report_file="/tmp/rollback-report-$ROLLBACK_ID.md"
    
    cat > "$report_file" <<EOF
# Yemen Market Integration V2 Rollback Report

**Rollback ID:** $ROLLBACK_ID
**Date:** $(date)
**Rollback Type:** $ROLLBACK_TYPE
**Triggered By:** ${USER:-system}

## Rollback Summary

### Actions Taken
- ✅ V2 data backup created
- ✅ Traffic rolled back to V1 (0% to V2)
- ✅ V2 application scaled down
- ✅ V1 restored to full production capacity
- ✅ DNS and routing updated
- ✅ V2 data preserved for future use
- ✅ Rollback verification completed

### Current State
- **Active Version:** V1
- **V1 Status:** Production (3 API replicas, 5 worker replicas)
- **V2 Status:** Scaled down (0 replicas)
- **Traffic Distribution:** 100% V1, 0% V2

### V1 Performance Post-Rollback
\`\`\`
$(kubectl top pods -n "$V1_NAMESPACE" 2>/dev/null || echo "Metrics not available")
\`\`\`

### Data Preservation
- V2 backup: pre-rollback-backup-$ROLLBACK_ID.sql
- V2 export: v2-data-export-$ROLLBACK_ID.json
- Backup location: backup-pvc

## Access Information
- **API Endpoint:** https://api.yemen-market.prod.com (now serving V1)
- **V1 Health:** $(kubectl exec -n "$V1_NAMESPACE" deployment/yemen-market-api -- curl -f http://localhost:8000/health &> /dev/null && echo "HEALTHY" || echo "NEEDS ATTENTION")

## Recovery Options

### Option 1: Re-attempt V2 Deployment
1. Fix the issues that caused the rollback
2. Run production-deployment.sh with fixes
3. Use preserved V2 data for faster migration

### Option 2: Investigate V2 Issues
1. Analyze V2 logs: \`kubectl logs -n $NAMESPACE deployment/yemen-market-api\`
2. Review V2 metrics and monitoring data
3. Check V2 data integrity with preserved backup

### Option 3: Cleanup V2 (if permanent rollback)
1. Wait 7 days for business confirmation
2. Run cleanup script to remove V2 infrastructure
3. Update documentation to reflect V1 as stable version

## Next Steps
1. ✅ Immediate: Monitor V1 performance and stability
2. 🔄 Within 2 hours: Conduct rollback post-mortem
3. 📊 Within 24 hours: Analyze root cause of V2 issues
4. 📋 Within 48 hours: Decide on next steps (retry V2 or stay on V1)

## Monitoring
- Continue monitoring V1 performance
- Set up alerts for any degradation
- Review logs for any issues related to the rollback

---
Generated at $(date) by rollback automation
EOF
    
    log_info "Rollback report saved to: $report_file"
    
    # Upload to S3 if configured
    if [ -n "${S3_BUCKET:-}" ]; then
        aws s3 cp "$report_file" "s3://$S3_BUCKET/rollbacks/" 2>/dev/null || true
    fi
}

execute_traffic_rollback() {
    log_info "Executing traffic-only rollback..."
    rollback_traffic
    verify_rollback
}

execute_application_rollback() {
    log_info "Executing application rollback..."
    rollback_traffic
    rollback_application
    restore_v1_full_capacity
    verify_rollback
}

execute_full_rollback() {
    log_info "Executing full rollback..."
    backup_v2_data
    rollback_traffic
    rollback_application
    restore_v1_full_capacity
    update_dns_and_routing
    preserve_v2_data
    verify_rollback
    mark_rollback_complete
}

main() {
    log_info "🔄 Starting Yemen Market Integration V2 Rollback"
    log_info "Rollback ID: $ROLLBACK_ID"
    log_info "Rollback Type: $ROLLBACK_TYPE"
    
    notify_slack "🔄 Starting V2 rollback - Type: $ROLLBACK_TYPE, ID: $ROLLBACK_ID" "#ff6600"
    
    check_prerequisites
    
    case "$ROLLBACK_TYPE" in
        "traffic")
            execute_traffic_rollback
            ;;
        "application")
            execute_application_rollback
            ;;
        "full")
            execute_full_rollback
            ;;
        *)
            log_error "Invalid rollback type: $ROLLBACK_TYPE"
            log_info "Valid types: traffic, application, full"
            exit 1
            ;;
    esac
    
    create_rollback_report
    
    log_success "🔄 Rollback completed successfully!"
    log_info "Rollback ID: $ROLLBACK_ID"
    log_info "V1 is now handling all production traffic"
    
    notify_slack "✅ V2 rollback completed successfully - ID: $ROLLBACK_ID. V1 is now active." "#36a64f"
}

# Handle script interruption
trap 'log_error "Rollback interrupted"; notify_slack "🚨 Rollback interrupted - ID: $ROLLBACK_ID" "#ff0000"' INT TERM

# Execute main function
main "$@"