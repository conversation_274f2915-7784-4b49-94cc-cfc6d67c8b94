#!/bin/bash
# Production Deployment Orchestrator for Yemen Market Integration V2
# Implements zero-downtime blue-green deployment with gradual traffic migration

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="yemen-market-v2"
V1_NAMESPACE="yemen-market"
CLUSTER_NAME="${CLUSTER_NAME:-yemen-market-prod}"
REGION="${AWS_REGION:-us-east-1}"
ENVIRONMENT="${ENVIRONMENT:-production}"
V2_IMAGE_TAG="${V2_IMAGE_TAG:-v2.1.0}"
DEPLOYMENT_ID=$(date +%Y%m%d-%H%M%S)
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

# Traffic migration configuration
TRAFFIC_STEPS=(10 25 50 75 90 100)
STEP_DURATION=300  # 5 minutes per step
ROLLBACK_THRESHOLD=0.05  # 5% error rate threshold

# Monitoring endpoints
PROMETHEUS_URL="${PROMETHEUS_URL:-http://prometheus.monitoring.svc.cluster.local:9090}"
GRAFANA_URL="${GRAFANA_URL:-http://grafana.monitoring.svc.cluster.local:3000}"

# Function definitions
log_info() {
    echo -e "${GREEN}[INFO $(date '+%H:%M:%S')]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN $(date '+%H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR $(date '+%H:%M:%S')]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP $(date '+%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS $(date '+%H:%M:%S')]${NC} $1"
}

notify_slack() {
    local message="$1"
    local color="${2:-#36a64f}"  # Default green
    
    if [ -n "$SLACK_WEBHOOK" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK" || true
    fi
}

check_prerequisites() {
    log_step "Checking deployment prerequisites..."
    
    # Check required tools
    local tools=("kubectl" "aws" "helm" "jq" "curl")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is required but not installed"
            exit 1
        fi
    done
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Verify V1 is running
    if ! kubectl get deployment -n "$V1_NAMESPACE" &> /dev/null; then
        log_error "V1 deployment not found in namespace $V1_NAMESPACE"
        exit 1
    fi
    
    # Check V2 image availability
    log_info "Verifying V2 image: yemen-market-v2:$V2_IMAGE_TAG"
    
    log_success "Prerequisites check passed"
    notify_slack "🚀 Starting V2 production deployment - ID: $DEPLOYMENT_ID"
}

setup_deployment_namespace() {
    log_step "Setting up V2 deployment namespace..."
    
    # Create namespace if it doesn't exist
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Label namespace
    kubectl label namespace "$NAMESPACE" \
        environment="$ENVIRONMENT" \
        deployment-id="$DEPLOYMENT_ID" \
        version="v2" \
        --overwrite
    
    log_success "Namespace $NAMESPACE ready"
}

deploy_v2_infrastructure() {
    log_step "Deploying V2 infrastructure components..."
    
    # Deploy secrets first
    if ! kubectl get secret yemen-market-secrets -n "$NAMESPACE" &> /dev/null; then
        log_info "Creating V2 secrets..."
        ./scripts/update-secrets.sh
    fi
    
    # Deploy configuration
    kubectl apply -f kubernetes/configmap.yaml
    
    # Deploy database (with backup of V1 data)
    log_info "Setting up V2 database with V1 data migration..."
    kubectl apply -f kubernetes/postgres.yaml
    
    # Wait for PostgreSQL
    kubectl wait --for=condition=ready pod -l app=postgres -n "$NAMESPACE" --timeout=300s
    
    # Run data migration from V1 to V2
    log_info "Running V1 to V2 data migration..."
    kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: v1-to-v2-migration-$DEPLOYMENT_ID
  namespace: $NAMESPACE
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: migrate
        image: yemen-market-v2:$V2_IMAGE_TAG
        command: ["python", "-m", "tools.migration.v1_to_v2_migrator"]
        env:
        - name: V1_DATABASE_URL
          value: "****************************************************************************************/yemen_market"
        - name: V2_DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yemen-market-secrets
              key: DATABASE_URL
        - name: MIGRATION_MODE
          value: "full"
EOF
    
    # Wait for migration
    kubectl wait --for=condition=complete job "v1-to-v2-migration-$DEPLOYMENT_ID" -n "$NAMESPACE" --timeout=600s
    
    # Deploy Redis with HA
    kubectl apply -f kubernetes/redis-ha.yaml
    kubectl wait --for=condition=ready pod -l app=redis -n "$NAMESPACE" --timeout=300s
    
    log_success "V2 infrastructure deployed"
}

deploy_v2_application() {
    log_step "Deploying V2 application in blue-green mode..."
    
    # Deploy blue environment (new V2)
    log_info "Deploying blue environment (V2)..."
    
    # Update deployment with blue environment label
    cat kubernetes/api-deployment.yaml | \
        sed "s/image: yemen-market-v2:latest/image: yemen-market-v2:$V2_IMAGE_TAG/" | \
        sed "/metadata:/a\\  labels:\n    environment: blue\n    deployment-id: $DEPLOYMENT_ID" | \
        kubectl apply -f -
    
    cat kubernetes/worker-deployment.yaml | \
        sed "s/image: yemen-market-v2:latest/image: yemen-market-v2:$V2_IMAGE_TAG/" | \
        sed "/metadata:/a\\  labels:\n    environment: blue\n    deployment-id: $DEPLOYMENT_ID" | \
        kubectl apply -f -
    
    # Wait for blue deployment
    kubectl rollout status deployment/yemen-market-api -n "$NAMESPACE" --timeout=600s
    kubectl rollout status deployment/yemen-market-worker -n "$NAMESPACE" --timeout=600s
    
    # Deploy monitoring for V2
    kubectl apply -f kubernetes/monitoring-enhanced.yaml
    
    log_success "V2 blue environment deployed"
}

create_traffic_routing() {
    log_step "Setting up traffic routing infrastructure..."
    
    # Create service selectors for blue-green
    kubectl apply -f - <<EOF
---
apiVersion: v1
kind: Service
metadata:
  name: yemen-market-api-blue
  namespace: $NAMESPACE
  labels:
    environment: blue
spec:
  selector:
    app: yemen-market-api
    environment: blue
  ports:
    - port: 8000
      targetPort: 8000
      name: http
---
apiVersion: v1
kind: Service
metadata:
  name: yemen-market-api-green
  namespace: $V1_NAMESPACE
  labels:
    environment: green
spec:
  selector:
    app: yemen-market-api
  ports:
    - port: 8000
      targetPort: 8000
      name: http
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: yemen-market-canary-ingress
  namespace: $NAMESPACE
  annotations:
    nginx.ingress.kubernetes.io/canary: "true"
    nginx.ingress.kubernetes.io/canary-weight: "0"
    nginx.ingress.kubernetes.io/canary-by-header: "X-Canary"
    nginx.ingress.kubernetes.io/canary-by-header-value: "v2"
spec:
  ingressClassName: nginx
  rules:
  - host: api.yemen-market.prod.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: yemen-market-api-blue
            port:
              number: 8000
EOF
    
    log_success "Traffic routing configured"
}

run_smoke_tests() {
    log_step "Running smoke tests on blue environment..."
    
    # Get blue service endpoint
    local blue_endpoint="http://yemen-market-api-blue.$NAMESPACE.svc.cluster.local:8000"
    
    # Create smoke test job
    kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: smoke-test-$DEPLOYMENT_ID
  namespace: $NAMESPACE
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: smoke-test
        image: curlimages/curl:latest
        command:
        - /bin/sh
        - -c
        - |
          set -e
          echo "Testing health endpoint..."
          curl -f $blue_endpoint/health
          
          echo "Testing ready endpoint..."
          curl -f $blue_endpoint/ready
          
          echo "Testing metrics endpoint..."
          curl -f $blue_endpoint/metrics
          
          echo "Testing API key authentication..."
          curl -f -H "X-API-Key: test-key" $blue_endpoint/api/v2/markets
          
          echo "All smoke tests passed!"
EOF
    
    # Wait for smoke tests
    if kubectl wait --for=condition=complete job "smoke-test-$DEPLOYMENT_ID" -n "$NAMESPACE" --timeout=180s; then
        log_success "Smoke tests passed"
    else
        log_error "Smoke tests failed"
        kubectl logs job/"smoke-test-$DEPLOYMENT_ID" -n "$NAMESPACE"
        exit 1
    fi
}

get_metrics() {
    local service="$1"
    local namespace="$2"
    
    # Query Prometheus for metrics
    local error_rate=$(curl -s "$PROMETHEUS_URL/api/v1/query" \
        --data-urlencode "query=rate(http_requests_total{job=\"$service\",code=~\"5..\"}[5m]) / rate(http_requests_total{job=\"$service\"}[5m])" | \
        jq -r '.data.result[0].value[1] // "0"')
    
    local latency_p95=$(curl -s "$PROMETHEUS_URL/api/v1/query" \
        --data-urlencode "query=histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"$service\"}[5m]))" | \
        jq -r '.data.result[0].value[1] // "0"')
    
    local throughput=$(curl -s "$PROMETHEUS_URL/api/v1/query" \
        --data-urlencode "query=rate(http_requests_total{job=\"$service\"}[5m])" | \
        jq -r '.data.result[0].value[1] // "0"')
    
    echo "$error_rate,$latency_p95,$throughput"
}

monitor_deployment() {
    local blue_metrics green_metrics
    local blue_error_rate blue_latency blue_throughput
    local green_error_rate green_latency green_throughput
    
    blue_metrics=$(get_metrics "yemen-market-api-blue" "$NAMESPACE")
    green_metrics=$(get_metrics "yemen-market-api-green" "$V1_NAMESPACE")
    
    IFS=',' read -r blue_error_rate blue_latency blue_throughput <<< "$blue_metrics"
    IFS=',' read -r green_error_rate green_latency green_throughput <<< "$green_metrics"
    
    log_info "Blue (V2) - Error Rate: ${blue_error_rate}, Latency P95: ${blue_latency}s, Throughput: ${blue_throughput} RPS"
    log_info "Green (V1) - Error Rate: ${green_error_rate}, Latency P95: ${green_latency}s, Throughput: ${green_throughput} RPS"
    
    # Check if blue environment is healthy
    if (( $(echo "$blue_error_rate > $ROLLBACK_THRESHOLD" | bc -l) )); then
        log_error "Blue environment error rate ($blue_error_rate) exceeds threshold ($ROLLBACK_THRESHOLD)"
        return 1
    fi
    
    if (( $(echo "$blue_latency > 2.0" | bc -l) )); then
        log_error "Blue environment latency ($blue_latency) too high"
        return 1
    fi
    
    return 0
}

migrate_traffic() {
    log_step "Starting gradual traffic migration..."
    
    for weight in "${TRAFFIC_STEPS[@]}"; do
        log_info "Migrating $weight% traffic to V2..."
        
        # Update canary weight
        kubectl patch ingress yemen-market-canary-ingress -n "$NAMESPACE" -p \
            "{\"metadata\":{\"annotations\":{\"nginx.ingress.kubernetes.io/canary-weight\":\"$weight\"}}}"
        
        # Wait for configuration to propagate
        sleep 30
        
        # Monitor for step duration
        local monitoring_start=$(date +%s)
        local monitoring_end=$((monitoring_start + STEP_DURATION))
        
        while [ $(date +%s) -lt $monitoring_end ]; do
            if ! monitor_deployment; then
                log_error "Health check failed during $weight% traffic migration"
                trigger_rollback "Health check failure during traffic migration"
                return 1
            fi
            
            sleep 30
        done
        
        log_success "$weight% traffic migrated successfully"
        notify_slack "✅ $weight% traffic migrated to V2 successfully"
        
        # Don't wait after 100%
        if [ "$weight" -eq 100 ]; then
            break
        fi
    done
    
    log_success "Traffic migration completed successfully"
}

trigger_rollback() {
    local reason="$1"
    log_error "TRIGGERING ROLLBACK: $reason"
    
    notify_slack "🚨 ROLLBACK TRIGGERED: $reason - Deployment ID: $DEPLOYMENT_ID" "#ff0000"
    
    # Immediate traffic rollback
    log_info "Rolling back traffic to V1..."
    kubectl patch ingress yemen-market-canary-ingress -n "$NAMESPACE" -p \
        '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary-weight":"0"}}}'
    
    # Scale down V2
    log_info "Scaling down V2 deployment..."
    kubectl scale deployment yemen-market-api --replicas=0 -n "$NAMESPACE"
    kubectl scale deployment yemen-market-worker --replicas=0 -n "$NAMESPACE"
    
    # Mark deployment as failed
    kubectl label namespace "$NAMESPACE" deployment-status="failed" --overwrite
    
    log_error "Rollback completed. V1 is handling all traffic."
    exit 1
}

finalize_deployment() {
    log_step "Finalizing V2 deployment..."
    
    # Update main ingress to point to V2
    kubectl patch ingress yemen-market-ingress -n "$V1_NAMESPACE" -p \
        "{\"spec\":{\"rules\":[{\"host\":\"api.yemen-market.prod.com\",\"http\":{\"paths\":[{\"path\":\"/\",\"pathType\":\"Prefix\",\"backend\":{\"service\":{\"name\":\"yemen-market-api-blue\",\"port\":{\"number\":8000}}}}]}}]}}"
    
    # Remove canary ingress
    kubectl delete ingress yemen-market-canary-ingress -n "$NAMESPACE"
    
    # Scale down V1 gradually
    log_info "Scaling down V1 deployment..."
    kubectl scale deployment yemen-market-api --replicas=1 -n "$V1_NAMESPACE"
    sleep 60
    kubectl scale deployment yemen-market-api --replicas=0 -n "$V1_NAMESPACE"
    kubectl scale deployment yemen-market-worker --replicas=0 -n "$V1_NAMESPACE"
    
    # Update labels
    kubectl label namespace "$NAMESPACE" deployment-status="completed" --overwrite
    kubectl label namespace "$V1_NAMESPACE" deployment-status="decommissioned" --overwrite
    
    log_success "V2 deployment finalized"
    notify_slack "🎉 V2 deployment completed successfully! - Deployment ID: $DEPLOYMENT_ID"
}

cleanup_old_deployments() {
    log_step "Cleaning up old deployments..."
    
    # Keep V1 namespace for rollback capability (mark for cleanup in 7 days)
    kubectl label namespace "$V1_NAMESPACE" cleanup-after="$(date -d '+7 days' +%Y-%m-%d)" --overwrite
    
    # Clean up old migration jobs (keep last 3)
    kubectl get jobs -n "$NAMESPACE" -o name | grep migration | head -n -3 | xargs -r kubectl delete -n "$NAMESPACE"
    
    log_success "Cleanup completed"
}

post_deployment_validation() {
    log_step "Running post-deployment validation..."
    
    # Run comprehensive validation suite
    kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: post-deployment-validation-$DEPLOYMENT_ID
  namespace: $NAMESPACE
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: validate
        image: yemen-market-v2:$V2_IMAGE_TAG
        command: ["python", "-m", "scripts.run_validation_suite"]
        env:
        - name: VALIDATION_TYPE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yemen-market-secrets
              key: DATABASE_URL
EOF
    
    # Wait for validation
    if kubectl wait --for=condition=complete job "post-deployment-validation-$DEPLOYMENT_ID" -n "$NAMESPACE" --timeout=600s; then
        log_success "Post-deployment validation passed"
    else
        log_warn "Post-deployment validation failed - check logs for details"
        kubectl logs job/"post-deployment-validation-$DEPLOYMENT_ID" -n "$NAMESPACE"
    fi
}

generate_deployment_report() {
    log_step "Generating deployment report..."
    
    local report_file="/tmp/deployment-report-$DEPLOYMENT_ID.md"
    
    cat > "$report_file" <<EOF
# Yemen Market Integration V2 Production Deployment Report

**Deployment ID:** $DEPLOYMENT_ID
**Date:** $(date)
**Environment:** $ENVIRONMENT
**V2 Image Tag:** $V2_IMAGE_TAG

## Deployment Summary

- ✅ Prerequisites check passed
- ✅ V2 infrastructure deployed
- ✅ V2 application deployed
- ✅ Traffic routing configured
- ✅ Smoke tests passed
- ✅ Gradual traffic migration completed
- ✅ V2 deployment finalized
- ✅ Post-deployment validation completed

## Final Metrics

### V2 Performance
$(kubectl exec -n "$NAMESPACE" deployment/yemen-market-api -- curl -s http://localhost:8000/metrics | grep -E "(http_requests_total|http_request_duration)" | head -10)

### Resource Usage
\`\`\`
$(kubectl top pods -n "$NAMESPACE")
\`\`\`

## Access Information

- **API Endpoint:** https://api.yemen-market.prod.com
- **Grafana Dashboard:** $GRAFANA_URL/d/yemen-market-v2
- **Prometheus:** $PROMETHEUS_URL

## Rollback Information

- **V1 Namespace:** $V1_NAMESPACE (preserved for 7 days)
- **Rollback Command:** \`kubectl apply -f rollback-$DEPLOYMENT_ID.yaml\`

## Next Steps

1. Monitor application performance for 24 hours
2. Update DNS TTL back to normal values
3. Schedule V1 infrastructure cleanup (after 7 days)
4. Update documentation with V2 specifics
5. Conduct post-deployment review meeting

---
Generated at $(date) by deployment automation
EOF
    
    log_info "Deployment report saved to: $report_file"
    
    # Upload to S3 if configured
    if [ -n "${S3_BUCKET:-}" ]; then
        aws s3 cp "$report_file" "s3://$S3_BUCKET/deployments/"
        log_info "Report uploaded to S3: s3://$S3_BUCKET/deployments/"
    fi
}

main() {
    log_info "🚀 Starting Yemen Market Integration V2 Production Deployment"
    log_info "Deployment ID: $DEPLOYMENT_ID"
    
    # Deployment phases
    check_prerequisites
    setup_deployment_namespace
    deploy_v2_infrastructure
    deploy_v2_application
    create_traffic_routing
    run_smoke_tests
    migrate_traffic
    finalize_deployment
    cleanup_old_deployments
    post_deployment_validation
    generate_deployment_report
    
    log_success "🎉 V2 Production Deployment Completed Successfully!"
    log_info "Deployment ID: $DEPLOYMENT_ID"
    log_info "Monitor the deployment at: $GRAFANA_URL/d/yemen-market-v2"
    
    notify_slack "🎉 V2 Production Deployment SUCCESSFUL! - ID: $DEPLOYMENT_ID"
}

# Trap for cleanup on script exit
trap 'log_error "Deployment interrupted"; notify_slack "🚨 Deployment interrupted - ID: $DEPLOYMENT_ID" "#ff0000"' INT TERM

# Execute main function
main "$@"