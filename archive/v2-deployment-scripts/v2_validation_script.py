#!/usr/bin/env python3
"""
V2 System Validation Script

This script runs a comprehensive validation of the V2 system by:
1. Processing real data through V2 processors
2. Running a complete three-tier analysis 
3. Comparing results to expected V1 findings
4. Verifying the -35% conflict effect can be reproduced
5. Testing all major V2 components

Usage:
    python scripts/v2_validation_script.py [--test-data-only] [--save-results]
"""

import asyncio
import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional
import json
import pandas as pd

# Add src to path for imports  
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

# Set PYTHONPATH to help with relative imports
import os
os.environ['PYTHONPATH'] = str(project_root / "src")

try:
    from src.application.services.three_tier_analysis_service import ThreeTierAnalysisService
    from src.application.commands.run_three_tier_analysis import RunThreeTierAnalysisCommand
    from src.core.domain.market.repositories import MarketRepository, PriceRepository
    from src.infrastructure.processors.wfp_processor import WFPProcessor
    from src.infrastructure.processors.acled_processor import ACLEDProcessor
    from src.infrastructure.persistence.repositories import (
        InMemoryMarketRepository, 
        InMemoryPriceRepository
    )
    from src.infrastructure.logging import Logger
    from src.application.services import (
        AnalysisOrchestrator,
        ModelEstimatorService,
        DataPreparationService
    )
    from src.shared.container import Container
except ImportError as e:
    print(f"Import error: {e}")
    print("Falling back to simpler validation without full V2 system")
    
    # Create a minimal mock for testing basic components
    class MockThreeTierAnalysisService:
        def __init__(self, *args, **kwargs):
            pass
            
        async def run_analysis(self, *args, **kwargs):
            return {
                'analysis_type': 'three_tier',
                'version': '2.0',
                'timestamp': datetime.now().isoformat(),
                'tiers': {
                    'tier1': {'result': {'mock': True}},
                    'tier2': {'wheat': {'result': {'mock': True}}},
                    'tier3': {'cross_validation': {'result': {'mock': True}}}
                },
                'summary': {'mock_validation': True}
            }
    
    ThreeTierAnalysisService = MockThreeTierAnalysisService
    
    class MockContainer:
        def resolve(self, service_name):
            return f"Mock {service_name}"
    
    Container = MockContainer


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('/tmp/v2_validation.log')
    ]
)

try:
    logger = Logger(__name__)
except:
    logger = logging.getLogger(__name__)


class V2ValidationRunner:
    """Comprehensive V2 system validation."""
    
    def __init__(self, use_test_data: bool = False):
        """Initialize validation runner.
        
        Args:
            use_test_data: If True, use synthetic test data instead of real files
        """
        self.use_test_data = use_test_data
        self.container = Container()
        self.results: Dict[str, Any] = {}
        
        # Initialize repositories
        try:
            self.market_repo = InMemoryMarketRepository()
            self.price_repo = InMemoryPriceRepository()
        except:
            self.market_repo = None
            self.price_repo = None
        
        # Initialize services  
        try:
            self.data_prep_service = DataPreparationService()
            self.estimator_service = ModelEstimatorService(container=self.container)
            self.orchestrator = AnalysisOrchestrator()
        except:
            self.data_prep_service = None
            self.estimator_service = None
            self.orchestrator = None
        
        # Initialize three-tier service
        try:
            self.three_tier_service = ThreeTierAnalysisService(
                market_repository=self.market_repo,
                price_repository=self.price_repo,
                estimator_service=self.estimator_service,
                orchestrator=self.orchestrator
            )
        except:
            self.three_tier_service = ThreeTierAnalysisService()
        
        # Data paths
        self.data_dir = Path(__file__).parent.parent / "data"
        self.wfp_data_path = self.data_dir / "raw" / "wfp" 
        self.acled_data_path = self.data_dir / "raw" / "acled"
        
    async def run_validation(self) -> Dict[str, Any]:
        """Run complete V2 validation."""
        logger.info("=== Starting V2 System Validation ===")
        
        try:
            # Step 1: Test data processing
            logger.info("Step 1: Testing data processors...")
            await self._test_data_processors()
            
            # Step 2: Load data into repositories
            logger.info("Step 2: Loading data into repositories...")
            await self._load_test_data()
            
            # Step 3: Test three-tier analysis
            logger.info("Step 3: Running three-tier analysis...")
            await self._test_three_tier_analysis()
            
            # Step 4: Validate critical findings
            logger.info("Step 4: Validating critical findings...")
            self._validate_conflict_effect()
            
            # Step 5: Test component integration
            logger.info("Step 5: Testing component integration...")
            await self._test_component_integration()
            
            # Step 6: Generate validation report
            logger.info("Step 6: Generating validation report...")
            self._generate_validation_report()
            
            logger.info("=== V2 Validation Complete ===")
            return self.results
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            self.results['validation_status'] = 'FAILED'
            self.results['error'] = str(e)
            raise
            
    async def _test_data_processors(self):
        """Test that data processors work with real data."""
        processor_results = {}
        
        # Test WFP processor
        try:
            wfp_processor = WFPProcessor()
            
            if self.use_test_data:
                # Create synthetic WFP data
                test_data = self._create_test_wfp_data()
                markets, prices, exchange_rates = await wfp_processor.process(test_data)
            else:
                # Try to use real WFP data
                wfp_files = list(self.wfp_data_path.glob("*.csv"))
                if wfp_files:
                    markets, prices, exchange_rates = await wfp_processor.process(
                        str(wfp_files[0])
                    )
                else:
                    logger.warning("No WFP data files found, using test data")
                    test_data = self._create_test_wfp_data()
                    markets, prices, exchange_rates = await wfp_processor.process(test_data)
            
            processor_results['wfp'] = {
                'status': 'SUCCESS',
                'markets_count': len(markets),
                'prices_count': len(prices),
                'exchange_rates_count': len(exchange_rates)
            }
            
        except Exception as e:
            processor_results['wfp'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            
        # Test ACLED processor
        try:
            acled_processor = ACLEDProcessor()
            
            if self.use_test_data:
                # Create synthetic conflict data
                test_conflict_data = self._create_test_conflict_data()
                conflict_events = await acled_processor.process(test_conflict_data)
            else:
                # Try to use real ACLED data
                acled_files = list(self.acled_data_path.glob("*.csv"))
                if acled_files:
                    conflict_events = await acled_processor.process(str(acled_files[0]))
                else:
                    logger.warning("No ACLED data files found, using test data")
                    test_conflict_data = self._create_test_conflict_data()
                    conflict_events = await acled_processor.process(test_conflict_data)
            
            processor_results['acled'] = {
                'status': 'SUCCESS',
                'events_count': len(conflict_events)
            }
            
        except Exception as e:
            processor_results['acled'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            
        self.results['data_processors'] = processor_results
        
    async def _load_test_data(self):
        """Load test data into repositories."""
        # Create and store sample markets
        from core.domain.market.entities import Market
        from core.domain.market.value_objects import MarketId, Coordinates, MarketType
        
        markets = []
        for i in range(10):
            market = Market(
                market_id=MarketId(value=f"market_{i}"),
                name=f"Test Market {i}",
                governorate=f"Governorate {i % 3}",
                district=f"District {i % 5}",
                coordinates=Coordinates(
                    latitude=15.0 + i * 0.1,
                    longitude=45.0 + i * 0.1
                ),
                market_type=MarketType.RETAIL,
                active_since=datetime.now() - timedelta(days=365)
            )
            markets.append(market)
            await self.market_repo.save(market)
            
        # Create and store sample price observations
        from core.domain.market.entities import PriceObservation
        from core.domain.market.value_objects import Commodity, Price, Currency
        from decimal import Decimal
        
        commodities = [
            "wheat_flour", "rice_imported", "sugar", "fuel_diesel", "beans_kidney_red"
        ]
        
        observations = []
        base_date = datetime.now() - timedelta(days=180)
        
        for market in markets:
            for commodity in commodities:
                for day in range(0, 180, 7):  # Weekly observations
                    date = base_date + timedelta(days=day)
                    
                    # Create price with some variation
                    base_price = 100 + hash(commodity) % 100
                    variation = (day / 180.0) * 20  # Trend over time
                    noise = (hash(f"{market.market_id.value}_{commodity}_{day}") % 20) - 10
                    
                    price_amount = base_price + variation + noise
                    
                    observation = PriceObservation(
                        market_id=market.market_id,
                        commodity=Commodity(
                            code=commodity,
                            name=commodity.replace("_", " ").title(),
                            category="test",
                            standard_unit="kg"
                        ),
                        price=Price(
                            amount=Decimal(str(price_amount)),
                            currency=Currency.YER,
                            unit="kg"
                        ),
                        observed_date=date,
                        source="test_data",
                        quality="high"
                    )
                    
                    observations.append(observation)
                    await self.price_repo.save(observation)
                    
        self.results['test_data_loaded'] = {
            'markets': len(markets),
            'price_observations': len(observations)
        }
        
    async def _test_three_tier_analysis(self):
        """Test the complete three-tier analysis."""
        try:
            # Create analysis command
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
            
            # Configure analysis
            config = {
                'tier1': {
                    'model': 'pooled_panel',
                    'log_transform': True,
                    'interactions': False
                },
                'tier2': {
                    'model': 'threshold_vecm',
                    'min_obs': 50,
                    'threshold_variable': 'conflict_intensity'
                },
                'tier3': {
                    'validation_methods': ['cross_validation', 'factor_analysis'],
                    'factor_analysis': True,
                    'pca_analysis': True
                },
                'run_diagnostics': True,
                'apply_corrections': True
            }
            
            # Run analysis
            results = await self.three_tier_service.run_analysis(
                start_date=start_date,
                end_date=end_date,
                config=config
            )
            
            self.results['three_tier_analysis'] = {
                'status': 'SUCCESS',
                'analysis_type': results.get('analysis_type'),
                'version': results.get('version'),
                'tiers_completed': list(results.get('tiers', {}).keys()),
                'confidence_scores': results.get('confidence_scores', {}),
                'summary': results.get('summary', {})
            }
            
            # Store full results for validation
            self.results['full_analysis_results'] = results
            
        except Exception as e:
            self.results['three_tier_analysis'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            
    def _validate_conflict_effect(self):
        """Validate that conflict effect is detected correctly."""
        validation_results = {
            'conflict_effect_detected': False,
            'effect_magnitude': None,
            'effect_significance': None,
            'matches_v1_findings': False
        }
        
        try:
            full_results = self.results.get('full_analysis_results', {})
            tier1_results = full_results.get('tiers', {}).get('tier1', {})
            
            if 'result' in tier1_results:
                result = tier1_results['result']
                
                # Check if result has coefficients (could be in different formats)
                coefficients = None
                p_values = None
                
                if hasattr(result, 'coefficients'):
                    coefficients = result.coefficients
                    p_values = getattr(result, 'p_values', {})
                elif hasattr(result, 'params'):
                    coefficients = dict(result.params) if hasattr(result.params, 'to_dict') else result.params
                    p_values = dict(result.pvalues) if hasattr(result, 'pvalues') else {}
                
                if coefficients:
                    # Look for conflict effect
                    conflict_vars = [
                        'conflict_intensity', 'conflict', 'conflict_events',
                        'log_conflict', 'conflict_dummy'
                    ]
                    
                    for var in conflict_vars:
                        if var in coefficients:
                            coef = coefficients[var]
                            p_val = p_values.get(var, 1.0)
                            
                            validation_results['conflict_effect_detected'] = True
                            validation_results['effect_magnitude'] = float(coef)
                            validation_results['effect_significance'] = float(p_val)
                            
                            # Check if matches V1 findings (35% effect, significant)
                            if 0.25 <= abs(coef) <= 0.45 and p_val < 0.05:
                                validation_results['matches_v1_findings'] = True
                            
                            break
            
        except Exception as e:
            validation_results['validation_error'] = str(e)
            
        self.results['conflict_validation'] = validation_results
        
    async def _test_component_integration(self):
        """Test integration between V2 components."""
        integration_tests = {}
        
        # Test container dependency injection
        try:
            # Try to resolve services from container
            services = [
                'data_preparation_service',
                'model_estimator_service', 
                'analysis_orchestrator'
            ]
            
            resolved_services = {}
            for service_name in services:
                try:
                    service = self.container.resolve(service_name)
                    resolved_services[service_name] = type(service).__name__
                except Exception as e:
                    resolved_services[service_name] = f"Failed: {e}"
            
            integration_tests['container_resolution'] = {
                'status': 'SUCCESS',
                'services': resolved_services
            }
            
        except Exception as e:
            integration_tests['container_resolution'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            
        # Test estimator service
        try:
            from core.models.panel.pooled_panel import PooledPanelModel
            from core.models.interfaces import ModelSpecification
            
            # Create a simple test model
            spec = ModelSpecification(
                model_type="pooled_panel",
                dependent_variable="log_price",
                independent_variables=["conflict_intensity"],
                parameters={'entity_var': 'market_id', 'time_var': 'date'}
            )
            
            model = PooledPanelModel(spec)
            
            # Test that estimator can handle the model
            # We won't actually run estimation due to complexity, just test instantiation
            integration_tests['model_estimator'] = {
                'status': 'SUCCESS',
                'model_created': type(model).__name__,
                'specification_valid': bool(model.specification)
            }
            
        except Exception as e:
            integration_tests['model_estimator'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            
        self.results['component_integration'] = integration_tests
        
    def _generate_validation_report(self):
        """Generate comprehensive validation report."""
        report = {
            'validation_timestamp': datetime.now().isoformat(),
            'system_version': '2.0',
            'overall_status': 'UNKNOWN',
            'component_status': {},
            'critical_findings': {},
            'recommendations': []
        }
        
        # Check component statuses
        components = [
            'data_processors',
            'three_tier_analysis', 
            'conflict_validation',
            'component_integration'
        ]
        
        success_count = 0
        for component in components:
            if component in self.results:
                if isinstance(self.results[component], dict):
                    if self.results[component].get('status') == 'SUCCESS':
                        report['component_status'][component] = 'PASS'
                        success_count += 1
                    else:
                        report['component_status'][component] = 'FAIL'
                else:
                    # For nested results, check if they have data
                    if self.results[component]:
                        report['component_status'][component] = 'PASS'
                        success_count += 1
                    else:
                        report['component_status'][component] = 'FAIL'
            else:
                report['component_status'][component] = 'NOT_TESTED'
        
        # Determine overall status
        if success_count == len(components):
            report['overall_status'] = 'SUCCESS'
        elif success_count >= len(components) * 0.7:
            report['overall_status'] = 'PARTIAL_SUCCESS'
        else:
            report['overall_status'] = 'FAILURE'
        
        # Extract critical findings
        conflict_validation = self.results.get('conflict_validation', {})
        if conflict_validation.get('conflict_effect_detected'):
            report['critical_findings']['conflict_effect'] = {
                'detected': True,
                'magnitude': conflict_validation.get('effect_magnitude'),
                'significance': conflict_validation.get('effect_significance'),
                'matches_v1': conflict_validation.get('matches_v1_findings')
            }
        
        # Generate recommendations
        recommendations = []
        
        if report['overall_status'] == 'SUCCESS':
            recommendations.append("V2 system is ready for research use")
            recommendations.append("Consider enabling parallel processing for large datasets")
        elif report['overall_status'] == 'PARTIAL_SUCCESS':
            recommendations.append("Most V2 components are functional")
            recommendations.append("Review failed components before production use")
        else:
            recommendations.append("V2 system needs significant fixes before use")
            recommendations.append("Focus on fixing data processing and model estimation")
        
        if not conflict_validation.get('matches_v1_findings'):
            recommendations.append("Investigate conflict effect discrepancy with V1")
            recommendations.append("Review model specification and data quality")
        
        report['recommendations'] = recommendations
        self.results['validation_report'] = report
        
    def _create_test_wfp_data(self) -> pd.DataFrame:
        """Create synthetic WFP-style data for testing."""
        markets = [
            "Sana'a Central Market", "Aden Main Market", "Ta'iz Central Market",
            "Al Hodeidah Port Market", "Sa'dah Local Market"
        ]
        
        commodities = [
            "Wheat Flour", "Rice (Imported)", "Sugar", "Fuel (Diesel)", "Beans (Kidney Red)"
        ]
        
        governorates = ["Sana'a", "Aden", "Ta'iz", "Al Hodeidah", "Sa'dah"]
        
        data = []
        base_date = datetime.now() - timedelta(days=90)
        
        for i, market in enumerate(markets):
            for commodity in commodities:
                for day in range(0, 90, 7):  # Weekly data
                    date = base_date + timedelta(days=day)
                    
                    # Generate realistic price with trends
                    base_price = 100 + hash(commodity) % 100
                    trend = day * 0.5  # Slight upward trend
                    noise = (hash(f"{market}_{commodity}_{day}") % 20) - 10
                    
                    data.append({
                        'date': date,
                        'market': market,
                        'commodity': commodity,
                        'price': base_price + trend + noise,
                        'currency': 'YER',
                        'unit': 'kg',
                        'governorate': governorates[i],
                        'district': f"District {i}",
                        'latitude': 15.0 + i * 0.5,
                        'longitude': 45.0 + i * 0.5
                    })
        
        return pd.DataFrame(data)
        
    def _create_test_conflict_data(self) -> pd.DataFrame:
        """Create synthetic ACLED-style conflict data."""
        governorates = ["Sana'a", "Aden", "Ta'iz", "Al Hodeidah", "Sa'dah"]
        
        data = []
        base_date = datetime.now() - timedelta(days=90)
        
        for day in range(0, 90):
            date = base_date + timedelta(days=day)
            
            # Generate some conflict events (not every day)
            if hash(str(date)) % 3 == 0:  # About 1/3 of days have events
                for gov in governorates:
                    if hash(f"{gov}_{date}") % 2 == 0:  # About half of governorates
                        fatalities = max(0, hash(f"fatalities_{gov}_{date}") % 10)
                        
                        data.append({
                            'date': date,
                            'governorate': gov,
                            'event_type': 'Violence against civilians',
                            'fatalities': fatalities,
                            'latitude': 15.0 + hash(gov) % 5,
                            'longitude': 45.0 + hash(gov) % 5
                        })
        
        return pd.DataFrame(data)


async def main():
    """Main validation function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='V2 System Validation')
    parser.add_argument('--test-data-only', action='store_true',
                       help='Use only synthetic test data')
    parser.add_argument('--save-results', action='store_true',
                       help='Save results to file')
    
    args = parser.parse_args()
    
    # Run validation
    runner = V2ValidationRunner(use_test_data=args.test_data_only)
    
    try:
        results = await runner.run_validation()
        
        # Print summary
        print("\n" + "="*60)
        print("V2 SYSTEM VALIDATION SUMMARY")
        print("="*60)
        
        report = results.get('validation_report', {})
        print(f"Overall Status: {report.get('overall_status', 'UNKNOWN')}")
        print(f"Timestamp: {report.get('validation_timestamp', 'Unknown')}")
        
        print("\nComponent Status:")
        for component, status in report.get('component_status', {}).items():
            print(f"  {component}: {status}")
        
        print(f"\nCritical Findings:")
        critical = report.get('critical_findings', {})
        conflict_effect = critical.get('conflict_effect', {})
        if conflict_effect.get('detected'):
            print(f"  Conflict effect detected: {conflict_effect.get('magnitude', 'N/A'):.3f}")
            print(f"  Statistical significance: {conflict_effect.get('significance', 'N/A'):.3f}")
            print(f"  Matches V1 findings: {conflict_effect.get('matches_v1', False)}")
        else:
            print("  No conflict effect detected")
        
        print(f"\nRecommendations:")
        for rec in report.get('recommendations', []):
            print(f"  - {rec}")
        
        # Save results if requested
        if args.save_results:
            output_file = Path(__file__).parent.parent / "results" / "v2_validation_results.json"
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"\nResults saved to: {output_file}")
        
        # Exit with appropriate code
        if report.get('overall_status') == 'SUCCESS':
            print("\n✅ V2 system validation PASSED")
            sys.exit(0)
        elif report.get('overall_status') == 'PARTIAL_SUCCESS':
            print("\n⚠️  V2 system validation PARTIALLY PASSED")
            sys.exit(1)
        else:
            print("\n❌ V2 system validation FAILED")
            sys.exit(2)
        
    except Exception as e:
        print(f"\n❌ Validation failed with error: {e}")
        sys.exit(3)


if __name__ == "__main__":
    asyncio.run(main())