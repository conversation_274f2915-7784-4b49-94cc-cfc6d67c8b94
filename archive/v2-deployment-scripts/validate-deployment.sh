#!/bin/bash
# Comprehensive deployment validation script

set -euo pipefail

NAMESPACE="yemen-market-v2"
FAILURES=0

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
    ((FAILURES++))
}

log_section() {
    echo
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check pod status
check_pods() {
    log_section "Checking Pod Status"
    
    # Get all pods
    pods=$(kubectl get pods -n "$NAMESPACE" -o json)
    
    # Check each pod
    echo "$pods" | jq -r '.items[] | "\(.metadata.name) \(.status.phase)"' | while read -r pod_name pod_status; do
        if [ "$pod_status" = "Running" ]; then
            log_info "Pod $pod_name is running"
        else
            log_error "Pod $pod_name is in state: $pod_status"
        fi
    done
    
    # Check for restart counts
    echo "$pods" | jq -r '.items[] | . as $pod | .status.containerStatuses[]? | "\($pod.metadata.name) \(.name) \(.restartCount)"' | while read -r pod_name container_name restart_count; do
        if [ "$restart_count" -gt 5 ]; then
            log_warn "Container $container_name in pod $pod_name has restarted $restart_count times"
        fi
    done
}

# Check services
check_services() {
    log_section "Checking Services"
    
    services=("api-service" "postgres-service" "redis-service")
    
    for service in "${services[@]}"; do
        if kubectl get service "$service" -n "$NAMESPACE" &> /dev/null; then
            endpoints=$(kubectl get endpoints "$service" -n "$NAMESPACE" -o jsonpath='{.subsets[*].addresses[*].ip}')
            if [ -n "$endpoints" ]; then
                log_info "Service $service has endpoints: $endpoints"
            else
                log_error "Service $service has no endpoints"
            fi
        else
            log_error "Service $service not found"
        fi
    done
}

# Check persistent volumes
check_storage() {
    log_section "Checking Storage"
    
    # Check PVCs
    pvcs=$(kubectl get pvc -n "$NAMESPACE" -o json)
    
    echo "$pvcs" | jq -r '.items[] | "\(.metadata.name) \(.status.phase)"' | while read -r pvc_name pvc_status; do
        if [ "$pvc_status" = "Bound" ]; then
            log_info "PVC $pvc_name is bound"
        else
            log_error "PVC $pvc_name is in state: $pvc_status"
        fi
    done
}

# Check ingress
check_ingress() {
    log_section "Checking Ingress"
    
    ingress=$(kubectl get ingress yemen-market-ingress -n "$NAMESPACE" -o json 2>/dev/null || echo "{}")
    
    if [ "$ingress" != "{}" ]; then
        # Get load balancer hostname
        lb_hostname=$(echo "$ingress" | jq -r '.status.loadBalancer.ingress[0].hostname // empty')
        
        if [ -n "$lb_hostname" ]; then
            log_info "Ingress has load balancer: $lb_hostname"
            
            # Try to resolve DNS
            if host "$lb_hostname" &> /dev/null; then
                log_info "DNS resolution successful for $lb_hostname"
            else
                log_warn "DNS resolution failed for $lb_hostname (may need time to propagate)"
            fi
        else
            log_error "Ingress has no load balancer assigned"
        fi
    else
        log_error "Ingress not found"
    fi
}

# Check database connectivity
check_database() {
    log_section "Checking Database"
    
    # Run a test query
    if kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
        psql -U yemen_market -d yemen_market_v2 -c "SELECT 1" &> /dev/null; then
        log_info "Database connection successful"
        
        # Check if migrations ran
        table_count=$(kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
            psql -U yemen_market -d yemen_market_v2 -t -c \
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'" | tr -d ' ')
        
        if [ "$table_count" -gt 0 ]; then
            log_info "Database has $table_count tables (migrations completed)"
        else
            log_error "Database has no tables (migrations may have failed)"
        fi
    else
        log_error "Cannot connect to database"
    fi
}

# Check Redis connectivity
check_redis() {
    log_section "Checking Redis"
    
    # Get Redis password
    redis_password=$(kubectl get secret yemen-market-secrets -n "$NAMESPACE" \
        -o jsonpath='{.data.REDIS_PASSWORD}' | base64 -d)
    
    if kubectl exec -it deployment/redis -n "$NAMESPACE" -- \
        redis-cli -a "$redis_password" ping &> /dev/null; then
        log_info "Redis connection successful"
    else
        log_error "Cannot connect to Redis"
    fi
}

# Check API health
check_api_health() {
    log_section "Checking API Health"
    
    # Port forward to API service
    kubectl port-forward -n "$NAMESPACE" service/api-service 8080:8000 &> /dev/null &
    pf_pid=$!
    sleep 5
    
    # Health check
    if curl -s http://localhost:8080/health | jq -e '.status == "healthy"' &> /dev/null; then
        log_info "API health check passed"
    else
        log_error "API health check failed"
    fi
    
    # Ready check
    if curl -s http://localhost:8080/ready | jq -e '.database.status == "ready"' &> /dev/null; then
        log_info "API readiness check passed"
    else
        log_error "API readiness check failed"
    fi
    
    # Test API endpoint
    if curl -s http://localhost:8080/api/v1/markets?limit=1 | jq -e '.items' &> /dev/null; then
        log_info "API endpoint test passed"
    else
        log_error "API endpoint test failed"
    fi
    
    # Kill port forward
    kill $pf_pid 2>/dev/null || true
}

# Check monitoring
check_monitoring() {
    log_section "Checking Monitoring"
    
    # Check Prometheus
    if kubectl get pod -l app=prometheus -n "$NAMESPACE" -o jsonpath='{.items[0].status.phase}' | grep -q "Running"; then
        log_info "Prometheus is running"
        
        # Check targets
        kubectl port-forward -n "$NAMESPACE" service/prometheus 9090:9090 &> /dev/null &
        pf_pid=$!
        sleep 5
        
        targets=$(curl -s http://localhost:9090/api/v1/targets | jq -r '.data.activeTargets | length')
        if [ "$targets" -gt 0 ]; then
            log_info "Prometheus has $targets active targets"
        else
            log_warn "Prometheus has no active targets"
        fi
        
        kill $pf_pid 2>/dev/null || true
    else
        log_error "Prometheus is not running"
    fi
    
    # Check Grafana
    if kubectl get pod -l app=grafana -n "$NAMESPACE" -o jsonpath='{.items[0].status.phase}' | grep -q "Running"; then
        log_info "Grafana is running"
    else
        log_error "Grafana is not running"
    fi
}

# Check autoscaling
check_autoscaling() {
    log_section "Checking Autoscaling"
    
    # Check HPA
    hpas=$(kubectl get hpa -n "$NAMESPACE" -o json)
    
    echo "$hpas" | jq -r '.items[] | "\(.metadata.name) \(.status.currentReplicas)/\(.spec.minReplicas)-\(.spec.maxReplicas)"' | \
    while read -r hpa_name replica_info; do
        log_info "HPA $hpa_name: $replica_info replicas"
    done
    
    # Check if metrics are available
    if kubectl top nodes &> /dev/null; then
        log_info "Metrics server is working"
    else
        log_warn "Metrics server not available (autoscaling may not work)"
    fi
}

# Check cronjobs
check_cronjobs() {
    log_section "Checking CronJobs"
    
    cronjobs=$(kubectl get cronjobs -n "$NAMESPACE" -o json)
    
    echo "$cronjobs" | jq -r '.items[] | "\(.metadata.name) \(.spec.schedule)"' | \
    while read -r job_name schedule; do
        log_info "CronJob $job_name scheduled at: $schedule"
        
        # Check last execution
        last_run=$(kubectl get cronjob "$job_name" -n "$NAMESPACE" \
            -o jsonpath='{.status.lastScheduleTime}')
        if [ -n "$last_run" ]; then
            log_info "  Last run: $last_run"
        fi
    done
}

# Performance check
check_performance() {
    log_section "Checking Performance"
    
    # Port forward to API
    kubectl port-forward -n "$NAMESPACE" service/api-service 8080:8000 &> /dev/null &
    pf_pid=$!
    sleep 5
    
    # Simple latency test
    start_time=$(date +%s%N)
    if curl -s http://localhost:8080/health > /dev/null; then
        end_time=$(date +%s%N)
        latency=$(( (end_time - start_time) / 1000000 ))
        
        if [ "$latency" -lt 100 ]; then
            log_info "API latency: ${latency}ms (excellent)"
        elif [ "$latency" -lt 500 ]; then
            log_warn "API latency: ${latency}ms (acceptable)"
        else
            log_error "API latency: ${latency}ms (poor)"
        fi
    fi
    
    kill $pf_pid 2>/dev/null || true
}

# Generate summary report
generate_report() {
    log_section "Validation Summary"
    
    total_checks=$((FAILURES + $(grep -c "✓" /tmp/validation.log || echo 0)))
    success_rate=$(( (total_checks - FAILURES) * 100 / total_checks ))
    
    echo
    echo "Total Checks: $total_checks"
    echo "Passed: $((total_checks - FAILURES))"
    echo "Failed: $FAILURES"
    echo "Success Rate: $success_rate%"
    echo
    
    if [ "$FAILURES" -eq 0 ]; then
        echo -e "${GREEN}✓ All validation checks passed!${NC}"
        echo "The deployment is healthy and ready for use."
    else
        echo -e "${RED}✗ Validation failed with $FAILURES errors${NC}"
        echo "Please review the errors above and take corrective action."
    fi
    
    # Save detailed report
    echo
    echo "Detailed report saved to: validation-report-$(date +%Y%m%d-%H%M%S).txt"
}

# Main execution
main() {
    echo "Yemen Market Integration v2 - Deployment Validation"
    echo "=================================================="
    echo "Namespace: $NAMESPACE"
    echo "Time: $(date)"
    echo
    
    # Redirect output to both terminal and log file
    exec > >(tee -a /tmp/validation.log)
    exec 2>&1
    
    check_pods
    check_services
    check_storage
    check_ingress
    check_database
    check_redis
    check_api_health
    check_monitoring
    check_autoscaling
    check_cronjobs
    check_performance
    
    generate_report
}

# Make scripts executable
chmod +x scripts/*.sh 2>/dev/null || true

# Run validation
main "$@"