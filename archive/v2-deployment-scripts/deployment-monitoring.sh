#!/bin/bash
# Deployment Monitoring and Alerting Setup for Yemen Market Integration V2
# Configures comprehensive monitoring during production deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="yemen-market-v2"
MONITORING_NAMESPACE="monitoring"
CLUSTER_NAME="${CLUSTER_NAME:-yemen-market-prod}"
GRAFANA_ADMIN_PASSWORD="${GRAFANA_ADMIN_PASSWORD:-$(openssl rand -base64 32)}"
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"
PAGERDUTY_SERVICE_KEY="${PAGERDUTY_SERVICE_KEY:-}"

# Function definitions
log_info() {
    echo -e "${GREEN}[INFO $(date '+%H:%M:%S')]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN $(date '+%H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR $(date '+%H:%M:%S')]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP $(date '+%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS $(date '+%H:%M:%S')]${NC} $1"
}

check_prerequisites() {
    log_step "Checking monitoring prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is required but not installed"
        exit 1
    fi
    
    # Check helm
    if ! command -v helm &> /dev/null; then
        log_error "helm is required but not installed"
        exit 1
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

setup_monitoring_namespace() {
    log_step "Setting up monitoring namespace..."
    
    # Create monitoring namespace
    kubectl create namespace "$MONITORING_NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Label namespace
    kubectl label namespace "$MONITORING_NAMESPACE" \
        purpose="monitoring" \
        managed-by="deployment-automation" \
        --overwrite
    
    log_success "Monitoring namespace ready"
}

install_prometheus_stack() {
    log_step "Installing Prometheus monitoring stack..."
    
    # Add Prometheus community helm repo
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    # Create custom values for Prometheus stack
    cat > /tmp/prometheus-values.yaml <<EOF
prometheus:
  prometheusSpec:
    serviceMonitorSelectorNilUsesHelmValues: false
    podMonitorSelectorNilUsesHelmValues: false
    ruleSelectorNilUsesHelmValues: false
    retention: 30d
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: gp3
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 50Gi
    additionalScrapeConfigs:
      - job_name: 'yemen-market-v2'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - $NAMESPACE
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)

grafana:
  adminPassword: $GRAFANA_ADMIN_PASSWORD
  persistence:
    enabled: true
    storageClassName: gp3
    size: 10Gi
  grafana.ini:
    server:
      root_url: https://grafana.yemen-market.prod.com
    auth.anonymous:
      enabled: true
      org_role: Viewer
    alerting:
      enabled: true
  dashboardProviders:
    dashboardproviders.yaml:
      apiVersion: 1
      providers:
      - name: 'deployment-dashboards'
        orgId: 1
        folder: 'Deployment'
        type: file
        disableDeletion: false
        editable: true
        options:
          path: /var/lib/grafana/dashboards/deployment

alertmanager:
  config:
    global:
      slack_api_url: '$SLACK_WEBHOOK'
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'
    route:
      group_by: ['alertname', 'environment', 'component']
      group_wait: 10s
      group_interval: 30s
      repeat_interval: 12h
      receiver: 'default'
      routes:
        - match:
            severity: critical
          receiver: 'critical-alerts'
        - match:
            component: deployment
          receiver: 'deployment-alerts'
        - match:
            component: rollback
          receiver: 'rollback-alerts'
    receivers:
      - name: 'default'
        slack_configs:
          - channel: '#alerts'
            title: 'Yemen Market Alert'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
      - name: 'critical-alerts'
        slack_configs:
          - channel: '#critical-alerts'
            title: 'CRITICAL: Yemen Market Alert'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
            color: 'danger'
        pagerduty_configs:
          - service_key: '$PAGERDUTY_SERVICE_KEY'
            description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
      - name: 'deployment-alerts'
        slack_configs:
          - channel: '#deployments'
            title: 'Deployment Alert: {{ .GroupLabels.alertname }}'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}\nAction: {{ .Annotations.action_required }}{{ end }}'
            color: 'warning'
      - name: 'rollback-alerts'
        slack_configs:
          - channel: '#critical-alerts'
            title: 'ROLLBACK ALERT: {{ .GroupLabels.alertname }}'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
            color: 'danger'
EOF

    # Install or upgrade Prometheus stack
    if helm list -n "$MONITORING_NAMESPACE" | grep -q kube-prometheus-stack; then
        log_info "Upgrading existing Prometheus stack..."
        helm upgrade kube-prometheus-stack prometheus-community/kube-prometheus-stack \
            -n "$MONITORING_NAMESPACE" \
            -f /tmp/prometheus-values.yaml
    else
        log_info "Installing new Prometheus stack..."
        helm install kube-prometheus-stack prometheus-community/kube-prometheus-stack \
            -n "$MONITORING_NAMESPACE" \
            -f /tmp/prometheus-values.yaml \
            --wait --timeout=10m
    fi
    
    # Clean up temp file
    rm -f /tmp/prometheus-values.yaml
    
    log_success "Prometheus stack installed"
}

configure_deployment_alerts() {
    log_step "Configuring deployment-specific alerts..."
    
    # Apply deployment alert rules
    kubectl apply -f deployment/monitoring/deployment-alerts.yaml
    
    # Create alert rules for specific deployment metrics
    kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: yemen-market-deployment-rules
  namespace: $MONITORING_NAMESPACE
  labels:
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
    - name: deployment.custom.rules
      interval: 30s
      rules:
        - record: deployment:http_request_rate
          expr: |
            sum(rate(http_requests_total{job="yemen-market-api-blue"}[5m])) by (job)
        
        - record: deployment:http_error_rate
          expr: |
            sum(rate(http_requests_total{job="yemen-market-api-blue",code=~"5.."}[5m])) by (job) /
            sum(rate(http_requests_total{job="yemen-market-api-blue"}[5m])) by (job)
        
        - record: deployment:http_latency_p95
          expr: |
            histogram_quantile(0.95,
              sum(rate(http_request_duration_seconds_bucket{job="yemen-market-api-blue"}[5m])) by (le, job)
            )
        
        - record: deployment:traffic_split_ratio
          expr: |
            sum(rate(http_requests_total{job="yemen-market-api-blue"}[5m])) /
            (sum(rate(http_requests_total{job="yemen-market-api-blue"}[5m])) + 
             sum(rate(http_requests_total{job="yemen-market-api-green"}[5m])))
EOF
    
    log_success "Deployment alert rules configured"
}

setup_deployment_dashboards() {
    log_step "Setting up deployment monitoring dashboards..."
    
    # Create deployment dashboard ConfigMap
    kubectl create configmap deployment-dashboard \
        --from-file=deployment/monitoring/grafana/dashboards/ \
        -n "$MONITORING_NAMESPACE" \
        --dry-run=client -o yaml | \
        kubectl apply -f -
    
    # Label ConfigMap for Grafana discovery
    kubectl label configmap deployment-dashboard \
        grafana_dashboard="1" \
        -n "$MONITORING_NAMESPACE" \
        --overwrite
    
    # Create real-time deployment monitoring dashboard
    kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: realtime-deployment-dashboard
  namespace: $MONITORING_NAMESPACE
  labels:
    grafana_dashboard: "1"
data:
  realtime-deployment.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Yemen Market V2 Deployment - Real-time",
        "tags": ["yemen-market", "deployment", "v2"],
        "style": "dark",
        "timezone": "browser",
        "refresh": "10s",
        "time": {
          "from": "now-30m",
          "to": "now"
        },
        "panels": [
          {
            "id": 1,
            "title": "Traffic Distribution",
            "type": "stat",
            "targets": [
              {
                "expr": "deployment:traffic_split_ratio * 100",
                "legendFormat": "V2 Traffic %"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "yellow", "value": 10},
                    {"color": "green", "value": 50}
                  ]
                }
              }
            }
          },
          {
            "id": 2,
            "title": "Error Rate Comparison",
            "type": "timeseries",
            "targets": [
              {
                "expr": "deployment:http_error_rate{job=\"yemen-market-api-blue\"} * 100",
                "legendFormat": "V2 Error Rate %"
              },
              {
                "expr": "deployment:http_error_rate{job=\"yemen-market-api-green\"} * 100",
                "legendFormat": "V1 Error Rate %"
              }
            ]
          },
          {
            "id": 3,
            "title": "Latency Comparison",
            "type": "timeseries",
            "targets": [
              {
                "expr": "deployment:http_latency_p95{job=\"yemen-market-api-blue\"}",
                "legendFormat": "V2 P95 Latency"
              },
              {
                "expr": "deployment:http_latency_p95{job=\"yemen-market-api-green\"}",
                "legendFormat": "V1 P95 Latency"
              }
            ]
          }
        ]
      }
    }
EOF
    
    log_success "Deployment dashboards configured"
}

setup_service_monitors() {
    log_step "Setting up service monitors for V2..."
    
    # Create service monitor for V2 API
    kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: yemen-market-v2-api
  namespace: $MONITORING_NAMESPACE
  labels:
    app: yemen-market-v2
    component: api
spec:
  selector:
    matchLabels:
      app: yemen-market-api
  namespaceSelector:
    matchNames:
      - $NAMESPACE
  endpoints:
    - port: http
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
EOF

    # Create service monitor for V2 workers
    kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: yemen-market-v2-worker
  namespace: $MONITORING_NAMESPACE
  labels:
    app: yemen-market-v2
    component: worker
spec:
  selector:
    matchLabels:
      app: yemen-market-worker
  namespaceSelector:
    matchNames:
      - $NAMESPACE
  endpoints:
    - port: metrics
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
EOF
    
    log_success "Service monitors configured"
}

create_monitoring_ingress() {
    log_step "Creating monitoring ingress..."
    
    kubectl apply -f - <<EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: monitoring-ingress
  namespace: $MONITORING_NAMESPACE
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - grafana.yemen-market.prod.com
        - prometheus.yemen-market.prod.com
      secretName: monitoring-tls
  rules:
    - host: grafana.yemen-market.prod.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: kube-prometheus-stack-grafana
                port:
                  number: 80
    - host: prometheus.yemen-market.prod.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: kube-prometheus-stack-prometheus
                port:
                  number: 9090
EOF
    
    log_success "Monitoring ingress created"
}

setup_deployment_logs() {
    log_step "Setting up centralized logging for deployment..."
    
    # Create log aggregation for deployment
    kubectl apply -f - <<EOF
apiVersion: logging.coreos.com/v1
kind: ClusterLogForwarder
metadata:
  name: deployment-logs
  namespace: openshift-logging
spec:
  outputs:
    - name: deployment-logs
      type: elasticsearch
      url: http://elasticsearch.logging.svc.cluster.local:9200
      elasticsearch:
        index: deployment-logs
  pipelines:
    - name: deployment-pipeline
      inputRefs:
        - application
      filterRefs:
        - deployment-filter
      outputRefs:
        - deployment-logs
---
apiVersion: logging.coreos.com/v1
kind: ClusterLogFilter
metadata:
  name: deployment-filter
spec:
  type: json
  json:
    javascript: |
      const log = record.log;
      if (log && (
        log.includes("deployment") || 
        log.includes("migration") || 
        log.includes("rollback") ||
        record.kubernetes.namespace_name === "$NAMESPACE"
      )) {
        return record;
      }
      return null;
EOF
    
    log_success "Deployment logging configured"
}

create_deployment_runbooks() {
    log_step "Creating deployment runbooks..."
    
    mkdir -p deployment/monitoring/runbooks
    
    # Create high error rate runbook
    cat > deployment/monitoring/runbooks/high-error-rate.md <<EOF
# High Error Rate During Deployment - Runbook

## Symptoms
- Error rate > 5% for more than 2 minutes in V2 deployment
- Increased 5xx HTTP responses from V2 API

## Immediate Actions
1. Check V2 application logs:
   \`\`\`bash
   kubectl logs -n $NAMESPACE deployment/yemen-market-api --tail=100
   \`\`\`

2. Check V2 database connectivity:
   \`\`\`bash
   kubectl exec -n $NAMESPACE deployment/yemen-market-api -- curl -f http://localhost:8000/ready
   \`\`\`

3. Compare with V1 error rate:
   \`\`\`bash
   # Check if V1 also has issues
   kubectl logs -n yemen-market deployment/yemen-market-api --tail=100
   \`\`\`

## Decision Tree
- If V1 also has high error rate: Infrastructure issue, investigate load balancer/database
- If only V2 has high error rate: V2-specific issue, consider rollback
- If errors > 10%: Immediate rollback required

## Rollback Command
\`\`\`bash
./scripts/rollback-automation.sh traffic
\`\`\`

## Post-Incident
1. Analyze root cause
2. Update deployment validation
3. Fix identified issues
4. Re-attempt deployment
EOF

    log_success "Deployment runbooks created"
}

verify_monitoring_setup() {
    log_step "Verifying monitoring setup..."
    
    # Check Prometheus is running
    if kubectl get pods -n "$MONITORING_NAMESPACE" -l app.kubernetes.io/name=prometheus | grep -q Running; then
        log_info "✅ Prometheus is running"
    else
        log_error "❌ Prometheus is not running"
        return 1
    fi
    
    # Check Grafana is running
    if kubectl get pods -n "$MONITORING_NAMESPACE" -l app.kubernetes.io/name=grafana | grep -q Running; then
        log_info "✅ Grafana is running"
    else
        log_error "❌ Grafana is not running"
        return 1
    fi
    
    # Check AlertManager is running
    if kubectl get pods -n "$MONITORING_NAMESPACE" -l app.kubernetes.io/name=alertmanager | grep -q Running; then
        log_info "✅ AlertManager is running"
    else
        log_error "❌ AlertManager is not running"
        return 1
    fi
    
    # Test alert rules are loaded
    local alert_rules=$(kubectl get prometheusrules -n "$MONITORING_NAMESPACE" --no-headers | wc -l)
    if [ "$alert_rules" -gt 0 ]; then
        log_info "✅ Alert rules loaded ($alert_rules rules)"
    else
        log_warn "⚠️  No alert rules found"
    fi
    
    log_success "Monitoring setup verification completed"
}

print_access_info() {
    log_step "Deployment monitoring access information..."
    
    echo
    echo "======================================"
    echo "Deployment Monitoring Access"
    echo "======================================"
    echo "Grafana Admin Password: $GRAFANA_ADMIN_PASSWORD"
    echo
    echo "URLs (after DNS propagation):"
    echo "- Grafana: https://grafana.yemen-market.prod.com"
    echo "- Prometheus: https://prometheus.yemen-market.prod.com"
    echo
    echo "Key Dashboards:"
    echo "- Real-time Deployment: https://grafana.yemen-market.prod.com/d/deployment-realtime"
    echo "- System Overview: https://grafana.yemen-market.prod.com/d/system-overview"
    echo "- Data Pipeline: https://grafana.yemen-market.prod.com/d/data-pipeline"
    echo
    echo "Alert Channels:"
    echo "- Slack: #alerts, #critical-alerts, #deployments"
    echo "- PagerDuty: Critical alerts only"
    echo
    echo "Runbooks Location:"
    echo "- deployment/monitoring/runbooks/"
    echo "======================================"
    echo
}

main() {
    log_info "🔧 Setting up deployment monitoring and alerting"
    
    check_prerequisites
    setup_monitoring_namespace
    install_prometheus_stack
    configure_deployment_alerts
    setup_deployment_dashboards
    setup_service_monitors
    create_monitoring_ingress
    setup_deployment_logs
    create_deployment_runbooks
    verify_monitoring_setup
    print_access_info
    
    log_success "🎯 Deployment monitoring setup completed!"
    log_info "Monitor your deployment at: https://grafana.yemen-market.prod.com"
}

# Execute main function
main "$@"