#!/bin/bash
# Script to update production secrets

set -euo pipefail

NAMESPACE="yemen-market-v2"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to safely read password
read_secret() {
    local prompt="$1"
    local var_name="$2"
    
    echo -n "$prompt: "
    read -s value
    echo
    eval "$var_name='$value'"
}

# Get current context
CURRENT_CONTEXT=$(kubectl config current-context)
echo "Current Kubernetes context: $CURRENT_CONTEXT"
echo -n "Are you sure you want to update secrets in this cluster? (yes/no): "
read confirmation

if [ "$confirmation" != "yes" ]; then
    log_error "Aborted"
    exit 1
fi

# Backup existing secrets
log_info "Backing up existing secrets..."
kubectl get secret yemen-market-secrets -n "$NAMESPACE" -o yaml > secrets-backup-$(date +%Y%m%d-%H%M%S).yaml

# Read new values
echo
log_info "Enter new secret values (press Enter to keep existing value):"

read_secret "Database Password" "DB_PASSWORD"
read_secret "JWT Secret" "JWT_SECRET"
read_secret "API Key" "API_KEY"
read_secret "Redis Password" "REDIS_PASSWORD"
read_secret "WFP API Key (optional)" "WFP_API_KEY"
read_secret "ACLED API Key (optional)" "ACLED_API_KEY"
read_secret "Grafana Admin Password" "GRAFANA_PASSWORD"

# Update secrets
log_info "Updating secrets..."

# Delete existing secret
kubectl delete secret yemen-market-secrets -n "$NAMESPACE" --ignore-not-found

# Create new secret with provided values
kubectl_args=(
    "create" "secret" "generic" "yemen-market-secrets"
    "-n" "$NAMESPACE"
)

# Add non-empty values
[ -n "$DB_PASSWORD" ] && kubectl_args+=("--from-literal=DB_PASSWORD=$DB_PASSWORD")
[ -n "$JWT_SECRET" ] && kubectl_args+=("--from-literal=JWT_SECRET=$JWT_SECRET")
[ -n "$API_KEY" ] && kubectl_args+=("--from-literal=API_KEY=$API_KEY")
[ -n "$REDIS_PASSWORD" ] && kubectl_args+=("--from-literal=REDIS_PASSWORD=$REDIS_PASSWORD")
[ -n "$WFP_API_KEY" ] && kubectl_args+=("--from-literal=WFP_API_KEY=$WFP_API_KEY")
[ -n "$ACLED_API_KEY" ] && kubectl_args+=("--from-literal=ACLED_API_KEY=$ACLED_API_KEY")
[ -n "$GRAFANA_PASSWORD" ] && kubectl_args+=("--from-literal=GRAFANA_ADMIN_PASSWORD=$GRAFANA_PASSWORD")

# Add computed values
if [ -n "$DB_PASSWORD" ]; then
    kubectl_args+=("--from-literal=DATABASE_URL=************************************************************/yemen_market_v2")
fi

if [ -n "$REDIS_PASSWORD" ]; then
    kubectl_args+=("--from-literal=REDIS_URL=redis://:$REDIS_PASSWORD@redis-service:6379/0")
fi

kubectl "${kubectl_args[@]}"

# Restart deployments to pick up new secrets
log_info "Restarting deployments..."
kubectl rollout restart deployment/yemen-market-api -n "$NAMESPACE"
kubectl rollout restart deployment/yemen-market-worker -n "$NAMESPACE"

# Wait for rollout
log_info "Waiting for rollout to complete..."
kubectl rollout status deployment/yemen-market-api -n "$NAMESPACE"
kubectl rollout status deployment/yemen-market-worker -n "$NAMESPACE"

log_info "Secrets updated successfully!"
log_info "Backup saved to: secrets-backup-$(date +%Y%m%d-%H%M%S).yaml"