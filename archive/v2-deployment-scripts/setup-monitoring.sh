#\!/bin/bash

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="yemen-market"
MONITORING_NAMESPACE="monitoring"

echo -e "${GREEN}Setting up monitoring for Yemen Market Integration${NC}"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "Checking prerequisites..."

if \! command_exists kubectl; then
    echo -e "${RED}kubectl is not installed. Please install kubectl first.${NC}"
    exit 1
fi

if \! command_exists helm; then
    echo -e "${RED}helm is not installed. Please install helm first.${NC}"
    exit 1
fi

# Create namespaces if they don't exist
echo "Creating namespaces..."
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace $MONITORING_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Label namespaces for monitoring
kubectl label namespace $NAMESPACE name=$NAMESPACE --overwrite
kubectl label namespace $MONITORING_NAMESPACE name=$MONITORING_NAMESPACE --overwrite

# Install Prometheus Operator using Helm
echo -e "${YELLOW}Installing Prometheus Operator...${NC}"
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

helm upgrade --install kube-prometheus-stack prometheus-community/kube-prometheus-stack \
    --namespace $MONITORING_NAMESPACE \
    --set prometheus.prometheusSpec.serviceMonitorSelectorNilUsesHelmValues=false \
    --set prometheus.prometheusSpec.podMonitorSelectorNilUsesHelmValues=false \
    --set prometheus.prometheusSpec.ruleSelectorNilUsesHelmValues=false \
    --set grafana.adminPassword=yemen-market-admin \
    --set grafana.persistence.enabled=true \
    --set grafana.persistence.size=10Gi \
    --wait

# Install Loki for log aggregation
echo -e "${YELLOW}Installing Loki...${NC}"
helm repo add grafana https://grafana.github.io/helm-charts
helm repo update

helm upgrade --install loki grafana/loki-stack \
    --namespace $MONITORING_NAMESPACE \
    --set loki.persistence.enabled=true \
    --set loki.persistence.size=50Gi \
    --set promtail.enabled=true \
    --wait

# Install Jaeger Operator
echo -e "${YELLOW}Installing Jaeger Operator...${NC}"
kubectl create namespace observability --dry-run=client -o yaml | kubectl apply -f -
kubectl create -f https://github.com/jaegertracing/jaeger-operator/releases/download/v1.51.0/jaeger-operator.yaml -n observability

# Wait for operators to be ready
echo "Waiting for operators to be ready..."
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=prometheus-operator -n $MONITORING_NAMESPACE --timeout=300s
kubectl wait --for=condition=ready pod -l name=jaeger-operator -n observability --timeout=300s

# Apply Yemen Market monitoring configuration
echo -e "${YELLOW}Applying Yemen Market monitoring configuration...${NC}"
kubectl apply -f kubernetes/monitoring.yaml

# Create Grafana dashboard ConfigMaps
echo "Creating Grafana dashboards..."
kubectl create configmap yemen-system-overview \
    --from-file=system-overview.json=deployment/monitoring/grafana/dashboards/system-overview.json \
    -n $MONITORING_NAMESPACE \
    --dry-run=client -o yaml | kubectl apply -f -

kubectl create configmap yemen-analysis-performance \
    --from-file=analysis-performance.json=deployment/monitoring/grafana/dashboards/analysis-performance.json \
    -n $MONITORING_NAMESPACE \
    --dry-run=client -o yaml | kubectl apply -f -

kubectl create configmap yemen-data-pipeline \
    --from-file=data-pipeline-health.json=deployment/monitoring/grafana/dashboards/data-pipeline-health.json \
    -n $MONITORING_NAMESPACE \
    --dry-run=client -o yaml | kubectl apply -f -

# Label ConfigMaps for Grafana autodiscovery
kubectl label configmap yemen-system-overview grafana_dashboard=1 -n $MONITORING_NAMESPACE --overwrite
kubectl label configmap yemen-analysis-performance grafana_dashboard=1 -n $MONITORING_NAMESPACE --overwrite
kubectl label configmap yemen-data-pipeline grafana_dashboard=1 -n $MONITORING_NAMESPACE --overwrite

# Create Prometheus alerts ConfigMap
echo "Creating Prometheus alerts..."
kubectl create configmap yemen-alerts \
    --from-file=alerts.yml=deployment/monitoring/prometheus/alerts.yml \
    -n $MONITORING_NAMESPACE \
    --dry-run=client -o yaml | kubectl apply -f -

# Configure Sentry integration (if DSN is provided)
if [ \! -z "${SENTRY_DSN:-}" ]; then
    echo -e "${YELLOW}Configuring Sentry integration...${NC}"
    kubectl create secret generic sentry-config \
        --from-literal=dsn=$SENTRY_DSN \
        -n $NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
fi

# Configure alert receivers
echo "Configuring alert receivers..."
kubectl create secret generic alertmanager-config \
    --from-literal=slack-webhook-url=${SLACK_WEBHOOK_URL:-https://hooks.slack.com/services/YOUR/WEBHOOK/URL} \
    --from-literal=pagerduty-service-key=${PAGERDUTY_SERVICE_KEY:-YOUR-SERVICE-KEY} \
    -n $MONITORING_NAMESPACE \
    --dry-run=client -o yaml | kubectl apply -f -

# Get access information
echo -e "\n${GREEN}Monitoring setup complete\!${NC}"
echo -e "\nAccess information:"

# Prometheus
PROMETHEUS_URL=$(kubectl get svc -n $MONITORING_NAMESPACE kube-prometheus-stack-prometheus -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
echo -e "Prometheus: http://${PROMETHEUS_URL:-localhost}:9090"

# Grafana
GRAFANA_URL=$(kubectl get svc -n $MONITORING_NAMESPACE kube-prometheus-stack-grafana -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
echo -e "Grafana: http://${GRAFANA_URL:-localhost}:3000"
echo -e "  Username: admin"
echo -e "  Password: yemen-market-admin"

# Jaeger
echo -e "\nTo access Jaeger UI:"
echo -e "kubectl port-forward -n $MONITORING_NAMESPACE svc/yemen-market-jaeger-query 16686:16686"
echo -e "Then open: http://localhost:16686"

# Port forwarding commands
echo -e "\n${YELLOW}For local access, run:${NC}"
echo "kubectl port-forward -n $MONITORING_NAMESPACE svc/kube-prometheus-stack-prometheus 9090:9090 &"
echo "kubectl port-forward -n $MONITORING_NAMESPACE svc/kube-prometheus-stack-grafana 3000:80 &"
echo "kubectl port-forward -n $MONITORING_NAMESPACE svc/yemen-market-jaeger-query 16686:16686 &"

# Verify setup
echo -e "\n${YELLOW}Verifying setup...${NC}"
kubectl get servicemonitor -n $MONITORING_NAMESPACE
kubectl get podmonitor -n $MONITORING_NAMESPACE
kubectl get prometheusrule -n $MONITORING_NAMESPACE

echo -e "\n${GREEN}Setup complete\! Monitor your Yemen Market Integration system.${NC}"
