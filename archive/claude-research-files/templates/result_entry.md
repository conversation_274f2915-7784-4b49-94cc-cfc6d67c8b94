# Result Entry: [SPECIFICATION_NAME]

## Metadata
- **Date**: YYYY-MM-DD HH:MM
- **Spec ID**: [e.g., main_1a]
- **Analyst**: [Name]
- **Version**: [Data version, code version]

## Specification
```python
# Exact specification run
[PASTE EXACT CODE]
```

### Sample
- Dataset: [e.g., balanced_panel_v3.parquet]
- Time period: [Start - End]
- N observations: [Total]
- N clusters: [Markets/Governorates]
- Sample restrictions: [Any filters applied]

## Results

### Main Coefficients
| Variable | Coef | SE | t | p | 95% CI |
|----------|------|----|----|-------|---------|
| conflict | -0.XXX | (0.XXX) | -X.XX | 0.XXX | [-X.XX, -X.XX] |
| exchange_rate | X.XXX | (0.XXX) | X.XX | 0.XXX | [X.XX, X.XX] |
| global_price | X.XXX | (0.XXX) | X.XX | 0.XXX | [X.XX, X.XX] |

### Model Statistics
- R-squared: 0.XXX
- Within R-sq: 0.XXX (if FE)
- F-statistic: XXX.XX (p < 0.001)
- Root MSE: X.XXX

### Diagnostic Tests
- [ ] Heteroskedasticity (Breusch-Pagan): p = 0.XXX
- [ ] Serial correlation (Wooldridge): p = 0.XXX
- [ ] Weak instruments (if IV): F = XX.XX

## Interpretation
[2-3 sentences on what this means economically]

## Hypothesis Support
- **H1 (Exchange Rate)**: [Positive/Negative/Neutral] - [Brief explanation]
- **H2 (Aid Channel)**: [Positive/Negative/Neutral] - [Brief explanation]

## Robustness Performed
- [ ] Winsorized 1%: [Change in results?]
- [ ] Exclude capitals: [Change in results?]
- [ ] Alternative clustering: [Change in results?]
- [ ] Add time trend: [Change in results?]

## Concerns/Surprises
1. [Any unexpected results]
2. [Potential issues with specification]
3. [Data quality concerns]

## Next Steps
1. [Additional robustness needed]
2. [Follow-up specifications]
3. [Data investigations]

## Files
- Output: `results/YYYY-MM-DD/spec_[ID]_output.log`
- Table: `results/YYYY-MM-DD/spec_[ID]_table.tex`
- Figure: `results/YYYY-MM-DD/spec_[ID]_coefplot.pdf`

## For Paper
- Target: [Table X / Figure Y / Appendix]
- Status: [Main result / Robustness check / Exploratory]

---
*Entry created: YYYY-MM-DD HH:MM*
*Validation script: `.claude/validation/replication/spec_[ID]_repl.py`*