# Project Knowledge Base

## Core Research Insights

### 1. The Exchange Rate Revelation (2025-01-31)
**Discovery**: The "negative price premium" in conflict areas may be entirely explained by exchange rate divergence.
- Houthi areas: 535-539 YER/USD (controlled)
- Government areas: 2,000-2,150 YER/USD (market-based)
- Same YER price = 4x different USD price
**Implication**: This fundamentally reframes the research from a conflict puzzle to a currency fragmentation study.

### 2. Price Patterns
- 292% wheat price increase (2019-2025)
- 540% sugar price increase
- 40% coefficient of variation across markets
- Low R-squared (1.3%) in initial models suggests missing variables

### 3. Data Coverage Achievements
- 46,200+ price observations collected
- 21 markets covered
- 16 commodities tracked
- 88.4% panel coverage despite conflict

## Methodological Decisions

### Variable Transformations
```python
# Always use these
conflict_it = log(1 + events_it)  # Handle zeros
exchange_rate_it = official * (1 + black_premium_it)
price_usd_it = price_yer_it / exchange_rate_it
```

### Identification Strategy
1. **Primary**: Triple-diff (Zone × Time × Import dependence)
2. **Secondary**: RD at currency zone boundaries
3. **Robustness**: IV using spatial conflict lags

### Key Econometric Choices
- Cluster SE at market level (not governorate)
- Include month FE for Ramadan effects
- First-difference non-stationary series (81.5%)
- Separate analysis for tradeable vs non-tradeable

## Data Quirks & Solutions

### Exchange Rates
- **Issue**: Multiple rates exist (official Aden, official Sana'a, parallel)
- **Solution**: Create composite rate with black market premium

### Missing Data
- **Issue**: Markets stop reporting during intense conflict
- **Solution**: Not random - need selection correction

### Aid Endogeneity
- **Issue**: Aid targets worst-affected areas
- **Solution**: Instrument with predetermined allocation rules

### Quality Changes
- **Issue**: "Wheat" in 2019 ≠ "Wheat" in 2025
- **Solution**: Commodity-time FE where possible

## Literature Connections

### Multi-Currency Zones
- Cariolle et al. (2023) - Syria parallel
- Key insight: Currency substitution in conflict

### Law of One Price
- Atkin & Donaldson (2015) - Within-country tests
- Application: Test in USD to bypass currency issues

### Aid in Conflict
- Burke et al. (2023) - Aid effectiveness
- Relevance: Dutch disease mechanism

## Policy Implications Emerging

1. **Currency Unification**: More important than assumed
2. **Cash vs In-Kind**: Depends on FX regime
3. **Market Integration**: Exists in USD terms
4. **Aid Targeting**: Consider FX effects

## Technical Learnings

### Performance
- Polars 30-60x faster than pandas for loading
- DuckDB ideal for panel construction
- Keep data in parquet for I/O speed

### Reproducibility
- Version all specifications
- Log every regression result
- Create replication scripts immediately

### Workflow
- Start each day reviewing active.md
- Log results as you go (not at end)
- Update hypotheses after each test

## Common Pitfalls Avoided

1. **Currency Confusion**: Always specify YER or USD
2. **Aggregation Error**: Never average across commodities
3. **Timing Mismatch**: Match price and FX dates exactly
4. **Selection Bias**: Markets that survive are different

---
*This file accumulates permanent project insights. Update weekly.*
*Last major update: 2025-01-31*