# Active Session Context

## Session: 2025-01-31
**Focus**: Refactoring .claude/ directory for econometric research best practices

### Current Sprint
Building comprehensive exchange rate dataset to test hypothesis that "negative price premiums" are FX artifacts

### Today's Progress
- [x] Refactored CLAUDE.md for research focus
- [x] Redesigned .claude/ directory structure based on best practices
- [ ] Build exchange rate dataset
- [ ] Run initial YER vs USD comparison

### Key Discoveries This Session
1. Exchange rates differ 4x between zones (Houthi: 537, Gov: 2,150 YER/USD)
2. This could explain entire "negative premium" puzzle
3. Need parallel market rates, not just official

### Active Hypotheses
- **H1**: Negative premiums disappear when using USD prices (PRIMARY)
- **H2**: Aid distribution creates local price depression
- **H3**: Demand destruction from displacement

### Immediate Next Steps
1. Find parallel market exchange rate data
2. Create dual price series (YER and USD)
3. Test coefficient of variation in both currencies
4. Run basic specification comparing results

### Blockers
- Need parallel market rates (Action: Contact market monitors)
- OCHA aid data access (Action: Check HDX platform)

### Code Context
```python
# Working on:
scripts/build_exchange_rates.py  # Creating
data/processed/exchange_rates_daily.csv  # Target output
```

### Questions for Review
1. Should exchange rate discovery lead the paper?
2. Triple-diff vs RD for identification?
3. Target journal - JDE or conflict-focused?

### End-of-Session Checklist
- [ ] Update knowledge.md with permanent insights
- [ ] Archive completed work
- [ ] Set next session's priorities
- [ ] Commit changes

---
*Session started: 09:00 | Last update: [TIME]*
*Next session focus: Complete exchange rate dataset*