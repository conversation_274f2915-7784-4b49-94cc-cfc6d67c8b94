#!/bin/bash
# Migration script for new .claude structure

echo "Migrating to new .claude structure..."

# Create directories if they don't exist
mkdir -p {memory,commands,specs/current,specs/archive,results,config,hypotheses,literature,instruments,robustness,paper,templates,checkpoints,validation,data_quality}

# Migrate research content if it exists
if [ -d "research" ]; then
    echo "Migrating research directory..."
    
    # Move hypothesis files
    [ -f "research/hypotheses/exchange_rate_hypothesis.md" ] && cp "research/hypotheses/exchange_rate_hypothesis.md" "hypotheses/h1_exchange_rate_imported.md"
    
    # Move specifications
    [ -f "research/specifications/main_specifications.md" ] && cp "research/specifications/main_specifications.md" "specs/archive/main_specifications_imported.md"
    
    # Move quick reference
    [ -f "research/QUICK_REFERENCE.md" ] && cp "research/QUICK_REFERENCE.md" "config/quick_reference.md"
    
    # Archive old research directory
    mv research archive/research_old_structure_$(date +%Y%m%d)
fi

# Set up initial files if they don't exist
[ ! -f "memory/decisions.md" ] && echo "# Key Decisions Log\n\n## Decision History\n\n---\n*Track important choices and rationale*" > memory/decisions.md

[ ! -f "robustness/checklist.md" ] && echo "# Robustness Checklist\n\n- [ ] Winsorize at 1%\n- [ ] Winsorize at 5%\n- [ ] Exclude capital cities\n- [ ] Alternative clustering\n- [ ] Add time trends\n- [ ] Bootstrap standard errors\n- [ ] Jackknife samples\n- [ ] Rolling window analysis" > robustness/checklist.md

[ ! -f "paper/outline.md" ] && echo "# Paper Outline\n\n## 1. Introduction\n- Hook: Conflict lowers prices?\n- Contribution: Exchange rate mechanism\n\n## 2. Context\n\n## 3. Data\n\n## 4. Methodology\n\n## 5. Results\n- Table 1: Summary statistics\n- Table 2: Main results (YER vs USD)\n- Table 3: Mechanisms\n\n## 6. Robustness\n\n## 7. Policy Implications\n\n## 8. Conclusion" > paper/outline.md

echo "Migration complete!"
echo "Next steps:"
echo "1. Review migrated files in hypotheses/ and specs/archive/"
echo "2. Update memory/active.md with current session"
echo "3. Start using new command structure"