project_overview:
  economic_context: "Analysis of conflict's impact on market integration and fragmentation in Yemen, focusing on food security and policy interventions. Employs a three-tier econometric methodology for multi-dimensional panel data (market x commodity x time)."
  key_finding: "PRD claims a -35% average market integration reduction due to conflict, with an estimated annual cost of $847M. This needs ongoing validation through model runs."
  data_sources:
    - "WFP Food Prices (CSV via API)"
    - "ACAPS Territorial Control (Shapefiles/GeoJSON)"
    - "ACLED Conflict Events (CSV via API)"
    - "HDX Administrative Boundaries (GeoPackage)"
    - "Exchange Rates (derived or from WFP)"

technical_foundation:
  panel_structure: "Targeting 28 markets × 23 commodities × 72 months. Actual balanced panel dimensions may vary based on data availability filters (e.g., V1 PanelBuilder mentions 21x16x75)."
  missing_data_approach: "V1 PanelBuilder uses ffill/bfill and linear interpolation (limited). PRD mentions 'smart imputation' and K-NN for spatial features."
  standard_errors:
    - "Clustered SE (by market, market-time)"
    - "Dr<PERSON><PERSON>-<PERSON><PERSON>ay for cross-sectional and spatial dependence"
    - "HAC (Newey-West) for time-series issues"
  econometric_models_implemented:
    - "Tier 1: Pooled Panel Regression (Fixed Effects, Two-Way Fixed Effects)"
    - "Tier 2: Commodity-Specific Threshold VECM"
    - "Tier 3: Static Factor Analysis (PCA), Dynamic Factor Model (statsmodels DFM)"
  v2_architecture: "Clean Architecture (Application, Core, Infrastructure, Interfaces) using FastAPI, AsyncPG, Redis. Event-driven components via an in-memory AsyncEventBus."

completed_tasks:
  # This section will be populated as tasks are defined and completed by AI agents.
  # Example structure:
  # - task_id: "task_01_setup_environment"
  #   description: "Set up Python virtual environment and install dependencies."
  #   outputs: ["venv created", "requirements.txt installed"]
  #   api_contracts: [] # N/A for this task
  # - task_id: "task_02_data_ingestion_wfp"
  #   description: "Implement WFP data ingestion and initial processing."
  #   outputs: ["data/raw/wfp/wfp_prices_raw.parquet", "data/interim/wfp_prices_cleaned.parquet"]
  #   api_contracts: ["WFPProcessor.fetch_data()", "WFPProcessor.clean_data()"]

current_focus:
  # This section will be updated dynamically by the orchestrating agent or dev workflow.
  task_id: "task_01_initial_codebase_review" # Placeholder for the first task I'll define
  description: "Review and understand the V1 and V2 codebase structure, focusing on data pipelines and model implementations."
  depends_on: []
  produces: ["Codebase Reality Report (partial)", "Initial PRD Discrepancy List"]

# Task Sequencing Strategy (High-Level):
# 1. Foundational Tasks: Environment setup, V1 data pipeline validation & refactoring (if needed).
# 2. V1 Model Validation: Ensure V1 models run, validate key findings (e.g., -35% conflict impact).
# 3. V2 Core Component Implementation/Validation: Focus on data ingestion, core domain logic for panel data, and econometric model execution within V2 architecture.
# 4. V2 Policy Tool Implementation/Validation: Welfare Impact Model, Early Warning System.
# 5. API & Interface Development (V2): Implement REST/GraphQL endpoints, SSE for status.
# 6. Documentation & Reporting: Generate realistic PRD, update technical docs, create final reports.
# 7. Testing & QA: Ensure >90% test coverage, economic validity checks.
# Tasks will be grouped by components (data, models, policy tools, API) and ordered by dependencies.
# Context switching will be minimized by assigning blocks of related tasks to specialized agents if possible.
