# Methodology Notes - Key Decisions

## Data Frequency

- **Decision**: Use monthly aggregation instead of weekly
- **Rationale**: WFP data mostly monthly updates despite weekly target
- **Implication**: Adjust threshold estimation window accordingly

## Exchange Rate Mapping

- **Decision**: Map markets to control zones using spatial joins with ACAPS data
- **Method**: Point-in-polygon using geopandas
- **Challenge**: Handle markets near boundaries with buffer zones

## Panel Data Structure (RESOLVED 2025-01-28)

- **Challenge**: 3D panel (market × commodity × time) incompatible with 2D packages
- **Solution**: Three-tier methodology approach
- **Details**: See `docs/models/yemen_panel_methodology.md`

## Model Selection Hierarchy (UPDATED)

1. **Primary**: Pooled panel regression with multi-way fixed effects
   - Leverages full 3D variation
   - Market + Commodity + Time FE
   - Commodity-specific conflict interactions
2. **Secondary**: Commodity-specific threshold VECMs
   - Separate 2D panels by commodity
   - Different thresholds per commodity type
   - Clean identification within commodities
3. **Validation**: Factor-based analysis
   - PCA/Dynamic Factor Models
   - Dimension reduction approach
   - Validates pooled results

## Threshold Estimation

- **Grid search range**: 15th to 85th percentile of ECM
- **Multiple thresholds**: Sequential testing (max 3)
- **Inference**: Wild bootstrap (1000 replications)

## Spatial Weights

- **Base**: Inverse distance (200km cutoff)
- **Adjustment**: 90% penalty for crossing control boundaries
- **Network**: Inferred from price correlation (>0.8)

## Policy Simulations

- **Scenarios**: Immediate, gradual (12-month), stepped (quarterly)
- **Uncertainty**: Bootstrap fan charts
- **Sensitivity**: Vary across conflict intensity regimes

## Machine Learning Integration

- **Random Forest**: Feature importance via SHAP
- **LSTM**: 6-month ahead convergence forecasts
- **Validation**: Time series cross-validation (expanding window)

## Key Assumptions

1. Exchange rates in WFP data reflect market conditions
2. ACAPS control boundaries are accurate within districts
3. Monthly aggregation preserves essential dynamics
4. Transaction costs are symmetric within regimes
