# Claude Code Implementation Guide: Three-Tier Econometric Models

## 🎯 Project Context

You are implementing the three-tier methodology for the Yemen Market Integration project. The panel data challenge has been resolved with a comprehensive approach that handles the 3D structure (market × commodity × time).

## 📋 Implementation Overview

### Three-Tier Approach
1. **Tier 1**: Pooled panel regression with multi-way fixed effects (PRIMARY)
2. **Tier 2**: Commodity-specific threshold VECMs (SUPPORTING)
3. **Tier 3**: Factor analysis for validation (VALIDATION)

All tiers work together to provide robust, policy-relevant results.

## 🏗️ Repository Structure

```
src/yemen_market/models/
├── base.py                    # BaseEconometricModel class
├── pooled_panel/             
│   ├── __init__.py
│   ├── panel_regression.py    # Tier 1: Multi-way FE implementation
│   └── standard_errors.py     # Clustered, Driscoll-Kraay corrections
├── commodity_specific/
│   ├── __init__.py
│   ├── threshold_vecm.py      # Tier 2: Threshold models
│   ├── data_extraction.py     # Extract 2D panels by commodity
│   └── regime_analysis.py     # Analyze threshold effects
├── factor_analysis/
│   ├── __init__.py
│   ├── static_factors.py      # PCA implementation
│   ├── dynamic_factors.py     # Dynamic factor models
│   └── factor_validation.py   # Correlate with conflict
└── three_tier_analysis.py     # Main coordinator class

src/yemen_market/diagnostics/
├── panel_diagnostics.py       # 3D panel-specific tests
├── standard_errors.py         # Various SE corrections
└── robustness_checks.py       # Sensitivity analysis
```

## 📝 Implementation Steps

### Day 1: Tier 1 - Pooled Panel Setup
```python
from yemen_market.utils.logging import info, timer
from linearmodels import PanelOLS

with timer("Tier 1 setup"):
    # Create entity identifier
    df['entity'] = df['market'] + '_' + df['commodity']
    
    # Set up panel
    panel = df.set_index(['entity', 'date'])
    
    # Run regression
    model = PanelOLS(
        panel['log_price'],
        panel[['conflict_intensity', 'controls']],
        entity_effects=True,
        time_effects=True
    )
```

### Day 2: Tier 2 - Commodity Analysis
```python
# Priority commodities
commodities = ['Wheat', 'Rice', 'Sugar', 'Fuel']

for commodity in commodities:
    with timer(f"Tier 2: {commodity}"):
        # Extract 2D panel
        comm_panel = extract_commodity_panel(df, commodity)
        
        # Run threshold VECM
        model = SimpleThresholdVECM()
        model.fit(comm_panel)
```

### Day 3: Tier 3 - Factor Validation
```python
from sklearn.decomposition import PCA
from statsmodels.tsa.statespace.dynamic_factor import DynamicFactor

with timer("Tier 3: Factor analysis"):
    # Create wide matrix
    price_matrix = create_wide_matrix(df)
    
    # Extract factors
    pca = PCA(n_components=3)
    factors = pca.fit_transform(price_matrix)
    
    # Validate against conflict
    validate_factors(factors, conflict_data)
```

## 🔧 Key Implementation Details

### Data Structure Handling
- **Problem**: Standard packages expect 2D (entity × time)
- **Solution**: Create market-commodity pairs as entities for Tier 1
- **Alternative**: Extract clean 2D panels by commodity for Tier 2

### Standard Error Corrections
1. **Clustered**: Cluster at market level (default)
2. **Driscoll-Kraay**: For spatial correlation
3. **HAC**: For serial correlation
4. **Multi-way**: Cluster by market and time

### Missing Data Strategy
- Forward fill up to 2 periods
- Drop series with <50% coverage
- Document all exclusions
- Sensitivity analysis on complete cases

## 📊 Expected Results

### Tier 1: Average Effects
- Conflict coefficient: ~0.02-0.05
- Interpretation: 10 conflict events → 2-5% price increase
- Significance: p < 0.01 expected

### Tier 2: Heterogeneity
- Imported goods: Higher sensitivity
- Local goods: More resilient
- Threshold: ~50 events/month

### Tier 3: Validation
- Factor 1: National trends (60-70% variance)
- Factor 2: Conflict patterns (10-20% variance)
- Remaining: Idiosyncratic variation

## 🐛 Common Issues

### "Index contains duplicate entries"
- **Cause**: Multiple commodities per market-date
- **Solution**: Use our three-tier approach

### Memory errors with large panels
- **Cause**: Too many fixed effects
- **Solution**: Process by commodity or use absorbing regression

### Convergence failures
- **Cause**: Multicollinearity or scaling
- **Solution**: Check VIF, standardize variables

## 📈 Quality Checks

After each tier:
1. Check coefficient signs (economic theory)
2. Verify standard errors (not too small)
3. Test residuals (no patterns)
4. Compare across tiers (consistency)
5. Document all decisions

## 🎯 Success Criteria

- [ ] Tier 1 runs without errors
- [ ] Tier 2 completes for all 4 commodities
- [ ] Tier 3 explains >80% variance
- [ ] Results consistent across tiers
- [ ] Clear policy implications identified

Use enhanced logging throughout and follow CLAUDE.md development rules!