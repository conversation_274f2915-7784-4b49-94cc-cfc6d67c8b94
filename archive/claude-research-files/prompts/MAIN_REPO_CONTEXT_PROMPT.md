# Yemen Market Integration: Complete Agent Context

## 🚨 CRITICAL CONTEXT AWARENESS 🚨

**You have been spawned in the MAIN repository root** after previously working under `docs/research-methodology-package/`. This is a comprehensive Yemen Market Integration econometric research project with significant existing implementation.

**AVOID DUPLICATION**: Before implementing anything, you MUST:

1. Use `codebase-retrieval` to understand what already exists
2. Check both `src/` (main implementation) and `v2/` (architectural redesign)
3. Review `CLAUDE.md` and `CLAUDE.md.development.bak` for project context
4. Examine existing data loading in `src/yemen_market/data/` and `v2/src/`

## 🎯 ESSENTIAL PROJECT KNOWLEDGE

### Core Research Discovery

**The Paradox**: High-conflict areas in Yemen show LOWER prices, contradicting standard economic theory.

**The Solution**: Exchange rate divergence explains this revolutionary finding:
- **Houthi areas**: 535-539 YER/USD (stable, controlled)
- **Government areas**: 2,000-2,150 YER/USD (4x depreciation!)
- **Key Insight**: When analyzed in USD, the "negative price premiums" disappear

### Critical Research Rules

1. **NEVER mix YER and USD** in same analysis
2. **Exchange rate timing matters** - match price and FX dates exactly
3. **Missing data is non-random** - markets stop reporting during conflict
4. **Aid is endogenous** - requires instrumental variables
5. **Quality changes over time** - same commodity name ≠ same product

### Key Hypotheses (H1-H10)

**H1 (PRIMARY)**: Exchange rate mechanism explains price paradox
**H2**: Aid distribution affects local prices
**H3**: Demand destruction from displacement
**H4-H10**: Import channels, market power, information, storage, transport, regulation, expectations

### Technical Framework

**Three-Tier Econometric Methodology**:

```python
# Tier 1: Pooled Panel Analysis (44k+ observations)
# Fixed effects with Driscoll-Kraay standard errors
# Handles cross-sectional dependence

# Tier 2: Commodity-Specific TVECM
# Threshold Vector Error Correction Models
# Tests regime switching at conflict thresholds

# Tier 3: Policy Validation
# Factor analysis for common shocks
# Natural experiment identification
```

**Main Specification** (tests H1):

```stata
# If currency_zone coefficient ≈ 0, exchange rate mechanism proven
reg price_usd conflict global_price aid_pc i.market##i.commodity i.month, cluster(market)
```

### Critical Findings

- Market integration declined 35% since conflict began
- Price increases: 292-540% in YER, much less in USD
- Aid 26% less effective in volatile currency zones
- Natural experiments: 2020 aid cuts (35-42% price spike), 2021 fuel crisis

## 📊 Project Implementation Status

### ✅ MAJOR IMPLEMENTATIONS COMPLETE

**Main Implementation (`src/yemen_market/`)**:

- **Data Pipeline**: Complete HDX client, WFP/ACLED/ACAPS processors, spatial joins
- **Three-Tier Models**: Full econometric framework (Tier 1: Pooled Panel, Tier 2: Commodity VECM, Tier 3: Factor Analysis)
- **Enhanced Logging**: Comprehensive logging system with timers, progress bars
- **Feature Engineering**: Data preparation, panel building, quality validation
- **V3 Performance**: Polars/DuckDB optimizations for <6s analysis times

**V2 Architecture (`v2/src/`)**:

- **Clean Architecture**: Domain-driven design with hexagonal architecture
- **API Framework**: REST/GraphQL endpoints, SSE streaming, authentication
- **Infrastructure**: Docker, Kubernetes, monitoring, deployment scripts
- **Plugin System**: Extensible models and data sources

### 🔄 CURRENT FOCUS AREAS

**Before implementing anything new, validate these existing capabilities**:

1. **Data Loading**: Check `src/yemen_market/data/hdx_client.py` and `v2/src/infrastructure/`
2. **Exchange Rate Processing**: Examine existing implementations in both codebases
3. **Spatial Analysis**: Review `src/yemen_market/data/spatial_joins.py`
4. **Panel Construction**: Check `src/yemen_market/data/panel_builder.py`

### ⚠️ AVOID THESE DUPLICATIONS

- Don't recreate data downloaders (HDX client exists)
- Don't reimplement spatial distance calculations (already done)
- Don't rebuild panel data structures (PanelBuilder handles this)
- Don't create new logging systems (enhanced logging implemented)

## 📁 ESSENTIAL FILES TO REVIEW

**Context Knowledge**: `docs/research-methodology-package/working-sessions/COMPLETE_CONTEXT_DUMP.md`

**Development Rules**: `CLAUDE.md` and `CLAUDE.md.development.bak`

**Main Implementation**:

- `src/yemen_market/data/hdx_client.py` - Data loading infrastructure
- `src/yemen_market/data/panel_builder.py` - Panel construction
- `src/yemen_market/models/three_tier/` - Econometric models
- `src/yemen_market/utils/logging.py` - Enhanced logging system

**V2 Architecture**:

- `v2/src/core/domain/` - Domain models and business logic
- `v2/src/application/services/` - Analysis pipeline services
- `v2/src/infrastructure/` - Data access and external integrations

## 🔄 Integration with Existing Work

### Before Starting ANY Task

1. **Run codebase analysis**: `codebase-retrieval` to understand current implementations
2. **Check existing data**: Examine `data/processed/` for already-available datasets
3. **Review model outputs**: Look at `results/` and `reports/` for completed analyses
4. **Validate V2 progress**: Check `v2/src/` for architectural implementations

### Coordination Points

- **Data loading**: Extend `HDXClient` rather than creating new downloaders
- **Spatial analysis**: Build on `SpatialJoiner` existing capabilities
- **Panel construction**: Use `PanelBuilder` for consistent data structures
- **Model integration**: Connect to existing three-tier framework
- **Logging**: Use enhanced logging system throughout (`yemen_market.utils.logging`)

## 💡 Key Insight to Maintain

The exchange rate discovery (535 vs 2000+ YER/USD) is genuinely revolutionary. Your role is to:

1. **Validate existing implementations** support this discovery
2. **Fill theoretical gaps** without duplicating existing work
3. **Enhance capabilities** where genuine gaps exist
4. **Document integration** between old and new components

## 🎯 Success Metrics

- **Zero duplication**: No reimplementation of existing functionality
- **Enhanced capabilities**: Meaningful additions to existing framework
- **Clear documentation**: Integration points and enhancement rationale
- **Empirical readiness**: Support for testing the exchange rate mechanism

## 🚀 COMPREHENSIVE WORKFLOW

### Step 1: Context Analysis (MANDATORY)

```python
# Always start with understanding existing capabilities
from yemen_market.data.hdx_client import HDXClient
from yemen_market.data.wfp_processor import WFPProcessor
from yemen_market.models.three_tier.integration import ThreeTierAnalysis

# Check what's already implemented
client = HDXClient()
# Test existing functionality before extending
```

### Step 2: Gap Identification

1. **Review existing implementations** with `codebase-retrieval`
2. **Check data availability** in `data/processed/`
3. **Examine model outputs** in `results/` and `reports/`
4. **Identify genuine gaps** vs existing capabilities
5. **Document integration points** clearly

### Step 3: Strategic Implementation

- **Extend existing classes** rather than creating new ones
- **Use established patterns** from `CLAUDE.md.development.bak`
- **Maintain compatibility** with existing test suite
- **Follow three-tier methodology** for all econometric work
- **Use enhanced logging** throughout (`yemen_market.utils.logging`)

### Step 4: Validation & Integration

- **Test with existing data** to ensure compatibility
- **Run existing test suite** to verify no regressions
- **Document all changes** with clear rationale
- **Ensure H1-H10 testability** with enhancements

## 📋 Development Standards

- **Follow CLAUDE.md.development.bak** for coding standards
- **Use enhanced logging** throughout (`yemen_market.utils.logging`)
- **Maintain test coverage** (>90% required)
- **Document all changes** with clear integration rationale
- **Respect existing architecture** in both src/ and v2/

---

*Remember: You're enhancing a sophisticated existing system, not building from scratch. Respect the existing architecture and build upon it strategically.*
