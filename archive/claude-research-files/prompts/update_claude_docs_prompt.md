# Prompt for Updating CLAUDE.md and .claude/ Folder

Use this prompt with Claude Code to maintain and update the project's Claude-specific documentation.

## Full Update Prompt

```
I need to update the Yemen Market Integration project's Claude documentation. Please help me:

1. **Review Current State**:
   - Check CLAUDE.md for outdated information
   - Review .claude/project_memory.md for latest progress
   - Check .claude/ACTIVE_CONTEXT.md for current focus
   - Review .claude/tasks/ for completed/pending tasks

2. **Update CLAUDE.md**:
   - Ensure critical rules are at the top (no changes to these)
   - Update the "Current Development Phase" section
   - Add any new patterns or anti-patterns discovered
   - Update code examples if APIs have changed
   - Add new troubleshooting items if needed

3. **Update .claude/project_memory.md**:
   - Add today's session accomplishments
   - Update development status percentages
   - Add any new decisions made
   - Update next steps based on completion
   - Add new blockers or limitations discovered

4. **Update .claude/ACTIVE_CONTEXT.md**:
   - Set current week/phase
   - List immediate tasks (next 3-5 items)
   - Update completion percentages
   - Add any urgent issues or blockers

5. **Update Task Tracking**:
   - <PERSON> completed tasks in .claude/tasks/current_sprint.md
   - Create new sprint file if transitioning weeks
   - Update .claude/models/week[X]_tasks.md with progress

6. **Ensure Consistency**:
   - No duplicate information across files
   - Follow documentation hierarchy rules
   - Maintain single source of truth principles
   - Update cross-references between files

Current status to incorporate:
[ADD YOUR SPECIFIC UPDATES HERE]

Please make all updates while preserving the existing structure and following the project's documentation standards.
```

## Quick Update Prompts

### After Completing a Major Feature
```
Update .claude/ folder after completing [FEATURE NAME]:
- Add accomplishment to project_memory.md
- Update completion % in ACTIVE_CONTEXT.md
- Mark tasks complete in current_sprint.md
- Add any new patterns learned to CLAUDE.md
- Document any new limitations discovered
```

### Starting a New Week/Phase
```
Transition to Week [X] - [PHASE NAME]:
- Create .claude/tasks/week[X]_sprint.md from template
- Update ACTIVE_CONTEXT.md with new focus
- Archive completed tasks
- Update CLAUDE.md "Current Development Phase"
- Add transition notes to project_memory.md
```

### After Discovering Issues/Solutions
```
Document new [issue/solution] in .claude/:
- Add to troubleshooting in CLAUDE.md
- Update known limitations in project_memory.md
- Add workaround to coding_standards.md if applicable
- Update methodology_notes.md if affects approach
```

### End of Day Summary
```
End of day update for .claude/ (Date: [DATE]):
1. What was completed today?
2. What blockers were encountered?
3. What's the immediate next task?
4. Any new insights or patterns?
5. Updated completion percentage?

Update project_memory.md, ACTIVE_CONTEXT.md, and current_sprint.md accordingly.
```

## Specific File Update Templates

### For CLAUDE.md
```
Update CLAUDE.md section [SECTION_NAME]:
- Keep critical rules unchanged
- Add new example: [DESCRIPTION]
- Update anti-pattern: [DESCRIPTION]
- Add troubleshooting: [ISSUE] -> [SOLUTION]
```

### For project_memory.md
```
Add to project_memory.md session context:
Date: [DATE]
- Completed: [LIST]
- Discovered: [LIST]
- Blocked by: [LIST]
- Next steps: [LIST]
- Key decisions: [LIST]
```

### For task tracking
```
Update sprint progress:
- [x] Completed: [TASK]
- [~] In progress: [TASK] (X% done)
- [ ] Blocked: [TASK] - [REASON]
- [ ] Next: [TASK]
```

## Rules for Updates

1. **Never duplicate information** - each fact lives in exactly one place
2. **Maintain hierarchy** - project_memory.md > ACTIVE_CONTEXT.md > sprint files
3. **Use consistent formatting** - follow existing patterns in each file
4. **Add dates** to all progress updates
5. **Link related items** with relative paths
6. **Keep critical rules sacred** - never modify the mandatory rules in CLAUDE.md

## Verification Checklist

After updates, verify:
- [ ] No information is duplicated across files
- [ ] All cross-references are valid
- [ ] Dates are added to new entries
- [ ] Completion percentages are consistent
- [ ] Task status matches across files
- [ ] Documentation hierarchy is maintained
- [ ] Critical rules remain unchanged
