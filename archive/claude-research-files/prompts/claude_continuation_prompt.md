# Prompt to Brief New Claude Instance

Copy and paste this prompt when starting a new conversation:

---

## Context: Yemen Market Integration Project - Continuing Development

I'm working on the Yemen Market Integration project, which analyzes food market dynamics in conflict-affected Yemen using World Bank-standard econometric methods. 

**Recent Work Completed**: Just finished migrating the diagnostic framework to integrate with the three-tier modeling architecture. All World Bank-standard tests (W<PERSON>dridge, Pesaran CD, etc.) are now automatically executed and corrections are applied when tests fail.

**Project Structure**:
- Three-tier econometric framework: Pooled → Threshold → Factor Analysis
- Located in: `src/yemen_market/models/three_tier/`
- New diagnostics: `src/yemen_market/models/three_tier/diagnostics/`
- Main runner: `src/yemen_market/models/three_tier/integration/three_tier_runner.py`

**Key Context Files**:
1. `.claude/ACTIVE_CONTEXT.md` - Current project state
2. `CLAUDE.md` - Coding standards (MUST follow)
3. `reports/diagnostic_migration_summary.md` - Latest work
4. `.claude/models/diagnostic_testing_framework.md` - Diagnostic details

**Important Standards**:
- Enhanced logging only (no print statements)
- Complete implementations (no placeholders/TODOs)
- Type hints required
- Test coverage > 90%
- ResultsContainer interface for all model outputs

**Current Priorities**:
1. Performance optimization for large datasets
2. Enhanced reporting (LaTeX tables)
3. API documentation updates
4. Interactive diagnostic dashboards

Please review the context files mentioned above to understand the current state. The project must maintain World Bank publication standards throughout.

What specific aspect would you like me to work on?

---

## Instructions for Using This Prompt:

1. Copy everything between the "---" markers
2. Paste as your first message to the new Claude instance
3. Claude will read the context files and understand the project state
4. You can then give specific tasks or continue development

## Additional Context Commands for Claude:

After the initial prompt, you can ask Claude to:
- "Review the diagnostic framework implementation"
- "Show me the current three-tier model structure"
- "Run the diagnostic integration tests"
- "Explain the ResultsContainer interface"
- "Check what needs to be done next according to ACTIVE_CONTEXT.md"
