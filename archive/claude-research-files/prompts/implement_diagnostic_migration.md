# Prompt: Implement Diagnostic Framework Migration for Three-Tier Models

## Context

You are implementing a critical diagnostic framework migration for the Yemen Market Integration project. The existing diagnostic modules (`worldbank_diagnostics.py` and `test_battery.py`) contain valuable econometric test implementations but are architecturally incompatible with the three-tier framework. Your task is to create a new diagnostic system that integrates seamlessly with the `ResultsContainer` interface while preserving the econometric rigor.

## Background

- **Current State**: Legacy diagnostic modules expect statsmodels-style objects (`.resid`, `.model.exog`)
- **Target State**: Three-tier models use `ResultsContainer` with different interfaces
- **Critical Gap**: No World Bank-standard tests (Wooldridge, Pesaran CD) are running in production

## Your Mission

### Phase 1: Create Core Diagnostic Framework

1. **Create the diagnostic module structure**:
   ```
   src/yemen_market/models/three_tier/diagnostics/
   ├── __init__.py
   ├── panel_diagnostics.py      # Main orchestrator
   ├── test_implementations.py   # Econometric tests
   ├── diagnostic_adapters.py    # ResultsContainer adapters
   └── diagnostic_reports.py     # Report generation
   ```

2. **Implement `ThreeTierPanelDiagnostics` class** in `panel_diagnostics.py`:
   - Must work with `ResultsContainer` interface
   - Run tier-specific diagnostic batteries
   - Store results back in ResultsContainer
   - Generate publication-ready reports

3. **Create `DiagnosticAdapter` class** in `diagnostic_adapters.py`:
   - Extract residuals: `results.get_residuals()`
   - Get panel structure from stored statistics
   - Reconstruct design matrices if needed
   - Handle different result formats across tiers

### Phase 2: Implement Critical Tests

Focus on the **MOST CRITICAL** tests first:

1. **Wooldridge Test** (Panel Serial Correlation):
   ```python
   def _wooldridge_serial_correlation(self, residuals, panel_info):
       """
       Wooldridge (2002) test for serial correlation in panel data.
       This is CRITICAL for valid inference in panels.
       """
       # Implementation must handle entity-time structure
       # Create lagged residuals within panels
       # Apply first-difference transformation
       # Return test statistic, p-value, and recommendation
   ```

2. **Pesaran CD Test** (Cross-sectional Dependence):
   ```python
   def _pesaran_cd_test(self, residuals, panel_info):
       """
       Pesaran (2004) test for cross-sectional dependence.
       Essential given spatial nature of Yemen markets.
       """
       # Reshape residuals to wide format
       # Calculate pairwise correlations
       # Compute CD statistic
       # Return results with Driscoll-Kraay recommendation if failed
   ```

3. **Im-Pesaran-Shin Test** (Panel Unit Roots):
   ```python
   def _ips_unit_root_test(self, series, panel_info):
       """
       Im-Pesaran-Shin (2003) panel unit root test.
       Required before panel regression.
       """
       # Run ADF for each panel
       # Combine into IPS statistic
       # Handle unbalanced panels
   ```

### Phase 3: Integrate with Three-Tier Runner

Modify `src/yemen_market/models/three_tier/integration/three_tier_runner.py`:

```python
def run_tier1_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
    """Run Tier 1 with automatic diagnostics."""
    
    # Fit model
    model = PooledPanelModel(self.tier1_config)
    model.fit(data)
    
    # NEW: Run diagnostics automatically
    if self.run_diagnostics:  # Default True
        diagnostics = ThreeTierPanelDiagnostics(tier=1)
        diag_report = diagnostics.run_diagnostics(
            model.results, 
            data,
            self.diagnostic_config
        )
        
        # Check for critical failures
        if diag_report.has_critical_failures():
            warning("Critical diagnostic failures detected")
            # Apply automatic corrections
            self._apply_diagnostic_corrections(model, diag_report)
            # Re-run model with corrections
    
    return {
        'model': model,
        'results': model.results,
        'diagnostics': diag_report  # NEW
    }
```

### Phase 4: Preserve Valuable Logic

Extract and adapt the good econometric logic from `worldbank_diagnostics.py`:

1. **DO NOT** copy the entire class structure (it's incompatible)
2. **DO** extract the statistical test implementations
3. **DO** preserve the test interpretations and recommendations
4. **DO** maintain the publication standards

Example adaptation:
```python
# OLD (from worldbank_diagnostics.py)
if hasattr(self.model, 'resid'):
    residuals = self.model.resid

# NEW (for three-tier)
residuals = self.results.get_residuals()
if residuals is None:
    residuals = self.results.residuals  # Fallback
```

### Phase 5: Add Deprecation Warnings

In the legacy modules, add:

```python
import warnings

warnings.warn(
    "This module is deprecated and will be removed in v2.0. "
    "Use yemen_market.models.three_tier.diagnostics instead.",
    DeprecationWarning,
    stacklevel=2
)
```

## Implementation Requirements

### Must Have:
- [ ] Wooldridge test working with ResultsContainer
- [ ] Pesaran CD test working with ResultsContainer
- [ ] Automatic execution in three_tier_runner
- [ ] Results stored in ResultsContainer.diagnostics
- [ ] Clear remediation messages when tests fail

### Should Have:
- [ ] Panel unit root tests (IPS)
- [ ] Heteroskedasticity tests for panels
- [ ] Diagnostic report generation
- [ ] LaTeX table export for publication

### Nice to Have:
- [ ] Parallel test execution
- [ ] Caching of test results
- [ ] Interactive diagnostic dashboard

## Testing Your Implementation

1. **Unit Tests**: Each diagnostic test should have unit tests
2. **Integration Test**: Full three-tier run with diagnostics
3. **Validation**: Compare results with R's plm package or Stata

Test example:
```python
def test_wooldridge_with_known_data():
    """Test Wooldridge against known result."""
    # Use Grunfeld data or similar benchmark
    # Verify test statistic matches published values
```

## Success Criteria

Your implementation is successful when:

1. ✅ Running `scripts/analysis/run_three_tier_models.py` automatically runs diagnostics
2. ✅ Diagnostic failures trigger appropriate warnings and corrections
3. ✅ Results include a `diagnostics` section with all test results
4. ✅ No imports from legacy diagnostic modules in production code
5. ✅ Test coverage > 90% for diagnostic modules

## Code Style Requirements

Follow CLAUDE.md guidelines:
- Use enhanced logging (not print statements)
- Complete implementation (no placeholders)
- Type hints on all functions
- NumPy-style docstrings
- Handle all edge cases

## References

Use these for test implementations:
- Wooldridge (2002) "Econometric Analysis of Cross Section and Panel Data" - Chapter 10
- Pesaran (2004) "General Diagnostic Tests" - Cambridge Working Paper
- Driscoll & Kraay (1998) - For spatial correlation corrections
- Python: linearmodels documentation for comparison

## Final Notes

- **Priority**: Wooldridge and Pesaran CD tests are CRITICAL
- **Compatibility**: Must work with existing ResultsContainer
- **Performance**: Tests should complete in < 5 seconds for typical data
- **Documentation**: Update `.claude/models/diagnostic_testing_framework.md` as you implement

Remember: This is for World Bank publication standards. The diagnostics must be rigorous, well-documented, and produce clear recommendations when tests fail.