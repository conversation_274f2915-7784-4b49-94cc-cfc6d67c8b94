# Task 16: Implement V2 Real-time Analysis Status Endpoint (SSE)

## Context Window Management
- **Essential files to read:**
  - `v2/src/interfaces/api/rest/routes/analysis.py` (or relevant API route file).
  - `v2/src/infrastructure/messaging/event_bus.py` (to understand event publishing).
  - `v2/src/application/services/` (to see how analysis services might publish progress events).
  - `docs/PRD_Yemen_Market_Integration.md` (Appendix C for API endpoint spec, FR-30, Section 9.3).
  - Output of Task 04 (V2 Codebase Analysis) and Task 11 (V2 API Review).
- **Key dependencies to understand:**
  - FastAPI's `StreamingResponse` and Server-Sent Events (SSE) protocol.
  - `asyncio` for handling concurrent operations and event streams.
  - How to subscribe to relevant events from the `AsyncEventBus`.
- **Relevant test files:**
  - New tests in `v2/tests/interfaces/api/` for the SSE endpoint.
- **Output expectations:**
  - A functional SSE endpoint at `GET /api/v2/analyses/{id}/status`.
  - The endpoint should stream progress updates for a given analysis ID.
  - Documentation for using the SSE endpoint.

## Economic Context
- **Why this component matters for Yemen analysis:** For potentially long-running econometric analyses, providing real-time feedback to the user (e.g., on a web dashboard) is crucial for user experience and for monitoring progress.
- **Expected econometric behavior:** N/A.
- **Policy implications of this component:** Faster and more transparent access to analysis status can help policymakers and researchers track ongoing work.

## Technical Scope
- **Input data structure:** Analysis ID (path parameter).
- **Processing requirements:**
  - Define a FastAPI route for `GET /api/v2/analyses/{id}/status`.
  - The route handler should return a `StreamingResponse` with `media_type="text/event-stream"`.
  - Implement logic to:
    - Subscribe to progress events related to the specified `analysis_id` from the `AsyncEventBus`.
    - Format these events as SSE messages (e.g., `data: {"status": "running", "progress": 25}\n\n`).
    - Stream these messages to the client.
    - Handle connection lifecycle (client disconnects, analysis completion/failure).
  - Ensure appropriate error handling if the analysis ID is not found or if the event bus is unavailable.
- **Output format:**
  - SSE stream.
- **Integration points:**
  - With V2 analysis services that publish progress events to the `AsyncEventBus`.
  - Potentially with a V2 frontend/dashboard that consumes this SSE stream.

## Success Criteria
- [ ] The SSE endpoint `GET /api/v2/analyses/{id}/status` is implemented.
- [ ] The endpoint correctly streams progress events for a simulated or actual analysis.
- [ ] Events are formatted according to SSE protocol.
- [ ] The endpoint handles client disconnects gracefully.
- [ ] Unit/integration tests are created for the SSE endpoint.
- [ ] Basic documentation for the endpoint is provided.

## Memory Bridge
- **Key variables/constants defined:** N/A.
- **API contracts established:** The SSE event format for progress updates.
- **Data structures created:** N/A.
- **Identified Gaps/Issues:** N/A (this task implements a feature).
- **Validated Platform Capabilities:** Real-time analysis status updates via SSE in V2.
