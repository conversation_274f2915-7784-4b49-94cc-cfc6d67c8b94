# Main Econometric Specifications

## Spec 1: Basic Price Regression (YER)
```stata
reg log(price_yer) conflict exchange_rate log(global_price) aid_pc ///
    i.market##i.commodity i.month i.year, ///
    cluster(market)
```
**Purpose**: Baseline in local currency  
**Expected**: Negative conflict coefficient (puzzling)

## Spec 2: USD Price Analysis
```stata
reg log(price_usd) conflict log(global_price) aid_pc ///
    i.market##i.commodity i.month i.year, ///
    cluster(market)
```
**Purpose**: Test if puzzle disappears in USD  
**Expected**: No significant conflict effect

## Spec 3: Law of One Price Test
```stata
gen price_diff_usd = abs(price_usd_i - price_usd_j)
reg price_diff_usd distance_ij same_control_ij conflict_diff_ij ///
    i.commodity i.month, cluster(market_pair)
```
**Purpose**: Test market integration in USD  
**Expected**: Distance matters, control zones don't

## Spec 4: Triple Difference (Currency Zone × Post × Import)
```stata
gen post = (date >= "2020m1")
gen houthi = (control_zone == "Houthi")
gen import_dependent = (import_share > 0.8)

reg log(price_yer) houthi##post##import_dependent ///
    log(global_price) aid_pc ///
    i.market##i.commodity i.month, ///
    cluster(market)
```
**Purpose**: Identify exchange rate channel  
**Expected**: Triple interaction negative and significant

## Spec 5: IV for Conflict
```stata
* First stage
reg conflict conflict_neighbors distance_frontline ///
    i.market i.month, cluster(market)
predict conflict_hat

* Second stage  
reg log(price_yer) conflict_hat exchange_rate log(global_price) ///
    i.market##i.commodity i.month, cluster(market)
```
**Purpose**: Address endogeneity  
**Expected**: Stronger negative effect

## Spec 6: Exchange Rate Pass-Through
```stata
reg d.log(price_yer) d.log(exchange_rate) d.log(global_price) ///
    l.log(price_yer) i.market##i.commodity i.month, ///
    cluster(market)
```
**Purpose**: Short vs long-run pass-through  
**Expected**: Incomplete pass-through (<1)

## Spec 7: Aid Impact with Controls
```stata
* Instrument aid with predetermined allocation
reg aid_pc population_pre deaths_lagged un_presence ///
    i.governorate i.month
predict aid_hat

reg log(price_yer) conflict exchange_rate aid_hat ///
    log(global_price) i.market##i.commodity i.month, ///
    cluster(market)
```
**Purpose**: Clean aid effect  
**Expected**: Negative aid coefficient

## Spec 8: Regression Discontinuity at Currency Border
```stata
rd log(price_yer) distance_to_currency_border, ///
    fuzzy(houthi_control) ///
    kernel(triangular) ///
    bwselect(mserd)
```
**Purpose**: Causal effect of currency regime  
**Expected**: Jump at boundary

## Robustness Checks for Each Spec
1. Winsorize at 1%, 5%
2. Exclude capital cities
3. Balanced panel only
4. Different clustering (governorate, two-way)
5. Add time trends
6. Commodity-by-commodity
7. Pre/post 2021 samples
8. Alternative conflict measures