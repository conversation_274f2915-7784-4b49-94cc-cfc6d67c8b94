# Main Specification 2: USD Prices

metadata:
  id: main_2_usd
  name: "Price Regression in USD - Law of One Price Test"
  version: 1.0
  created: 2025-01-31
  author: "Research Team"

specification:
  dependent_var: "log_price_usd"
  
  independent_vars:
    - conflict: "log_conflict"
    - global: "log(global_price)"
    - aid: "aid_pc"
    # Note: No exchange rate needed when using USD prices
  
  fixed_effects:
    - market_commodity: "market##commodity"
    - time: "year_month"
  
  cluster: "market"
  
  sample_restrictions:
    - "year >= 2019"
    - "year <= 2025"
    - "!price_usd.isna()"
    - "market in balanced_panel_markets"

output:
  tables:
    - format: "latex"
      stars: true
      se_below: true
      compare_with: "main_1_yer"  # Side-by-side comparison
  
  figures:
    - coefplot: true
      exclude: ["fixed_effects"]
      compare_with: "main_1_yer"
  
  save:
    - coefficients: "csv"
    - full_results: "pickle"
    - comparison: "xlsx"

hypothesis_tests:
  h1_exchange_rate:
    test: "conflict coefficient should be insignificant"
    interpretation: "Law of one price holds in USD"
    compare_to: "main_1_yer conflict coefficient"
  
  h2_global_passthrough:
    test: "global_price coefficient close to 1"
    interpretation: "Full pass-through in USD terms"

diagnostics:
  - breusch_pagan: "heteroskedasticity"
  - wooldridge: "serial_correlation"
  - pesaran_cd: "cross_sectional_dependence"

notes: |
  This specification tests whether the negative conflict premium
  disappears when prices are measured in USD, supporting the
  exchange rate mechanism hypothesis.