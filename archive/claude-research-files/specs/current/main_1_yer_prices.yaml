# Main Specification 1: YER Prices

metadata:
  id: main_1_yer
  name: "Basic Price Regression in Local Currency"
  version: 1.0
  created: 2025-01-31
  author: "Research Team"

specification:
  dependent_var: "log_price_yer"
  
  independent_vars:
    - conflict: "log_conflict"
    - exchange: "log(fx_official)"
    - global: "log(global_price)"
    - aid: "aid_pc"
  
  fixed_effects:
    - market_commodity: "market##commodity"
    - time: "year_month"
  
  cluster: "market"
  
  sample_restrictions:
    - "year >= 2019"
    - "year <= 2025"
    - "!price_yer.isna()"
    - "market in balanced_panel_markets"

output:
  tables:
    - format: "latex"
      stars: true
      se_below: true
    - format: "text"
      verbose: true
  
  figures:
    - coefplot: true
      exclude: ["fixed_effects"]
  
  save:
    - coefficients: "csv"
    - full_results: "pickle"
    - log: "text"

hypothesis_tests:
  h1_exchange_rate:
    test: "conflict coefficient should be negative"
    interpretation: "Reflects exchange rate divergence"
  
  h2_aid:
    test: "aid_pc coefficient should be negative"
    interpretation: "Aid depresses local prices"

robustness:
  - winsorize: [0.01, 0.05]
  - exclude: ["Sanaa City", "Aden City"]
  - cluster: ["governorate", "market_month"]
  - weights: "population"