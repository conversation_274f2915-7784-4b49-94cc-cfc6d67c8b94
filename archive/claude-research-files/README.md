# .claude Directory - Yemen Market Integration Research

## Overview
This directory implements best practices for managing an econometric research project with <PERSON>. It combines Model Context Protocol (MCP) standards with research-specific workflows.

## Directory Structure

```
.claude/
├── memory/              # Context persistence (short & long-term)
│   ├── active.md       # Current session context
│   ├── knowledge.md    # Accumulated project insights
│   └── decisions.md    # Key decisions and rationale
│
├── commands/           # Custom slash commands for research tasks
│   ├── run-spec.md    # /run-spec [id] - Execute specification
│   ├── log-result.md  # /log-result - Document regression results
│   ├── update-hyp.md  # /update-hyp [id] - Update hypothesis
│   └── gen-table.md   # /gen-table [spec] - Generate LaTeX table
│
├── specs/             # Econometric specifications (versioned)
│   ├── current/       # Active specifications
│   ├── archive/       # Previous versions with dates
│   └── README.md      # Specification naming convention
│
├── results/           # Structured result storage
│   ├── YYYY-MM-DD/    # Daily result folders
│   ├── summary.json   # Aggregated results metadata
│   └── README.md      # Result logging guidelines
│
├── config/            # Project configuration
│   ├── variables.yaml # Variable definitions & transformations
│   ├── settings.json  # Output preferences, paths
│   └── constants.py   # Reusable code constants
│
├── hypotheses/        # Hypothesis tracking
│   ├── h1_exchange_rate.md   # Primary hypothesis
│   ├── h2_aid_channel.md     # Secondary hypotheses
│   └── evidence_matrix.csv   # Summary of evidence
│
├── literature/        # Literature integration
│   ├── key_papers/    # PDFs and notes
│   ├── citations.bib  # Bibliography
│   └── findings.md    # Relevant findings mapped to our work
│
├── instruments/       # IV definitions and validation
│   ├── spatial_conflict.md    # Instrument documentation
│   ├── validation_tests.md    # First-stage results
│   └── README.md             # IV strategy overview
│
├── robustness/        # Robustness tracking
│   ├── checklist.md   # Master checklist
│   ├── completed/     # Completed robustness checks
│   └── planned/       # Planned sensitivity analyses
│
├── paper/            # Paper development
│   ├── outline.md    # Dynamic outline linked to results
│   ├── tables/       # Table specifications
│   ├── figures/      # Figure specifications
│   └── draft/        # Working draft sections
│
├── templates/        # Consistent documentation templates
│   ├── result_entry.md       # For logging results
│   ├── hypothesis_update.md  # For updating hypotheses
│   ├── literature_note.md    # For paper summaries
│   └── checkpoint.md         # For milestone documentation
│
├── checkpoints/      # Research milestones
│   ├── YYYY-MM-DD_description.md  # Snapshot of full context
│   └── README.md                  # When to create checkpoints
│
├── validation/       # Reproducibility
│   ├── replication/  # Scripts to replicate all results
│   ├── data_checks/  # Data quality validation
│   └── code_review/  # Code verification logs
│
├── data_quality/     # Data documentation
│   ├── coverage_reports/     # Missing data analysis
│   ├── anomaly_detection/    # Outlier documentation
│   └── merge_validation/     # Join quality checks
│
├── .mcp.json         # MCP server configuration
├── .gitignore        # Ignore results, keep templates
└── README.md         # This file
```

## Best Practices

### 1. Memory Management
- **Short-term**: Update `memory/active.md` at start/end of each session
- **Long-term**: Add key insights to `memory/knowledge.md` weekly
- **Decisions**: Document why choices were made in `memory/decisions.md`

### 2. Context Window Optimization
- Keep `active.md` under 2000 lines
- Archive completed work to `checkpoints/`
- Use references rather than duplicating content

### 3. Reproducibility
- Every result must have a `validation/replication/` script
- Use semantic versioning for specifications
- Document all data transformations

### 4. Workflow Integration
```bash
# Start new session
.claude/scripts/new_session.sh

# Log a result
/log-result spec_1a

# Update hypothesis
/update-hyp h1 --evidence positive --strength strong

# Create checkpoint
.claude/scripts/checkpoint.sh "Completed exchange rate analysis"
```

### 5. Version Control
- Track: configs, templates, specs, hypotheses
- Ignore: results/YYYY-MM-DD/, paper/draft/
- Tag: major milestones (v1-data-complete, v2-main-results)

## Quick Start

1. **New Session**:
   ```bash
   cat .claude/memory/active.md  # Review current context
   # Update with today's goals
   ```

2. **Run Analysis**:
   ```bash
   /run-spec main_1  # Execute specification
   /log-result       # Document results
   /update-hyp h1    # Update hypothesis
   ```

3. **End Session**:
   ```bash
   # Update memory/active.md with progress
   # Add insights to memory/knowledge.md
   ```

## MCP Configuration

The `.mcp.json` file configures research-specific tools:
```json
{
  "mcpServers": {
    "stata-server": {
      "command": "stata-mcp",
      "args": ["--interactive"],
      "env": {"STATA_PATH": "/usr/local/stata17"}
    },
    "data-validator": {
      "command": "data-quality-mcp",
      "args": ["--config", ".claude/config/validation.yaml"]
    }
  }
}
```

## Integration with Main Project

- `CLAUDE.md` in project root contains high-level instructions
- `.claude/` contains detailed research management
- Results feed into `results/` in main project
- Paper outputs go to `docs/paper/` in main project

## Maintenance

- Weekly: Archive old results, update knowledge.md
- Monthly: Create checkpoint, review hypotheses
- Quarterly: Major version tag, clean archives

---
*Based on Claude/MCP best practices and econometric research workflows*
*Last updated: January 2025*