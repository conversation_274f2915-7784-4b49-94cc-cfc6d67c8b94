{"project": {"name": "yemen-market-integration-v2", "type": "econometric-analysis-platform", "version": "2.0.0", "repository": "https://github.com/star-boy-95/yemen-market-integration-v2", "private": true}, "claude_code": {"enabled": true, "working_directory": "/Users/<USER>/Documents/GitHub/yemen-market-integration", "context_files": ["docs/PRD_Yemen_Market_Integration_Refactored.md", "README.md", "METHODOLOGY.md"], "ignore_patterns": ["node_modules/**", "venv/**", "*.pyc", "__pycache__/**", ".git/**"]}, "task_master": {"enabled": true, "prd_file": "docs/PRD_Yemen_Market_Integration_Refactored.md", "task_directory": ".taskmaster/tasks", "auto_commit": true, "commit_prefix": "[TM]"}, "integration": {"mode": "claude-code-cli", "batch_size": 5, "auto_test": true, "auto_lint": true}}