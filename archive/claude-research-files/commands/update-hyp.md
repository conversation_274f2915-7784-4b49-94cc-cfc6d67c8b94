Update hypothesis with new evidence from recent results.

Usage: /update-hyp $HYPOTHESIS_ID --evidence [positive|negative|mixed] --strength [strong|moderate|weak]

Actions:
1. Open `.claude/hypotheses/${HYPOTHESIS_ID}.md`
2. Add new evidence entry with:
   - Date and result reference
   - Direction and strength of evidence
   - Specific coefficients/tests
   - Interpretation
3. Update overall hypothesis status
4. Regenerate evidence matrix
5. Flag if hypothesis needs revision

Examples:
- /update-hyp h1 --evidence positive --strength strong
- /update-hyp h2_aid --evidence mixed --strength moderate

The command should also:
- Check for contradicting evidence
- Suggest additional tests if evidence is mixed
- Update paper outline if hypothesis status changes significantly