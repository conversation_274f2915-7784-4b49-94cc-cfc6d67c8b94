Log regression results with full documentation.

Creates a structured result entry in `.claude/results/YYYY-MM-DD/` with:
- Specification details
- Main coefficients and significance
- Model statistics (R², F-stat, N)
- Interpretation notes
- Links to output files

Interactive prompts for:
1. Which specification was run?
2. Key findings (significant coefficients)
3. Surprises or concerns
4. Which hypothesis does this support/refute?
5. What robustness checks are needed?

The result entry should follow the template in `.claude/templates/result_entry.md` and automatically update:
- Evidence matrix for relevant hypotheses
- Paper outline with table/figure placement
- Next steps in active.md