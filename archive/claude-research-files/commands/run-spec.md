Run econometric specification $SPEC_ID from the specs/current/ directory.

Steps:
1. Load the specification from `.claude/specs/current/${SPEC_ID}.yaml`
2. Execute the regression with proper error handling
3. Generate standard output tables
4. Log results to `.claude/results/YYYY-MM-DD/`
5. Update hypothesis evidence if relevant

Example usage:
- /run-spec main_1
- /run-spec robustness_3a
- /run-spec iv_spatial

The command should:
- Check data availability
- Run specification exactly as defined
- Calculate robust standard errors
- Generate both text and LaTeX output
- Save all results with metadata