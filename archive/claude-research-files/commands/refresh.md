Refresh my memory about the Yemen Market Integration project by:

1. Updating the project context:

```bash
cd /Users/<USER>/Documents/GitHub/yemen-market-integration && make update-claude-commands
```

2. Reading the updated context from `.claude/commands/project_context.md`

3. Providing me with the complete project overview including:
   - Project purpose (econometric analysis of Yemen markets in conflict)
   - Three-tier methodology (pooled panel → commodity VECM → factor analysis)
   - Current progress percentage and phase
   - Implementation status of all models
   - Test coverage and statistics
   - Critical development rules (enhanced logging, no temp files, complete implementations)
   - Current sprint focus and next steps
   - Recent git activity

This single command refreshes everything I need to know about the project state.
