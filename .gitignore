# Claude AI Assistant
.claude/
**/.claude/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Jupyter
.ipynb_checkpoints/
*.ipynb_checkpoints

# Data - track only raw data structure
data/interim/
data/processed/
data/external/
!data/raw/.gitkeep
!data/interim/.gitkeep
!data/processed/.gitkeep
!data/external/.gitkeep

# Environment
.env
venv/
.venv/
env/
ENV/
conda-env/
.python-version

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.hypothesis/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Documentation
docs/_build/
docs/_static/
docs/_templates/

# Reports (keep structure, not content)
reports/figures/*.png
reports/figures/*.pdf
reports/tables/*.tex
reports/tables/*.csv
reports/drafts/*.pdf
reports/final/*.pdf
!reports/**/.gitkeep

# Logs
*.log
logs/

# Model outputs
*.pkl
*.joblib
*.h5
*.pt
*.pth
# Remove models/ entry to allow docs/models to be tracked
# !src/yemen_market/models/

# Temporary files
*.tmp
tmp/
temp/

# Added by Claude Task Master
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 