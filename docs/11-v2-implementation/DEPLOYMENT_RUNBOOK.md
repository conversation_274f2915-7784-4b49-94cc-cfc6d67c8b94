# Yemen Market Integration V2 - Deployment Runbook

## Quick Reference

| Emergency Action | Command |
|------------------|---------|
| **Emergency Rollback** | `./scripts/rollback-automation.sh full` |
| **Traffic Rollback** | `./scripts/rollback-automation.sh traffic` |
| **Check V2 Health** | `kubectl get pods -n yemen-market-v2` |
| **Check V1 Health** | `kubectl get pods -n yemen-market` |
| **View Deployment Logs** | `kubectl logs -n yemen-market-v2 deployment/yemen-market-api` |

## Deployment Execution Commands

### 1. Pre-Deployment Setup

```bash
# Set environment variables
export CLUSTER_NAME="yemen-market-prod"
export V2_IMAGE_TAG="v2.1.0"
export ENVIRONMENT="production"
export SLACK_WEBHOOK="https://hooks.slack.com/services/YOUR/WEBHOOK/URL"

# Verify cluster access
kubectl cluster-info
aws eks update-kubeconfig --name $CLUSTER_NAME --region us-east-1

# Check prerequisites
kubectl get nodes
kubectl get ns
kubectl get pods -n yemen-market
```

### 2. Monitoring Setup

```bash
# Deploy monitoring stack
./scripts/deployment-monitoring.sh

# Verify monitoring
kubectl get pods -n monitoring
kubectl get servicemonitors -n monitoring

# Access monitoring URLs
echo "Grafana: https://grafana.yemen-market.prod.com"
echo "Prometheus: https://prometheus.yemen-market.prod.com"
```

### 3. Production Deployment

```bash
# Execute full deployment
./scripts/production-deployment.sh

# Monitor deployment progress
kubectl get pods -n yemen-market-v2 -w
kubectl rollout status deployment/yemen-market-api -n yemen-market-v2
```

### 4. Traffic Migration Commands

```bash
# Check current traffic distribution
kubectl get ingress yemen-market-canary-ingress -n yemen-market-v2 \
  -o jsonpath='{.metadata.annotations.nginx\.ingress\.kubernetes\.io/canary-weight}'

# Manual traffic control (if needed)
kubectl patch ingress yemen-market-canary-ingress -n yemen-market-v2 -p \
  '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary-weight":"25"}}}'

# Monitor traffic metrics
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  curl -s http://localhost:8000/metrics | grep http_requests_total
```

## Health Check Commands

### V2 System Health

```bash
# Pod status
kubectl get pods -n yemen-market-v2

# Service status
kubectl get svc -n yemen-market-v2

# Ingress status
kubectl get ingress -n yemen-market-v2

# Application health endpoint
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  curl -f http://localhost:8000/health

# Database connectivity
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  curl -f http://localhost:8000/ready

# External service connectivity
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  curl -f https://api.worldbank.org/v2/country/YEM
```

### Resource Monitoring

```bash
# Resource usage
kubectl top pods -n yemen-market-v2
kubectl top nodes

# Storage usage
kubectl get pv
kubectl get pvc -n yemen-market-v2

# Network policies
kubectl get networkpolicies -n yemen-market-v2
```

## Troubleshooting Commands

### 1. High Error Rate Investigation

```bash
# Check V2 application logs
kubectl logs -n yemen-market-v2 deployment/yemen-market-api --tail=100

# Check V2 worker logs
kubectl logs -n yemen-market-v2 deployment/yemen-market-worker --tail=100

# Check database logs
kubectl logs -n yemen-market-v2 statefulset/postgres

# Check Redis logs
kubectl logs -n yemen-market-v2 deployment/redis

# Describe problematic pods
kubectl describe pods -n yemen-market-v2 -l app=yemen-market-api
```

### 2. Performance Issues

```bash
# Check resource limits
kubectl describe pods -n yemen-market-v2 -l app=yemen-market-api

# Check HPA status
kubectl get hpa -n yemen-market-v2

# Check node capacity
kubectl describe nodes

# Database performance
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  python -c "
import psycopg2
import time
start = time.time()
# Test database query
conn = psycopg2.connect(os.environ['DATABASE_URL'])
cur = conn.cursor()
cur.execute('SELECT COUNT(*) FROM prices;')
result = cur.fetchone()
print(f'Query result: {result[0]}, Time: {time.time()-start:.2f}s')
"
```

### 3. Data Migration Issues

```bash
# Check migration job status
kubectl get jobs -n yemen-market-v2 | grep migration

# View migration logs
kubectl logs -n yemen-market-v2 job/v1-to-v2-migration-* --tail=50

# Check migration job description
kubectl describe job -n yemen-market-v2 v1-to-v2-migration-*

# Verify data consistency
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  python -c "
import psycopg2
conn = psycopg2.connect(os.environ['DATABASE_URL'])
cur = conn.cursor()
cur.execute('SELECT COUNT(*) FROM prices;')
v2_count = cur.fetchone()[0]
print(f'V2 price records: {v2_count}')
"

kubectl exec -n yemen-market deployment/yemen-market-api -- \
  python -c "
import psycopg2
conn = psycopg2.connect(os.environ['DATABASE_URL'])
cur = conn.cursor()
cur.execute('SELECT COUNT(*) FROM prices;')
v1_count = cur.fetchone()[0]
print(f'V1 price records: {v1_count}')
"
```

### 4. External Service Issues

```bash
# Test World Bank API
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  curl -v https://api.worldbank.org/v2/country/YEM?format=json

# Test ACLED API
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  curl -v "https://api.acleddata.com/acled/read/?country=Yemen&limit=1"

# Test HDX API
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  curl -v "https://data.humdata.org/api/3/action/package_list"

# Check DNS resolution
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  nslookup api.worldbank.org
```

## Rollback Procedures

### 1. Traffic Rollback (30 seconds)

```bash
# Immediate traffic rollback to V1
./scripts/rollback-automation.sh traffic

# Manual traffic rollback
kubectl patch ingress yemen-market-canary-ingress -n yemen-market-v2 -p \
  '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary-weight":"0"}}}'

# Verify traffic is on V1
kubectl exec -n yemen-market deployment/yemen-market-api -- \
  curl -s http://localhost:8000/metrics | grep http_requests_total
```

### 2. Application Rollback (2 minutes)

```bash
# Full application rollback
./scripts/rollback-automation.sh application

# Manual application rollback
kubectl scale deployment yemen-market-api --replicas=0 -n yemen-market-v2
kubectl scale deployment yemen-market-worker --replicas=0 -n yemen-market-v2

# Scale up V1
kubectl scale deployment yemen-market-api --replicas=3 -n yemen-market
kubectl scale deployment yemen-market-worker --replicas=5 -n yemen-market
```

### 3. Full Rollback (5 minutes)

```bash
# Complete rollback with data preservation
./scripts/rollback-automation.sh full

# Verify rollback completion
kubectl get pods -n yemen-market
kubectl get pods -n yemen-market-v2
```

## Monitoring Queries

### Prometheus Queries

```promql
# Error rate
rate(http_requests_total{job="yemen-market-api-blue",code=~"5.."}[5m]) / 
rate(http_requests_total{job="yemen-market-api-blue"}[5m])

# Latency P95
histogram_quantile(0.95, 
  rate(http_request_duration_seconds_bucket{job="yemen-market-api-blue"}[5m])
)

# Traffic distribution
rate(http_requests_total{job="yemen-market-api-blue"}[5m]) /
(rate(http_requests_total{job="yemen-market-api-blue"}[5m]) + 
 rate(http_requests_total{job="yemen-market-api-green"}[5m]))

# Database connections
sum(database_connections_active{service="yemen-market-api-blue"})

# Memory usage
container_memory_working_set_bytes{pod=~"yemen-market-api-.*",namespace="yemen-market-v2"} / 
container_spec_memory_limit_bytes{pod=~"yemen-market-api-.*",namespace="yemen-market-v2"}
```

### Log Queries

```bash
# Error logs in the last 10 minutes
kubectl logs -n yemen-market-v2 deployment/yemen-market-api --since=10m | grep ERROR

# Database connection errors
kubectl logs -n yemen-market-v2 deployment/yemen-market-api --since=5m | grep "database"

# Performance logs
kubectl logs -n yemen-market-v2 deployment/yemen-market-api --since=5m | grep "slow"

# Migration logs
kubectl logs -n yemen-market-v2 job/v1-to-v2-migration-* | tail -20
```

## Decision Trees

### Error Rate Decision Tree

```
Error Rate > 5%?
├── Yes → Check if V1 also has issues
│   ├── V1 also high error rate → Infrastructure issue
│   │   └── Investigate load balancer, database, network
│   └── Only V2 has high error rate → V2-specific issue
│       ├── Error rate > 10% → Immediate rollback
│       └── Error rate 5-10% → Investigate for 2 minutes, then rollback
└── No → Continue monitoring
```

### Performance Decision Tree

```
Latency > 2s?
├── Yes → Check resource usage
│   ├── High CPU/Memory → Scale up resources
│   ├── Database slow → Check DB performance
│   └── External service slow → Check external APIs
└── No → Check if throughput is acceptable
    ├── Throughput < 80% of V1 → Investigate capacity
    └── Throughput OK → Continue monitoring
```

### Traffic Migration Decision Tree

```
Ready for next traffic step?
├── Error rate < 1% AND Latency < 500ms → Proceed
├── Error rate 1-5% OR Latency 500ms-2s → Wait and monitor
├── Error rate 5-10% OR Latency 2s-5s → Consider rollback
└── Error rate > 10% OR Latency > 5s → Immediate rollback
```

## Emergency Contacts

### Escalation Matrix

| Issue Severity | Response Time | Contact |
|----------------|---------------|---------|
| Critical (P0) | Immediate | On-call engineer + Team lead |
| High (P1) | 15 minutes | Team lead |
| Medium (P2) | 1 hour | Assigned engineer |
| Low (P3) | Next business day | Team queue |

### Contact Information

```yaml
Team Lead:
  Name: [Lead Name]
  Phone: [Phone Number]
  Slack: @[username]
  Email: [email]

Platform Engineer:
  Name: [Engineer Name]
  Phone: [Phone Number]
  Slack: @[username]
  Email: [email]

Database Admin:
  Name: [DBA Name]
  Phone: [Phone Number]
  Slack: @[username]
  Email: [email]

Security Engineer:
  Name: [Security Name]
  Phone: [Phone Number]
  Slack: @[username]
  Email: [email]
```

## Post-Incident Procedures

### Immediate Actions (0-30 minutes)

1. **Stabilize the system**
   ```bash
   # Ensure V1 is handling traffic
   kubectl get pods -n yemen-market
   kubectl exec -n yemen-market deployment/yemen-market-api -- curl -f http://localhost:8000/health
   ```

2. **Document the incident**
   - Timeline of events
   - Actions taken
   - Current system state
   - Impact assessment

3. **Communicate status**
   - Update stakeholders
   - Post in incident channel
   - Update status page

### Investigation (30 minutes - 2 hours)

1. **Collect evidence**
   ```bash
   # Save logs
   kubectl logs -n yemen-market-v2 deployment/yemen-market-api > incident-logs.txt
   
   # Export metrics
   curl "$PROMETHEUS_URL/api/v1/query_range?query=rate(http_requests_total[5m])&start=$INCIDENT_START&end=$INCIDENT_END&step=30s" > incident-metrics.json
   
   # Capture configuration
   kubectl get deployment yemen-market-api -n yemen-market-v2 -o yaml > deployment-config.yaml
   ```

2. **Analyze root cause**
   - Review logs and metrics
   - Identify failure points
   - Determine contributing factors

3. **Plan remediation**
   - Identify fixes needed
   - Update procedures
   - Schedule re-deployment

### Follow-up (2-24 hours)

1. **Conduct post-mortem**
   - Review timeline
   - Identify lessons learned
   - Update documentation

2. **Implement improvements**
   - Fix identified issues
   - Update monitoring
   - Enhance testing

3. **Prepare for re-deployment**
   - Test fixes
   - Update deployment plan
   - Schedule next attempt

---

*This runbook should be kept up-to-date and easily accessible during deployments. All team members should be familiar with these procedures.*