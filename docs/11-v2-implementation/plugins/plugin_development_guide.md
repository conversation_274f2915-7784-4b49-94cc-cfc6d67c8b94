# V2 Plugin Development Guide

## Overview

The Yemen Market Integration V2 platform supports a plugin system that enables extensible functionality for:
- **Data Sources**: Integrate new data providers (WFP, HDX, custom APIs)
- **Models**: Add custom econometric models beyond the three-tier framework
- **Outputs**: Export results in different formats (LaTeX, Excel, dashboards)

This guide explains how to develop, test, and deploy plugins for the V2 platform.

## Architecture

### Plugin Types

1. **DataSourcePlugin**: For ingesting data from external sources
2. **ModelPlugin**: For custom econometric models
3. **OutputPlugin**: For custom export formats

### Plugin Structure

```
plugins/
├── data_sources/
│   └── your_plugin/
│       ├── __init__.py
│       ├── plugin.py          # Main plugin implementation
│       ├── config.schema.json # Optional configuration schema
│       └── README.md          # Plugin documentation
├── models/
│   └── custom_model/
│       └── ...
└── outputs/
    └── latex_export/
        └── ...
```

## Creating a Data Source Plugin

### 1. Basic Structure

Create a new plugin by implementing the `MarketDataSourcePlugin` interface:

```python
# plugins/data_sources/your_plugin/plugin.py

from typing import Dict, Any, List, Optional
from datetime import datetime
from v2.plugins.data_sources.__plugin_interface import MarketDataSourcePlugin
from v2.src.shared.plugins.interfaces import PluginMetadata

class YourDataSourcePlugin(MarketDataSourcePlugin):
    """Your custom data source plugin."""
    
    @property
    def metadata(self) -> PluginMetadata:
        """Plugin metadata."""
        return PluginMetadata(
            name="your_data_source",
            version="1.0.0",
            author="Your Name",
            description="Description of your data source",
            dependencies=["requests>=2.28.0"],
            config_schema={
                "type": "object",
                "properties": {
                    "api_url": {"type": "string"},
                    "api_key": {"type": "string"}
                },
                "required": ["api_url"]
            }
        )
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize with configuration."""
        self.api_url = config["api_url"]
        self.api_key = config.get("api_key")
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate configuration."""
        return "api_url" in config
    
    async def fetch_price_data(
        self,
        start_date: datetime,
        end_date: datetime,
        markets: Optional[List[str]] = None,
        commodities: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Fetch price data from your source."""
        # Implement your data fetching logic
        pass
```

### 2. Required Methods

#### `metadata` Property
Returns plugin information including dependencies and configuration schema.

#### `initialize(config)`
Sets up the plugin with user-provided configuration.

#### `validate_config(config)`
Validates configuration before initialization.

#### `fetch_price_data(...)`
Core method for retrieving price data. Must return standardized format:

```python
[
    {
        "date": "2024-01-01T00:00:00",
        "market_id": "SAN001",
        "commodity_code": "WHEAT",
        "commodity_name": "Wheat",
        "price": 150.0,
        "price_unit": "kg",
        "currency": "YER",
        "price_type": "retail",
        "source": "YourSource",
        "metadata": {
            "admin1": "Sana'a",
            "admin2": "Old City",
            "latitude": 15.3694,
            "longitude": 44.1910
        }
    }
]
```

### 3. Optional Methods

#### `fetch_market_metadata()`
Returns information about available markets.

#### `transform_data(raw_data)`
Converts raw data to pandas DataFrame or other formats.

#### `on_load()` / `on_unload()`
Lifecycle hooks for setup/cleanup.

## Plugin Discovery and Loading

### Automatic Discovery

Plugins are automatically discovered if placed in the standard directories:
- `v2/plugins/data_sources/`
- `v2/plugins/models/`
- `v2/plugins/outputs/`

### Manual Loading

```python
from v2.src.shared.plugins.manager import PluginManager

# Initialize manager
manager = PluginManager(plugin_dirs=[Path("plugins")])

# Discover plugins
discovered = manager.discover_plugins()

# Load specific plugin
plugin = manager.load_plugin("data_sources", "your_plugin")

# Configure and use
plugin.initialize({"api_url": "https://api.example.com"})
data = await plugin.fetch_price_data(start_date, end_date)
```

## Configuration Management

### Schema Definition

Define configuration schema in plugin metadata:

```python
config_schema={
    "type": "object",
    "properties": {
        "api_url": {
            "type": "string",
            "description": "Base URL for API"
        },
        "timeout": {
            "type": "integer",
            "default": 30,
            "description": "Request timeout in seconds"
        },
        "cache_enabled": {
            "type": "boolean",
            "default": True
        }
    },
    "required": ["api_url"]
}
```

### Environment Variables

Plugins can read from environment:

```python
import os

def initialize(self, config: Dict[str, Any]) -> None:
    self.api_key = config.get("api_key") or os.getenv("YOUR_API_KEY")
```

## Error Handling

### Best Practices

1. **Validate inputs early**:
```python
def validate_query(self, query: Dict[str, Any]) -> bool:
    if "data_type" not in query:
        return False
    if query["data_type"] not in self.get_supported_data_types():
        return False
    return True
```

2. **Provide meaningful errors**:
```python
async def fetch_data(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
    if not self.validate_query(query):
        raise ValueError(f"Invalid query: {query}")
    
    try:
        response = await self._make_request(query)
    except RequestException as e:
        raise RuntimeError(f"Failed to fetch data: {e}")
```

3. **Handle missing data gracefully**:
```python
if not price_file.exists():
    logger.warning(f"Price file not found: {price_file}")
    return []
```

## Testing Plugins

### Unit Tests

Create tests in `v2/tests/unit/plugins/`:

```python
import pytest
from v2.plugins.data_sources.your_plugin import YourDataSourcePlugin

class TestYourPlugin:
    def test_metadata(self):
        plugin = YourDataSourcePlugin()
        assert plugin.metadata.name == "your_data_source"
    
    def test_validate_config(self):
        plugin = YourDataSourcePlugin()
        assert plugin.validate_config({"api_url": "http://test"})
        assert not plugin.validate_config({})
    
    @pytest.mark.asyncio
    async def test_fetch_data(self, mock_api):
        plugin = YourDataSourcePlugin()
        plugin.initialize({"api_url": "http://test"})
        
        data = await plugin.fetch_price_data(
            datetime(2024, 1, 1),
            datetime(2024, 1, 31)
        )
        
        assert len(data) > 0
        assert all("price" in d for d in data)
```

### Integration Tests

Test plugin with the plugin manager:

```python
def test_plugin_discovery():
    manager = PluginManager()
    discovered = manager.discover_plugins()
    assert "your_plugin" in discovered["data_sources"]

def test_plugin_loading():
    manager = PluginManager()
    plugin = manager.load_plugin("data_sources", "your_plugin")
    assert plugin is not None
```

## Deployment

### Package Structure

For distribution, structure your plugin as a package:

```
your-plugin/
├── setup.py
├── README.md
├── LICENSE
├── requirements.txt
└── your_plugin/
    ├── __init__.py
    └── plugin.py
```

### Installation

Users can install via:
1. **Copy to plugins directory**: Simple drag-and-drop
2. **pip install**: `pip install your-plugin`
3. **Git submodule**: For development

### Version Compatibility

Specify compatible platform versions:

```python
metadata = PluginMetadata(
    name="your_plugin",
    version="1.0.0",
    platform_version=">=2.0.0,<3.0.0"
)
```

## Examples

### WFP Data Source (Included)

See `v2/plugins/data_sources/wfp_poc/` for a complete example that:
- Reads CSV price data
- Implements filtering by date/market/commodity
- Provides metadata loading
- Transforms data to DataFrame

### Custom API Integration

```python
class APIDataSource(MarketDataSourcePlugin):
    async def fetch_price_data(self, ...):
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.api_url}/prices",
                params={
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                headers={"Authorization": f"Bearer {self.api_key}"}
            )
            return response.json()
```

## Best Practices

1. **Follow Standards**: Use the standardized data format for interoperability
2. **Document Configuration**: Provide clear schema and examples
3. **Handle Rate Limits**: Implement retries and backoff for APIs
4. **Cache When Possible**: Reduce API calls and improve performance
5. **Log Appropriately**: Use the platform's logging system
6. **Test Thoroughly**: Include unit and integration tests
7. **Version Carefully**: Follow semantic versioning

## Support

- **Documentation**: See platform docs for API details
- **Examples**: Check `v2/examples/` for usage patterns
- **Issues**: Report bugs via project issue tracker