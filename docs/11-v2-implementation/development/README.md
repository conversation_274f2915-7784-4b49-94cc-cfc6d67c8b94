# Yemen Market Integration v2 Developer Guide

## Overview

This developer guide provides comprehensive information for contributors to the Yemen Market Integration v2 project, including architecture documentation, coding standards, development workflows, and contribution guidelines.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Development Setup](#development-setup)
- [Coding Standards](#coding-standards)
- [Development Workflow](#development-workflow)
- [Testing Guidelines](#testing-guidelines)
- [Plugin Development](#plugin-development)
- [API Development](#api-development)
- [Contributing Guidelines](#contributing-guidelines)
- [Code Examples](#code-examples)

## Architecture Overview

### System Architecture

The Yemen Market Integration v2 system follows Clean Architecture principles with clear separation of concerns:

```
┌─────────────────────────────────────────────────┐
│                Interfaces Layer                 │
│    (REST API, GraphQL, CLI, Web Dashboard)     │
├─────────────────────────────────────────────────┤
│               Application Layer                 │
│     (Use Cases, Commands, Queries, Services)   │
├─────────────────────────────────────────────────┤
│                 Domain Layer                    │
│   (Entities, Value Objects, Domain Services)   │
├─────────────────────────────────────────────────┤
│              Infrastructure Layer               │
│   (Database, External APIs, File System, etc.) │
└─────────────────────────────────────────────────┘
```

### Core Principles

1. **Dependency Inversion**: High-level modules don't depend on low-level modules
2. **Single Responsibility**: Each class/module has one reason to change
3. **Open/Closed**: Open for extension, closed for modification
4. **Interface Segregation**: Clients shouldn't depend on unused interfaces
5. **DRY (Don't Repeat Yourself)**: Avoid code duplication
6. **SOLID Principles**: Foundation for maintainable code

### Domain Model

#### Core Entities

```python
# Market Entity
class Market:
    """Represents a physical marketplace"""
    
    def __init__(self, id: MarketId, name: str, location: GeographicLocation):
        self.id = id
        self.name = name
        self.location = location
        self.governorate = location.governorate
        self.control_status = ControlStatus.UNKNOWN
        self._price_observations = []
    
    def add_price_observation(self, observation: PriceObservation) -> None:
        """Add a price observation to this market"""
        if not observation.market_id == self.id:
            raise ValueError("Price observation market_id must match market")
        
        self._price_observations.append(observation)
    
    def get_price_history(self, commodity: CommodityCode, 
                         start_date: date, end_date: date) -> List[PriceObservation]:
        """Get price history for a specific commodity and date range"""
        return [
            obs for obs in self._price_observations
            if obs.commodity_code == commodity 
            and start_date <= obs.date <= end_date
        ]

# Price Observation Value Object
@dataclass(frozen=True)
class PriceObservation:
    """Immutable price observation"""
    
    market_id: MarketId
    commodity_code: CommodityCode
    date: date
    price: Price
    currency: Currency
    unit: MeasurementUnit
    data_source: DataSource
    quality_indicators: QualityIndicators
    
    def __post_init__(self):
        if self.price.amount <= 0:
            raise ValueError("Price must be positive")
        
        if self.date > date.today():
            raise ValueError("Price observation cannot be in the future")

# Analysis Aggregate Root
class Analysis:
    """Aggregate root for analysis operations"""
    
    def __init__(self, id: AnalysisId, config: AnalysisConfiguration):
        self.id = id
        self.config = config
        self.status = AnalysisStatus.PENDING
        self._tier_results = {}
        self._events = []
    
    def start_execution(self) -> None:
        """Start analysis execution"""
        if self.status != AnalysisStatus.PENDING:
            raise InvalidStateTransitionError(f"Cannot start analysis in status {self.status}")
        
        self.status = AnalysisStatus.RUNNING
        self._add_event(AnalysisStartedEvent(self.id, datetime.utcnow()))
    
    def complete_tier(self, tier: AnalysisTier, results: TierResults) -> None:
        """Mark a tier as completed with results"""
        if self.status != AnalysisStatus.RUNNING:
            raise InvalidStateTransitionError(f"Cannot complete tier when analysis status is {self.status}")
        
        self._tier_results[tier] = results
        self._add_event(TierCompletedEvent(self.id, tier, datetime.utcnow()))
        
        # Check if all tiers are complete
        if self._all_tiers_complete():
            self._complete_analysis()
    
    def _add_event(self, event: DomainEvent) -> None:
        """Add domain event"""
        self._events.append(event)
```

#### Domain Services

```python
class MarketIntegrationAnalyzer:
    """Domain service for market integration analysis"""
    
    def __init__(self, price_repository: PriceRepository):
        self._price_repository = price_repository
    
    def calculate_integration_score(self, 
                                  market_a: Market, 
                                  market_b: Market,
                                  commodity: CommodityCode,
                                  period: DateRange) -> IntegrationScore:
        """Calculate integration score between two markets"""
        
        # Get price data for both markets
        prices_a = market_a.get_price_history(commodity, period.start, period.end)
        prices_b = market_b.get_price_history(commodity, period.start, period.end)
        
        if len(prices_a) < 20 or len(prices_b) < 20:
            raise InsufficientDataError("Need at least 20 observations per market")
        
        # Calculate correlation coefficient
        correlation = self._calculate_price_correlation(prices_a, prices_b)
        
        # Calculate speed of adjustment
        adjustment_speed = self._calculate_adjustment_speed(prices_a, prices_b)
        
        # Combine metrics into overall score
        integration_score = IntegrationScore(
            correlation=correlation,
            adjustment_speed=adjustment_speed,
            overall_score=self._combine_metrics(correlation, adjustment_speed)
        )
        
        return integration_score
```

### Application Layer

#### Use Cases

```python
class RunThreeTierAnalysisUseCase:
    """Use case for executing three-tier analysis"""
    
    def __init__(self, 
                 analysis_repository: AnalysisRepository,
                 price_repository: PriceRepository,
                 tier1_estimator: Tier1Estimator,
                 tier2_estimator: Tier2Estimator,
                 tier3_estimator: Tier3Estimator,
                 event_bus: EventBus):
        self._analysis_repo = analysis_repository
        self._price_repo = price_repository
        self._tier1_estimator = tier1_estimator
        self._tier2_estimator = tier2_estimator
        self._tier3_estimator = tier3_estimator
        self._event_bus = event_bus
    
    async def execute(self, command: RunThreeTierAnalysisCommand) -> AnalysisId:
        """Execute the three-tier analysis use case"""
        
        # 1. Create analysis entity
        analysis_id = AnalysisId.generate()
        config = AnalysisConfiguration.from_command(command)
        analysis = Analysis(analysis_id, config)
        
        # 2. Validate prerequisites
        await self._validate_prerequisites(command)
        
        # 3. Save analysis and start execution
        await self._analysis_repo.save(analysis)
        analysis.start_execution()
        
        # 4. Publish events
        for event in analysis.get_events():
            await self._event_bus.publish(event)
        
        # 5. Execute tiers asynchronously
        await self._execute_analysis_tiers(analysis)
        
        return analysis_id
    
    async def _execute_analysis_tiers(self, analysis: Analysis) -> None:
        """Execute analysis tiers in sequence"""
        
        try:
            # Tier 1: Pooled Panel Analysis
            tier1_results = await self._tier1_estimator.estimate(analysis.config)
            analysis.complete_tier(AnalysisTier.TIER1, tier1_results)
            
            # Tier 2: Commodity-specific VECM
            tier2_results = await self._tier2_estimator.estimate(analysis.config)
            analysis.complete_tier(AnalysisTier.TIER2, tier2_results)
            
            # Tier 3: Factor Analysis Validation
            tier3_results = await self._tier3_estimator.estimate(analysis.config)
            analysis.complete_tier(AnalysisTier.TIER3, tier3_results)
            
            # Save final results
            await self._analysis_repo.save(analysis)
            
        except Exception as e:
            analysis.mark_as_failed(str(e))
            await self._analysis_repo.save(analysis)
            raise
```

#### Command Handlers

```python
@dataclass
class RunThreeTierAnalysisCommand:
    """Command to run three-tier analysis"""
    
    start_date: date
    end_date: date
    market_ids: List[MarketId]
    commodity_codes: List[CommodityCode]
    tier1_config: Optional[Dict[str, Any]] = None
    tier2_config: Optional[Dict[str, Any]] = None
    tier3_config: Optional[Dict[str, Any]] = None
    run_diagnostics: bool = True
    apply_corrections: bool = True
    save_intermediate: bool = True

class RunThreeTierAnalysisCommandHandler:
    """Handler for three-tier analysis command"""
    
    def __init__(self, use_case: RunThreeTierAnalysisUseCase):
        self._use_case = use_case
    
    async def handle(self, command: RunThreeTierAnalysisCommand) -> AnalysisId:
        """Handle the command"""
        
        # Validate command
        self._validate_command(command)
        
        # Execute use case
        return await self._use_case.execute(command)
    
    def _validate_command(self, command: RunThreeTierAnalysisCommand) -> None:
        """Validate command parameters"""
        
        if command.end_date <= command.start_date:
            raise ValidationError("End date must be after start date")
        
        if len(command.market_ids) < 2:
            raise ValidationError("At least 2 markets required for analysis")
        
        if len(command.commodity_codes) < 1:
            raise ValidationError("At least 1 commodity required for analysis")
        
        # Validate date range (not too large)
        date_diff = (command.end_date - command.start_date).days
        if date_diff > 365 * 3:  # 3 years max
            raise ValidationError("Date range cannot exceed 3 years")
```

### Infrastructure Layer

#### Repository Implementations

```python
class PostgreSQLPriceRepository(PriceRepository):
    """PostgreSQL implementation of price repository"""
    
    def __init__(self, session_factory: SessionFactory):
        self._session_factory = session_factory
    
    async def find_by_market_and_commodity(self, 
                                          market_id: MarketId,
                                          commodity_code: CommodityCode,
                                          date_range: DateRange) -> List[PriceObservation]:
        """Find price observations by market, commodity, and date range"""
        
        async with self._session_factory() as session:
            query = select(PriceObservationORM).where(
                PriceObservationORM.market_id == market_id.value,
                PriceObservationORM.commodity_code == commodity_code.value,
                PriceObservationORM.date >= date_range.start,
                PriceObservationORM.date <= date_range.end
            ).order_by(PriceObservationORM.date)
            
            result = await session.execute(query)
            orm_objects = result.scalars().all()
            
            return [self._to_domain_object(orm_obj) for orm_obj in orm_objects]
    
    async def save(self, price_observation: PriceObservation) -> None:
        """Save a price observation"""
        
        async with self._session_factory() as session:
            orm_object = self._to_orm_object(price_observation)
            session.add(orm_object)
            await session.commit()
    
    def _to_domain_object(self, orm_obj: PriceObservationORM) -> PriceObservation:
        """Convert ORM object to domain object"""
        
        return PriceObservation(
            market_id=MarketId(orm_obj.market_id),
            commodity_code=CommodityCode(orm_obj.commodity_code),
            date=orm_obj.date,
            price=Price(orm_obj.price, Currency(orm_obj.currency)),
            unit=MeasurementUnit(orm_obj.unit),
            data_source=DataSource(orm_obj.data_source),
            quality_indicators=QualityIndicators.from_dict(orm_obj.quality_data)
        )
```

#### External Service Adapters

```python
class WFPDataSourceAdapter(DataSourceAdapter):
    """Adapter for WFP API data source"""
    
    def __init__(self, http_client: HttpClient, api_key: str):
        self._http_client = http_client
        self._api_key = api_key
        self._base_url = "https://api.wfp.org/vam-data-bridges/5.0.0"
    
    async def fetch_price_data(self, 
                              country_code: str,
                              start_date: date,
                              end_date: date) -> List[RawPriceData]:
        """Fetch price data from WFP API"""
        
        url = f"{self._base_url}/MarketPrices/Retail/CountryCode/{country_code}"
        params = {
            "startDate": start_date.isoformat(),
            "endDate": end_date.isoformat(),
            "format": "json"
        }
        headers = {
            "Subscription-Key": self._api_key,
            "Accept": "application/json"
        }
        
        try:
            response = await self._http_client.get(url, params=params, headers=headers)
            response.raise_for_status()
            
            raw_data = response.json()
            return [self._parse_price_record(record) for record in raw_data]
            
        except httpx.HTTPStatusError as e:
            raise ExternalServiceError(f"WFP API error: {e.response.status_code}")
        except httpx.RequestError as e:
            raise ExternalServiceError(f"WFP API connection error: {str(e)}")
    
    def _parse_price_record(self, record: Dict[str, Any]) -> RawPriceData:
        """Parse raw price record from WFP API"""
        
        return RawPriceData(
            market_name=record.get("marketName"),
            commodity_name=record.get("commodityName"),
            date=datetime.strptime(record.get("date"), "%Y-%m-%d").date(),
            price=float(record.get("price", 0)),
            currency=record.get("currency", "YER"),
            unit=record.get("unit"),
            source="WFP"
        )
```

## Development Setup

### Prerequisites

- **Python 3.11+**: Modern Python with improved performance
- **Poetry**: Dependency management and packaging
- **Docker**: Container development and testing
- **PostgreSQL 14+**: Primary database
- **Redis 6.2+**: Caching and message broker
- **Git**: Version control

### Environment Setup

```bash
# Clone repository
git clone https://github.com/worldbank/yemen-market-integration-v2.git
cd yemen-market-integration-v2/v2

# Install Poetry (if not already installed)
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install --with dev,test

# Activate virtual environment
poetry shell

# Install pre-commit hooks
pre-commit install

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration

# Start development services
docker-compose -f docker-compose.dev.yml up -d

# Run database migrations
python -m src.infrastructure.persistence.migrations.migrate

# Seed development data
python scripts/seed_development_data.py

# Verify setup
python -m pytest tests/unit/ -v
```

### Development Environment Configuration

**.env.development**:
```env
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/yemen_market_v2_dev
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=5

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=5

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_ENV=development
API_DEBUG=true
API_RELOAD=true

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed

# External Services (use test/sandbox environments)
WFP_API_KEY=test_wfp_api_key
ACLED_API_KEY=test_acled_api_key
HDX_API_KEY=test_hdx_api_key

# Security (development only)
JWT_SECRET_KEY=development-secret-key-not-for-production
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Performance
ENABLE_CACHING=false  # Disable for development
ENABLE_RATE_LIMITING=false
```

### IDE Configuration

#### VS Code Settings

**.vscode/settings.json**:
```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": [
        "tests/"
    ],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true,
        ".coverage": true,
        "htmlcov": true
    }
}
```

**.vscode/launch.json**:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "FastAPI Development Server",
            "type": "python",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "src.interfaces.api.rest.app:app",
                "--reload",
                "--host", "0.0.0.0",
                "--port", "8000"
            ],
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "console": "integratedTerminal"
        },
        {
            "name": "Run Tests",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": [
                "tests/",
                "-v",
                "--tb=short"
            ],
            "console": "integratedTerminal"
        }
    ]
}
```

## Coding Standards

### Python Style Guide

We follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) with some modifications:

- **Line length**: 88 characters (Black default)
- **String quotes**: Double quotes preferred
- **Import organization**: isort with Black compatibility
- **Type hints**: Required for all public APIs
- **Docstrings**: Google style

### Code Formatting

```bash
# Format code with Black
black src/ tests/

# Sort imports with isort
isort src/ tests/

# Check formatting
black --check src/ tests/
isort --check-only src/ tests/
```

### Type Checking

```bash
# Run MyPy type checker
mypy src/

# Type check specific module
mypy src/domain/entities/market.py
```

### Linting

```bash
# Run flake8 linter
flake8 src/ tests/

# Run pylint
pylint src/
```

### Pre-commit Configuration

**.pre-commit-config.yaml**:
```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict

  - repo: https://github.com/psf/black
    rev: 22.10.0
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 5.0.4
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.991
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

### Documentation Standards

#### Docstring Format

```python
def calculate_integration_score(
    price_series_a: List[float],
    price_series_b: List[float],
    method: str = "correlation"
) -> float:
    """Calculate market integration score between two price series.
    
    This function computes a market integration score using various methods
    to measure the strength of price transmission between markets.
    
    Args:
        price_series_a: Price observations for the first market.
        price_series_b: Price observations for the second market.
        method: Integration calculation method. Options are:
            - "correlation": Pearson correlation coefficient
            - "cointegration": Engle-Granger cointegration test
            - "granger": Granger causality test
    
    Returns:
        Integration score between 0 and 1, where 1 indicates perfect
        integration and 0 indicates no integration.
    
    Raises:
        ValueError: If price series have different lengths or are empty.
        UnsupportedMethodError: If the specified method is not implemented.
    
    Example:
        >>> prices_sanaa = [100, 102, 105, 103, 104]
        >>> prices_aden = [98, 100, 103, 101, 102]
        >>> score = calculate_integration_score(prices_sanaa, prices_aden)
        >>> print(f"Integration score: {score:.3f}")
        Integration score: 0.967
    """
    pass
```

#### API Documentation

```python
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional

router = APIRouter(prefix="/markets", tags=["markets"])

@router.get("/", response_model=List[MarketResponse])
async def list_markets(
    governorate: Optional[str] = None,
    control_status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
) -> List[MarketResponse]:
    """List markets with optional filtering.
    
    Retrieve a paginated list of markets available for analysis.
    Markets can be filtered by governorate and control status.
    
    Parameters:
        governorate: Filter by governorate name (e.g., "Sana'a", "Aden")
        control_status: Filter by control status ("government", "opposition", "contested")
        limit: Maximum number of markets to return (1-100)
        offset: Number of markets to skip for pagination
    
    Returns:
        List of market objects with basic information and metadata.
    
    Raises:
        HTTPException: 400 if invalid filter parameters
        HTTPException: 401 if authentication required
        HTTPException: 403 if insufficient permissions
    """
    pass
```

## Development Workflow

### Git Workflow

We use GitHub Flow with the following conventions:

1. **Branch Naming**:
   - `feature/description`: New features
   - `bugfix/description`: Bug fixes
   - `hotfix/description`: Critical production fixes
   - `refactor/description`: Code refactoring
   - `docs/description`: Documentation updates

2. **Commit Messages**:
   ```
   type(scope): brief description
   
   Longer description if needed, explaining what and why.
   
   Closes #123
   ```

   Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

3. **Pull Request Process**:
   - Create feature branch from `main`
   - Implement changes with tests
   - Update documentation if needed
   - Create pull request with detailed description
   - Pass all CI checks
   - Get code review approval
   - Merge to `main`

### Branch Protection Rules

```yaml
# .github/branch_protection.yml
main:
  required_status_checks:
    - tests
    - linting
    - type-checking
    - security-scan
  required_pull_request_reviews:
    required_approving_review_count: 1
    dismiss_stale_reviews: true
    require_code_owner_reviews: true
  enforce_admins: true
  allow_force_pushes: false
  allow_deletions: false
```

### Continuous Integration

**.github/workflows/ci.yml**:
```yaml
name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:6.2
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --with dev,test
    
    - name: Run linting
      run: |
        poetry run flake8 src/ tests/
        poetry run black --check src/ tests/
        poetry run isort --check-only src/ tests/
    
    - name: Run type checking
      run: poetry run mypy src/
    
    - name: Run tests
      run: |
        poetry run pytest tests/ \
          --cov=src \
          --cov-report=xml \
          --cov-report=html \
          --junitxml=test-results.xml
      env:
        DATABASE_URL: postgresql://postgres:password@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
    
    - name: Security scan
      run: poetry run bandit -r src/
```

## Testing Guidelines

### Test Structure

```
tests/
├── unit/              # Unit tests
│   ├── domain/        # Domain logic tests
│   ├── application/   # Use case tests
│   └── infrastructure/ # Infrastructure tests
├── integration/       # Integration tests
│   ├── api/          # API endpoint tests
│   ├── database/     # Database integration tests
│   └── external/     # External service tests
├── e2e/              # End-to-end tests
├── fixtures/         # Test fixtures and data
└── conftest.py       # PyTest configuration
```

### Unit Testing

```python
# tests/unit/domain/test_market.py
import pytest
from datetime import date
from src.domain.entities.market import Market
from src.domain.value_objects import MarketId, GeographicLocation, PriceObservation

class TestMarket:
    """Test cases for Market entity"""
    
    def test_create_market_with_valid_data(self):
        """Test creating a market with valid data"""
        # Given
        market_id = MarketId("market_001")
        location = GeographicLocation(latitude=15.3694, longitude=44.1910, governorate="Sana'a")
        
        # When
        market = Market(market_id, "Sana'a Central Market", location)
        
        # Then
        assert market.id == market_id
        assert market.name == "Sana'a Central Market"
        assert market.governorate == "Sana'a"
    
    def test_add_price_observation_success(self):
        """Test adding a valid price observation"""
        # Given
        market = self._create_test_market()
        observation = self._create_test_price_observation(market.id)
        
        # When
        market.add_price_observation(observation)
        
        # Then
        assert len(market._price_observations) == 1
        assert market._price_observations[0] == observation
    
    def test_add_price_observation_wrong_market_raises_error(self):
        """Test adding price observation with wrong market ID raises error"""
        # Given
        market = self._create_test_market()
        wrong_market_id = MarketId("different_market")
        observation = self._create_test_price_observation(wrong_market_id)
        
        # When & Then
        with pytest.raises(ValueError, match="Price observation market_id must match market"):
            market.add_price_observation(observation)
    
    def _create_test_market(self) -> Market:
        """Helper to create test market"""
        market_id = MarketId("test_market")
        location = GeographicLocation(15.3694, 44.1910, "Sana'a")
        return Market(market_id, "Test Market", location)
    
    def _create_test_price_observation(self, market_id: MarketId) -> PriceObservation:
        """Helper to create test price observation"""
        return PriceObservation(
            market_id=market_id,
            commodity_code=CommodityCode("wheat"),
            date=date(2024, 1, 15),
            price=Price(100.0, Currency("YER")),
            unit=MeasurementUnit("kg"),
            data_source=DataSource("test"),
            quality_indicators=QualityIndicators()
        )
```

### Integration Testing

```python
# tests/integration/test_analysis_workflow.py
import pytest
from httpx import AsyncClient
from datetime import date, timedelta

@pytest.mark.asyncio
class TestAnalysisWorkflow:
    """Integration tests for complete analysis workflow"""
    
    async def test_complete_three_tier_analysis(self, client: AsyncClient, auth_headers):
        """Test complete three-tier analysis workflow"""
        
        # 1. Create analysis
        analysis_request = {
            "start_date": "2023-01-01",
            "end_date": "2023-03-31",
            "markets": ["Sana'a", "Aden"],
            "commodities": ["wheat", "rice"],
            "confidence_level": 0.95,
            "include_diagnostics": True
        }
        
        response = await client.post(
            "/api/v1/analysis/three-tier",
            json=analysis_request,
            headers=auth_headers
        )
        
        assert response.status_code == 202
        analysis_data = response.json()
        analysis_id = analysis_data["id"]
        
        # 2. Wait for analysis completion
        completed = await self._wait_for_analysis_completion(client, analysis_id, auth_headers)
        assert completed
        
        # 3. Verify results
        response = await client.get(
            f"/api/v1/analysis/{analysis_id}/results",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        results = response.json()
        
        # Verify structure
        assert "summary" in results
        assert "tier1_results" in results
        assert "tier2_results" in results
        assert "tier3_results" in results
        
        # Verify content
        assert results["summary"]["markets_analyzed"] == 2
        assert results["summary"]["commodities_analyzed"] == 2
        assert 0 <= results["summary"]["integration_score"] <= 1
    
    async def _wait_for_analysis_completion(self, client, analysis_id, headers, timeout=300):
        """Wait for analysis to complete with timeout"""
        import asyncio
        
        start_time = asyncio.get_event_loop().time()
        
        while True:
            response = await client.get(f"/api/v1/analysis/{analysis_id}", headers=headers)
            
            if response.status_code == 200:
                status_data = response.json()
                if status_data["status"] == "completed":
                    return True
                elif status_data["status"] == "failed":
                    pytest.fail(f"Analysis failed: {status_data.get('error', 'Unknown error')}")
            
            # Check timeout
            if asyncio.get_event_loop().time() - start_time > timeout:
                pytest.fail("Analysis did not complete within timeout")
            
            await asyncio.sleep(5)  # Wait 5 seconds before checking again
```

### Test Fixtures

```python
# tests/conftest.py
import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from src.interfaces.api.rest.app import create_app
from src.infrastructure.persistence.database import get_session

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def test_db():
    """Create test database"""
    engine = create_async_engine("postgresql+asyncpg://postgres:password@localhost:5432/test_db")
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Clean up
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

@pytest.fixture
async def session(test_db):
    """Create database session"""
    async with AsyncSession(test_db) as session:
        yield session

@pytest.fixture
async def client(test_db):
    """Create test client"""
    app = create_app()
    
    # Override database dependency
    async def override_get_session():
        async with AsyncSession(test_db) as session:
            yield session
    
    app.dependency_overrides[get_session] = override_get_session
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest.fixture
def auth_headers():
    """Create authentication headers for testing"""
    return {"Authorization": "Bearer test_api_key"}

@pytest.fixture
def sample_price_data():
    """Sample price data for testing"""
    return [
        {
            "market": "Sana'a",
            "commodity": "wheat",
            "date": "2023-01-01",
            "price": 100.0,
            "currency": "YER"
        },
        {
            "market": "Aden",
            "commodity": "wheat", 
            "date": "2023-01-01",
            "price": 102.0,
            "currency": "YER"
        }
    ]
```

### Coverage Requirements

- **Minimum Coverage**: 85% overall
- **Domain Layer**: 95% coverage required
- **Application Layer**: 90% coverage required
- **Infrastructure Layer**: 80% coverage required

```bash
# Generate coverage report
pytest tests/ --cov=src --cov-report=html --cov-report=term-missing

# Check coverage threshold
pytest tests/ --cov=src --cov-fail-under=85
```

## Plugin Development

The system supports extensible plugins for data sources, models, and outputs.

### Plugin Interface

```python
# src/shared/plugins/interfaces.py
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

class DataSourcePlugin(ABC):
    """Interface for data source plugins"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Plugin name"""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Plugin version"""
        pass
    
    @abstractmethod
    async def collect_data(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect data from the source"""
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate plugin configuration"""
        pass

class ModelPlugin(ABC):
    """Interface for model plugins"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Model name"""
        pass
    
    @abstractmethod
    async def estimate(self, data: Any, config: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate model"""
        pass
    
    @abstractmethod
    def get_diagnostics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Get model diagnostics"""
        pass

class OutputPlugin(ABC):
    """Interface for output plugins"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Output format name"""
        pass
    
    @property
    @abstractmethod
    def file_extension(self) -> str:
        """File extension for output"""
        pass
    
    @abstractmethod
    async def generate_output(self, 
                            data: Dict[str, Any], 
                            config: Dict[str, Any]) -> bytes:
        """Generate output in specific format"""
        pass
```

### Example Plugin Implementation

```python
# plugins/data_sources/world_bank/plugin.py
from typing import Any, Dict, List
import httpx
from src.shared.plugins.interfaces import DataSourcePlugin

class WorldBankDataSourcePlugin(DataSourcePlugin):
    """World Bank API data source plugin"""
    
    @property
    def name(self) -> str:
        return "world_bank"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate configuration"""
        required_fields = ["country_code", "indicator_codes"]
        return all(field in config for field in required_fields)
    
    async def collect_data(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect data from World Bank API"""
        
        if not self.validate_config(config):
            raise ValueError("Invalid configuration")
        
        country_code = config["country_code"]
        indicator_codes = config["indicator_codes"]
        start_date = config.get("start_date", "2020")
        end_date = config.get("end_date", "2024")
        
        data = []
        
        async with httpx.AsyncClient() as client:
            for indicator in indicator_codes:
                url = f"https://api.worldbank.org/v2/country/{country_code}/indicator/{indicator}"
                params = {
                    "date": f"{start_date}:{end_date}",
                    "format": "json",
                    "per_page": 1000
                }
                
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                json_data = response.json()
                if len(json_data) > 1:  # World Bank API returns metadata in first element
                    indicator_data = json_data[1]
                    
                    for record in indicator_data:
                        if record["value"] is not None:
                            data.append({
                                "indicator_code": indicator,
                                "indicator_name": record["indicator"]["value"],
                                "country_code": record["countryiso3code"],
                                "country_name": record["country"]["value"],
                                "date": record["date"],
                                "value": float(record["value"]),
                                "source": "world_bank"
                            })
        
        return data
```

### Plugin Registration

```python
# src/shared/plugins/registry.py
class PluginRegistry:
    """Registry for managing plugins"""
    
    def __init__(self):
        self._data_source_plugins = {}
        self._model_plugins = {}
        self._output_plugins = {}
    
    def register_data_source_plugin(self, plugin: DataSourcePlugin):
        """Register a data source plugin"""
        self._data_source_plugins[plugin.name] = plugin
    
    def register_model_plugin(self, plugin: ModelPlugin):
        """Register a model plugin"""
        self._model_plugins[plugin.name] = plugin
    
    def register_output_plugin(self, plugin: OutputPlugin):
        """Register an output plugin"""
        self._output_plugins[plugin.name] = plugin
    
    def get_data_source_plugin(self, name: str) -> Optional[DataSourcePlugin]:
        """Get data source plugin by name"""
        return self._data_source_plugins.get(name)
    
    def list_available_plugins(self) -> Dict[str, List[str]]:
        """List all available plugins"""
        return {
            "data_sources": list(self._data_source_plugins.keys()),
            "models": list(self._model_plugins.keys()),
            "outputs": list(self._output_plugins.keys())
        }

# Plugin auto-discovery
def discover_plugins():
    """Discover and register plugins"""
    registry = PluginRegistry()
    
    # Scan plugins directory
    plugins_dir = Path(__file__).parent.parent.parent / "plugins"
    
    for plugin_type in ["data_sources", "models", "outputs"]:
        type_dir = plugins_dir / plugin_type
        
        if type_dir.exists():
            for plugin_dir in type_dir.iterdir():
                if plugin_dir.is_dir() and (plugin_dir / "plugin.py").exists():
                    try:
                        spec = importlib.util.spec_from_file_location(
                            f"plugins.{plugin_type}.{plugin_dir.name}.plugin",
                            plugin_dir / "plugin.py"
                        )
                        module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(module)
                        
                        # Find plugin class
                        for attr_name in dir(module):
                            attr = getattr(module, attr_name)
                            if (isinstance(attr, type) and 
                                issubclass(attr, (DataSourcePlugin, ModelPlugin, OutputPlugin)) and
                                attr != DataSourcePlugin and attr != ModelPlugin and attr != OutputPlugin):
                                
                                plugin_instance = attr()
                                
                                if isinstance(plugin_instance, DataSourcePlugin):
                                    registry.register_data_source_plugin(plugin_instance)
                                elif isinstance(plugin_instance, ModelPlugin):
                                    registry.register_model_plugin(plugin_instance)
                                elif isinstance(plugin_instance, OutputPlugin):
                                    registry.register_output_plugin(plugin_instance)
                                
                                break
                    
                    except Exception as e:
                        print(f"Failed to load plugin {plugin_dir.name}: {e}")
    
    return registry
```

## API Development

### Creating New Endpoints

```python
# src/interfaces/api/rest/routes/new_endpoint.py
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from src.application.services import SomeService
from src.shared.container import Container
from .dependencies import require_auth
from .schemas import RequestSchema, ResponseSchema

router = APIRouter(prefix="/new-endpoint", tags=["new-endpoint"])

@router.post("/", response_model=ResponseSchema, status_code=201)
async def create_resource(
    request: RequestSchema,
    current_user: dict = Depends(require_auth),
    service: SomeService = Depends(Container.some_service)
):
    """Create a new resource.
    
    This endpoint creates a new resource with the provided data.
    Requires authentication and appropriate permissions.
    """
    try:
        result = await service.create_resource(request.dict())
        return ResponseSchema.from_domain_object(result)
    
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/", response_model=List[ResponseSchema])
async def list_resources(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    filter_param: Optional[str] = None,
    current_user: dict = Depends(require_auth),
    service: SomeService = Depends(Container.some_service)
):
    """List resources with pagination and filtering."""
    
    filters = {}
    if filter_param:
        filters["filter_param"] = filter_param
    
    results = await service.list_resources(
        limit=limit,
        offset=offset,
        filters=filters
    )
    
    return [ResponseSchema.from_domain_object(result) for result in results]
```

### Request/Response Schemas

```python
# src/interfaces/api/rest/schemas/new_schemas.py
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from src.domain.value_objects import MarketId, CommodityCode

class CreateResourceRequest(BaseModel):
    """Request schema for creating a resource"""
    
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    market_ids: List[str] = Field(..., min_items=1)
    commodity_codes: List[str] = Field(..., min_items=1)
    config: Optional[dict] = None
    
    @validator("market_ids")
    def validate_market_ids(cls, v):
        """Validate market IDs format"""
        for market_id in v:
            try:
                MarketId(market_id)
            except ValueError:
                raise ValueError(f"Invalid market ID: {market_id}")
        return v
    
    @validator("commodity_codes")
    def validate_commodity_codes(cls, v):
        """Validate commodity codes"""
        valid_codes = ["wheat", "rice", "oil", "beans", "sugar"]  # Example
        for code in v:
            if code not in valid_codes:
                raise ValueError(f"Invalid commodity code: {code}")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Market Integration Analysis",
                "description": "Analysis of price transmission patterns",
                "market_ids": ["market_001", "market_002"],
                "commodity_codes": ["wheat", "rice"],
                "config": {
                    "confidence_level": 0.95,
                    "include_diagnostics": True
                }
            }
        }

class ResourceResponse(BaseModel):
    """Response schema for resource data"""
    
    id: str
    name: str
    description: Optional[str]
    status: str
    created_at: datetime
    updated_at: datetime
    results: Optional[dict] = None
    
    @classmethod
    def from_domain_object(cls, domain_obj):
        """Create response from domain object"""
        return cls(
            id=str(domain_obj.id),
            name=domain_obj.name,
            description=domain_obj.description,
            status=domain_obj.status.value,
            created_at=domain_obj.created_at,
            updated_at=domain_obj.updated_at,
            results=domain_obj.results.to_dict() if domain_obj.results else None
        )
    
    class Config:
        schema_extra = {
            "example": {
                "id": "resource_123",
                "name": "Market Integration Analysis",
                "description": "Analysis of price transmission patterns",
                "status": "completed",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:35:00Z",
                "results": {
                    "integration_score": 0.85,
                    "r_squared": 0.72
                }
            }
        }
```

This comprehensive developer documentation provides all the necessary information for contributors to understand the architecture, set up their development environment, follow coding standards, and contribute effectively to the Yemen Market Integration v2 project.