# Yemen Market Integration v2 Operations Guide

## Overview

This operations guide provides comprehensive documentation for production support, monitoring, incident response, and day-to-day operational tasks for the Yemen Market Integration v2 system.

## Table of Contents

- [System Monitoring](#system-monitoring)
- [Alert Response Procedures](#alert-response-procedures)
- [Backup and Recovery](#backup-and-recovery)
- [Scaling Guidelines](#scaling-guidelines)
- [Performance Optimization](#performance-optimization)
- [Security Operations](#security-operations)
- [Incident Response](#incident-response)
- [Maintenance Procedures](#maintenance-procedures)
- [Troubleshooting Runbooks](#troubleshooting-runbooks)

## System Monitoring

### Monitoring Stack

The system uses a comprehensive monitoring stack:

- **Prometheus**: Metrics collection and storage
- **Grafana**: Visualization and dashboards
- **AlertManager**: Alert routing and notification
- **Jaeger**: Distributed tracing
- **ELK Stack**: Log aggregation and analysis
- **Sentry**: Error tracking and performance monitoring

### Key Metrics to Monitor

#### Application Metrics

```yaml
# Core API Metrics
- name: http_requests_total
  description: Total HTTP requests by method, endpoint, and status
  labels: [method, endpoint, status_code]
  
- name: http_request_duration_seconds
  description: HTTP request duration histogram
  labels: [method, endpoint]
  
- name: analysis_queue_size
  description: Number of analyses in queue
  
- name: analysis_completion_time_seconds
  description: Time to complete analysis by type
  labels: [analysis_type, tier]
  
- name: active_analyses_count
  description: Number of currently running analyses
  
- name: database_connections_active
  description: Active database connections
  
- name: redis_connections_active
  description: Active Redis connections
```

#### Infrastructure Metrics

```yaml
# System Resource Metrics
- name: cpu_usage_percent
  description: CPU utilization by pod/node
  
- name: memory_usage_bytes
  description: Memory utilization by pod/node
  
- name: disk_usage_percent
  description: Disk space utilization
  
- name: network_io_bytes
  description: Network I/O statistics
  
# Database Metrics
- name: postgresql_connections_active
  description: Active PostgreSQL connections
  
- name: postgresql_query_duration_seconds
  description: Query execution time
  
- name: postgresql_deadlocks_total
  description: Database deadlock count
  
# Cache Metrics
- name: redis_memory_usage_bytes
  description: Redis memory utilization
  
- name: redis_hit_rate
  description: Cache hit rate percentage
```

### Grafana Dashboards

#### System Overview Dashboard

```json
{
  "dashboard": {
    "title": "Yemen Market Integration - System Overview",
    "tags": ["yemen-market", "overview"],
    "panels": [
      {
        "title": "API Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{ method }} {{ endpoint }}"
          }
        ],
        "yAxes": [
          {
            "label": "Requests/sec"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
            "legendFormat": "Error Rate %"
          }
        ],
        "thresholds": [
          {"color": "green", "value": 0},
          {"color": "yellow", "value": 1},
          {"color": "red", "value": 5}
        ]
      },
      {
        "title": "Response Time (95th percentile)",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Active Analyses",
        "type": "stat",
        "targets": [
          {
            "expr": "active_analyses_count",
            "legendFormat": "Running"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "database_connections_active",
            "legendFormat": "Active connections"
          }
        ]
      },
      {
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "memory_usage_bytes / 1024 / 1024 / 1024",
            "legendFormat": "{{ pod }} Memory (GB)"
          }
        ]
      }
    ]
  }
}
```

#### Analysis Performance Dashboard

```json
{
  "dashboard": {
    "title": "Yemen Market Integration - Analysis Performance",
    "panels": [
      {
        "title": "Analysis Completion Times",
        "type": "heatmap",
        "targets": [
          {
            "expr": "rate(analysis_completion_time_seconds_bucket[5m])",
            "legendFormat": "{{ tier }}"
          }
        ]
      },
      {
        "title": "Queue Depth",
        "type": "graph",
        "targets": [
          {
            "expr": "analysis_queue_size",
            "legendFormat": "Queued analyses"
          }
        ]
      },
      {
        "title": "Success Rate by Tier",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(analysis_completed_total[5m]) / rate(analysis_started_total[5m]) * 100",
            "legendFormat": "{{ tier }} Success Rate %"
          }
        ]
      }
    ]
  }
}
```

### Health Checks

#### Application Health Endpoints

```python
# Health check implementation
@router.get("/health")
async def health_check():
    """Comprehensive health check"""
    
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "yemen-market-integration-api",
        "version": "2.0.0",
        "checks": {}
    }
    
    try:
        # Database connectivity
        await check_database_health()
        health_status["checks"]["database"] = {"status": "healthy"}
    except Exception as e:
        health_status["checks"]["database"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "unhealthy"
    
    try:
        # Redis connectivity
        await check_redis_health()
        health_status["checks"]["redis"] = {"status": "healthy"}
    except Exception as e:
        health_status["checks"]["redis"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "unhealthy"
    
    try:
        # External services
        external_status = await check_external_services()
        health_status["checks"]["external_services"] = external_status
    except Exception as e:
        health_status["checks"]["external_services"] = {"status": "degraded", "error": str(e)}
    
    # Analysis system health
    try:
        queue_size = await get_analysis_queue_size()
        if queue_size > 100:
            health_status["checks"]["analysis_queue"] = {"status": "warning", "queue_size": queue_size}
        else:
            health_status["checks"]["analysis_queue"] = {"status": "healthy", "queue_size": queue_size}
    except Exception as e:
        health_status["checks"]["analysis_queue"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "unhealthy"
    
    return health_status

@router.get("/health/ready")
async def readiness_check():
    """Kubernetes readiness probe"""
    try:
        # Quick database check
        await quick_db_check()
        return {"status": "ready"}
    except Exception:
        raise HTTPException(status_code=503, detail="Service not ready")

@router.get("/health/live")
async def liveness_check():
    """Kubernetes liveness probe"""
    return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}
```

#### Monitoring Scripts

```bash
#!/bin/bash
# check-system-health.sh
# Comprehensive system health monitoring script

set -e

API_URL=${1:-"https://api.yemen-market-integration.org"}
ALERT_WEBHOOK=${2:-"$SLACK_WEBHOOK_URL"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

alert() {
    local message="$1"
    local severity="${2:-warning}"
    
    log "$message"
    
    if [ -n "$ALERT_WEBHOOK" ]; then
        curl -X POST "$ALERT_WEBHOOK" \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 Yemen Market Integration Alert\\n**Severity:** $severity\\n**Message:** $message\"}" \
            --silent
    fi
}

check_api_health() {
    log "Checking API health..."
    
    response=$(curl -s -w "%{http_code}" "$API_URL/health" -o /tmp/health_response.json)
    
    if [ "$response" != "200" ]; then
        alert "API health check failed - HTTP $response" "critical"
        return 1
    fi
    
    status=$(jq -r '.status' /tmp/health_response.json)
    if [ "$status" != "healthy" ]; then
        alert "API reports unhealthy status: $status" "critical"
        return 1
    fi
    
    echo -e "${GREEN}✓${NC} API health check passed"
    return 0
}

check_database_connectivity() {
    log "Checking database connectivity..."
    
    # Use kubectl to check database pod
    db_status=$(kubectl get pods -l app=postgresql -o jsonpath='{.items[0].status.phase}' 2>/dev/null || echo "Unknown")
    
    if [ "$db_status" != "Running" ]; then
        alert "Database pod not running - Status: $db_status" "critical"
        return 1
    fi
    
    echo -e "${GREEN}✓${NC} Database connectivity check passed"
    return 0
}

check_redis_connectivity() {
    log "Checking Redis connectivity..."
    
    redis_status=$(kubectl get pods -l app=redis -o jsonpath='{.items[0].status.phase}' 2>/dev/null || echo "Unknown")
    
    if [ "$redis_status" != "Running" ]; then
        alert "Redis pod not running - Status: $redis_status" "warning"
        return 1
    fi
    
    echo -e "${GREEN}✓${NC} Redis connectivity check passed"
    return 0
}

check_analysis_queue() {
    log "Checking analysis queue..."
    
    queue_size=$(curl -s "$API_URL/metrics" | grep "analysis_queue_size" | awk '{print $2}' || echo "0")
    
    if [ "$queue_size" -gt 100 ]; then
        alert "Analysis queue backlog detected - Size: $queue_size" "warning"
    elif [ "$queue_size" -gt 200 ]; then
        alert "Critical analysis queue backlog - Size: $queue_size" "critical"
        return 1
    fi
    
    echo -e "${GREEN}✓${NC} Analysis queue check passed (Size: $queue_size)"
    return 0
}

check_disk_space() {
    log "Checking disk space..."
    
    # Check all nodes for disk space
    kubectl get nodes -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | while read node; do
        disk_usage=$(kubectl describe node "$node" | grep -A 5 "Allocated resources" | grep "ephemeral-storage" | awk '{print $3}' | sed 's/[()]//g' || echo "0%")
        disk_percent=$(echo "$disk_usage" | sed 's/%//')
        
        if [ "$disk_percent" -gt 85 ]; then
            alert "High disk usage on node $node: $disk_usage" "warning"
        elif [ "$disk_percent" -gt 95 ]; then
            alert "Critical disk usage on node $node: $disk_usage" "critical"
        fi
    done
    
    echo -e "${GREEN}✓${NC} Disk space check completed"
    return 0
}

check_certificate_expiry() {
    log "Checking SSL certificate expiry..."
    
    expiry_date=$(echo | openssl s_client -servername $(echo "$API_URL" | sed 's|.*://||' | sed 's|/.*||') -connect $(echo "$API_URL" | sed 's|.*://||' | sed 's|/.*||'):443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    
    if [ -n "$expiry_date" ]; then
        expiry_epoch=$(date -d "$expiry_date" +%s)
        current_epoch=$(date +%s)
        days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        if [ "$days_until_expiry" -lt 30 ]; then
            alert "SSL certificate expires in $days_until_expiry days" "warning"
        elif [ "$days_until_expiry" -lt 7 ]; then
            alert "SSL certificate expires in $days_until_expiry days" "critical"
        fi
        
        echo -e "${GREEN}✓${NC} SSL certificate valid for $days_until_expiry more days"
    fi
    
    return 0
}

# Main execution
main() {
    log "Starting comprehensive health check..."
    
    failed_checks=0
    
    check_api_health || ((failed_checks++))
    check_database_connectivity || ((failed_checks++))
    check_redis_connectivity || ((failed_checks++))
    check_analysis_queue || ((failed_checks++))
    check_disk_space || ((failed_checks++))
    check_certificate_expiry || ((failed_checks++))
    
    if [ $failed_checks -eq 0 ]; then
        log "All health checks passed ✅"
        exit 0
    else
        log "Health check completed with $failed_checks failed checks ❌"
        exit 1
    fi
}

# Run main function
main "$@"
```

## Alert Response Procedures

### Alert Severity Levels

| Severity | Response Time | Escalation | Examples |
|----------|---------------|------------|----------|
| **Critical** | 15 minutes | Immediate | API down, database failure, security breach |
| **High** | 1 hour | 2 hours | High error rate, performance degradation |
| **Medium** | 4 hours | 8 hours | Queue backlog, cache misses |
| **Low** | 1 business day | 2 business days | Log warnings, maintenance reminders |

### Critical Alerts

#### API Service Down

**Alert**: `up{job="yemen-market-api"} == 0`

**Response Procedure**:
```bash
# 1. Immediate assessment
kubectl get pods -n yemen-market-v2 -l app=yemen-market-api

# 2. Check pod logs
kubectl logs -n yemen-market-v2 deployment/yemen-market-api --tail=100

# 3. Check recent deployments
kubectl rollout history deployment/yemen-market-api -n yemen-market-v2

# 4. Check resource usage
kubectl top pods -n yemen-market-v2

# 5. Emergency restart if needed
kubectl rollout restart deployment/yemen-market-api -n yemen-market-v2

# 6. Monitor recovery
kubectl rollout status deployment/yemen-market-api -n yemen-market-v2
```

**Escalation**: 
- If not resolved in 15 minutes, escalate to senior engineer
- If not resolved in 30 minutes, escalate to engineering manager

#### Database Connection Failure

**Alert**: `postgresql_up == 0`

**Response Procedure**:
```bash
# 1. Check database pod status
kubectl get pods -n yemen-market-v2 -l app=postgresql

# 2. Check database logs
kubectl logs -n yemen-market-v2 -l app=postgresql --tail=200

# 3. Check persistent volume status
kubectl get pv,pvc -n yemen-market-v2

# 4. Attempt connection test
kubectl exec -it deployment/yemen-market-api -n yemen-market-v2 -- \
  psql $DATABASE_URL -c "SELECT 1"

# 5. If master is down, check for standby promotion
helm upgrade postgresql bitnami/postgresql-ha \
  --namespace yemen-market-v2 \
  --set postgresql.repmgr.partner_nodes=""
```

#### High Error Rate

**Alert**: `rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05`

**Response Procedure**:
```bash
# 1. Identify error patterns
kubectl logs -n yemen-market-v2 deployment/yemen-market-api --tail=500 | grep ERROR

# 2. Check specific error endpoints
curl -H "Authorization: Bearer $API_KEY" \
     https://api.yemen-market-integration.org/metrics | \
     grep http_requests_total | grep "5.."

# 3. Check external service dependencies
curl -s https://api.wfp.org/health || echo "WFP API down"
curl -s https://api.acleddata.com/health || echo "ACLED API down"

# 4. Scale up if resource constrained
kubectl scale deployment yemen-market-api --replicas=5 -n yemen-market-v2

# 5. Check for recent configuration changes
kubectl get configmap yemen-market-config -n yemen-market-v2 -o yaml
```

### High Priority Alerts

#### Performance Degradation

**Alert**: `histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2`

**Response Procedure**:
```bash
# 1. Check current load
kubectl top pods -n yemen-market-v2

# 2. Identify slow queries
kubectl exec -it postgresql-primary-0 -n yemen-market-v2 -- \
  psql -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10"

# 3. Check cache performance
kubectl exec -it redis-master-0 -n yemen-market-v2 -- \
  redis-cli info stats | grep hit_rate

# 4. Scale horizontally if needed
kubectl patch hpa yemen-market-api-hpa -n yemen-market-v2 \
  -p '{"spec":{"maxReplicas":10}}'
```

#### Queue Backlog

**Alert**: `analysis_queue_size > 100`

**Response Procedure**:
```bash
# 1. Check worker status
kubectl get pods -n yemen-market-v2 -l app=yemen-market-worker

# 2. Scale workers
kubectl scale deployment yemen-market-worker --replicas=8 -n yemen-market-v2

# 3. Check for stuck analyses
curl -H "Authorization: Bearer $API_KEY" \
     "https://api.yemen-market-integration.org/api/v1/analysis?status=running&limit=10"

# 4. Clear failed analyses if necessary
# Manual intervention may be required
```

### Alert Routing Configuration

```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
    repeat_interval: 15m
  - match:
      severity: warning
    receiver: 'warning-alerts'
    repeat_interval: 1h

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'https://hooks.slack.com/services/your/slack/webhook'

- name: 'critical-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
    body: |
      Alert: {{ .GroupLabels.alertname }}
      Severity: {{ .CommonLabels.severity }}
      
      {{ range .Alerts }}
      Description: {{ .Annotations.description }}
      Runbook: {{ .Annotations.runbook_url }}
      {{ end }}
  pagerduty_configs:
  - routing_key: 'your-pagerduty-key'
    description: '{{ .GroupLabels.alertname }}'

- name: 'warning-alerts'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/your/slack/webhook'
    channel: '#alerts'
    text: '⚠️ {{ .GroupLabels.alertname }}: {{ .CommonAnnotations.description }}'
```

## Backup and Recovery

### Backup Strategy

#### Database Backups

**Daily Full Backups**:
```bash
#!/bin/bash
# daily-backup.sh

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="yemen_market_v2_backup_${BACKUP_DATE}.sql"
S3_BUCKET="yemen-market-v2-backups"
RETENTION_DAYS=30

# Create backup
kubectl exec postgresql-primary-0 -n yemen-market-v2 -- \
  pg_dumpall -U postgres > /tmp/$BACKUP_FILE

# Compress backup
gzip /tmp/$BACKUP_FILE

# Upload to S3
aws s3 cp /tmp/${BACKUP_FILE}.gz s3://$S3_BUCKET/daily/

# Clean up local file
rm /tmp/${BACKUP_FILE}.gz

# Remove old backups
aws s3 ls s3://$S3_BUCKET/daily/ | \
  awk '{print $4}' | \
  sort | \
  head -n -$RETENTION_DAYS | \
  xargs -I {} aws s3 rm s3://$S3_BUCKET/daily/{}

# Verify backup
aws s3 ls s3://$S3_BUCKET/daily/ | tail -1

echo "Backup completed: ${BACKUP_FILE}.gz"
```

**Point-in-Time Recovery Setup**:
```yaml
# postgresql-backup-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-backup-config
  namespace: yemen-market-v2
data:
  postgresql.conf: |
    wal_level = replica
    archive_mode = on
    archive_command = 'aws s3 cp %p s3://yemen-market-v2-wal-archive/%f'
    max_wal_senders = 3
    wal_keep_segments = 64
  
  recovery.conf: |
    restore_command = 'aws s3 cp s3://yemen-market-v2-wal-archive/%f %p'
    recovery_target_time = '2024-01-15 14:30:00'
```

#### Application Data Backups

```bash
#!/bin/bash
# backup-application-data.sh

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
NAMESPACE="yemen-market-v2"

# Backup ConfigMaps and Secrets
kubectl get configmaps -n $NAMESPACE -o yaml > configmaps_$BACKUP_DATE.yaml
kubectl get secrets -n $NAMESPACE -o yaml > secrets_$BACKUP_DATE.yaml

# Backup analysis results
kubectl exec deployment/yemen-market-api -n $NAMESPACE -- \
  python scripts/export_analysis_results.py --output=/tmp/analysis_results_$BACKUP_DATE.json

# Copy from pod to local
kubectl cp $NAMESPACE/$(kubectl get pod -l app=yemen-market-api -o jsonpath='{.items[0].metadata.name}'):/tmp/analysis_results_$BACKUP_DATE.json \
  ./analysis_results_$BACKUP_DATE.json

# Upload to S3
aws s3 cp analysis_results_$BACKUP_DATE.json s3://yemen-market-v2-backups/application-data/

echo "Application data backup completed"
```

### Recovery Procedures

#### Database Recovery from Full Backup

```bash
#!/bin/bash
# restore-database.sh

BACKUP_FILE=${1:-"latest"}
NAMESPACE="yemen-market-v2"

if [ "$BACKUP_FILE" == "latest" ]; then
    BACKUP_FILE=$(aws s3 ls s3://yemen-market-v2-backups/daily/ | sort | tail -1 | awk '{print $4}')
fi

echo "Restoring from backup: $BACKUP_FILE"

# 1. Scale down application
kubectl scale deployment yemen-market-api --replicas=0 -n $NAMESPACE
kubectl scale deployment yemen-market-worker --replicas=0 -n $NAMESPACE

# 2. Download backup
aws s3 cp s3://yemen-market-v2-backups/daily/$BACKUP_FILE /tmp/

# 3. Extract backup
gunzip /tmp/$BACKUP_FILE

# 4. Drop existing database (CAUTION!)
kubectl exec postgresql-primary-0 -n $NAMESPACE -- \
  psql -U postgres -c "DROP DATABASE IF EXISTS yemen_market_v2"

# 5. Restore from backup
kubectl exec -i postgresql-primary-0 -n $NAMESPACE -- \
  psql -U postgres < /tmp/${BACKUP_FILE%.gz}

# 6. Verify restore
kubectl exec postgresql-primary-0 -n $NAMESPACE -- \
  psql -U postgres -d yemen_market_v2 -c "SELECT COUNT(*) FROM markets"

# 7. Scale up application
kubectl scale deployment yemen-market-api --replicas=3 -n $NAMESPACE
kubectl scale deployment yemen-market-worker --replicas=2 -n $NAMESPACE

# 8. Verify application health
kubectl rollout status deployment/yemen-market-api -n $NAMESPACE

echo "Database restore completed"
```

#### Point-in-Time Recovery

```bash
#!/bin/bash
# point-in-time-recovery.sh

RECOVERY_TIME=${1:-"2024-01-15 14:30:00"}
NAMESPACE="yemen-market-v2"

echo "Performing point-in-time recovery to: $RECOVERY_TIME"

# 1. Scale down applications
kubectl scale deployment yemen-market-api --replicas=0 -n $NAMESPACE
kubectl scale deployment yemen-market-worker --replicas=0 -n $NAMESPACE

# 2. Stop PostgreSQL
kubectl scale statefulset postgresql-primary --replicas=0 -n $NAMESPACE

# 3. Download base backup
LATEST_BACKUP=$(aws s3 ls s3://yemen-market-v2-backups/daily/ | sort | tail -1 | awk '{print $4}')
aws s3 cp s3://yemen-market-v2-backups/daily/$LATEST_BACKUP /tmp/

# 4. Restore base backup to temporary location
mkdir -p /tmp/postgres_recovery
gunzip /tmp/$LATEST_BACKUP
psql -f /tmp/${LATEST_BACKUP%.gz} postgresql://postgres@localhost/template1

# 5. Configure recovery
cat > /tmp/recovery.conf << EOF
restore_command = 'aws s3 cp s3://yemen-market-v2-wal-archive/%f %p'
recovery_target_time = '$RECOVERY_TIME'
recovery_target_action = 'promote'
EOF

# 6. Start PostgreSQL with recovery configuration
kubectl scale statefulset postgresql-primary --replicas=1 -n $NAMESPACE

# 7. Wait for recovery completion
kubectl wait --for=condition=ready pod/postgresql-primary-0 -n $NAMESPACE --timeout=600s

# 8. Verify recovery point
RECOVERED_TIME=$(kubectl exec postgresql-primary-0 -n $NAMESPACE -- \
  psql -U postgres -t -c "SELECT pg_last_xact_replay_timestamp()")

echo "Recovery completed. Last transaction time: $RECOVERED_TIME"

# 9. Scale up applications
kubectl scale deployment yemen-market-api --replicas=3 -n $NAMESPACE
kubectl scale deployment yemen-market-worker --replicas=2 -n $NAMESPACE

echo "Point-in-time recovery completed successfully"
```

## Scaling Guidelines

### Horizontal Pod Autoscaling (HPA)

```yaml
# hpa-configuration.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: yemen-market-api-hpa
  namespace: yemen-market-v2
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: yemen-market-api
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 4
        periodSeconds: 15
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      selectPolicy: Min

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: yemen-market-worker-hpa
  namespace: yemen-market-v2
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: yemen-market-worker
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Pods
    pods:
      metric:
        name: analysis_queue_size_per_worker
      target:
        type: AverageValue
        averageValue: "5"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 120
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 600
      policies:
      - type: Pods
        value: 1
        periodSeconds: 180
```

### Vertical Pod Autoscaling (VPA)

```yaml
# vpa-configuration.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: yemen-market-api-vpa
  namespace: yemen-market-v2
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: yemen-market-api
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: api
      minAllowed:
        cpu: 100m
        memory: 512Mi
      maxAllowed:
        cpu: 2
        memory: 4Gi
      controlledResources: ["cpu", "memory"]
```

### Database Scaling

#### Read Replicas

```yaml
# postgresql-ha-values.yaml
postgresql:
  replication:
    enabled: true
    numSynchronousReplicas: 1
    synchronousCommit: "on"
    
pgpool:
  replicaCount: 2
  loadBalanceMode: true
  childMaxConnections: 25
  
  readOnlyLoadBalance:
    enabled: true
    weight: 0.5  # 50% of read queries to replicas
```

#### Connection Pooling

```yaml
# pgbouncer-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: pgbouncer-config
  namespace: yemen-market-v2
data:
  pgbouncer.ini: |
    [databases]
    yemen_market_v2 = host=postgresql-primary port=5432 dbname=yemen_market_v2
    
    [pgbouncer]
    listen_port = 6432
    listen_addr = 0.0.0.0
    auth_type = md5
    auth_file = /etc/pgbouncer/userlist.txt
    
    pool_mode = transaction
    server_reset_query = DISCARD ALL
    max_client_conn = 1000
    default_pool_size = 20
    reserve_pool_size = 5
    reserve_pool_timeout = 3
    
    server_lifetime = 3600
    server_idle_timeout = 600
    
    log_connections = 1
    log_disconnections = 1
```

### Scaling Decision Matrix

| Metric | Threshold | Action | Notes |
|--------|-----------|---------|-------|
| **CPU Usage** | > 70% | Scale out API pods | Monitor for 2 minutes before scaling |
| **Memory Usage** | > 80% | Scale out API pods | Check for memory leaks first |
| **Request Rate** | > 100 RPS/pod | Scale out API pods | Consider caching optimizations |
| **Queue Size** | > 50 items | Scale out workers | Check for stuck analyses |
| **Database Connections** | > 80% of max | Add read replicas | Enable connection pooling |
| **Response Time** | > 2 seconds (95th) | Scale out + optimize | Profile slow queries |
| **Error Rate** | > 5% | Investigate + scale | Fix errors before scaling |

## Performance Optimization

### Database Optimization

#### Query Performance Monitoring

```sql
-- Monitor slow queries
SELECT
    query,
    calls,
    total_time,
    mean_time,
    stddev_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
WHERE mean_time > 100  -- Queries taking more than 100ms on average
ORDER BY mean_time DESC
LIMIT 20;

-- Check index usage
SELECT
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes
WHERE idx_scan < 100  -- Indexes with low usage
ORDER BY idx_scan;

-- Monitor connection statistics
SELECT
    datname,
    numbackends,
    xact_commit,
    xact_rollback,
    blks_read,
    blks_hit,
    tup_returned,
    tup_fetched,
    tup_inserted,
    tup_updated,
    tup_deleted
FROM pg_stat_database
WHERE datname = 'yemen_market_v2';
```

#### Index Optimization

```sql
-- Create optimal indexes for common queries
CREATE INDEX CONCURRENTLY idx_prices_market_commodity_date 
ON prices (market_id, commodity_id, date DESC);

CREATE INDEX CONCURRENTLY idx_conflict_events_location_date 
ON conflict_events USING GIST (location, date);

CREATE INDEX CONCURRENTLY idx_analysis_status_created 
ON analyses (status, created_at DESC) 
WHERE status IN ('running', 'pending');

-- Partial indexes for common filters
CREATE INDEX CONCURRENTLY idx_prices_recent 
ON prices (market_id, commodity_id, price) 
WHERE date >= CURRENT_DATE - INTERVAL '1 year';
```

### Application Performance

#### Caching Strategy

```python
# Redis caching implementation
class CacheManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.default_ttl = 3600  # 1 hour
        
    async def get_market_prices(self, market_id, start_date, end_date):
        """Get cached market prices with fallback to database"""
        
        cache_key = f"prices:{market_id}:{start_date}:{end_date}"
        
        # Try cache first
        cached_data = await self.redis.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
        
        # Fallback to database
        data = await self.fetch_from_database(market_id, start_date, end_date)
        
        # Cache for future requests
        await self.redis.setex(
            cache_key, 
            self.default_ttl, 
            json.dumps(data, default=str)
        )
        
        return data
    
    async def invalidate_market_cache(self, market_id):
        """Invalidate all cached data for a market"""
        
        pattern = f"prices:{market_id}:*"
        keys = await self.redis.keys(pattern)
        
        if keys:
            await self.redis.delete(*keys)
```

#### Connection Pool Optimization

```python
# Database connection pool configuration
DATABASE_CONFIG = {
    "pool_size": 20,
    "max_overflow": 10,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": True,
    "echo": False,  # Set to True for query debugging
}

# Redis connection pool
REDIS_CONFIG = {
    "connection_pool_kwargs": {
        "max_connections": 50,
        "socket_keepalive": True,
        "socket_keepalive_options": {},
        "health_check_interval": 30,
    }
}
```

### API Performance Optimization

```python
# Rate limiting and throttling
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)

@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    """Apply rate limiting based on API key tier"""
    
    api_key = request.headers.get("Authorization", "").replace("Bearer ", "")
    
    if api_key:
        user_tier = await get_user_tier(api_key)
        rate_limit = get_rate_limit_for_tier(user_tier)
        
        # Apply dynamic rate limiting
        request.state.rate_limit = rate_limit
    
    response = await call_next(request)
    return response

@router.get("/markets")
@limiter.limit("100/minute")  # Default limit
async def list_markets(request: Request):
    """List markets with rate limiting"""
    pass

# Response compression
@app.middleware("http") 
async def compression_middleware(request: Request, call_next):
    """Compress responses for better performance"""
    
    response = await call_next(request)
    
    # Compress large responses
    if (response.headers.get("content-length", 0) > 1024 and 
        "gzip" in request.headers.get("accept-encoding", "")):
        
        # FastAPI handles this automatically with GZipMiddleware
        pass
    
    return response
```

This comprehensive operations guide provides the foundation for reliable production operation of the Yemen Market Integration v2 system, covering monitoring, alerting, scaling, backup/recovery, and performance optimization procedures.