# V2 Policy Model Integration Guide

## Overview

The V2 policy models are now fully integrated with the real data pipeline, enabling production-ready policy analysis for Yemen's humanitarian and development programs. This guide covers the implementation details and usage patterns.

## Architecture

### Key Components

1. **Policy Models** (`v2/src/core/models/policy/`)
   - `WelfareImpactModel`: Analyzes welfare impacts of interventions
   - `EarlyWarningSystem`: Generates alerts for food security crises

2. **Policy Services** (`v2/src/application/services/`)
   - `PolicyDataAdapter`: Converts panel data to policy model formats
   - `PolicyOrchestrator`: Manages policy analysis workflows

3. **Policy Repository** (`v2/src/infrastructure/persistence/`)
   - `PolicyResultsRepository`: Stores and retrieves analysis results

4. **API Endpoints** (`v2/src/interfaces/api/`)
   - `policy_endpoints.py`: RESTful API for policy analysis

## Data Flow

```
Panel Data Pipeline → PolicyDataAdapter → Policy Models → PolicyOrchestrator → API/Storage
                           ↓                    ↓                  ↓
                    Data Transformation   Analysis Results   Orchestration
```

## Key Features

### 1. Welfare Impact Analysis

Analyzes the welfare impacts of policy interventions:

```python
# Example: Cash Transfer Analysis
intervention = PolicyIntervention(
    type='cash_transfer',
    target_markets=['Sanaa', 'Aden'],
    target_commodities=['WHEAT', 'RICE'],
    magnitude=50000,  # YER per household
    duration_months=6,
    targeting_criteria={'income_below': 200000},
    budget_constraint=Money(10_000_000_000, "YER")
)

results = await orchestrator.analyze_policy_intervention(
    intervention=intervention,
    panel_data=panel_data,
    household_survey_path="path/to/survey.csv"
)
```

**Outputs:**
- Consumer & producer surplus changes
- Government costs and deadweight loss
- Distributional effects by income quintile
- Cost per beneficiary
- Optimal intervention parameters

### 2. Early Warning System

Generates alerts based on market anomalies and predictions:

```python
warnings = await orchestrator.generate_early_warnings(
    panel_data=current_data,
    conflict_data=acled_data,
    climate_data=rainfall_data,
    forecast_horizon=30
)
```

**Alert Types:**
- Price spikes (current and forecasted)
- Supply chain disruptions
- Food security crises
- Market functionality degradation

**Alert Levels:**
- CRITICAL (IPC Phase 4-5)
- HIGH (IPC Phase 3)
- MEDIUM (Emerging risks)
- LOW (Monitoring required)

### 3. Policy Comparison

Compares multiple interventions for cost-effectiveness:

```python
interventions = [
    cash_transfer_intervention,
    price_subsidy_intervention,
    in_kind_distribution
]

comparison = await orchestrator.run_policy_comparison(
    interventions=interventions,
    panel_data=panel_data
)
```

### 4. Impact Monitoring

Tracks ongoing interventions against expected outcomes:

```python
monitoring = await orchestrator.monitor_policy_impacts(
    active_interventions=active_list,
    panel_data=recent_data
)
```

## Data Requirements

### Panel Data Structure
- Market-level price data with temporal coverage
- Commodity prices in local currency (YER)
- Market identifiers and geographic information

### Household Survey Data (Optional)
- Income distribution
- Household size
- Consumption patterns by commodity
- Geographic distribution

### Conflict Data (For Early Warning)
- ACLED event data with dates and locations
- Fatality counts
- Event types

### Climate Data (Optional)
- Rainfall measurements
- Temperature data
- Drought indicators

## API Endpoints

### Welfare Impact Analysis
```
POST /api/v1/policy/welfare-impact/analyze
{
    "type": "cash_transfer",
    "target_markets": ["Sanaa", "Aden"],
    "target_commodities": ["WHEAT", "RICE"],
    "magnitude": 50000,
    "duration_months": 6,
    "targeting_criteria": {"income_below": 200000},
    "budget_constraint": 10000000000
}
```

### Early Warning Generation
```
POST /api/v1/policy/early-warning/generate
{
    "forecast_horizon": 30,
    "include_conflict_data": true,
    "include_climate_data": false,
    "alert_threshold": "MEDIUM"
}
```

### Policy Comparison
```
POST /api/v1/policy/compare-interventions
{
    "interventions": [...],
    "household_survey_path": "path/to/survey.csv"
}
```

### Recent Warnings
```
GET /api/v1/policy/early-warning/recent?days=7&min_alert_level=HIGH
```

### Performance Metrics
```
GET /api/v1/policy/performance-metrics?start_date=2023-01-01&end_date=2023-12-31
```

## Exchange Rate Considerations

The policy models are designed to handle Yemen's dual exchange rate system:

1. **Automatic Currency Detection**: Models detect whether prices are in YER or USD
2. **Exchange Rate Parameters**: Can specify different rates for different regions
3. **Welfare Calculations**: Account for purchasing power differences
4. **Result Reporting**: Always specifies currency for monetary values

## Elasticity Estimation

The system estimates demand and supply elasticities from panel data:

### Default Elasticities (Yemen-specific)
- **Staples (Wheat, Rice)**: -0.35 to -0.40 (inelastic)
- **Proteins (Beans, Meat)**: -0.55 to -0.65 (more elastic)
- **Fuel**: -0.25 (very inelastic)
- **Salt**: -0.15 (extremely inelastic)

### Supply Elasticities
- Most goods: 0.15-0.25 (limited due to import dependency)
- Local production: 0.30-0.40 (slightly more responsive)

## Result Storage

All analysis results are automatically stored with:
- Unique identifiers
- Timestamps
- Full analysis parameters
- Metadata for search and retrieval

Results are saved in:
- JSON format for full details
- CSV format for key metrics
- Searchable by date, type, and alert level

## Integration with V2 Pipeline

The policy models seamlessly integrate with:
1. **Panel Builder** (Task 30): Provides balanced panel data
2. **Data Ingestion** (Task 29): Supplies real-time price updates
3. **Analysis Service** (Task 31): Orchestrates complex workflows
4. **Three-tier Models**: Uses econometric results for parameters

## Best Practices

1. **Data Quality**: Ensure panel data is balanced and recent
2. **Household Surveys**: Use actual survey data when available
3. **Conflict Integration**: Always include conflict data for comprehensive analysis
4. **Budget Constraints**: Set realistic budget limits for interventions
5. **Monitoring**: Track active interventions continuously
6. **Alert Response**: Act on HIGH/CRITICAL alerts immediately

## Error Handling

The system includes comprehensive error handling:
- Data validation before analysis
- Graceful handling of missing data
- Clear error messages for API responses
- Automatic retry for transient failures
- Result persistence even on partial failure

## Performance Considerations

- Panel data is processed efficiently using vectorized operations
- Early warning models are pre-trained for fast inference
- Results are cached to avoid redundant calculations
- Background tasks handle long-running analyses
- API responses return immediately with job IDs

## Future Enhancements

1. **Machine Learning Models**: Deep learning for price prediction
2. **Real-time Streaming**: Connect to live price feeds
3. **Geographic Visualization**: Interactive maps of alerts
4. **Mobile Alerts**: Push notifications for critical warnings
5. **Multi-language Support**: Arabic interface for local users