openapi: 3.0.3
info:
  title: Yemen Market Integration API
  description: |
    RESTful API for econometric analysis of market integration in Yemen.
    
    ## Overview
    
    The Yemen Market Integration API v2 provides comprehensive econometric analysis capabilities for studying market price integration patterns in conflict settings. The API follows REST principles and supports real-time updates via Server-Sent Events (SSE).
    
    ## Key Features
    
    - **Three-Tier Analysis**: Pooled panel, commodity-specific VECM, and factor validation models
    - **Real-time Updates**: Stream analysis progress via Server-Sent Events
    - **Comprehensive Data**: Markets, commodities, prices, and conflict data
    - **Authentication**: JWT-based authentication with role-based access control
    - **Validation**: Input validation and econometric diagnostic tests
    - **Scalability**: Async processing with background job management
    
    ## Authentication
    
    All API endpoints require authentication. Use one of the following methods:
    
    ### API Key Authentication
    ```
    Authorization: Bearer YOUR_API_KEY
    ```
    
    ### JWT Token Authentication
    ```
    Authorization: Bearer YOUR_JWT_TOKEN
    ```
    
    ## Rate Limiting
    
    API requests are rate-limited to prevent abuse:
    - **Standard endpoints**: 1000 requests per hour per API key
    - **Analysis endpoints**: 10 concurrent analyses per API key
    - **SSE connections**: 5 concurrent connections per API key
    
    ## Error Handling
    
    The API uses standard HTTP status codes and returns detailed error information:
    
    ```json
    {
      "error": "ValidationError",
      "message": "End date must be after start date",
      "details": {
        "field": "end_date",
        "value": "2023-01-01",
        "constraint": "must_be_after_start_date"
      },
      "request_id": "req_abc123"
    }
    ```
    
  version: "2.0.0"
  contact:
    name: Yemen Market Integration Team
    email: <EMAIL>
    url: https://yemen-market-integration.org
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://yemen-market-integration.org/terms

servers:
  - url: https://api.yemen-market-integration.org/api/v1
    description: Production server
  - url: https://staging-api.yemen-market-integration.org/api/v1
    description: Staging server
  - url: http://localhost:8000/api/v1
    description: Local development server

security:
  - ApiKeyAuth: []
  - JWTAuth: []

paths:
  /analysis/three-tier:
    post:
      summary: Create Three-Tier Analysis
      description: |
        Initiates a comprehensive three-tier econometric analysis of market integration.
        
        The analysis includes:
        1. **Tier 1**: Pooled panel regression with fixed effects
        2. **Tier 2**: Commodity-specific Vector Error Correction Models (VECM)
        3. **Tier 3**: Factor analysis validation and robustness checks
        
        ## Processing Flow
        
        1. Request validation and data preparation
        2. Background analysis execution with progress updates
        3. Real-time status updates via SSE (optional)
        4. Results storage and notification upon completion
        
        ## Estimated Duration
        
        - **Small dataset** (< 1000 observations): 2-5 minutes
        - **Medium dataset** (1000-10000 observations): 5-15 minutes  
        - **Large dataset** (> 10000 observations): 15-60 minutes
        
        The actual duration depends on data complexity, number of commodities, and diagnostic requirements.
      operationId: createThreeTierAnalysis
      tags:
        - Analysis
      security:
        - ApiKeyAuth: []
        - JWTAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ThreeTierAnalysisRequest'
            examples:
              basic_analysis:
                summary: Basic Analysis
                description: Simple analysis with default settings
                value:
                  start_date: "2023-01-01"
                  end_date: "2023-12-31"
                  markets: ["Sana'a", "Aden", "Taiz"]
                  commodities: ["wheat", "rice", "oil"]
                  confidence_level: 0.95
                  include_diagnostics: true
              advanced_analysis:
                summary: Advanced Analysis
                description: Analysis with custom configuration and commodity groups
                value:
                  start_date: "2020-01-01"
                  end_date: "2023-12-31"
                  markets: ["Sana'a", "Aden", "Taiz", "Hodeidah", "Ibb"]
                  commodities: ["wheat", "rice", "beans", "oil", "sugar"]
                  commodity_groups: 
                    cereals: ["wheat", "rice"]
                    proteins: ["beans"]
                    fats: ["oil"]
                    other: ["sugar"]
                  confidence_level: 0.99
                  include_diagnostics: true
                  tier1_config:
                    include_time_trends: true
                    cluster_standard_errors: true
                  tier2_config:
                    max_lags: 4
                    cointegration_test: "johansen"
                  tier3_config:
                    factor_method: "pca"
                    n_factors: 3
      responses:
        '202':
          description: Analysis request accepted and queued for processing
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisResponse'
              examples:
                success_response:
                  summary: Successful Analysis Creation
                  value:
                    id: "analysis_abc123def456"
                    status: "pending"
                    message: "Analysis queued for processing"
                    estimated_duration_seconds: 300
                    created_at: "2024-01-15T10:30:00Z"
                    sse_endpoint: "/api/v1/analysis/abc123def456/status"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                validation_error:
                  summary: Validation Error
                  value:
                    error: "ValidationError"
                    message: "End date must be after start date"
                    details:
                      field: "end_date"
                      value: "2022-01-01"
                      constraint: "must_be_after_start_date"
                    request_id: "req_abc123"
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '429':
          $ref: '#/components/responses/RateLimitError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /analysis/{id}:
    get:
      summary: Get Analysis Status
      description: |
        Retrieves the current status and progress of an analysis job.
        
        ## Status Values
        
        - **pending**: Analysis is queued and waiting to start
        - **running**: Analysis is currently executing
        - **completed**: Analysis finished successfully
        - **failed**: Analysis encountered an error
        - **cancelled**: Analysis was cancelled by user
        
        ## Progress Tracking
        
        The progress field indicates completion percentage (0-100) and includes tier-specific progress for detailed monitoring.
      operationId: getAnalysisStatus
      tags:
        - Analysis
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier for the analysis
          schema:
            type: string
            example: "analysis_abc123def456"
      responses:
        '200':
          description: Analysis status information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisStatusResponse'
              examples:
                running_analysis:
                  summary: Running Analysis
                  value:
                    id: "analysis_abc123def456"
                    type: "three_tier"
                    status: "running"
                    progress: 45
                    start_time: "2024-01-15T10:30:00Z"
                    end_time: null
                    error: null
                    tiers_progress:
                      tier1:
                        status: "completed"
                        progress: 100
                        duration_seconds: 120
                      tier2:
                        status: "running"
                        progress: 30
                        current_commodity: "wheat"
                        commodities_completed: 2
                        commodities_total: 5
                      tier3:
                        status: "pending"
                        progress: 0
                    results: null
                completed_analysis:
                  summary: Completed Analysis
                  value:
                    id: "analysis_abc123def456"
                    type: "three_tier"
                    status: "completed"
                    progress: 100
                    start_time: "2024-01-15T10:30:00Z"
                    end_time: "2024-01-15T10:35:30Z"
                    error: null
                    tiers_progress:
                      tier1:
                        status: "completed"
                        progress: 100
                        duration_seconds: 120
                      tier2:
                        status: "completed"
                        progress: 100
                        duration_seconds: 180
                      tier3:
                        status: "completed"
                        progress: 100
                        duration_seconds: 30
                    results:
                      summary:
                        total_observations: 15420
                        markets_analyzed: 5
                        commodities_analyzed: 3
                        integration_score: 0.67
        '404':
          description: Analysis not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_found:
                  summary: Analysis Not Found
                  value:
                    error: "NotFoundError"
                    message: "Analysis with ID 'invalid_id' not found"
                    request_id: "req_xyz789"
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /analysis/{id}/status:
    get:
      summary: Stream Analysis Status (SSE)
      description: |
        Establishes a Server-Sent Events (SSE) connection to stream real-time analysis status updates.
        
        ## Event Types
        
        - **initial**: Initial status when connection is established
        - **progress**: Progress updates during analysis execution
        - **tier_started**: When a new tier begins processing
        - **tier_completed**: When a tier completes successfully
        - **completed**: Analysis completed successfully
        - **failed**: Analysis encountered an error
        - **cancelled**: Analysis was cancelled
        
        ## Connection Management
        
        - **Heartbeat**: Server sends heartbeat every 30 seconds to keep connection alive
        - **Timeout**: Connections automatically close after 5 minutes of inactivity
        - **Retry**: Clients should implement automatic reconnection with exponential backoff
        
        ## Example Usage
        
        ```javascript
        const eventSource = new EventSource('/api/v1/analysis/abc123/status');
        
        eventSource.onmessage = function(event) {
          const data = JSON.parse(event.data);
          console.log('Status update:', data);
        };
        
        eventSource.onerror = function(event) {
          console.error('SSE error:', event);
        };
        ```
      operationId: streamAnalysisStatus
      tags:
        - Analysis
        - Real-time
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier for the analysis
          schema:
            type: string
            example: "analysis_abc123def456"
      responses:
        '200':
          description: SSE stream of analysis status updates
          content:
            text/event-stream:
              schema:
                type: string
                description: Server-Sent Events stream
              examples:
                sse_events:
                  summary: SSE Event Stream
                  value: |
                    data: {
                      "event": "initial",
                      "analysis_id": "analysis_abc123def456",
                      "status": "running",
                      "progress": 25,
                      "timestamp": "2024-01-15T10:32:00Z"
                    }
                    
                    data: {
                      "event": "progress",
                      "analysis_id": "analysis_abc123def456",
                      "status": "running",
                      "progress": 50,
                      "tier": "tier2",
                      "message": "Processing commodity: wheat",
                      "timestamp": "2024-01-15T10:33:30Z"
                    }
                    
                    data: {
                      "event": "completed",
                      "analysis_id": "analysis_abc123def456",
                      "status": "completed",
                      "progress": 100,
                      "timestamp": "2024-01-15T10:35:30Z"
                    }
        '404':
          description: Analysis not found
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /analysis/{id}/results:
    get:
      summary: Get Analysis Results
      description: |
        Retrieves complete results for a successfully completed analysis.
        
        ## Result Structure
        
        Results are organized by tier and include:
        
        ### Tier 1 Results (Pooled Panel)
        - Model coefficients and standard errors
        - R-squared and diagnostic statistics
        - Fixed effects estimates
        - Residual analysis
        
        ### Tier 2 Results (Commodity VECM)
        - Cointegration test results
        - VECM coefficient estimates
        - Impulse response functions
        - Variance decomposition
        
        ### Tier 3 Results (Factor Analysis)
        - Principal component loadings
        - Explained variance ratios
        - Factor scores by market
        - Validation metrics
        
        ## Download Formats
        
        Results can be downloaded in multiple formats using the `format` query parameter:
        - `json` (default): Structured JSON format
        - `csv`: Tabular data in CSV format
        - `excel`: Multi-sheet Excel workbook
        - `latex`: LaTeX tables for academic papers
      operationId: getAnalysisResults
      tags:
        - Analysis
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier for the analysis
          schema:
            type: string
            example: "analysis_abc123def456"
        - name: format
          in: query
          required: false
          description: Output format for results
          schema:
            type: string
            enum: [json, csv, excel, latex]
            default: json
        - name: include_diagnostics
          in: query
          required: false
          description: Include diagnostic test results
          schema:
            type: boolean
            default: true
        - name: include_plots
          in: query
          required: false
          description: Include base64-encoded plot data
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Complete analysis results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisResultsResponse'
        '400':
          description: Analysis not completed or invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_completed:
                  summary: Analysis Not Completed
                  value:
                    error: "BadRequestError"
                    message: "Analysis abc123 is not completed. Current status: running"
                    request_id: "req_xyz789"
        '404':
          description: Analysis or results not found
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /analysis/{id}:
    delete:
      summary: Cancel or Delete Analysis
      description: |
        Cancels a running analysis or deletes a completed/failed analysis.
        
        ## Behavior by Status
        
        - **pending/running**: Analysis is cancelled and cleaned up
        - **completed/failed**: Analysis data is deleted from storage
        - **cancelled**: No action taken (already cancelled)
        
        ## Cleanup Process
        
        When an analysis is cancelled or deleted:
        1. Processing is stopped immediately
        2. Temporary files are cleaned up
        3. Database records are marked as deleted
        4. SSE connections are closed
        5. Background tasks are cancelled
        
        **Note**: This action cannot be undone. Ensure you have downloaded any required results before deletion.
      operationId: deleteAnalysis
      tags:
        - Analysis
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier for the analysis
          schema:
            type: string
            example: "analysis_abc123def456"
      responses:
        '204':
          description: Analysis successfully cancelled or deleted
        '404':
          description: Analysis not found
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /markets:
    get:
      summary: List Markets
      description: |
        Retrieves a paginated list of markets available for analysis.
        
        Markets can be filtered by various criteria including:
        - Geographic region (governorate)
        - Control status (government/opposition/contested)
        - Market type (urban/rural)
        - Data availability period
        
        ## Sorting Options
        
        Results can be sorted by:
        - `name`: Market name (alphabetical)
        - `governorate`: Governorate name
        - `last_updated`: Most recently updated markets first
        - `data_completeness`: Markets with most complete data first
      operationId: listMarkets
      tags:
        - Markets
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
        - name: governorate
          in: query
          description: Filter by governorate name
          schema:
            type: string
            example: "Sana'a"
        - name: control_status
          in: query
          description: Filter by control status
          schema:
            type: string
            enum: [government, opposition, contested]
        - name: market_type
          in: query
          description: Filter by market type
          schema:
            type: string
            enum: [urban, rural]
        - name: sort
          in: query
          description: Sort order
          schema:
            type: string
            enum: [name, governorate, last_updated, data_completeness]
            default: name
        - name: order
          in: query
          description: Sort direction
          schema:
            type: string
            enum: [asc, desc]
            default: asc
      responses:
        '200':
          description: List of markets
          content:
            application/json:
              schema:
                type: object
                properties:
                  markets:
                    type: array
                    items:
                      $ref: '#/components/schemas/Market'
                  pagination:
                    $ref: '#/components/schemas/PaginationInfo'
        '400':
          description: Invalid query parameters
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /markets/{id}:
    get:
      summary: Get Market Details
      description: |
        Retrieves detailed information about a specific market including:
        - Geographic location and administrative details
        - Available commodities and price history
        - Data quality metrics and completeness
        - Recent conflict events in the area
      operationId: getMarket
      tags:
        - Markets
      parameters:
        - name: id
          in: path
          required: true
          description: Market identifier
          schema:
            type: string
            example: "market_sanaa_central"
      responses:
        '200':
          description: Market details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketDetail'
        '404':
          description: Market not found
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /commodities:
    get:
      summary: List Commodities
      description: |
        Retrieves a list of commodities available for analysis.
        
        Commodities are categorized by type (cereals, proteins, fats, etc.) and include metadata about:
        - Units of measurement
        - Seasonal patterns
        - Market availability
        - Price volatility characteristics
      operationId: listCommodities
      tags:
        - Commodities
      parameters:
        - name: category
          in: query
          description: Filter by commodity category
          schema:
            type: string
            enum: [cereals, proteins, fats, vegetables, other]
        - name: unit
          in: query
          description: Filter by unit of measurement
          schema:
            type: string
            example: "kg"
      responses:
        '200':
          description: List of commodities
          content:
            application/json:
              schema:
                type: object
                properties:
                  commodities:
                    type: array
                    items:
                      $ref: '#/components/schemas/Commodity'

  /prices:
    get:
      summary: Get Price Data
      description: |
        Retrieves price data for specified markets and commodities within a date range.
        
        ## Query Optimization
        
        For better performance with large datasets:
        - Limit date ranges to reasonable periods
        - Specify specific markets and commodities when possible
        - Use aggregation parameters for summary statistics
        
        ## Data Quality
        
        Response includes data quality indicators:
        - Missing data percentages
        - Outlier flags
        - Data source reliability scores
      operationId: getPrices
      tags:
        - Prices
      parameters:
        - name: start_date
          in: query
          required: true
          description: Start date for price data
          schema:
            type: string
            format: date
            example: "2023-01-01"
        - name: end_date
          in: query
          required: true
          description: End date for price data
          schema:
            type: string
            format: date
            example: "2023-12-31"
        - name: markets
          in: query
          description: Comma-separated list of market IDs
          schema:
            type: string
            example: "market_sanaa,market_aden"
        - name: commodities
          in: query
          description: Comma-separated list of commodity codes
          schema:
            type: string
            example: "wheat,rice,oil"
        - name: currency
          in: query
          description: Currency for price values
          schema:
            type: string
            enum: [YER, USD]
            default: YER
        - name: aggregate
          in: query
          description: Aggregation level for time series
          schema:
            type: string
            enum: [daily, weekly, monthly]
            default: daily
      responses:
        '200':
          description: Price data
          content:
            application/json:
              schema:
                type: object
                properties:
                  prices:
                    type: array
                    items:
                      $ref: '#/components/schemas/PriceObservation'
                  metadata:
                    $ref: '#/components/schemas/PriceDataMetadata'

components:
  securitySchemes:
    ApiKeyAuth:
      type: http
      scheme: bearer
      bearerFormat: API_KEY
      description: |
        API key authentication. Provide your API key in the Authorization header:
        ```
        Authorization: Bearer YOUR_API_KEY
        ```
    JWTAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT token authentication. Provide your JWT token in the Authorization header:
        ```
        Authorization: Bearer YOUR_JWT_TOKEN
        ```

  schemas:
    ThreeTierAnalysisRequest:
      type: object
      required:
        - start_date
        - end_date
      properties:
        start_date:
          type: string
          format: date
          description: Analysis start date
          example: "2023-01-01"
        end_date:
          type: string
          format: date
          description: Analysis end date
          example: "2023-12-31"
        markets:
          type: array
          items:
            type: string
          description: List of market names or IDs to include
          example: ["Sana'a", "Aden", "Taiz"]
        commodities:
          type: array
          items:
            type: string
          description: List of commodity names or codes to include
          example: ["wheat", "rice", "oil"]
        commodity_groups:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          description: Grouping of commodities for analysis
          example:
            cereals: ["wheat", "rice"]
            proteins: ["beans"]
        confidence_level:
          type: number
          minimum: 0.8
          maximum: 0.99
          default: 0.95
          description: Statistical confidence level for tests
        include_diagnostics:
          type: boolean
          default: true
          description: Whether to run diagnostic tests
        tier1_config:
          type: object
          description: Configuration for Tier 1 pooled panel analysis
          properties:
            include_time_trends:
              type: boolean
              default: false
            cluster_standard_errors:
              type: boolean
              default: true
        tier2_config:
          type: object
          description: Configuration for Tier 2 VECM analysis
          properties:
            max_lags:
              type: integer
              minimum: 1
              maximum: 12
              default: 4
            cointegration_test:
              type: string
              enum: [johansen, engle_granger]
              default: johansen
        tier3_config:
          type: object
          description: Configuration for Tier 3 factor analysis
          properties:
            factor_method:
              type: string
              enum: [pca, factor_analysis]
              default: pca
            n_factors:
              type: integer
              minimum: 1
              maximum: 10

    AnalysisResponse:
      type: object
      properties:
        id:
          type: string
          description: Unique analysis identifier
          example: "analysis_abc123def456"
        status:
          type: string
          enum: [pending, running, completed, failed, cancelled]
          description: Current analysis status
        message:
          type: string
          description: Human-readable status message
          example: "Analysis queued for processing"
        estimated_duration_seconds:
          type: integer
          description: Estimated completion time in seconds
          example: 300
        created_at:
          type: string
          format: date-time
          description: Analysis creation timestamp
        sse_endpoint:
          type: string
          description: SSE endpoint for real-time updates
          example: "/api/v1/analysis/abc123def456/status"

    AnalysisStatusResponse:
      type: object
      properties:
        id:
          type: string
          description: Analysis identifier
        type:
          type: string
          description: Analysis type
          example: "three_tier"
        status:
          type: string
          enum: [pending, running, completed, failed, cancelled]
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: Completion percentage
        start_time:
          type: string
          format: date-time
          description: Analysis start time
        end_time:
          type: string
          format: date-time
          nullable: true
          description: Analysis completion time
        error:
          type: string
          nullable: true
          description: Error message if analysis failed
        tiers_progress:
          type: object
          description: Progress breakdown by tier
          properties:
            tier1:
              $ref: '#/components/schemas/TierProgress'
            tier2:
              $ref: '#/components/schemas/TierProgress'
            tier3:
              $ref: '#/components/schemas/TierProgress'
        results:
          type: object
          nullable: true
          description: Summary results (if completed)

    TierProgress:
      type: object
      properties:
        status:
          type: string
          enum: [pending, running, completed, failed]
        progress:
          type: integer
          minimum: 0
          maximum: 100
        duration_seconds:
          type: integer
          description: Execution time in seconds
        current_commodity:
          type: string
          nullable: true
          description: Currently processing commodity (Tier 2 only)
        commodities_completed:
          type: integer
          description: Number of commodities completed (Tier 2 only)
        commodities_total:
          type: integer
          description: Total number of commodities (Tier 2 only)

    AnalysisResultsResponse:
      type: object
      properties:
        analysis_id:
          type: string
        summary:
          $ref: '#/components/schemas/AnalysisSummary'
        tier1_results:
          $ref: '#/components/schemas/Tier1Results'
        tier2_results:
          $ref: '#/components/schemas/Tier2Results'
        tier3_results:
          $ref: '#/components/schemas/Tier3Results'
        diagnostics:
          $ref: '#/components/schemas/DiagnosticResults'

    AnalysisSummary:
      type: object
      properties:
        total_observations:
          type: integer
        markets_analyzed:
          type: integer
        commodities_analyzed:
          type: integer
        integration_score:
          type: number
          minimum: 0
          maximum: 1
          description: Overall market integration score
        data_quality_score:
          type: number
          minimum: 0
          maximum: 1

    Tier1Results:
      type: object
      properties:
        model_type:
          type: string
          example: "pooled_panel"
        coefficients:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/CoefficientEstimate'
        r_squared:
          type: number
        adjusted_r_squared:
          type: number
        f_statistic:
          type: number
        p_value:
          type: number
        observations:
          type: integer
        fixed_effects:
          type: object
          description: Market and time fixed effects

    Tier2Results:
      type: object
      properties:
        commodities:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/CommodityVECMResults'

    CommodityVECMResults:
      type: object
      properties:
        commodity:
          type: string
        cointegration_rank:
          type: integer
        cointegration_test:
          $ref: '#/components/schemas/CointegrationTest'
        vecm_coefficients:
          type: object
        impulse_responses:
          type: object
        variance_decomposition:
          type: object

    Tier3Results:
      type: object
      properties:
        method:
          type: string
          example: "pca"
        n_factors:
          type: integer
        explained_variance_ratio:
          type: array
          items:
            type: number
        factor_loadings:
          type: object
        factor_scores:
          type: object

    Market:
      type: object
      properties:
        id:
          type: string
          example: "market_sanaa_central"
        name:
          type: string
          example: "Sana'a Central Market"
        governorate:
          type: string
          example: "Sana'a"
        latitude:
          type: number
          format: float
        longitude:
          type: number
          format: float
        market_type:
          type: string
          enum: [urban, rural]
        control_status:
          type: string
          enum: [government, opposition, contested]
        last_updated:
          type: string
          format: date-time
        data_completeness:
          type: number
          minimum: 0
          maximum: 1

    MarketDetail:
      allOf:
        - $ref: '#/components/schemas/Market'
        - type: object
          properties:
            commodities:
              type: array
              items:
                type: string
            price_history_range:
              type: object
              properties:
                start_date:
                  type: string
                  format: date
                end_date:
                  type: string
                  format: date
            data_quality:
              $ref: '#/components/schemas/DataQualityMetrics'

    Commodity:
      type: object
      properties:
        code:
          type: string
          example: "wheat"
        name:
          type: string
          example: "Wheat"
        category:
          type: string
          enum: [cereals, proteins, fats, vegetables, other]
        unit:
          type: string
          example: "kg"
        description:
          type: string

    PriceObservation:
      type: object
      properties:
        market_id:
          type: string
        commodity_code:
          type: string
        date:
          type: string
          format: date
        price:
          type: number
          format: float
        currency:
          type: string
          enum: [YER, USD]
        unit:
          type: string
        data_source:
          type: string
        quality_flag:
          type: string
          enum: [good, suspect, outlier]

    CoefficientEstimate:
      type: object
      properties:
        estimate:
          type: number
        std_error:
          type: number
        t_statistic:
          type: number
        p_value:
          type: number
        confidence_interval:
          type: object
          properties:
            lower:
              type: number
            upper:
              type: number

    CointegrationTest:
      type: object
      properties:
        test_name:
          type: string
        test_statistic:
          type: number
        critical_values:
          type: object
        p_value:
          type: number
        rank:
          type: integer

    DiagnosticResults:
      type: object
      properties:
        panel_tests:
          type: object
          properties:
            hausman_test:
              $ref: '#/components/schemas/StatisticalTest'
            breusch_pagan_test:
              $ref: '#/components/schemas/StatisticalTest'
            pesaran_cd_test:
              $ref: '#/components/schemas/StatisticalTest'
        time_series_tests:
          type: object
          properties:
            unit_root_tests:
              type: object
              additionalProperties:
                $ref: '#/components/schemas/StatisticalTest'
            stationarity_tests:
              type: object
              additionalProperties:
                $ref: '#/components/schemas/StatisticalTest'

    StatisticalTest:
      type: object
      properties:
        test_name:
          type: string
        test_statistic:
          type: number
        p_value:
          type: number
        critical_values:
          type: object
        result:
          type: string
          enum: [reject, fail_to_reject]
        interpretation:
          type: string

    DataQualityMetrics:
      type: object
      properties:
        completeness:
          type: number
          minimum: 0
          maximum: 1
        missing_percentage:
          type: number
        outlier_percentage:
          type: number
        last_update:
          type: string
          format: date-time

    PriceDataMetadata:
      type: object
      properties:
        total_observations:
          type: integer
        date_range:
          type: object
          properties:
            start_date:
              type: string
              format: date
            end_date:
              type: string
              format: date
        markets_included:
          type: array
          items:
            type: string
        commodities_included:
          type: array
          items:
            type: string
        data_quality:
          $ref: '#/components/schemas/DataQualityMetrics'

    PaginationInfo:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        pages:
          type: integer
        has_next:
          type: boolean
        has_prev:
          type: boolean

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error type identifier
        message:
          type: string
          description: Human-readable error message
        details:
          type: object
          description: Additional error details
        request_id:
          type: string
          description: Request identifier for debugging

  responses:
    UnauthorizedError:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            missing_auth:
              summary: Missing Authentication
              value:
                error: "UnauthorizedError"
                message: "Authentication required. Please provide a valid API key or JWT token."
                request_id: "req_abc123"

    ForbiddenError:
      description: Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            insufficient_permissions:
              summary: Insufficient Permissions
              value:
                error: "ForbiddenError"
                message: "Insufficient permissions to perform this action"
                details:
                  required_permission: "analysis:create"
                  user_permissions: ["analysis:read", "markets:read"]
                request_id: "req_xyz789"

    RateLimitError:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            rate_limit:
              summary: Rate Limit Exceeded
              value:
                error: "RateLimitError"
                message: "Rate limit exceeded. Try again in 60 seconds."
                details:
                  limit: 1000
                  window: "1h"
                  reset_time: "2024-01-15T11:00:00Z"
                request_id: "req_def456"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            server_error:
              summary: Internal Server Error
              value:
                error: "InternalServerError"
                message: "An unexpected error occurred. Please try again later."
                request_id: "req_ghi789"

tags:
  - name: Analysis
    description: |
      Econometric analysis operations including three-tier market integration analysis.
      
      The analysis endpoints provide comprehensive tools for studying market price integration
      in conflict settings using advanced econometric methods.
  - name: Markets
    description: |
      Market information and metadata operations.
      
      Access detailed information about markets including location, control status,
      and data availability.
  - name: Commodities
    description: |
      Commodity information and classification operations.
      
      Manage commodity definitions, categories, and measurement units.
  - name: Prices
    description: |
      Price data retrieval and management operations.
      
      Access historical price data with filtering, aggregation, and quality metrics.
  - name: Real-time
    description: |
      Real-time updates and Server-Sent Events (SSE) operations.
      
      Stream live updates for analysis progress and system events.

externalDocs:
  description: Yemen Market Integration Documentation
  url: https://docs.yemen-market-integration.org