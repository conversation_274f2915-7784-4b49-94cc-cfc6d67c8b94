# Authentication and Authorization Guide

## Overview

The Yemen Market Integration API v2 uses JWT-based authentication with role-based access control (RBAC) to secure endpoints and manage user access. The system supports both user authentication (via JWT tokens) and machine-to-machine authentication (via API keys).

## Authentication Methods

### 1. JWT Authentication

JWT (JSON Web Token) authentication is used for user sessions and provides short-lived access tokens with optional refresh tokens.

#### Login

```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "SecurePassword123!",
  "remember_me": true
}
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "username": "<EMAIL>",
    "email": "<EMAIL>",
    "full_name": "Data Analyst",
    "roles": ["analyst"],
    "email_verified": true
  }
}
```

#### Using Access Tokens

Include the access token in the Authorization header:

```bash
GET /api/v1/analysis/123
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

#### Refreshing Tokens

When the access token expires, use the refresh token to get a new one:

```bash
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 2. API Key Authentication

API keys are used for machine-to-machine authentication and long-lived integrations.

#### Using API Keys

Include the API key in the X-API-Key header:

```bash
GET /api/v1/markets
X-API-Key: ymi_1234567890abcdef...
```

## User Roles and Permissions

### Roles

1. **Admin** (`admin`)
   - Full system access
   - User management
   - System configuration
   - All data operations

2. **Analyst** (`analyst`)
   - Create and run analyses
   - Read all market data
   - Update analysis configurations
   - View conflict data

3. **Viewer** (`viewer`)
   - Read-only access to analyses
   - View market and price data
   - Access reports

4. **API User** (`api_user`)
   - Limited programmatic access
   - Read market data
   - Manage own API keys

5. **Policy Maker** (`policy_maker`)
   - Access policy analysis tools
   - Create policy scenarios
   - View all data

### Permission Matrix

| Resource | Action | Admin | Analyst | Viewer | API User | Policy Maker |
|----------|--------|-------|---------|--------|----------|--------------|
| Analysis | Read   | ✅    | ✅      | ✅     | ✅       | ✅           |
| Analysis | Create | ✅    | ✅      | ❌     | ❌       | ❌           |
| Analysis | Update | ✅    | ✅      | ❌     | ❌       | ❌           |
| Analysis | Delete | ✅    | ❌      | ❌     | ❌       | ❌           |
| Markets  | Read   | ✅    | ✅      | ✅     | ✅       | ✅           |
| Markets  | Write  | ✅    | ❌      | ❌     | ❌       | ❌           |
| Users    | Manage | ✅    | ❌      | ❌     | ❌       | ❌           |
| API Keys | Create | ✅    | ✅      | ❌     | ✅       | ✅           |
| Policy   | All    | ✅    | ❌      | ❌     | ❌       | ✅           |

## Security Features

### 1. Rate Limiting

All endpoints are rate-limited to prevent abuse:

- **Default limits**: 60 requests/minute, 1000 requests/hour
- **Burst protection**: Token bucket algorithm with 10 token capacity
- **Custom limits**: Some endpoints have stricter limits

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1640995200
```

### 2. Security Headers

The API includes comprehensive security headers:

- **HSTS**: Enforces HTTPS connections
- **CSP**: Prevents XSS attacks
- **X-Frame-Options**: Prevents clickjacking
- **X-Content-Type-Options**: Prevents MIME sniffing

### 3. Password Requirements

Passwords must meet the following criteria:
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one digit
- At least one special character

### 4. Token Security

- **Access tokens**: 30-minute expiration (configurable)
- **Refresh tokens**: 7-day expiration
- **Token revocation**: Logout invalidates tokens
- **Secure storage**: Tokens should be stored securely client-side

## API Endpoints

### Authentication Endpoints

#### POST /api/v1/auth/login
Authenticate user and receive tokens.

#### POST /api/v1/auth/login/oauth
OAuth2-compatible login endpoint.

#### POST /api/v1/auth/refresh
Refresh access token using refresh token.

#### POST /api/v1/auth/logout
Logout and optionally revoke tokens.

#### GET /api/v1/auth/verify
Verify current token validity.

#### POST /api/v1/auth/password-reset/request
Request password reset email.

### User Management Endpoints

#### GET /api/v1/users
List users (requires USER_READ permission).

#### POST /api/v1/users
Create new user (requires USER_CREATE permission).

#### GET /api/v1/users/me
Get current user profile.

#### PATCH /api/v1/users/{user_id}
Update user details (requires USER_UPDATE permission).

#### DELETE /api/v1/users/{user_id}
Delete user (requires USER_DELETE permission).

#### POST /api/v1/users/me/change-password
Change current user's password.

### API Key Management Endpoints

#### GET /api/v1/api-keys
List API keys for current user.

#### POST /api/v1/api-keys
Create new API key (requires API_KEY_CREATE permission).

#### GET /api/v1/api-keys/{key_id}
Get API key details.

#### GET /api/v1/api-keys/{key_id}/usage
Get API key usage statistics.

#### POST /api/v1/api-keys/{key_id}/revoke
Revoke an API key.

#### POST /api/v1/api-keys/{key_id}/rotate
Rotate an API key (requires API_KEY_UPDATE permission).

## Implementation Examples

### Python Client Example

```python
import httpx
from typing import Optional

class YemenMarketAPIClient:
    def __init__(self, base_url: str = "https://api.yemenmarket.org"):
        self.base_url = base_url
        self.client = httpx.AsyncClient()
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
    
    async def login(self, username: str, password: str) -> dict:
        """Login and store tokens."""
        response = await self.client.post(
            f"{self.base_url}/api/v1/auth/login",
            json={
                "username": username,
                "password": password,
                "remember_me": True
            }
        )
        response.raise_for_status()
        data = response.json()
        
        self.access_token = data["access_token"]
        self.refresh_token = data["refresh_token"]
        return data["user"]
    
    async def refresh_tokens(self) -> None:
        """Refresh access token."""
        response = await self.client.post(
            f"{self.base_url}/api/v1/auth/refresh",
            json={"refresh_token": self.refresh_token}
        )
        response.raise_for_status()
        data = response.json()
        
        self.access_token = data["access_token"]
        self.refresh_token = data["refresh_token"]
    
    async def get_analysis(self, analysis_id: str) -> dict:
        """Get analysis with authentication."""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        response = await self.client.get(
            f"{self.base_url}/api/v1/analysis/{analysis_id}",
            headers=headers
        )
        
        if response.status_code == 401:
            # Token expired, try refreshing
            await self.refresh_tokens()
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = await self.client.get(
                f"{self.base_url}/api/v1/analysis/{analysis_id}",
                headers=headers
            )
        
        response.raise_for_status()
        return response.json()
```

### JavaScript Client Example

```javascript
class YemenMarketAPIClient {
  constructor(baseUrl = 'https://api.yemenmarket.org') {
    this.baseUrl = baseUrl;
    this.accessToken = null;
    this.refreshToken = null;
  }

  async login(username, password) {
    const response = await fetch(`${this.baseUrl}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username,
        password,
        remember_me: true
      })
    });

    if (!response.ok) {
      throw new Error('Login failed');
    }

    const data = await response.json();
    this.accessToken = data.access_token;
    this.refreshToken = data.refresh_token;
    return data.user;
  }

  async makeAuthenticatedRequest(url, options = {}) {
    let response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${this.accessToken}`
      }
    });

    if (response.status === 401) {
      // Token expired, refresh and retry
      await this.refreshTokens();
      response = await fetch(url, {
        ...options,
        headers: {
          ...options.headers,
          'Authorization': `Bearer ${this.accessToken}`
        }
      });
    }

    return response;
  }

  async refreshTokens() {
    const response = await fetch(`${this.baseUrl}/api/v1/auth/refresh`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        refresh_token: this.refreshToken
      })
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    this.accessToken = data.access_token;
    this.refreshToken = data.refresh_token;
  }
}
```

## Security Best Practices

1. **Token Storage**
   - Never store tokens in localStorage (XSS vulnerable)
   - Use httpOnly cookies or secure memory storage
   - Clear tokens on logout

2. **API Key Management**
   - Rotate API keys regularly
   - Use IP whitelisting for production keys
   - Monitor key usage for anomalies

3. **Network Security**
   - Always use HTTPS
   - Implement certificate pinning for mobile apps
   - Use secure WebSocket connections for real-time data

4. **Error Handling**
   - Don't expose sensitive information in errors
   - Log security events for monitoring
   - Implement account lockout for failed logins

5. **Compliance**
   - Follow OWASP security guidelines
   - Implement audit logging
   - Regular security assessments