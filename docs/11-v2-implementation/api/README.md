# Yemen Market Integration API Documentation

## Overview

The Yemen Market Integration API v2 provides RESTful endpoints for conducting comprehensive econometric analysis of market price integration in conflict settings. Built with FastAPI, the API offers high-performance, async-first operations with real-time capabilities through Server-Sent Events (SSE).

## Table of Contents

- [Quick Start](#quick-start)
- [Authentication](#authentication)
- [Core Concepts](#core-concepts)
- [API Endpoints](#api-endpoints)
- [Real-time Updates](#real-time-updates)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [SDKs and Libraries](#sdks-and-libraries)
- [Examples](#examples)

## Quick Start

### Base URLs

| Environment | URL |
|-------------|-----|
| Production | `https://api.yemen-market-integration.org/api/v1` |
| Staging | `https://staging-api.yemen-market-integration.org/api/v1` |
| Local | `http://localhost:8000/api/v1` |

### Basic Example

```bash
# Get API information
curl https://api.yemen-market-integration.org/

# List available markets
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.yemen-market-integration.org/api/v1/markets

# Create a three-tier analysis
curl -X POST \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "start_date": "2023-01-01",
       "end_date": "2023-12-31",
       "markets": ["Sana'\''a", "Aden", "Taiz"],
       "commodities": ["wheat", "rice", "oil"],
       "confidence_level": 0.95,
       "include_diagnostics": true
     }' \
     https://api.yemen-market-integration.org/api/v1/analysis/three-tier
```

## Authentication

The API supports two authentication methods:

### 1. API Key Authentication (Recommended)

Include your API key in the Authorization header:

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.yemen-market-integration.org/api/v1/markets
```

### 2. JWT Token Authentication

For applications requiring user-specific access:

```bash
# First, obtain a JWT token
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{
       "username": "your_username",
       "password": "your_password"
     }' \
     https://api.yemen-market-integration.org/api/v1/auth/login

# Use the token for subsequent requests
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     https://api.yemen-market-integration.org/api/v1/analysis
```

### Getting API Keys

1. **Sign up** at [yemen-market-integration.org](https://yemen-market-integration.org)
2. **Verify your email** and complete profile setup
3. **Generate API key** in your dashboard
4. **Set permissions** based on your use case

## Core Concepts

### Three-Tier Analysis Framework

The API's core functionality centers around a three-tier econometric analysis framework:

1. **Tier 1: Pooled Panel Analysis**
   - Fixed effects regression across all markets and commodities
   - Tests for overall market integration patterns
   - Provides baseline integration metrics

2. **Tier 2: Commodity-Specific VECM Analysis**
   - Vector Error Correction Models for each commodity
   - Cointegration testing and long-run relationships
   - Impulse response functions and variance decomposition

3. **Tier 3: Factor Analysis Validation**
   - Principal Component Analysis of market relationships
   - Factor model validation of integration patterns
   - Robustness checks and sensitivity analysis

### Data Model

```
Market ─┐
        ├─ PriceObservation ─┐
Commodity ┘                  ├─ Analysis ─ Results
                             │
ConflictEvent ─┐             │
               ├─ Analysis ──┘
ExchangeRate ──┘
```

### Analysis Lifecycle

```
Request → Validation → Queue → Execution → Results
   ↓           ↓         ↓        ↓         ↓
Create      Check     Background Real-time   Access
Analysis    Data      Processing Updates    Complete
Request     Quality                         Results
```

## API Endpoints

### Analysis Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/analysis/three-tier` | Create new three-tier analysis |
| `GET` | `/analysis/{id}` | Get analysis status |
| `GET` | `/analysis/{id}/status` | Stream real-time status (SSE) |
| `GET` | `/analysis/{id}/results` | Get complete results |
| `DELETE` | `/analysis/{id}` | Cancel or delete analysis |

### Data Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/markets` | List markets with filtering |
| `GET` | `/markets/{id}` | Get market details |
| `GET` | `/commodities` | List available commodities |
| `GET` | `/prices` | Get price data with filtering |

### Utility Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | API root and service information |
| `GET` | `/health` | Health check endpoint |
| `GET` | `/docs` | Interactive API documentation |

## Real-time Updates

### Server-Sent Events (SSE)

Monitor analysis progress in real-time using SSE:

```javascript
// JavaScript example
const eventSource = new EventSource(
  'https://api.yemen-market-integration.org/api/v1/analysis/abc123/status',
  {
    headers: {
      'Authorization': 'Bearer YOUR_API_KEY'
    }
  }
);

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Analysis update:', data);
  
  // Update UI based on progress
  updateProgressBar(data.progress);
  
  if (data.status === 'completed') {
    loadResults(data.analysis_id);
    eventSource.close();
  }
};

eventSource.onerror = function(event) {
  console.error('SSE error:', event);
  // Implement reconnection logic
  setTimeout(() => {
    eventSource.close();
    connectToAnalysis(analysisId);
  }, 5000);
};
```

### Event Types

| Event | Description | Data |
|-------|-------------|------|
| `initial` | Connection established | Current status |
| `progress` | Progress update | Progress percentage |
| `tier_started` | New tier begun | Tier information |
| `tier_completed` | Tier completed | Results summary |
| `completed` | Analysis finished | Final results |
| `failed` | Error occurred | Error details |
| `cancelled` | Analysis cancelled | Cancellation reason |

## Error Handling

### HTTP Status Codes

| Code | Description | When it occurs |
|------|-------------|----------------|
| `200` | Success | Request completed successfully |
| `202` | Accepted | Analysis request queued |
| `400` | Bad Request | Invalid parameters |
| `401` | Unauthorized | Missing or invalid authentication |
| `403` | Forbidden | Insufficient permissions |
| `404` | Not Found | Resource doesn't exist |
| `429` | Rate Limited | Too many requests |
| `500` | Server Error | Internal server error |

### Error Response Format

```json
{
  "error": "ValidationError",
  "message": "End date must be after start date",
  "details": {
    "field": "end_date",
    "value": "2022-01-01",
    "constraint": "must_be_after_start_date"
  },
  "request_id": "req_abc123"
}
```

### Common Error Scenarios

#### Authentication Errors

```bash
# Missing API key
HTTP 401 Unauthorized
{
  "error": "UnauthorizedError",
  "message": "Authentication required. Please provide a valid API key."
}

# Invalid API key
HTTP 401 Unauthorized
{
  "error": "UnauthorizedError",
  "message": "Invalid API key provided."
}
```

#### Validation Errors

```bash
# Invalid date range
HTTP 400 Bad Request
{
  "error": "ValidationError",
  "message": "End date must be after start date",
  "details": {
    "field": "end_date",
    "provided": "2022-01-01",
    "start_date": "2023-01-01"
  }
}
```

#### Rate Limiting

```bash
HTTP 429 Too Many Requests
{
  "error": "RateLimitError",
  "message": "Rate limit exceeded. Try again in 60 seconds.",
  "details": {
    "limit": 1000,
    "window": "1h",
    "reset_time": "2024-01-15T11:00:00Z"
  }
}
```

## Rate Limiting

### Limits by Endpoint Type

| Endpoint Type | Limit | Window |
|---------------|-------|--------|
| Analysis Creation | 10 requests | 1 hour |
| Data Retrieval | 1000 requests | 1 hour |
| SSE Connections | 5 concurrent | - |
| Authentication | 100 requests | 1 hour |

### Rate Limit Headers

The API includes rate limit information in response headers:

```bash
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 995
X-RateLimit-Reset: 1642248000
X-RateLimit-Window: 3600
```

### Handling Rate Limits

```python
import time
import requests

def make_request_with_backoff(url, headers, max_retries=3):
    for attempt in range(max_retries):
        response = requests.get(url, headers=headers)
        
        if response.status_code == 429:
            # Rate limited - wait and retry
            reset_time = int(response.headers.get('X-RateLimit-Reset', 0))
            wait_time = max(reset_time - int(time.time()), 60)
            time.sleep(wait_time)
            continue
            
        return response
    
    raise Exception("Max retries exceeded")
```

## SDKs and Libraries

### Official Python SDK

```python
# Installation
pip install yemen-market-integration

# Usage
from yemen_market import YemenMarketClient

client = YemenMarketClient(api_key="YOUR_API_KEY")

# Create analysis
analysis = client.create_analysis(
    start_date="2023-01-01",
    end_date="2023-12-31",
    markets=["Sana'a", "Aden"],
    commodities=["wheat", "rice"]
)

# Monitor progress
for update in client.stream_analysis_status(analysis.id):
    print(f"Progress: {update.progress}%")
    if update.status == "completed":
        break

# Get results
results = client.get_analysis_results(analysis.id)
print(f"Integration score: {results.summary.integration_score}")
```

### JavaScript/Node.js SDK

```javascript
// Installation
npm install yemen-market-integration

// Usage
const { YemenMarketClient } = require('yemen-market-integration');

const client = new YemenMarketClient({
  apiKey: 'YOUR_API_KEY',
  baseUrl: 'https://api.yemen-market-integration.org'
});

// Create analysis
const analysis = await client.createAnalysis({
  startDate: '2023-01-01',
  endDate: '2023-12-31',
  markets: ['Sana\'a', 'Aden'],
  commodities: ['wheat', 'rice']
});

// Stream progress
const stream = client.streamAnalysisStatus(analysis.id);
stream.on('progress', (update) => {
  console.log(`Progress: ${update.progress}%`);
});

stream.on('completed', async () => {
  const results = await client.getAnalysisResults(analysis.id);
  console.log(`Integration score: ${results.summary.integrationScore}`);
});
```

### R Package

```r
# Installation
install.packages("devtools")
devtools::install_github("worldbank/yemen-market-integration-r")

# Usage
library(yemen.market)

# Setup client
client <- YemenMarketClient$new(api_key = "YOUR_API_KEY")

# Create analysis
analysis <- client$create_analysis(
  start_date = "2023-01-01",
  end_date = "2023-12-31",
  markets = c("Sana'a", "Aden"),
  commodities = c("wheat", "rice")
)

# Wait for completion
results <- client$wait_for_analysis(analysis$id)

# Extract results
integration_score <- results$summary$integration_score
tier1_results <- results$tier1_results
```

## Examples

### Complete Analysis Workflow

This example demonstrates a complete analysis workflow from data validation to results interpretation:

```python
import asyncio
from yemen_market import YemenMarketClient
from datetime import datetime, timedelta

async def complete_analysis_workflow():
    client = YemenMarketClient(api_key="YOUR_API_KEY")
    
    # 1. Validate data availability
    markets = await client.list_markets(
        governorate="Sana'a",
        data_completeness_min=0.8
    )
    
    commodities = await client.list_commodities(
        category="cereals"
    )
    
    # 2. Check price data coverage
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    price_data = await client.get_prices(
        start_date=start_date.strftime("%Y-%m-%d"),
        end_date=end_date.strftime("%Y-%m-%d"),
        markets=[m.id for m in markets[:5]],
        commodities=[c.code for c in commodities[:3]]
    )
    
    print(f"Available observations: {len(price_data.prices)}")
    print(f"Data completeness: {price_data.metadata.data_quality.completeness:.2%}")
    
    # 3. Create analysis with optimal configuration
    analysis = await client.create_analysis(
        start_date=start_date.strftime("%Y-%m-%d"),
        end_date=end_date.strftime("%Y-%m-%d"),
        markets=[m.name for m in markets[:5]],
        commodities=[c.code for c in commodities[:3]],
        confidence_level=0.95,
        include_diagnostics=True,
        tier1_config={
            "include_time_trends": True,
            "cluster_standard_errors": True
        },
        tier2_config={
            "max_lags": 4,
            "cointegration_test": "johansen"
        }
    )
    
    print(f"Analysis created: {analysis.id}")
    
    # 4. Monitor progress with detailed logging
    async for update in client.stream_analysis_status(analysis.id):
        if update.event == "progress":
            print(f"Progress: {update.progress}% - {update.message}")
        elif update.event == "tier_completed":
            print(f"Tier {update.tier} completed in {update.duration}s")
        elif update.event == "completed":
            print("Analysis completed!")
            break
        elif update.event == "failed":
            print(f"Analysis failed: {update.error}")
            return None
    
    # 5. Retrieve and process results
    results = await client.get_analysis_results(analysis.id)
    
    # Summary statistics
    summary = results.summary
    print(f"\nAnalysis Summary:")
    print(f"- Total observations: {summary.total_observations:,}")
    print(f"- Markets analyzed: {summary.markets_analyzed}")
    print(f"- Commodities analyzed: {summary.commodities_analyzed}")
    print(f"- Overall integration score: {summary.integration_score:.3f}")
    print(f"- Data quality score: {summary.data_quality_score:.3f}")
    
    # Tier 1 results
    tier1 = results.tier1_results
    print(f"\nTier 1 Results (Pooled Panel):")
    print(f"- R-squared: {tier1.r_squared:.3f}")
    print(f"- F-statistic: {tier1.f_statistic:.2f}")
    print(f"- Observations: {tier1.observations:,}")
    
    # Key coefficients
    for coef_name, coef_data in tier1.coefficients.items():
        if coef_data.p_value < 0.05:
            print(f"- {coef_name}: {coef_data.estimate:.3f} "
                  f"(SE: {coef_data.std_error:.3f}, p: {coef_data.p_value:.3f})")
    
    # Tier 2 results by commodity
    print(f"\nTier 2 Results (VECM by Commodity):")
    for commodity, vecm_results in results.tier2_results.commodities.items():
        coint_test = vecm_results.cointegration_test
        print(f"- {commodity.title()}:")
        print(f"  - Cointegration rank: {vecm_results.cointegration_rank}")
        print(f"  - Test statistic: {coint_test.test_statistic:.3f}")
        print(f"  - P-value: {coint_test.p_value:.3f}")
    
    # Tier 3 results
    tier3 = results.tier3_results
    print(f"\nTier 3 Results (Factor Analysis):")
    print(f"- Method: {tier3.method}")
    print(f"- Number of factors: {tier3.n_factors}")
    print(f"- Explained variance ratios: {[f'{x:.3f}' for x in tier3.explained_variance_ratio[:3]]}")
    
    # Diagnostic results
    if results.diagnostics:
        diagnostics = results.diagnostics
        print(f"\nDiagnostic Tests:")
        
        # Panel diagnostics
        hausman = diagnostics.panel_tests.hausman_test
        print(f"- Hausman test: {hausman.test_statistic:.3f} "
              f"(p: {hausman.p_value:.3f}) - {hausman.result}")
        
        bp_test = diagnostics.panel_tests.breusch_pagan_test
        print(f"- Breusch-Pagan test: {bp_test.test_statistic:.3f} "
              f"(p: {bp_test.p_value:.3f}) - {bp_test.result}")
    
    return results

# Run the workflow
if __name__ == "__main__":
    results = asyncio.run(complete_analysis_workflow())
```

### Batch Analysis for Multiple Regions

```python
async def batch_regional_analysis():
    client = YemenMarketClient(api_key="YOUR_API_KEY")
    
    # Define regions
    regions = {
        "North": ["Sana'a", "Sa'ada", "Hajjah"],
        "South": ["Aden", "Lahj", "Abyan"],
        "East": ["Marib", "Al Jawf", "Shabwah"],
        "West": ["Hodeidah", "Taiz", "Ibb"]
    }
    
    analyses = {}
    
    # Start analyses for each region
    for region_name, markets in regions.items():
        analysis = await client.create_analysis(
            start_date="2023-01-01",
            end_date="2023-12-31",
            markets=markets,
            commodities=["wheat", "rice", "oil"],
            confidence_level=0.95
        )
        analyses[region_name] = analysis
        print(f"Started analysis for {region_name}: {analysis.id}")
    
    # Wait for all analyses to complete
    results = {}
    for region_name, analysis in analyses.items():
        print(f"Waiting for {region_name} analysis...")
        result = await client.wait_for_analysis(analysis.id)
        results[region_name] = result
        print(f"{region_name} completed with integration score: "
              f"{result.summary.integration_score:.3f}")
    
    # Compare regional integration patterns
    print("\nRegional Integration Comparison:")
    for region, result in results.items():
        score = result.summary.integration_score
        print(f"- {region}: {score:.3f}")
    
    return results
```

### Real-time Analysis Dashboard

```html
<!DOCTYPE html>
<html>
<head>
    <title>Yemen Market Integration - Live Analysis</title>
    <style>
        .progress-bar {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 5px;
            overflow: hidden;
        }
        .progress-fill {
            height: 20px;
            background-color: #4CAF50;
            transition: width 0.3s ease;
        }
        .tier-status {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .completed { background-color: #d4edda; }
        .running { background-color: #d1ecf1; }
        .pending { background-color: #f8f9fa; }
        .failed { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>Yemen Market Integration Analysis Dashboard</h1>
    
    <div id="analysis-form">
        <h2>Create New Analysis</h2>
        <form id="create-analysis">
            <label>Start Date: <input type="date" id="start-date" required></label><br>
            <label>End Date: <input type="date" id="end-date" required></label><br>
            <label>Markets: <input type="text" id="markets" placeholder="Sana'a,Aden,Taiz" required></label><br>
            <label>Commodities: <input type="text" id="commodities" placeholder="wheat,rice,oil" required></label><br>
            <button type="submit">Start Analysis</button>
        </form>
    </div>
    
    <div id="analysis-status" style="display: none;">
        <h2>Analysis Progress</h2>
        <div id="overall-progress">
            <p>Overall Progress: <span id="progress-text">0%</span></p>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>
        </div>
        
        <div id="tier-progress">
            <div id="tier1-status" class="tier-status pending">
                <h3>Tier 1: Pooled Panel Analysis</h3>
                <p id="tier1-details">Pending...</p>
            </div>
            <div id="tier2-status" class="tier-status pending">
                <h3>Tier 2: Commodity VECM Analysis</h3>
                <p id="tier2-details">Pending...</p>
            </div>
            <div id="tier3-status" class="tier-status pending">
                <h3>Tier 3: Factor Analysis Validation</h3>
                <p id="tier3-details">Pending...</p>
            </div>
        </div>
        
        <div id="results-section" style="display: none;">
            <h2>Analysis Results</h2>
            <div id="results-content"></div>
            <button onclick="downloadResults()">Download Results</button>
        </div>
    </div>
    
    <script>
        const API_KEY = 'YOUR_API_KEY';
        const BASE_URL = 'https://api.yemen-market-integration.org/api/v1';
        
        let currentAnalysisId = null;
        let eventSource = null;
        
        document.getElementById('create-analysis').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                start_date: document.getElementById('start-date').value,
                end_date: document.getElementById('end-date').value,
                markets: document.getElementById('markets').value.split(',').map(s => s.trim()),
                commodities: document.getElementById('commodities').value.split(',').map(s => s.trim()),
                confidence_level: 0.95,
                include_diagnostics: true
            };
            
            try {
                const response = await fetch(`${BASE_URL}/analysis/three-tier`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                currentAnalysisId = result.id;
                
                // Show progress section
                document.getElementById('analysis-form').style.display = 'none';
                document.getElementById('analysis-status').style.display = 'block';
                
                // Start SSE connection
                connectToAnalysis(result.id);
                
            } catch (error) {
                alert(`Error creating analysis: ${error.message}`);
            }
        });
        
        function connectToAnalysis(analysisId) {
            eventSource = new EventSource(`${BASE_URL}/analysis/${analysisId}/status`, {
                headers: {
                    'Authorization': `Bearer ${API_KEY}`
                }
            });
            
            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateProgress(data);
            };
            
            eventSource.onerror = function(event) {
                console.error('SSE error:', event);
                // Implement reconnection logic
                setTimeout(() => {
                    eventSource.close();
                    connectToAnalysis(analysisId);
                }, 5000);
            };
        }
        
        function updateProgress(data) {
            // Update overall progress
            document.getElementById('progress-text').textContent = `${data.progress}%`;
            document.getElementById('progress-fill').style.width = `${data.progress}%`;
            
            // Update tier-specific progress
            if (data.tiers_progress) {
                updateTierStatus('tier1', data.tiers_progress.tier1);
                updateTierStatus('tier2', data.tiers_progress.tier2);
                updateTierStatus('tier3', data.tiers_progress.tier3);
            }
            
            // Handle completion
            if (data.event === 'completed') {
                loadResults(data.analysis_id);
                eventSource.close();
            } else if (data.event === 'failed') {
                showError(data.error);
                eventSource.close();
            }
        }
        
        function updateTierStatus(tierId, tierData) {
            if (!tierData) return;
            
            const statusElement = document.getElementById(`${tierId}-status`);
            const detailsElement = document.getElementById(`${tierId}-details`);
            
            // Update status class
            statusElement.className = `tier-status ${tierData.status}`;
            
            // Update details text
            let details = `Status: ${tierData.status}`;
            if (tierData.progress !== undefined) {
                details += ` (${tierData.progress}%)`;
            }
            if (tierData.duration_seconds) {
                details += ` - Completed in ${tierData.duration_seconds}s`;
            }
            if (tierData.current_commodity) {
                details += ` - Processing: ${tierData.current_commodity}`;
            }
            
            detailsElement.textContent = details;
        }
        
        async function loadResults(analysisId) {
            try {
                const response = await fetch(`${BASE_URL}/analysis/${analysisId}/results`, {
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const results = await response.json();
                displayResults(results);
                
            } catch (error) {
                showError(`Error loading results: ${error.message}`);
            }
        }
        
        function displayResults(results) {
            const resultsSection = document.getElementById('results-section');
            const resultsContent = document.getElementById('results-content');
            
            const summary = results.summary;
            const tier1 = results.tier1_results;
            
            resultsContent.innerHTML = `
                <h3>Summary</h3>
                <ul>
                    <li>Total observations: ${summary.total_observations.toLocaleString()}</li>
                    <li>Markets analyzed: ${summary.markets_analyzed}</li>
                    <li>Commodities analyzed: ${summary.commodities_analyzed}</li>
                    <li>Integration score: ${summary.integration_score.toFixed(3)}</li>
                    <li>Data quality score: ${summary.data_quality_score.toFixed(3)}</li>
                </ul>
                
                <h3>Tier 1 Results</h3>
                <ul>
                    <li>Model type: ${tier1.model_type}</li>
                    <li>R-squared: ${tier1.r_squared.toFixed(3)}</li>
                    <li>Adjusted R-squared: ${tier1.adjusted_r_squared.toFixed(3)}</li>
                    <li>F-statistic: ${tier1.f_statistic.toFixed(2)}</li>
                    <li>P-value: ${tier1.p_value.toFixed(6)}</li>
                </ul>
            `;
            
            resultsSection.style.display = 'block';
        }
        
        function showError(message) {
            alert(`Analysis failed: ${message}`);
            document.getElementById('analysis-form').style.display = 'block';
            document.getElementById('analysis-status').style.display = 'none';
        }
        
        async function downloadResults() {
            if (!currentAnalysisId) return;
            
            try {
                const response = await fetch(`${BASE_URL}/analysis/${currentAnalysisId}/results?format=excel`, {
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `analysis_${currentAnalysisId}_results.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
            } catch (error) {
                alert(`Error downloading results: ${error.message}`);
            }
        }
        
        // Set default dates
        const today = new Date();
        const lastYear = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
        
        document.getElementById('end-date').value = today.toISOString().split('T')[0];
        document.getElementById('start-date').value = lastYear.toISOString().split('T')[0];
    </script>
</body>
</html>
```

This comprehensive API documentation provides developers and researchers with all the information needed to effectively use the Yemen Market Integration API v2. The documentation includes practical examples, error handling strategies, and real-world use cases for both programmatic access and web-based integrations.