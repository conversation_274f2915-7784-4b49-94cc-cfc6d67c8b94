# Yemen Market Integration v2 Deployment Guide

## Overview

This comprehensive deployment guide covers all aspects of deploying the Yemen Market Integration v2 system across different environments, from local development to production-scale Kubernetes clusters.

## Table of Contents

- [Quick Start](#quick-start)
- [Local Development](#local-development)
- [Staging Environment](#staging-environment)
- [Production Deployment](#production-deployment)
- [Container Orchestration](#container-orchestration)
- [Monitoring and Observability](#monitoring-and-observability)
- [Security Configuration](#security-configuration)
- [Backup and Recovery](#backup-and-recovery)
- [Troubleshooting](#troubleshooting)

## Quick Start

### Prerequisites

- **Docker**: 20.10+ with Docker Compose v2
- **Kubernetes**: 1.25+ (for production)
- **Python**: 3.11+ (for local development)
- **PostgreSQL**: 14+ (or use containerized version)
- **Redis**: 6.2+ (or use containerized version)

### 5-Minute Local Setup

```bash
# Clone repository
git clone https://github.com/worldbank/yemen-market-integration-v2.git
cd yemen-market-integration-v2/v2

# Start with Docker Compose (recommended)
docker-compose up -d

# Verify deployment
curl http://localhost:8000/health

# Access API documentation
open http://localhost:8000/docs
```

## Local Development

### Option 1: Docker Compose (Recommended)

The fastest way to get started with a complete development environment:

```bash
# Clone and navigate
git clone <repository-url>
cd yemen-market-integration-v2/v2

# Copy environment configuration
cp .env.example .env

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f api

# Stop services
docker-compose down
```

**Services Started:**
- **API Server**: http://localhost:8000
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **Grafana**: http://localhost:3000
- **Prometheus**: http://localhost:9090

### Option 2: Native Python Development

For active development with hot reloading:

```bash
# Setup Python environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Install in development mode
pip install -e .

# Setup environment variables
export DATABASE_URL="postgresql://postgres:password@localhost:5432/yemen_market_v2"
export REDIS_URL="redis://localhost:6379"
export JWT_SECRET_KEY="your-secret-key"
export API_ENV="development"

# Start database (Docker)
docker run -d --name postgres \
  -e POSTGRES_DB=yemen_market_v2 \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:14

# Start Redis (Docker)
docker run -d --name redis \
  -p 6379:6379 \
  redis:6.2-alpine

# Run database migrations
python -m src.infrastructure.persistence.migrations.migrate

# Start API server with hot reload
uvicorn src.interfaces.api.rest.app:app --reload --host 0.0.0.0 --port 8000

# Start worker processes (separate terminal)
python -m src.application.services.worker

# Run tests
pytest tests/ -v --cov=src
```

### Development Configuration

Create `.env` file with development settings:

```env
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/yemen_market_v2
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# Redis
REDIS_URL=redis://localhost:6379
REDIS_POOL_SIZE=10

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_ENV=development
API_DEBUG=true
API_RELOAD=true

# Security
JWT_SECRET_KEY=your-development-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24
API_KEY_EXPIRATION_DAYS=365

# External Services
WFP_API_KEY=your-wfp-api-key
ACLED_API_KEY=your-acled-api-key
HDX_API_KEY=your-hdx-api-key

# Observability
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=DEBUG
METRICS_ENABLED=true

# Performance
WORKER_CONCURRENCY=4
ANALYSIS_TIMEOUT_SECONDS=3600
```

### Development Tools

```bash
# Code formatting
black src/ tests/
isort src/ tests/

# Linting
flake8 src/ tests/
mypy src/

# Testing with coverage
pytest tests/ --cov=src --cov-report=html

# Generate API documentation
python scripts/generate_api_docs.py

# Database operations
python scripts/db_migrate.py
python scripts/db_seed.py  # Load sample data
python scripts/db_reset.py  # Reset database
```

## Staging Environment

### AWS ECS Deployment

Staging environment using AWS ECS Fargate for container orchestration:

```bash
# Prerequisites
aws configure  # Configure AWS credentials
docker login <your-ecr-repository>

# Build and push images
docker build -t yemen-market-v2-api .
docker tag yemen-market-v2-api:latest <account>.dkr.ecr.region.amazonaws.com/yemen-market-v2:staging
docker push <account>.dkr.ecr.region.amazonaws.com/yemen-market-v2:staging

# Deploy using Terraform
cd infrastructure/terraform/staging
terraform init
terraform plan -var-file="staging.tfvars"
terraform apply

# Or deploy using AWS CDK
cd infrastructure/cdk
npm install
cdk deploy YemenMarketV2StagingStack --profile staging
```

### Staging Configuration

**Environment Variables:**
```env
# Environment
API_ENV=staging
API_DEBUG=false
LOG_LEVEL=INFO

# Database (RDS)
DATABASE_URL=postgresql://user:<EMAIL>:5432/yemen_market_v2
DATABASE_SSL_MODE=require

# Redis (ElastiCache)
REDIS_URL=redis://staging-redis.cache.amazonaws.com:6379
REDIS_SSL=true

# Load Balancer
API_HOST=0.0.0.0
API_PORT=8000
ALLOWED_HOSTS=staging-api.yemen-market-integration.org

# Security
JWT_SECRET_KEY=staging-secret-key-from-secrets-manager
CORS_ORIGINS=https://staging.yemen-market-integration.org

# External APIs
WFP_API_KEY=staging-wfp-api-key
ACLED_API_KEY=staging-acled-api-key
HDX_API_KEY=staging-hdx-api-key

# Monitoring
SENTRY_DSN=https://<EMAIL>/project
DATADOG_API_KEY=staging-datadog-api-key
NEW_RELIC_LICENSE_KEY=staging-newrelic-license

# Performance
WORKER_CONCURRENCY=8
ANALYSIS_TIMEOUT_SECONDS=1800
```

### Staging Validation

```bash
# Health checks
curl https://staging-api.yemen-market-integration.org/health

# API functionality test
curl -H "Authorization: Bearer staging-api-key" \
     https://staging-api.yemen-market-integration.org/api/v1/markets

# Load test
ab -n 1000 -c 10 -H "Authorization: Bearer staging-api-key" \
   https://staging-api.yemen-market-integration.org/api/v1/health

# Integration test suite
pytest tests/integration/ -v --staging
```

## Production Deployment

### Kubernetes Deployment (Recommended)

Production deployment using Kubernetes with high availability and auto-scaling:

#### Prerequisites

```bash
# Install required tools
kubectl version --client
helm version
istioctl version  # Optional: for service mesh

# Configure kubectl context
kubectl config use-context production-cluster

# Verify cluster access
kubectl cluster-info
kubectl get nodes
```

#### Deployment Steps

```bash
# 1. Create namespace and secrets
kubectl apply -f kubernetes/namespace.yaml
kubectl apply -f kubernetes/secrets.yaml

# 2. Deploy PostgreSQL (HA setup)
helm repo add bitnami https://charts.bitnami.com/bitnami
helm install postgresql bitnami/postgresql-ha \
  --namespace yemen-market-v2 \
  --values kubernetes/postgresql-values.yaml

# 3. Deploy Redis (HA setup)
helm install redis bitnami/redis \
  --namespace yemen-market-v2 \
  --values kubernetes/redis-values.yaml

# 4. Deploy application components
kubectl apply -f kubernetes/configmap.yaml
kubectl apply -f kubernetes/api-deployment.yaml
kubectl apply -f kubernetes/worker-deployment.yaml
kubectl apply -f kubernetes/worker-hpa.yaml

# 5. Setup ingress and load balancer
kubectl apply -f kubernetes/ingress.yaml

# 6. Deploy monitoring stack
kubectl apply -f kubernetes/monitoring-enhanced.yaml

# 7. Setup backup strategy
kubectl apply -f kubernetes/backup-strategy.yaml
kubectl apply -f kubernetes/backup-cronjob.yaml
```

#### Production Configuration Files

**kubernetes/api-deployment.yaml:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yemen-market-api
  namespace: yemen-market-v2
  labels:
    app: yemen-market-api
    version: v2.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: yemen-market-api
  template:
    metadata:
      labels:
        app: yemen-market-api
        version: v2.0.0
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: api
        image: your-registry/yemen-market-v2:v2.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yemen-market-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: yemen-market-secrets
              key: redis-url
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: yemen-market-secrets
              key: jwt-secret
        envFrom:
        - configMapRef:
            name: yemen-market-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
---
apiVersion: v1
kind: Service
metadata:
  name: yemen-market-api-service
  namespace: yemen-market-v2
  labels:
    app: yemen-market-api
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: yemen-market-api
```

#### Blue-Green Deployment

```bash
# 1. Deploy green version
sed 's/app: yemen-market-api/app: yemen-market-api-green/' kubernetes/api-deployment.yaml | kubectl apply -f -

# 2. Wait for green deployment to be ready
kubectl rollout status deployment/yemen-market-api-green -n yemen-market-v2

# 3. Run health checks on green version
kubectl port-forward service/yemen-market-api-green-service 8080:80 -n yemen-market-v2 &
curl http://localhost:8080/health

# 4. Switch traffic to green
kubectl patch service yemen-market-api-service -n yemen-market-v2 -p '{"spec":{"selector":{"app":"yemen-market-api-green"}}}'

# 5. Monitor and verify
kubectl logs -f deployment/yemen-market-api-green -n yemen-market-v2

# 6. Remove blue version (after validation)
kubectl delete deployment yemen-market-api -n yemen-market-v2
```

### Production Environment Variables

```env
# Environment
API_ENV=production
API_DEBUG=false
LOG_LEVEL=WARNING
METRICS_ENABLED=true

# Database (Managed PostgreSQL)
DATABASE_URL=postgresql://user:<EMAIL>:5432/yemen_market_v2
DATABASE_SSL_MODE=require
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10
DATABASE_POOL_TIMEOUT=30

# Redis (Managed Redis)
REDIS_URL=redis://prod-redis.cluster.amazonaws.com:6379
REDIS_SSL=true
REDIS_POOL_SIZE=20

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
ALLOWED_HOSTS=api.yemen-market-integration.org
CORS_ORIGINS=https://yemen-market-integration.org,https://app.yemen-market-integration.org

# Security
JWT_SECRET_KEY=production-secret-key-from-vault
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24
API_RATE_LIMIT=1000/hour
ENABLE_RATE_LIMITING=true

# External APIs
WFP_API_KEY=production-wfp-api-key
ACLED_API_KEY=production-acled-api-key
HDX_API_KEY=production-hdx-api-key

# Observability
SENTRY_DSN=https://<EMAIL>/project
SENTRY_TRACES_SAMPLE_RATE=0.1
DATADOG_API_KEY=production-datadog-api-key
NEW_RELIC_LICENSE_KEY=production-newrelic-license

# Performance
WORKER_CONCURRENCY=16
ANALYSIS_TIMEOUT_SECONDS=3600
MAX_CONCURRENT_ANALYSES=50
ENABLE_CACHING=true
CACHE_TTL_SECONDS=3600

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
S3_BACKUP_BUCKET=yemen-market-v2-backups
```

## Container Orchestration

### Docker Compose for Development

**docker-compose.yml:**
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: yemen_market_v2
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: ********************************************/yemen_market_v2
      REDIS_URL: redis://redis:6379
      API_ENV: development
      LOG_LEVEL: DEBUG
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./src:/app/src
      - ./data:/app/data
    command: ["uvicorn", "src.interfaces.api.rest.app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      DATABASE_URL: ********************************************/yemen_market_v2
      REDIS_URL: redis://redis:6379
      API_ENV: development
      LOG_LEVEL: DEBUG
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./src:/app/src
      - ./data:/app/data
    command: ["python", "-m", "src.application.services.worker"]

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  default:
    name: yemen-market-v2
```

### Docker Build Optimization

**Multi-stage Dockerfile:**
```dockerfile
# Build stage
FROM python:3.11-slim as builder

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim as production

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

# Copy Python packages from builder
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
WORKDIR /app
COPY --chown=appuser:appuser src/ src/
COPY --chown=appuser:appuser scripts/ scripts/
COPY --chown=appuser:appuser pyproject.toml .

# Make sure scripts are executable
RUN chmod +x scripts/*.py

# Switch to non-root user
USER appuser

# Set PATH to include user packages
ENV PATH=/home/<USER>/.local/bin:$PATH

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python scripts/health_check.py

# Default command
CMD ["uvicorn", "src.interfaces.api.rest.app:app", "--host", "0.0.0.0", "--port", "8000"]
```

## Monitoring and Observability

### Metrics Collection

**Prometheus Configuration:**
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'yemen-market-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
      - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https

alertmanager:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Grafana Dashboards

**API Performance Dashboard:**
```json
{
  "dashboard": {
    "title": "Yemen Market Integration API Performance",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{ method }} {{ endpoint }}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "5xx errors"
          }
        ]
      },
      {
        "title": "Active Analyses",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(analysis_status{status=\"running\"})",
            "legendFormat": "Running"
          }
        ]
      }
    ]
  }
}
```

### Alerting Rules

**prometheus/alert_rules.yml:**
```yaml
groups:
  - name: yemen-market-api
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      - alert: DatabaseConnectionFailure
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "PostgreSQL database is unreachable"

      - alert: AnalysisQueueBacklog
        expr: analysis_queue_size > 100
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Analysis queue backlog"
          description: "{{ $value }} analyses queued for more than 10 minutes"
```

## Security Configuration

### SSL/TLS Setup

**Nginx SSL Configuration:**
```nginx
server {
    listen 443 ssl http2;
    server_name api.yemen-market-integration.org;

    # SSL certificates
    ssl_certificate /etc/ssl/certs/yemen-market-integration.org.crt;
    ssl_certificate_key /etc/ssl/private/yemen-market-integration.org.key;

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # Proxy configuration
    location / {
        proxy_pass http://yemen-market-api-service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE support
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
    }

    # Health check endpoint (no rate limiting)
    location /health {
        limit_req off;
        proxy_pass http://yemen-market-api-service/health;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name api.yemen-market-integration.org;
    return 301 https://$server_name$request_uri;
}
```

### Secrets Management

**Kubernetes Secrets with External Secrets Operator:**
```yaml
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: yemen-market-secrets
  namespace: yemen-market-v2
spec:
  refreshInterval: 15m
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: yemen-market-secrets
    creationPolicy: Owner
  data:
  - secretKey: database-url
    remoteRef:
      key: secret/yemen-market-v2
      property: database_url
  - secretKey: redis-url
    remoteRef:
      key: secret/yemen-market-v2
      property: redis_url
  - secretKey: jwt-secret
    remoteRef:
      key: secret/yemen-market-v2
      property: jwt_secret_key
  - secretKey: wfp-api-key
    remoteRef:
      key: secret/yemen-market-v2
      property: wfp_api_key
```

## Backup and Recovery

### Database Backup Strategy

**Automated PostgreSQL Backup:**
```bash
#!/bin/bash
# backup-database.sh

set -e

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="yemen_market_v2_backup_${BACKUP_DATE}.sql"
S3_BUCKET="yemen-market-v2-backups"

# Create backup
pg_dump $DATABASE_URL > /tmp/$BACKUP_FILE

# Compress backup
gzip /tmp/$BACKUP_FILE

# Upload to S3
aws s3 cp /tmp/${BACKUP_FILE}.gz s3://$S3_BUCKET/

# Clean up local file
rm /tmp/${BACKUP_FILE}.gz

# Remove old backups (keep 30 days)
aws s3 ls s3://$S3_BUCKET/ | \
  awk '{print $4}' | \
  sort | \
  head -n -30 | \
  xargs -I {} aws s3 rm s3://$S3_BUCKET/{}

echo "Backup completed: ${BACKUP_FILE}.gz"
```

**Kubernetes CronJob for Backups:**
```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: database-backup
  namespace: yemen-market-v2
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: postgres:14-alpine
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: database-url
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            command:
            - /bin/bash
            - -c
            - |
              BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
              BACKUP_FILE="yemen_market_v2_backup_${BACKUP_DATE}.sql"
              
              pg_dump $DATABASE_URL > /tmp/$BACKUP_FILE
              gzip /tmp/$BACKUP_FILE
              
              # Install AWS CLI
              apk add --no-cache aws-cli
              
              # Upload to S3
              aws s3 cp /tmp/${BACKUP_FILE}.gz s3://yemen-market-v2-backups/
              
              echo "Backup completed: ${BACKUP_FILE}.gz"
          restartPolicy: OnFailure
```

### Disaster Recovery

**Recovery Procedures:**
```bash
# 1. Restore from latest backup
LATEST_BACKUP=$(aws s3 ls s3://yemen-market-v2-backups/ | sort | tail -n 1 | awk '{print $4}')
aws s3 cp s3://yemen-market-v2-backups/$LATEST_BACKUP /tmp/

# 2. Restore database
gunzip /tmp/$LATEST_BACKUP
psql $DATABASE_URL < /tmp/${LATEST_BACKUP%.gz}

# 3. Verify data integrity
python scripts/verify_data_integrity.py

# 4. Restart services
kubectl rollout restart deployment/yemen-market-api -n yemen-market-v2
kubectl rollout restart deployment/yemen-market-worker -n yemen-market-v2
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check database connectivity
kubectl exec -it deployment/yemen-market-api -n yemen-market-v2 -- psql $DATABASE_URL -c "SELECT 1"

# Check database logs
kubectl logs postgresql-primary-0 -n yemen-market-v2

# Restart database if needed
kubectl delete pod postgresql-primary-0 -n yemen-market-v2
```

#### Memory Issues
```bash
# Check pod memory usage
kubectl top pods -n yemen-market-v2

# Increase memory limits
kubectl patch deployment yemen-market-api -n yemen-market-v2 -p '{"spec":{"template":{"spec":{"containers":[{"name":"api","resources":{"limits":{"memory":"4Gi"}}}]}}}}'

# Check for memory leaks
kubectl exec -it deployment/yemen-market-api -n yemen-market-v2 -- python scripts/memory_profile.py
```

#### Performance Issues
```bash
# Check API metrics
curl -H "Authorization: Bearer $API_KEY" https://api.yemen-market-integration.org/metrics

# Profile slow queries
kubectl exec -it postgresql-primary-0 -n yemen-market-v2 -- psql -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10"

# Scale workers
kubectl scale deployment yemen-market-worker --replicas=10 -n yemen-market-v2
```

### Deployment Validation

**Health Check Script:**
```bash
#!/bin/bash
# validate-deployment.sh

API_URL=${1:-"https://api.yemen-market-integration.org"}
API_KEY=${2:-"$PRODUCTION_API_KEY"}

echo "Validating deployment at $API_URL"

# Basic health check
if ! curl -f "$API_URL/health" > /dev/null 2>&1; then
    echo "❌ Health check failed"
    exit 1
fi
echo "✅ Health check passed"

# API authentication
if ! curl -f -H "Authorization: Bearer $API_KEY" "$API_URL/api/v1/markets" > /dev/null 2>&1; then
    echo "❌ API authentication failed"
    exit 1
fi
echo "✅ API authentication passed"

# Database connectivity
if ! curl -f -H "Authorization: Bearer $API_KEY" "$API_URL/api/v1/markets?limit=1" > /dev/null 2>&1; then
    echo "❌ Database connectivity failed"
    exit 1
fi
echo "✅ Database connectivity passed"

# Create test analysis
ANALYSIS_ID=$(curl -s -X POST \
    -H "Authorization: Bearer $API_KEY" \
    -H "Content-Type: application/json" \
    -d '{
        "start_date": "2023-01-01",
        "end_date": "2023-01-31",
        "markets": ["Test Market"],
        "commodities": ["wheat"],
        "confidence_level": 0.95
    }' \
    "$API_URL/api/v1/analysis/three-tier" | jq -r '.id')

if [ "$ANALYSIS_ID" = "null" ]; then
    echo "❌ Analysis creation failed"
    exit 1
fi
echo "✅ Analysis creation passed (ID: $ANALYSIS_ID)"

echo "🎉 All deployment validation checks passed!"
```

This comprehensive deployment guide provides everything needed to successfully deploy and operate the Yemen Market Integration v2 system across all environments, from local development to production-scale Kubernetes clusters.