# Yemen Market Integration v2 Troubleshooting Guide

## Overview

This comprehensive troubleshooting guide helps diagnose and resolve common issues with the Yemen Market Integration v2 system. The guide is organized by component and includes diagnostic procedures, solutions, and prevention strategies.

## Table of Contents

- [Quick Diagnosis](#quick-diagnosis)
- [Installation Issues](#installation-issues)
- [API Issues](#api-issues)
- [Database Issues](#database-issues)
- [Analysis Issues](#analysis-issues)
- [Performance Issues](#performance-issues)
- [Network and Connectivity](#network-and-connectivity)
- [Security Issues](#security-issues)
- [Data Quality Issues](#data-quality-issues)
- [Deployment Issues](#deployment-issues)
- [Development Issues](#development-issues)

## Quick Diagnosis

### System Health Check

Run this quick diagnostic script to identify common issues:

```bash
#!/bin/bash
# quick_health_check.sh - Quick system diagnosis

echo "=== Yemen Market Integration v2 Health Check ==="

# 1. Check API availability
echo "1. API Health:"
API_URL=${1:-"http://localhost:8000"}
if curl -s -f "$API_URL/health" > /dev/null; then
    echo "   ✅ API is responding"
    api_status=$(curl -s "$API_URL/health" | jq -r '.status // "unknown"')
    echo "   Status: $api_status"
else
    echo "   ❌ API is not responding"
    echo "   Check: Is the server running? Is the port correct?"
fi

# 2. Check database connectivity
echo "2. Database:"
if command -v psql > /dev/null && [ -n "$DATABASE_URL" ]; then
    if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        echo "   ✅ Database is accessible"
    else
        echo "   ❌ Database connection failed"
        echo "   Check: DATABASE_URL, credentials, network"
    fi
else
    echo "   ⚠️  Cannot test database (psql not available or DATABASE_URL not set)"
fi

# 3. Check Redis
echo "3. Redis:"
if command -v redis-cli > /dev/null && [ -n "$REDIS_URL" ]; then
    if redis-cli -u "$REDIS_URL" ping > /dev/null 2>&1; then
        echo "   ✅ Redis is accessible"
    else
        echo "   ❌ Redis connection failed"
        echo "   Check: REDIS_URL, Redis server status"
    fi
else
    echo "   ⚠️  Cannot test Redis (redis-cli not available or REDIS_URL not set)"
fi

# 4. Check Python environment
echo "4. Python Environment:"
if python --version > /dev/null 2>&1; then
    python_version=$(python --version 2>&1)
    echo "   ✅ Python available: $python_version"
    
    # Check key packages
    if python -c "import fastapi, sqlalchemy, redis" 2>/dev/null; then
        echo "   ✅ Key packages installed"
    else
        echo "   ❌ Missing required packages"
        echo "   Run: pip install -r requirements.txt"
    fi
else
    echo "   ❌ Python not available"
fi

# 5. Check disk space
echo "5. Disk Space:"
disk_usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$disk_usage" -lt 85 ]; then
    echo "   ✅ Disk space OK ($disk_usage% used)"
else
    echo "   ⚠️  Disk space low ($disk_usage% used)"
    echo "   Consider cleaning up logs and temporary files"
fi

# 6. Check memory
echo "6. Memory:"
if command -v free > /dev/null; then
    memory_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100}')
    if [ "$memory_usage" -lt 90 ]; then
        echo "   ✅ Memory usage OK ($memory_usage%)"
    else
        echo "   ⚠️  High memory usage ($memory_usage%)"
    fi
else
    echo "   ⚠️  Cannot check memory (free command not available)"
fi

echo "=== Health Check Complete ==="
```

### Diagnostic Commands

```bash
# Check service status
curl http://localhost:8000/health

# Check API endpoints
curl -H "Authorization: Bearer YOUR_API_KEY" \
     http://localhost:8000/api/v1/markets

# Check logs
docker-compose logs api
kubectl logs -f deployment/yemen-market-api -n yemen-market-v2

# Check database connection
psql $DATABASE_URL -c "SELECT version();"

# Check Redis connection
redis-cli -u $REDIS_URL ping
```

## Installation Issues

### Issue: Poetry Installation Fails

**Symptoms:**
- `poetry: command not found`
- Permission errors during Poetry installation
- Poetry installs but doesn't work

**Solution:**
```bash
# Method 1: Official installer (recommended)
curl -sSL https://install.python-poetry.org | python3 -

# Add to PATH
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Method 2: pip installation (if official fails)
pip install --user poetry

# Method 3: Using package manager
# Ubuntu/Debian
sudo apt update && sudo apt install python3-poetry

# macOS
brew install poetry

# Verify installation
poetry --version
```

**Prevention:**
- Use virtual environments
- Check Python version compatibility (3.11+)
- Ensure PATH is correctly configured

### Issue: Dependency Conflicts

**Symptoms:**
- `poetry install` fails with dependency conflicts
- ImportError when running the application
- Package version mismatches

**Diagnostic:**
```bash
# Check dependency tree
poetry show --tree

# Check for conflicts
poetry check

# Show outdated packages
poetry show --outdated
```

**Solution:**
```bash
# Clear poetry cache
poetry cache clear --all pypi

# Update lock file
poetry lock --no-update

# Install with verbose output
poetry install -vvv

# For specific conflicts, update pyproject.toml
poetry add "package>=version,<next_version"

# Force reinstall if needed
poetry install --no-deps
```

### Issue: Docker Build Failures

**Symptoms:**
- Docker build stops with errors
- Out of disk space during build
- Network timeouts during package installation

**Solution:**
```bash
# Clean Docker cache
docker system prune -a

# Build with more verbose output
docker build --progress=plain -t yemen-market-v2 .

# Build with specific platform
docker build --platform linux/amd64 -t yemen-market-v2 .

# For network issues, use build args
docker build --build-arg HTTP_PROXY=http://proxy:8080 .

# Check disk space
df -h
docker system df
```

## API Issues

### Issue: API Not Starting

**Symptoms:**
- Server fails to start
- Port already in use errors
- Import errors on startup

**Diagnostic:**
```bash
# Check if port is in use
netstat -tulpn | grep :8000
lsof -i :8000

# Check environment variables
env | grep -E "(DATABASE_URL|REDIS_URL|API_)"

# Run with debug mode
export API_DEBUG=true
python -m uvicorn src.interfaces.api.rest.app:app --reload --log-level debug
```

**Solution:**
```bash
# Kill process using port
sudo kill -9 $(lsof -t -i:8000)

# Use different port
uvicorn src.interfaces.api.rest.app:app --port 8001

# Check configuration
python -c "
from src.interfaces.api.rest.app import create_app
app = create_app()
print('App created successfully')
"

# Fix missing environment variables
cp .env.example .env
# Edit .env with correct values
```

### Issue: 500 Internal Server Error

**Symptoms:**
- API returns 500 errors
- Database connection errors
- Authentication failures

**Diagnostic:**
```bash
# Check API logs
docker-compose logs api
tail -f logs/api.log

# Test database connection manually
python -c "
import asyncpg
import asyncio
async def test():
    conn = await asyncpg.connect('$DATABASE_URL')
    result = await conn.fetchval('SELECT 1')
    print(f'Database test: {result}')
    await conn.close()
asyncio.run(test())
"

# Check API health endpoint
curl -v http://localhost:8000/health
```

**Solution:**
```bash
# Fix database connection
export DATABASE_URL="postgresql://user:pass@localhost:5432/yemen_market_v2"

# Restart services
docker-compose restart

# Check for missing migrations
python -m alembic upgrade head

# Verify configuration
python scripts/verify_config.py
```

### Issue: Authentication Errors

**Symptoms:**
- 401 Unauthorized responses
- JWT token errors
- API key not working

**Diagnostic:**
```bash
# Test API key
curl -H "Authorization: Bearer YOUR_API_KEY" \
     http://localhost:8000/api/v1/auth/verify

# Check JWT token
python -c "
import jwt
token = 'YOUR_JWT_TOKEN'
try:
    decoded = jwt.decode(token, options={'verify_signature': False})
    print('Token payload:', decoded)
except Exception as e:
    print('Token error:', e)
"
```

**Solution:**
```bash
# Generate new API key
curl -X POST http://localhost:8000/api/v1/auth/api-keys \
     -H "Authorization: Bearer ADMIN_TOKEN" \
     -d '{"name": "test-key"}'

# Check JWT secret
export JWT_SECRET_KEY="your-secret-key"

# Verify authentication middleware
grep -r "AuthenticationMiddleware" src/interfaces/api/
```

### Issue: Slow API Responses

**Symptoms:**
- API responses take >5 seconds
- Timeout errors
- High memory/CPU usage

**Diagnostic:**
```bash
# Test response times
time curl http://localhost:8000/api/v1/markets

# Check database query performance
psql $DATABASE_URL -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
"

# Monitor system resources
top -p $(pgrep -f uvicorn)
docker stats
```

**Solution:**
```bash
# Enable caching
export ENABLE_CACHING=true

# Add database indexes
psql $DATABASE_URL -c "
CREATE INDEX CONCURRENTLY idx_prices_market_date 
ON price_observations (market_id, date DESC);
"

# Increase worker processes
uvicorn src.interfaces.api.rest.app:app --workers 4

# Optimize queries
python scripts/analyze_slow_queries.py
```

## Database Issues

### Issue: Connection Refused

**Symptoms:**
- `psycopg2.OperationalError: connection refused`
- Database server not responding
- Cannot connect to database

**Diagnostic:**
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql
docker ps | grep postgres

# Test connection
pg_isready -h localhost -p 5432

# Check connection string
echo $DATABASE_URL
```

**Solution:**
```bash
# Start PostgreSQL service
sudo systemctl start postgresql

# Start Docker PostgreSQL
docker run -d --name postgres \
  -e POSTGRES_DB=yemen_market_v2 \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:14

# Fix connection string
export DATABASE_URL="postgresql://postgres:password@localhost:5432/yemen_market_v2"

# Test connection
psql $DATABASE_URL -c "SELECT version();"
```

### Issue: Migration Failures

**Symptoms:**
- Alembic migration errors
- Schema version conflicts
- Missing tables/columns

**Diagnostic:**
```bash
# Check migration status
python -m alembic current
python -m alembic history

# Check database schema
psql $DATABASE_URL -c "\dt"  # List tables
psql $DATABASE_URL -c "\d markets"  # Describe table
```

**Solution:**
```bash
# Reset migrations (CAUTION: This drops data)
python -m alembic downgrade base
python -m alembic upgrade head

# Fix migration conflicts
python -m alembic merge heads

# Manual schema fix
psql $DATABASE_URL -f scripts/fix_schema.sql

# Stamp current version
python -m alembic stamp head
```

### Issue: Performance Problems

**Symptoms:**
- Slow query execution
- High CPU usage
- Lock timeouts

**Diagnostic:**
```bash
# Check running queries
psql $DATABASE_URL -c "
SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';
"

# Check locks
psql $DATABASE_URL -c "
SELECT * FROM pg_locks WHERE NOT granted;
"

# Check index usage
psql $DATABASE_URL -c "
SELECT schemaname,tablename,attname,n_distinct,correlation
FROM pg_stats
WHERE tablename = 'price_observations';
"
```

**Solution:**
```bash
# Kill long-running queries
psql $DATABASE_URL -c "
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'active' AND query_start < now() - interval '10 minutes';
"

# Add missing indexes
psql $DATABASE_URL -c "
CREATE INDEX CONCURRENTLY idx_prices_market_commodity_date 
ON price_observations (market_id, commodity_id, date DESC);
"

# Update table statistics
psql $DATABASE_URL -c "ANALYZE price_observations;"

# Vacuum large tables
psql $DATABASE_URL -c "VACUUM ANALYZE price_observations;"
```

## Analysis Issues

### Issue: Analysis Fails to Start

**Symptoms:**
- Analysis stuck in "pending" status
- Worker processes not running
- Queue processing errors

**Diagnostic:**
```bash
# Check worker status
docker-compose ps worker
kubectl get pods -l app=yemen-market-worker

# Check analysis queue
curl -H "Authorization: Bearer $API_KEY" \
     http://localhost:8000/api/v1/analysis/queue

# Check worker logs
docker-compose logs worker
kubectl logs deployment/yemen-market-worker
```

**Solution:**
```bash
# Restart workers
docker-compose restart worker
kubectl rollout restart deployment/yemen-market-worker

# Scale up workers
kubectl scale deployment yemen-market-worker --replicas=3

# Clear stuck analyses
python scripts/clear_stuck_analyses.py

# Check worker configuration
python -c "
from src.application.services.worker import WorkerConfig
config = WorkerConfig()
print('Worker config:', config.__dict__)
"
```

### Issue: Analysis Returns Errors

**Symptoms:**
- Analysis completes with errors
- NaN values in results
- Statistical test failures

**Diagnostic:**
```bash
# Check analysis logs
curl -H "Authorization: Bearer $API_KEY" \
     http://localhost:8000/api/v1/analysis/ANALYSIS_ID/logs

# Validate input data
python scripts/validate_analysis_data.py \
  --markets "Sana'a,Aden" \
  --commodities "wheat,rice" \
  --start-date "2023-01-01" \
  --end-date "2023-12-31"

# Check data quality
python -c "
import pandas as pd
from src.data.panel_builder import PanelBuilder
builder = PanelBuilder()
data = builder.create_balanced_panel(
    markets=['Sana\'a', 'Aden'],
    commodities=['wheat'],
    start_date='2023-01-01',
    end_date='2023-01-31'
)
print('Data shape:', data.shape)
print('Missing values:', data.isnull().sum().sum())
print('Data types:', data.dtypes)
"
```

**Solution:**
```bash
# Fix data quality issues
python scripts/clean_data.py

# Use different analysis configuration
curl -X POST http://localhost:8000/api/v1/analysis/three-tier \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "start_date": "2023-01-01",
    "end_date": "2023-03-31",
    "markets": ["Sana'\''a", "Aden"],
    "commodities": ["wheat"],
    "confidence_level": 0.90,
    "tier1_config": {"method": "pooled_ols"},
    "tier2_config": {"max_lags": 2}
  }'

# Check minimum data requirements
python scripts/check_data_requirements.py
```

### Issue: Inconsistent Results

**Symptoms:**
- Different results from same analysis
- Results don't match expectations
- Tier results don't align

**Diagnostic:**
```bash
# Compare analysis configurations
curl -H "Authorization: Bearer $API_KEY" \
     http://localhost:8000/api/v1/analysis/ANALYSIS_ID1/config
curl -H "Authorization: Bearer $API_KEY" \
     http://localhost:8000/api/v1/analysis/ANALYSIS_ID2/config

# Check data versions
python scripts/check_data_versions.py

# Validate statistical results
python scripts/validate_results.py --analysis-id ANALYSIS_ID
```

**Solution:**
```bash
# Use fixed random seed
export ANALYSIS_RANDOM_SEED=12345

# Clear cache and rerun
redis-cli -u $REDIS_URL FLUSHALL
# Rerun analysis

# Use validation mode
curl -X POST http://localhost:8000/api/v1/analysis/three-tier \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "validation_mode": true,
    "random_seed": 12345,
    ...
  }'
```

## Performance Issues

### Issue: High Memory Usage

**Symptoms:**
- Out of memory errors
- System becomes unresponsive
- Process killed by OS

**Diagnostic:**
```bash
# Monitor memory usage
docker stats
kubectl top pods

# Check memory leaks
python -c "
import psutil
import os
process = psutil.Process(os.getpid())
print('Memory info:', process.memory_info())
print('Memory percent:', process.memory_percent())
"

# Profile memory usage
python -m memory_profiler scripts/run_analysis.py
```

**Solution:**
```bash
# Increase container memory limits
docker run -m 4g yemen-market-v2

# Scale horizontally
kubectl scale deployment yemen-market-api --replicas=3

# Optimize data loading
export BATCH_SIZE=1000
export USE_CHUNKING=true

# Enable memory monitoring
export MEMORY_LIMIT_MB=2048
python scripts/run_with_memory_limit.py
```

### Issue: Slow Data Loading

**Symptoms:**
- Data loading takes >10 minutes
- Timeout errors during data fetch
- High disk I/O

**Diagnostic:**
```bash
# Monitor I/O
iostat -x 1

# Check data size
du -sh data/
psql $DATABASE_URL -c "
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname='public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
"

# Profile data loading
python -m cProfile -o profile.stats scripts/load_data.py
python -c "
import pstats
stats = pstats.Stats('profile.stats')
stats.sort_stats('cumulative').print_stats(10)
"
```

**Solution:**
```bash
# Use parallel loading
export PARALLEL_WORKERS=4
python scripts/parallel_data_loader.py

# Enable compression
export USE_COMPRESSION=true

# Add database indexes for common queries
psql $DATABASE_URL -c "
CREATE INDEX CONCURRENTLY idx_prices_date 
ON price_observations (date DESC);
"

# Use connection pooling
export DATABASE_POOL_SIZE=20
```

## Network and Connectivity

### Issue: External API Failures

**Symptoms:**
- WFP/ACLED API returns errors
- Network timeouts
- Rate limiting errors

**Diagnostic:**
```bash
# Test external APIs directly
curl -v "https://api.wfp.org/vam-data-bridges/5.0.0/MarketPrices/Retail/CountryCode/YE"
curl -v "https://api.acleddata.com/acled/read?country=Yemen&limit=1"

# Check API keys
echo "WFP_API_KEY: ${WFP_API_KEY:0:10}..."
echo "ACLED_API_KEY: ${ACLED_API_KEY:0:10}..."

# Test network connectivity
ping api.wfp.org
nslookup api.acleddata.com
```

**Solution:**
```bash
# Update API keys
export WFP_API_KEY="new_valid_key"
export ACLED_API_KEY="new_valid_key"

# Configure retry logic
export API_RETRY_ATTEMPTS=5
export API_RETRY_DELAY=60

# Use proxy if needed
export HTTP_PROXY=http://proxy:8080
export HTTPS_PROXY=http://proxy:8080

# Test with curl through proxy
curl --proxy http://proxy:8080 https://api.wfp.org/health
```

### Issue: SSL/TLS Errors

**Symptoms:**
- Certificate verification failures
- SSL handshake errors
- HTTPS connection refused

**Diagnostic:**
```bash
# Check SSL certificate
openssl s_client -connect api.yemen-market-integration.org:443

# Test certificate chain
curl -vI https://api.yemen-market-integration.org

# Check certificate expiry
echo | openssl s_client -servername api.yemen-market-integration.org -connect api.yemen-market-integration.org:443 2>/dev/null | openssl x509 -noout -dates
```

**Solution:**
```bash
# Update certificate
sudo certbot renew

# Fix certificate chain
sudo nginx -t
sudo systemctl reload nginx

# For development, disable SSL verification (NOT for production)
export PYTHONHTTPSVERIFY=0
curl -k https://api.yemen-market-integration.org
```

## Security Issues

### Issue: Authentication Bypass

**Symptoms:**
- Unauthorized access to protected endpoints
- JWT tokens not validating
- API keys accepted when invalid

**Diagnostic:**
```bash
# Test without authentication
curl http://localhost:8000/api/v1/analysis

# Test with invalid token
curl -H "Authorization: Bearer invalid_token" \
     http://localhost:8000/api/v1/markets

# Check JWT configuration
python -c "
import os
print('JWT_SECRET_KEY set:', bool(os.getenv('JWT_SECRET_KEY')))
print('JWT_ALGORITHM:', os.getenv('JWT_ALGORITHM', 'HS256'))
"
```

**Solution:**
```bash
# Verify authentication middleware
grep -r "AuthenticationMiddleware" src/interfaces/api/rest/

# Reset JWT secret
export JWT_SECRET_KEY=$(openssl rand -base64 32)

# Force token refresh
redis-cli -u $REDIS_URL DEL "auth:*"

# Check authentication order in middleware stack
python -c "
from src.interfaces.api.rest.app import create_app
app = create_app()
print('Middleware:', [m.__class__.__name__ for m in app.user_middleware])
"
```

### Issue: Rate Limiting Not Working

**Symptoms:**
- Excessive requests not blocked
- Rate limit headers missing
- Performance degradation under load

**Diagnostic:**
```bash
# Test rate limiting
for i in {1..20}; do
  curl -w "%{http_code}\n" -o /dev/null -s \
       -H "Authorization: Bearer $API_KEY" \
       http://localhost:8000/api/v1/markets
done

# Check rate limit configuration
curl -I -H "Authorization: Bearer $API_KEY" \
     http://localhost:8000/api/v1/markets
```

**Solution:**
```bash
# Enable rate limiting
export ENABLE_RATE_LIMITING=true
export RATE_LIMIT_PER_MINUTE=100

# Configure Redis for rate limiting
export REDIS_URL="redis://localhost:6379/1"

# Check rate limiter implementation
python -c "
from src.infrastructure.security.rate_limiter import RateLimiter
limiter = RateLimiter()
print('Rate limiter configured:', limiter.is_enabled())
"
```

## Data Quality Issues

### Issue: Missing Data

**Symptoms:**
- Gaps in price series
- Markets with no data
- Analysis fails due to insufficient data

**Diagnostic:**
```bash
# Check data completeness
python scripts/data_quality_report.py

# Query missing data patterns
psql $DATABASE_URL -c "
SELECT 
  market_id, 
  commodity_id,
  COUNT(*) as observations,
  MIN(date) as first_date,
  MAX(date) as last_date,
  MAX(date) - MIN(date) as date_range
FROM price_observations 
GROUP BY market_id, commodity_id
HAVING COUNT(*) < 100;
"
```

**Solution:**
```bash
# Re-fetch missing data
python scripts/backfill_data.py \
  --start-date "2023-01-01" \
  --end-date "2023-12-31" \
  --sources "wfp,acled"

# Use interpolation for gaps
python scripts/interpolate_missing_data.py

# Exclude markets with insufficient data
export MIN_OBSERVATIONS_PER_MARKET=50
```

### Issue: Data Inconsistencies

**Symptoms:**
- Duplicate price observations
- Conflicting data from different sources
- Extreme outliers

**Diagnostic:**
```bash
# Check for duplicates
psql $DATABASE_URL -c "
SELECT market_id, commodity_id, date, COUNT(*)
FROM price_observations
GROUP BY market_id, commodity_id, date
HAVING COUNT(*) > 1;
"

# Identify outliers
python scripts/detect_outliers.py
```

**Solution:**
```bash
# Remove duplicates
psql $DATABASE_URL -c "
DELETE FROM price_observations 
WHERE id NOT IN (
  SELECT MIN(id) 
  FROM price_observations 
  GROUP BY market_id, commodity_id, date
);
"

# Apply outlier filtering
python scripts/filter_outliers.py --method=iqr --threshold=3

# Reconcile conflicting sources
python scripts/reconcile_data_sources.py
```

## Deployment Issues

### Issue: Kubernetes Pod Failures

**Symptoms:**
- Pods in CrashLoopBackOff state
- Image pull errors
- Resource limit exceeded

**Diagnostic:**
```bash
# Check pod status
kubectl get pods -n yemen-market-v2

# Describe failing pod
kubectl describe pod FAILING_POD -n yemen-market-v2

# Check logs
kubectl logs FAILING_POD -n yemen-market-v2 --previous

# Check events
kubectl get events -n yemen-market-v2 --sort-by='.lastTimestamp'
```

**Solution:**
```bash
# Fix image pull issues
kubectl create secret docker-registry regcred \
  --docker-server=your-registry \
  --docker-username=your-username \
  --docker-password=your-password

# Increase resource limits
kubectl patch deployment yemen-market-api -n yemen-market-v2 -p '
{
  "spec": {
    "template": {
      "spec": {
        "containers": [
          {
            "name": "api",
            "resources": {
              "limits": {
                "memory": "4Gi",
                "cpu": "2"
              }
            }
          }
        ]
      }
    }
  }
}'

# Force pod restart
kubectl delete pod FAILING_POD -n yemen-market-v2

# Scale down and up
kubectl scale deployment yemen-market-api --replicas=0 -n yemen-market-v2
kubectl scale deployment yemen-market-api --replicas=3 -n yemen-market-v2
```

### Issue: Docker Compose Failures

**Symptoms:**
- Services fail to start
- Network connectivity issues between containers
- Volume mount problems

**Diagnostic:**
```bash
# Check service status
docker-compose ps

# Check logs
docker-compose logs

# Check networks
docker network ls
docker network inspect yemen-market-v2_default

# Check volumes
docker volume ls
docker volume inspect yemen-market-v2_postgres_data
```

**Solution:**
```bash
# Recreate services
docker-compose down -v
docker-compose up -d

# Fix network issues
docker-compose down
docker network prune
docker-compose up -d

# Fix volume permissions
sudo chown -R $(id -u):$(id -g) ./data
docker-compose restart

# Update compose file
docker-compose config  # Validate syntax
```

## Development Issues

### Issue: Hot Reload Not Working

**Symptoms:**
- Changes not reflected automatically
- Need to restart server manually
- Import errors during reload

**Solution:**
```bash
# Use correct uvicorn command
uvicorn src.interfaces.api.rest.app:app --reload --reload-dir src

# Check file watching limits (Linux)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Exclude problematic directories
uvicorn src.interfaces.api.rest.app:app --reload --reload-exclude "*.pyc" --reload-exclude "__pycache__"

# Use development configuration
export API_ENV=development
export API_DEBUG=true
```

### Issue: Import Errors

**Symptoms:**
- ModuleNotFoundError
- Circular import errors
- Path issues

**Diagnostic:**
```bash
# Check Python path
python -c "import sys; print('\n'.join(sys.path))"

# Test imports
python -c "
try:
    from src.interfaces.api.rest.app import create_app
    print('Import successful')
except ImportError as e:
    print('Import failed:', e)
"

# Check package structure
find src -name "__init__.py"
```

**Solution:**
```bash
# Fix Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Install in development mode
pip install -e .

# Fix circular imports
python -c "
import ast
import sys
def find_imports(filename):
    with open(filename) as f:
        tree = ast.parse(f.read())
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                print(f'{filename}: import {alias.name}')
        elif isinstance(node, ast.ImportFrom):
            print(f'{filename}: from {node.module} import ...')

# Analyze import patterns
import glob
for f in glob.glob('src/**/*.py', recursive=True):
    find_imports(f)
"
```

### Issue: Test Failures

**Symptoms:**
- Tests fail in CI but pass locally
- Database conflicts in tests
- Async test issues

**Diagnostic:**
```bash
# Run tests with verbose output
pytest tests/ -v -s

# Run specific failing test
pytest tests/unit/test_failing.py::test_function -v

# Check test database
psql $TEST_DATABASE_URL -c "SELECT version();"

# Check async event loop
python -c "
import asyncio
print('Event loop policy:', asyncio.get_event_loop_policy())
"
```

**Solution:**
```bash
# Use test database
export DATABASE_URL="postgresql://postgres:password@localhost:5432/test_db"

# Fix async tests
# Add to conftest.py:
# pytest.fixture(scope="session")
# def event_loop():
#     loop = asyncio.new_event_loop()
#     yield loop
#     loop.close()

# Isolate test data
pytest tests/ --create-db --reuse-db

# Run tests in parallel
pytest tests/ -n 4
```

## Getting Help

### Log Analysis

```bash
# Collect all relevant logs
mkdir -p debug_logs
docker-compose logs > debug_logs/docker-compose.log
kubectl logs deployment/yemen-market-api -n yemen-market-v2 > debug_logs/k8s-api.log
journalctl -u postgresql > debug_logs/postgresql.log

# Create diagnostic bundle
tar -czf yemen-market-debug-$(date +%Y%m%d-%H%M%S).tar.gz debug_logs/
```

### Support Channels

1. **GitHub Issues**: [Repository Issues](https://github.com/worldbank/yemen-market-integration-v2/issues)
2. **Documentation**: [Full Documentation](https://docs.yemen-market-integration.org)
3. **Community Forum**: [Discussions](https://github.com/worldbank/yemen-market-integration-v2/discussions)
4. **Email Support**: <EMAIL>

### Reporting Issues

When reporting issues, include:

1. **Environment Details**:
   - Operating system and version
   - Python version
   - Docker/Kubernetes version
   - Hardware specifications

2. **Error Information**:
   - Complete error messages
   - Stack traces
   - Log files
   - Steps to reproduce

3. **Configuration**:
   - Environment variables (sanitized)
   - Configuration files
   - Deployment method

4. **Context**:
   - What you were trying to accomplish
   - When the issue started
   - Any recent changes

This troubleshooting guide covers the most common issues encountered with the Yemen Market Integration v2 system. For issues not covered here, please consult the documentation or reach out through the support channels.