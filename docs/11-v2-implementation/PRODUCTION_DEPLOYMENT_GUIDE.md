# Yemen Market Integration V2 - Production Deployment Guide

## Overview

This guide provides comprehensive instructions for executing the production deployment of Yemen Market Integration V2 with zero-downtime blue-green deployment, gradual traffic migration, and automated rollback capabilities.

## Table of Contents

1. [Pre-Deployment Checklist](#pre-deployment-checklist)
2. [Infrastructure Requirements](#infrastructure-requirements)
3. [Deployment Process](#deployment-process)
4. [Traffic Migration Strategy](#traffic-migration-strategy)
5. [Monitoring and Alerting](#monitoring-and-alerting)
6. [Rollback Procedures](#rollback-procedures)
7. [Post-Deployment Validation](#post-deployment-validation)
8. [Troubleshooting](#troubleshooting)
9. [Team Handover](#team-handover)

## Pre-Deployment Checklist

### ✅ Code and Testing
- [ ] All V2 components pass unit tests (>95% coverage)
- [ ] Integration tests pass
- [ ] Performance benchmarks meet SLA requirements
- [ ] Security scans completed with no critical vulnerabilities
- [ ] V1/V2 parallel validation confirms 35% conflict effect accuracy

### ✅ Infrastructure
- [ ] Kubernetes cluster is healthy and has sufficient capacity
- [ ] Database migration scripts tested and validated
- [ ] Backup systems are operational
- [ ] Monitoring stack is deployed and configured
- [ ] DNS records are prepared (with low TTL)
- [ ] SSL certificates are valid and configured

### ✅ Data and Configuration
- [ ] Production secrets are configured in sealed-secrets
- [ ] Configuration maps are up to date
- [ ] Data migration plan is approved and tested
- [ ] External service endpoints are verified

### ✅ Team Readiness
- [ ] Deployment team is available for the entire deployment window
- [ ] Rollback team is on standby
- [ ] Business stakeholders are notified
- [ ] Communication channels are active (Slack, PagerDuty)

## Infrastructure Requirements

### Kubernetes Cluster
- **Minimum Version**: 1.25+
- **Node Capacity**: 20 nodes (m5.xlarge or equivalent)
- **Storage**: 500GB+ available persistent storage
- **Network**: Load balancer with SSL termination

### Resource Allocation
```yaml
V2 Production Resources:
  API Pods: 5 replicas
    CPU: 2 cores per pod
    Memory: 2GB per pod
  Worker Pods: 10 replicas
    CPU: 1 core per pod
    Memory: 1GB per pod
  Database: PostgreSQL 15
    CPU: 4 cores
    Memory: 8GB
    Storage: 200GB SSD
  Redis: HA configuration
    CPU: 1 core
    Memory: 2GB
```

### External Dependencies
- **World Bank API**: Rate limits configured
- **ACLED API**: Authentication keys updated
- **HDX API**: Service availability confirmed
- **WFP Data Sources**: Access validated

## Deployment Process

### Phase 1: Infrastructure Setup (30 minutes)

1. **Initialize Monitoring**
   ```bash
   cd /path/to/yemen-market-integration/v2
   ./scripts/deployment-monitoring.sh
   ```

2. **Verify Cluster State**
   ```bash
   kubectl cluster-info
   kubectl get nodes
   kubectl get ns
   ```

3. **Check V1 Status**
   ```bash
   kubectl get pods -n yemen-market
   kubectl get svc -n yemen-market
   ```

### Phase 2: V2 Deployment (45 minutes)

1. **Set Environment Variables**
   ```bash
   export CLUSTER_NAME="yemen-market-prod"
   export V2_IMAGE_TAG="v2.1.0"
   export ENVIRONMENT="production"
   export SLACK_WEBHOOK="https://hooks.slack.com/..."
   ```

2. **Execute Production Deployment**
   ```bash
   ./scripts/production-deployment.sh
   ```

3. **Monitor Deployment Progress**
   - Watch Grafana deployment dashboard
   - Monitor Slack notifications
   - Check pod status: `kubectl get pods -n yemen-market-v2 -w`

### Phase 3: Traffic Migration (60 minutes)

The deployment script automatically handles traffic migration in these steps:

1. **10% Traffic to V2** (5 minutes monitoring)
2. **25% Traffic to V2** (5 minutes monitoring)
3. **50% Traffic to V2** (5 minutes monitoring)
4. **75% Traffic to V2** (5 minutes monitoring)
5. **90% Traffic to V2** (5 minutes monitoring)
6. **100% Traffic to V2** (Final cutover)

Each step includes:
- Automated health checks
- Performance monitoring
- Error rate validation
- Latency verification

### Phase 4: Finalization (15 minutes)

1. **V1 Scale Down**: Gradual reduction of V1 capacity
2. **DNS Update**: Point main domain to V2
3. **Cleanup**: Remove temporary resources
4. **Validation**: Run post-deployment tests

## Traffic Migration Strategy

### Monitoring Thresholds

| Metric | Threshold | Action |
|--------|-----------|--------|
| Error Rate | > 5% | Automatic rollback |
| Latency P95 | > 2.0s | Alert and manual review |
| Throughput | < 80% of V1 | Alert and investigate |
| Database Errors | > 0.1/sec | Immediate investigation |

### Migration Controls

```bash
# Manual traffic control (if needed)
kubectl patch ingress yemen-market-canary-ingress -n yemen-market-v2 -p \
  '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary-weight":"25"}}}'

# Check current traffic distribution
kubectl get ingress yemen-market-canary-ingress -n yemen-market-v2 \
  -o jsonpath='{.metadata.annotations.nginx\.ingress\.kubernetes\.io/canary-weight}'
```

### Rollback Triggers

- **Automatic Rollback**: Error rate > 5% for 2+ minutes
- **Manual Rollback**: Performance degradation or business critical issues
- **Emergency Rollback**: Any production-impacting issues

## Monitoring and Alerting

### Key Dashboards

1. **Real-time Deployment Dashboard**
   - URL: `https://grafana.yemen-market.prod.com/d/deployment-realtime`
   - Metrics: Traffic split, error rates, latency comparison

2. **System Overview Dashboard**
   - URL: `https://grafana.yemen-market.prod.com/d/system-overview`
   - Metrics: Resource usage, database performance, external services

3. **Data Pipeline Health Dashboard**
   - URL: `https://grafana.yemen-market.prod.com/d/data-pipeline`
   - Metrics: Data ingestion rates, processing latency, queue status

### Alert Channels

- **Slack Channels**:
  - `#alerts`: General alerts
  - `#critical-alerts`: Critical issues requiring immediate attention
  - `#deployments`: Deployment-specific notifications

- **PagerDuty**: Critical alerts that require immediate response

### Key Metrics to Monitor

```yaml
Critical Metrics:
  - HTTP Error Rate (< 1%)
  - HTTP Latency P95 (< 500ms)
  - Database Connection Errors (0)
  - Pod Restart Count (< 5/hour)
  - Memory Usage (< 80%)
  - CPU Usage (< 70%)

Business Metrics:
  - API Request Rate
  - Data Processing Throughput
  - Analysis Completion Time
  - External Service Availability
```

## Rollback Procedures

### Rollback Types

1. **Traffic Rollback** (30 seconds)
   ```bash
   ./scripts/rollback-automation.sh traffic
   ```

2. **Application Rollback** (2 minutes)
   ```bash
   ./scripts/rollback-automation.sh application
   ```

3. **Full Rollback** (5 minutes)
   ```bash
   ./scripts/rollback-automation.sh full
   ```

### Rollback Decision Matrix

| Situation | Rollback Type | Timeframe |
|-----------|---------------|-----------|
| High error rate | Traffic | Immediate |
| Performance degradation | Application | < 5 minutes |
| Data integrity issues | Full | < 10 minutes |
| External service failures | Traffic | Immediate |
| Security concerns | Full | Immediate |

### Post-Rollback Actions

1. **Immediate** (0-15 minutes):
   - Verify V1 is handling all traffic
   - Confirm system stability
   - Notify stakeholders

2. **Short-term** (15 minutes - 2 hours):
   - Conduct incident analysis
   - Identify root cause
   - Plan remediation

3. **Medium-term** (2-24 hours):
   - Fix identified issues
   - Update deployment procedures
   - Prepare for re-deployment

## Post-Deployment Validation

### Automated Validation Suite

The deployment automatically runs comprehensive validation:

```bash
# Validation includes:
# - API endpoint testing
# - Database connectivity
# - External service integration
# - Performance benchmarks
# - Data consistency checks
# - Security validation
```

### Manual Validation Checklist

- [ ] API endpoints respond correctly
- [ ] Authentication and authorization work
- [ ] Data queries return expected results
- [ ] Analysis pipeline processes data
- [ ] External integrations function
- [ ] Monitoring and alerting active
- [ ] Backup systems operational

### Performance Validation

```bash
# Load testing
kubectl run load-test --image=busybox --rm -it -- \
  wget -O- https://api.yemen-market.prod.com/health

# Database performance
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  python -c "import time; start=time.time(); # Run test query; print(f'Query time: {time.time()-start:.2f}s')"
```

## Troubleshooting

### Common Issues and Solutions

#### 1. High Error Rate During Migration

**Symptoms**: Error rate > 5% in V2
**Investigation**:
```bash
kubectl logs -n yemen-market-v2 deployment/yemen-market-api --tail=100
kubectl describe pods -n yemen-market-v2 -l app=yemen-market-api
```
**Solutions**:
- Check database connectivity
- Verify configuration
- Scale up resources if needed
- Rollback if persistent

#### 2. Database Migration Failure

**Symptoms**: Migration job fails
**Investigation**:
```bash
kubectl logs -n yemen-market-v2 job/v1-to-v2-migration-*
kubectl describe job -n yemen-market-v2 v1-to-v2-migration-*
```
**Solutions**:
- Check data compatibility
- Verify database permissions
- Re-run migration with fixes
- Restore from backup if needed

#### 3. External Service Integration Issues

**Symptoms**: External API calls failing
**Investigation**:
```bash
kubectl exec -n yemen-market-v2 deployment/yemen-market-api -- \
  curl -v https://api.worldbank.org/v2/country
```
**Solutions**:
- Verify API keys and credentials
- Check network policies
- Validate external service status
- Update configuration if needed

#### 4. Resource Constraints

**Symptoms**: Pods OOMKilled or CPU throttled
**Investigation**:
```bash
kubectl top pods -n yemen-market-v2
kubectl describe node
```
**Solutions**:
- Scale up node capacity
- Adjust resource limits
- Optimize application performance
- Consider horizontal scaling

### Emergency Contacts

| Role | Contact | Phone | Slack |
|------|---------|-------|-------|
| Deployment Lead | [Name] | [Phone] | @[username] |
| Platform Engineer | [Name] | [Phone] | @[username] |
| Database Admin | [Name] | [Phone] | @[username] |
| Security Engineer | [Name] | [Phone] | @[username] |

## Team Handover

### Knowledge Transfer Items

1. **Technical Documentation**
   - Architecture diagrams updated
   - API documentation current
   - Database schema documented
   - Monitoring runbooks available

2. **Operational Procedures**
   - Deployment process documented
   - Rollback procedures tested
   - Incident response playbooks
   - Performance tuning guides

3. **Access and Credentials**
   - Kubernetes cluster access
   - Monitoring system access
   - Production database access
   - External service credentials

### Ongoing Responsibilities

#### Development Team
- Code maintenance and bug fixes
- Feature development
- Testing and quality assurance
- Documentation updates

#### Platform Team
- Infrastructure maintenance
- Monitoring and alerting
- Capacity planning
- Security updates

#### Data Team
- Data pipeline monitoring
- Data quality validation
- External service management
- Backup and recovery

### Success Criteria

- [ ] V2 handling 100% production traffic
- [ ] All monitoring and alerting operational
- [ ] Performance meeting or exceeding SLA
- [ ] Team members trained on new system
- [ ] Documentation complete and accessible
- [ ] Rollback procedures validated

### Performance Targets

| Metric | Target | Current V1 | V2 Goal |
|--------|--------|------------|---------|
| API Response Time (P95) | < 500ms | 650ms | < 400ms |
| Error Rate | < 0.5% | 0.8% | < 0.3% |
| Throughput | > 1000 RPS | 800 RPS | > 1200 RPS |
| Availability | > 99.9% | 99.7% | > 99.95% |
| Data Processing | < 10min | 15min | < 8min |

### Next Steps

1. **Week 1**: Monitor performance and stability
2. **Week 2**: Optimize based on production data
3. **Month 1**: Complete V1 decommissioning
4. **Month 2**: Implement V2-specific enhancements
5. **Month 3**: Plan next iteration improvements

---

## Deployment Execution Checklist

### Pre-Deployment (T-1 hour)
- [ ] All prerequisites verified
- [ ] Team assembled and ready
- [ ] Monitoring systems active
- [ ] Communication channels open
- [ ] Rollback team on standby

### Deployment Start (T+0)
- [ ] Execute monitoring setup
- [ ] Begin V2 infrastructure deployment
- [ ] Monitor deployment progress
- [ ] Validate smoke tests

### Traffic Migration (T+30min)
- [ ] Start gradual traffic migration
- [ ] Monitor metrics at each step
- [ ] Validate performance thresholds
- [ ] Complete traffic cutover

### Post-Deployment (T+90min)
- [ ] Run validation suite
- [ ] Scale down V1
- [ ] Update DNS
- [ ] Generate deployment report
- [ ] Conduct team handover

### Success Confirmation (T+2hours)
- [ ] V2 stable under full load
- [ ] All systems operational
- [ ] Performance targets met
- [ ] Team trained and ready
- [ ] Documentation complete

---

*This guide should be reviewed and updated after each deployment to incorporate lessons learned and process improvements.*