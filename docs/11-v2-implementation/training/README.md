# Yemen Market Integration v2 Training Materials

## Overview

This comprehensive training program is designed to onboard new team members, researchers, and stakeholders to the Yemen Market Integration v2 system. The training is structured in progressive modules, from basic concepts to advanced econometric analysis.

## Table of Contents

- [Learning Paths](#learning-paths)
- [Module 1: System Overview](#module-1-system-overview)
- [Module 2: Getting Started](#module-2-getting-started)
- [Module 3: Data Management](#module-3-data-management)
- [Module 4: Three-Tier Analysis](#module-4-three-tier-analysis)
- [Module 5: API Integration](#module-5-api-integration)
- [Module 6: Advanced Operations](#module-6-advanced-operations)
- [Hands-on Exercises](#hands-on-exercises)
- [Video Tutorials](#video-tutorials)
- [Best Practices](#best-practices)
- [Certification](#certification)

## Learning Paths

### 🎓 Researcher Path (4-6 hours)
**For economists and researchers conducting market integration analysis**

1. **Module 1**: System Overview (30 min)
2. **Module 2**: Getting Started (45 min)
3. **Module 4**: Three-Tier Analysis (90 min)
4. **Exercise 1**: Basic Analysis Workflow (60 min)
5. **Exercise 3**: Interpreting Results (45 min)
6. **Best Practices**: Research Guidelines (30 min)

### 👨‍💻 Developer Path (6-8 hours)
**For software engineers and technical contributors**

1. **Module 1**: System Overview (30 min)
2. **Module 2**: Getting Started (45 min)
3. **Module 3**: Data Management (60 min)
4. **Module 5**: API Integration (90 min)
5. **Module 6**: Advanced Operations (60 min)
6. **Exercise 2**: API Development (90 min)
7. **Exercise 4**: System Administration (60 min)
8. **Best Practices**: Development Guidelines (45 min)

### 🎯 Operations Path (3-4 hours)
**For system administrators and DevOps engineers**

1. **Module 1**: System Overview (30 min)
2. **Module 2**: Getting Started (30 min)
3. **Module 6**: Advanced Operations (90 min)
4. **Exercise 4**: System Administration (60 min)
5. **Best Practices**: Operational Guidelines (30 min)

### 🔍 Analyst Path (2-3 hours)
**For data analysts and policy researchers**

1. **Module 1**: System Overview (30 min)
2. **Module 2**: Getting Started (30 min)
3. **Module 4**: Three-Tier Analysis (60 min)
4. **Exercise 1**: Basic Analysis Workflow (45 min)
5. **Best Practices**: Research Guidelines (15 min)

## Module 1: System Overview

### Learning Objectives
After completing this module, you will:
- Understand the purpose and scope of the Yemen Market Integration project
- Recognize the three-tier analysis framework
- Identify key system components and their relationships
- Appreciate the economic theory behind market integration analysis

### 1.1 Project Background

#### The Yemen Context
Yemen has experienced ongoing conflict since 2014, creating unique challenges for market analysis:

- **Geographic Fragmentation**: Multiple control zones with varying governance
- **Economic Disruption**: Currency devaluation, supply chain interruptions
- **Data Challenges**: Inconsistent reporting, security constraints
- **Policy Implications**: Humanitarian aid targeting, market interventions

#### Research Questions
The system addresses fundamental questions about market behavior in conflict:

1. **How does conflict affect market integration?**
   - Do markets become more isolated during violence?
   - What mechanisms drive price transmission across regions?

2. **What role do exchange rates play?**
   - How do multiple currency regimes affect price relationships?
   - Why might conflict areas show lower prices in local currency?

3. **How effective are humanitarian interventions?**
   - Do cash transfers affect local price dynamics?
   - What is the optimal targeting for aid distribution?

### 1.2 Three-Tier Analysis Framework

#### Tier 1: Pooled Panel Analysis
**Purpose**: Establish baseline integration patterns across all markets and commodities

**Methods**:
- Fixed effects regression
- Time and market controls
- Clustered standard errors
- Diagnostic testing

**Output**: Overall integration coefficients and statistical significance

**Example Interpretation**:
```
If Tier 1 shows a coefficient of 0.65 for price transmission, 
this suggests that a 1% price increase in reference markets 
leads to a 0.65% increase in local markets on average.
```

#### Tier 2: Commodity-Specific VECM Analysis
**Purpose**: Examine long-run relationships and short-run dynamics for each commodity

**Methods**:
- Cointegration testing (Johansen, Engle-Granger)
- Vector Error Correction Models (VECM)
- Impulse response functions
- Variance decomposition

**Output**: Commodity-specific integration patterns and adjustment speeds

**Example Interpretation**:
```
Wheat markets show strong cointegration (rank=2) with 
adjustment speed of 0.23, meaning deviations from 
long-run equilibrium are corrected within 4-5 periods.
```

#### Tier 3: Factor Analysis Validation
**Purpose**: Validate integration patterns using factor models and robustness checks

**Methods**:
- Principal Component Analysis (PCA)
- Dynamic Factor Models
- Cross-validation techniques
- Sensitivity analysis

**Output**: Factor loadings, explained variance, validation metrics

**Example Interpretation**:
```
First factor explains 67% of price variation, with 
high loadings on northern markets (0.8+), suggesting 
regional integration clusters.
```

### 1.3 System Architecture

#### High-Level Components

```
┌─────────────────────────────────────────────────┐
│                  API Layer                      │
│         (REST API, GraphQL, CLI, SDK)          │
├─────────────────────────────────────────────────┤
│              Application Layer                  │
│          (Use Cases, Orchestration)            │
├─────────────────────────────────────────────────┤
│                Domain Layer                     │
│        (Entities, Value Objects, Rules)        │
├─────────────────────────────────────────────────┤
│            Infrastructure Layer                 │
│    (Database, External APIs, Messaging)        │
└─────────────────────────────────────────────────┘
```

#### Key Services
- **Analysis Orchestrator**: Manages three-tier workflow execution
- **Data Ingestion Service**: Collects and processes external data
- **Panel Builder Service**: Creates balanced panel datasets
- **Model Estimator Service**: Runs econometric models
- **Results Storage**: Manages analysis outputs and metadata

### 1.4 Economic Theory Foundation

#### Market Integration Theory
Market integration refers to the extent to which spatially separated markets are connected through arbitrage. Perfect integration implies:

1. **Law of One Price**: Identical goods sell for the same price across markets (adjusting for transport costs)
2. **Price Transmission**: Price changes in one market are transmitted to connected markets
3. **Arbitrage**: Price differences create profit opportunities that traders exploit

#### Conflict Effects on Markets
Conflict can affect market integration through multiple channels:

**Direct Effects**:
- Physical destruction of infrastructure
- Increased transportation costs and risks
- Reduced trader activity

**Indirect Effects**:
- Currency regime changes
- Institutional breakdown
- Population displacement
- Aid distribution effects

#### Measurement Challenges
Traditional integration measures may be misleading in conflict settings:

- **Currency Effects**: Multiple exchange rates distort price comparisons
- **Selection Bias**: Only resilient markets continue reporting
- **Quality Changes**: Commodity quality may deteriorate
- **Seasonal Disruption**: Conflict may disrupt normal seasonal patterns

### Knowledge Check
1. What are the three tiers of analysis and their primary purposes?
2. How might conflict affect the interpretation of market integration measures?
3. What role do exchange rates play in price transmission analysis?
4. Why is factor analysis used as a validation method?

---

## Module 2: Getting Started

### Learning Objectives
After completing this module, you will:
- Set up a local development environment
- Authenticate with the API
- Run your first market integration analysis
- Navigate the web interface and API documentation

### 2.1 Environment Setup

#### Option 1: Docker Compose (Recommended for beginners)

```bash
# 1. Clone the repository
git clone https://github.com/worldbank/yemen-market-integration-v2.git
cd yemen-market-integration-v2/v2

# 2. Start all services
docker-compose up -d

# 3. Verify services are running
docker-compose ps

# 4. Check API health
curl http://localhost:8000/health

# 5. Access web interface
open http://localhost:8000/docs
```

#### Option 2: Native Python Setup

```bash
# 1. Create Python virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 2. Install dependencies
pip install -r requirements.txt
pip install -e .

# 3. Set environment variables
export DATABASE_URL="postgresql://postgres:password@localhost:5432/yemen_market_v2"
export REDIS_URL="redis://localhost:6379"
export JWT_SECRET_KEY="development-secret-key"

# 4. Start database (Docker)
docker run -d --name postgres \
  -e POSTGRES_DB=yemen_market_v2 \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:14

# 5. Start API server
uvicorn src.interfaces.api.rest.app:app --reload
```

### 2.2 Authentication Setup

#### Getting API Keys

1. **Register Account**:
   ```bash
   curl -X POST http://localhost:8000/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "username": "your_username",
       "email": "<EMAIL>",
       "password": "secure_password123",
       "full_name": "Your Full Name",
       "organization": "Your Organization"
     }'
   ```

2. **Login and Get Token**:
   ```bash
   curl -X POST http://localhost:8000/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{
       "username": "your_username",
       "password": "secure_password123"
     }'
   ```

3. **Generate API Key**:
   ```bash
   curl -X POST http://localhost:8000/api/v1/auth/api-keys \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Development Key",
       "description": "For training exercises",
       "permissions": ["analysis:create", "analysis:read", "data:read"]
     }'
   ```

#### Using API Keys

```bash
# Set environment variable
export API_KEY="your_api_key_here"

# Test authentication
curl -H "Authorization: Bearer $API_KEY" \
     http://localhost:8000/api/v1/markets
```

### 2.3 First Analysis

#### Step 1: Explore Available Data

```bash
# List available markets
curl -H "Authorization: Bearer $API_KEY" \
     "http://localhost:8000/api/v1/markets?limit=10"

# List available commodities
curl -H "Authorization: Bearer $API_KEY" \
     "http://localhost:8000/api/v1/commodities"

# Check price data availability
curl -H "Authorization: Bearer $API_KEY" \
     "http://localhost:8000/api/v1/prices?start_date=2023-01-01&end_date=2023-01-31&limit=5"
```

#### Step 2: Create Analysis

```bash
# Create a simple three-tier analysis
curl -X POST http://localhost:8000/api/v1/analysis/three-tier \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "start_date": "2023-01-01",
    "end_date": "2023-03-31",
    "markets": ["Sana'\''a", "Aden", "Taiz"],
    "commodities": ["wheat", "rice"],
    "confidence_level": 0.95,
    "include_diagnostics": true
  }'
```

#### Step 3: Monitor Progress

```bash
# Get analysis status (replace ANALYSIS_ID with actual ID)
ANALYSIS_ID="analysis_abc123def456"
curl -H "Authorization: Bearer $API_KEY" \
     "http://localhost:8000/api/v1/analysis/$ANALYSIS_ID"

# Stream real-time updates (requires SSE-capable client)
curl -H "Authorization: Bearer $API_KEY" \
     -H "Accept: text/event-stream" \
     "http://localhost:8000/api/v1/analysis/$ANALYSIS_ID/status"
```

#### Step 4: Retrieve Results

```bash
# Wait for analysis to complete, then get results
curl -H "Authorization: Bearer $API_KEY" \
     "http://localhost:8000/api/v1/analysis/$ANALYSIS_ID/results"
```

### 2.4 Web Interface Tutorial

#### Interactive API Documentation

1. **Open Swagger UI**: Navigate to `http://localhost:8000/docs`
2. **Authenticate**: Click "Authorize" and enter your API key
3. **Explore Endpoints**: Expand endpoint groups to see available operations
4. **Try It Out**: Use the "Try it out" button to test endpoints interactively

#### Creating Analysis via Web UI

```html
<!-- This would be part of a web dashboard -->
<script>
async function createAnalysis() {
  const response = await fetch('/api/v1/analysis/three-tier', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + localStorage.getItem('api_key'),
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      start_date: document.getElementById('start-date').value,
      end_date: document.getElementById('end-date').value,
      markets: ['Sana\'a', 'Aden'],
      commodities: ['wheat', 'rice'],
      confidence_level: 0.95
    })
  });
  
  const result = await response.json();
  console.log('Analysis created:', result.id);
  
  // Start monitoring progress
  monitorAnalysis(result.id);
}
</script>
```

### 2.5 Common First-Time Issues

#### Database Connection Errors
```bash
# Check if PostgreSQL is running
docker ps | grep postgres

# Check database connection
docker exec -it postgres psql -U postgres -d yemen_market_v2 -c "SELECT 1;"

# Reset database if needed
docker-compose down -v
docker-compose up -d
```

#### Authentication Issues
```bash
# Verify API key format
echo $API_KEY | cut -c1-10  # Should show key prefix

# Check token expiration
curl -H "Authorization: Bearer $API_KEY" \
     http://localhost:8000/api/v1/auth/verify

# Generate new key if needed
curl -X POST http://localhost:8000/api/v1/auth/api-keys \
     -H "Authorization: Bearer $JWT_TOKEN" \
     -d '{"name": "New Key"}'
```

#### Performance Issues
```bash
# Check system resources
docker stats

# Monitor API logs
docker-compose logs -f api

# Check analysis queue
curl -H "Authorization: Bearer $API_KEY" \
     http://localhost:8000/api/v1/analysis/queue/status
```

### Hands-on Exercise 2.1

**Objective**: Complete your first end-to-end analysis workflow

**Steps**:
1. Set up local environment using Docker Compose
2. Create user account and generate API key
3. Explore available markets and commodities
4. Create analysis for 2 markets, 2 commodities, 3-month period
5. Monitor progress using both polling and SSE
6. Download and examine results

**Expected Time**: 45 minutes

**Deliverable**: Screenshot of completed analysis results and interpretation of integration scores

---

## Module 3: Data Management

### Learning Objectives
After completing this module, you will:
- Understand data sources and collection processes
- Manage data quality and validation
- Create and maintain balanced panels
- Handle missing data and outliers
- Configure data ingestion pipelines

### 3.1 Data Sources Overview

#### Primary Data Sources

**World Food Programme (WFP)**
- **Content**: Retail food prices from local markets
- **Coverage**: 200+ markets, 50+ commodities
- **Frequency**: Weekly to monthly
- **API**: REST API with authentication
- **Quality**: High reliability, standardized collection

**Armed Conflict Location & Event Data (ACLED)**
- **Content**: Conflict events, fatalities, locations
- **Coverage**: Daily event records since 2015
- **Frequency**: Real-time updates
- **API**: REST API with rate limiting
- **Quality**: Georeferenced, conflict type classification

**Humanitarian Data Exchange (HDX)**
- **Content**: Administrative boundaries, population data
- **Coverage**: National and subnational statistics
- **Frequency**: Irregular updates
- **API**: CKAN-based API
- **Quality**: UN-validated datasets

**Assessment Capacities Project (ACAPS)**
- **Content**: Crisis analysis, access constraints
- **Coverage**: Regional situation reports
- **Frequency**: Weekly to monthly
- **API**: Custom JSON API
- **Quality**: Expert analysis, qualitative indicators

#### Data Processing Pipeline

```
External APIs → Raw Data Storage → Validation → 
Standardization → Spatial Joining → Panel Construction → 
Analysis-Ready Datasets
```

### 3.2 Data Ingestion Service

#### Configuration

**Data source configuration** (`config/data_sources.yaml`):
```yaml
wfp:
  base_url: "https://api.wfp.org/vam-data-bridges/5.0.0"
  api_key: "${WFP_API_KEY}"
  timeout: 30
  retry_attempts: 3
  rate_limit: 100  # requests per minute
  
acled:
  base_url: "https://api.acleddata.com/acled/read"
  api_key: "${ACLED_API_KEY}"
  timeout: 60
  retry_attempts: 5
  rate_limit: 60
  
hdx:
  base_url: "https://data.humdata.org/api/3"
  timeout: 120
  retry_attempts: 3
  rate_limit: 30
```

#### Automated Collection

```python
# Example: WFP data collection
from src.application.services import DataIngestionService

async def collect_wfp_data():
    ingestion_service = DataIngestionService()
    
    # Configure collection parameters
    config = {
        "source": "wfp",
        "country_code": "YE",
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
        "commodity_categories": ["cereals", "oil", "pulses"],
        "output_format": "standardized"
    }
    
    # Start collection job
    job_id = await ingestion_service.start_collection(config)
    
    # Monitor progress
    async for status in ingestion_service.monitor_collection(job_id):
        print(f"Progress: {status.progress}% - {status.message}")
        if status.completed:
            break
    
    # Get collected data summary
    summary = await ingestion_service.get_collection_summary(job_id)
    print(f"Collected {summary.total_records} records")
    
    return job_id
```

#### Manual Data Upload

```bash
# Upload CSV data via API
curl -X POST http://localhost:8000/api/v1/data/upload \
  -H "Authorization: Bearer $API_KEY" \
  -F "file=@market_prices.csv" \
  -F "source=manual" \
  -F "data_type=prices" \
  -F "validation_schema=wfp_price_schema"

# Upload with metadata
curl -X POST http://localhost:8000/api/v1/data/upload \
  -H "Authorization: Bearer $API_KEY" \
  -F "file=@conflict_events.csv" \
  -F "metadata={
    \"source\": \"acled\",
    \"collection_date\": \"2024-01-15\",
    \"description\": \"Yemen conflict events Q4 2023\",
    \"validation_rules\": [\"date_format\", \"geo_validation\"]
  }"
```

### 3.3 Data Quality Management

#### Validation Rules

**Price Data Validation**:
```python
class PriceDataValidator:
    def validate_record(self, record):
        errors = []
        
        # Required fields
        required_fields = ['market', 'commodity', 'date', 'price', 'currency']
        for field in required_fields:
            if not record.get(field):
                errors.append(f"Missing required field: {field}")
        
        # Date validation
        try:
            date = datetime.strptime(record['date'], '%Y-%m-%d')
            if date > datetime.now():
                errors.append("Future date not allowed")
        except ValueError:
            errors.append("Invalid date format")
        
        # Price validation
        try:
            price = float(record['price'])
            if price <= 0:
                errors.append("Price must be positive")
            if price > 1000000:  # Reasonable upper bound
                errors.append("Price exceeds reasonable limit")
        except (ValueError, TypeError):
            errors.append("Invalid price format")
        
        # Currency validation
        valid_currencies = ['YER', 'USD', 'SAR']
        if record.get('currency') not in valid_currencies:
            errors.append(f"Invalid currency: {record.get('currency')}")
        
        return errors
```

#### Outlier Detection

```python
class OutlierDetector:
    def detect_price_outliers(self, data, method='iqr'):
        """Detect outliers in price data"""
        
        if method == 'iqr':
            # Interquartile range method
            Q1 = data['price'].quantile(0.25)
            Q3 = data['price'].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers = data[(data['price'] < lower_bound) | 
                          (data['price'] > upper_bound)]
            
        elif method == 'zscore':
            # Z-score method
            mean = data['price'].mean()
            std = data['price'].std()
            threshold = 3
            outliers = data[abs((data['price'] - mean) / std) > threshold]
            
        elif method == 'isolation_forest':
            # Machine learning approach
            from sklearn.ensemble import IsolationForest
            clf = IsolationForest(contamination=0.1)
            outlier_labels = clf.fit_predict(data[['price']])
            outliers = data[outlier_labels == -1]
        
        return outliers
```

#### Data Quality Dashboard

```python
def generate_quality_report(data_source, start_date, end_date):
    """Generate comprehensive data quality report"""
    
    report = {
        "source": data_source,
        "period": f"{start_date} to {end_date}",
        "summary": {},
        "issues": [],
        "recommendations": []
    }
    
    # Completeness metrics
    total_expected = calculate_expected_records(data_source, start_date, end_date)
    total_actual = count_actual_records(data_source, start_date, end_date)
    completeness = total_actual / total_expected
    
    report["summary"]["completeness"] = {
        "percentage": round(completeness * 100, 2),
        "expected_records": total_expected,
        "actual_records": total_actual,
        "missing_records": total_expected - total_actual
    }
    
    # Timeliness metrics
    latest_update = get_latest_update(data_source)
    staleness_days = (datetime.now() - latest_update).days
    
    report["summary"]["timeliness"] = {
        "latest_update": latest_update.isoformat(),
        "staleness_days": staleness_days,
        "is_current": staleness_days <= 7
    }
    
    # Accuracy metrics
    outliers = detect_outliers(data_source, start_date, end_date)
    outlier_percentage = len(outliers) / total_actual * 100
    
    report["summary"]["accuracy"] = {
        "outlier_percentage": round(outlier_percentage, 2),
        "outlier_count": len(outliers),
        "quality_score": calculate_quality_score(completeness, staleness_days, outlier_percentage)
    }
    
    # Generate recommendations
    if completeness < 0.8:
        report["recommendations"].append("Investigate data collection gaps")
    if staleness_days > 14:
        report["recommendations"].append("Update data collection frequency")
    if outlier_percentage > 10:
        report["recommendations"].append("Review data validation rules")
    
    return report
```

### 3.4 Panel Construction

#### Balanced Panel Creation

A balanced panel ensures consistent market-commodity-time observations:

```python
class PanelBuilder:
    def create_balanced_panel(self, 
                            markets, 
                            commodities, 
                            start_date, 
                            end_date, 
                            frequency='monthly'):
        """Create balanced panel dataset"""
        
        # Generate complete date range
        date_range = pd.date_range(
            start=start_date, 
            end=end_date, 
            freq=frequency[0].upper()  # 'M' for monthly
        )
        
        # Create all possible combinations
        combinations = []
        for market in markets:
            for commodity in commodities:
                for date in date_range:
                    combinations.append({
                        'market': market,
                        'commodity': commodity,
                        'date': date
                    })
        
        # Create panel structure
        panel_structure = pd.DataFrame(combinations)
        
        # Merge with actual data
        actual_data = self.load_price_data(markets, commodities, start_date, end_date)
        balanced_panel = panel_structure.merge(
            actual_data, 
            on=['market', 'commodity', 'date'],
            how='left'
        )
        
        # Handle missing values
        balanced_panel = self.handle_missing_values(balanced_panel)
        
        # Add metadata
        balanced_panel['panel_id'] = self.generate_panel_id()
        balanced_panel['created_at'] = datetime.now()
        
        return balanced_panel
    
    def handle_missing_values(self, panel, method='interpolation'):
        """Handle missing price values in panel"""
        
        if method == 'interpolation':
            # Linear interpolation within market-commodity pairs
            panel['price'] = panel.groupby(['market', 'commodity'])['price'].transform(
                lambda x: x.interpolate(method='linear', limit=2)
            )
            
        elif method == 'forward_fill':
            # Forward fill within market-commodity pairs
            panel['price'] = panel.groupby(['market', 'commodity'])['price'].transform(
                lambda x: x.fillna(method='ffill', limit=1)
            )
            
        elif method == 'market_average':
            # Fill with market average for the commodity
            market_averages = panel.groupby(['market', 'date'])['price'].mean()
            panel['price'] = panel['price'].fillna(
                panel.set_index(['market', 'date']).index.map(market_averages)
            )
        
        # Mark imputed values
        panel['imputed'] = panel['price'].isna()
        
        return panel
```

#### Spatial Joining

```python
class SpatialJoiner:
    def join_conflict_data(self, price_panel, conflict_events, radius_km=50):
        """Join conflict events to price observations based on proximity"""
        
        import geopandas as gpd
        from shapely.geometry import Point
        
        # Create GeoDataFrames
        price_geo = self.create_geo_dataframe(price_panel)
        conflict_geo = self.create_geo_dataframe(conflict_events)
        
        # Spatial join within radius
        joined_data = []
        for _, price_row in price_geo.iterrows():
            # Find nearby conflicts
            nearby_conflicts = conflict_geo[
                conflict_geo.geometry.distance(price_row.geometry) <= 
                radius_km / 111.0  # Convert km to degrees (approximate)
            ]
            
            # Aggregate conflict metrics
            if len(nearby_conflicts) > 0:
                conflict_metrics = {
                    'conflict_events': len(nearby_conflicts),
                    'total_fatalities': nearby_conflicts['fatalities'].sum(),
                    'avg_distance_km': nearby_conflicts.geometry.distance(
                        price_row.geometry
                    ).mean() * 111.0,
                    'conflict_types': nearby_conflicts['event_type'].unique().tolist()
                }
            else:
                conflict_metrics = {
                    'conflict_events': 0,
                    'total_fatalities': 0,
                    'avg_distance_km': None,
                    'conflict_types': []
                }
            
            # Combine price and conflict data
            combined_row = {**price_row.to_dict(), **conflict_metrics}
            joined_data.append(combined_row)
        
        return pd.DataFrame(joined_data)
```

### 3.5 Data Pipeline Automation

#### Airflow DAG Configuration

```python
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta

default_args = {
    'owner': 'yemen-market-team',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=15)
}

dag = DAG(
    'yemen_market_data_pipeline',
    default_args=default_args,
    description='Daily data collection and processing pipeline',
    schedule_interval='0 6 * * *',  # Daily at 6 AM
    catchup=False,
    max_active_runs=1
)

# Task definitions
collect_wfp_data = PythonOperator(
    task_id='collect_wfp_data',
    python_callable=collect_wfp_data_task,
    dag=dag
)

collect_acled_data = PythonOperator(
    task_id='collect_acled_data',
    python_callable=collect_acled_data_task,
    dag=dag
)

validate_data = PythonOperator(
    task_id='validate_data',
    python_callable=validate_data_task,
    dag=dag
)

process_spatial_joins = PythonOperator(
    task_id='process_spatial_joins',
    python_callable=spatial_join_task,
    dag=dag
)

update_panels = PythonOperator(
    task_id='update_panels',
    python_callable=update_panels_task,
    dag=dag
)

# Task dependencies
[collect_wfp_data, collect_acled_data] >> validate_data >> process_spatial_joins >> update_panels
```

### Hands-on Exercise 3.1

**Objective**: Set up and manage a complete data pipeline

**Tasks**:
1. Configure data source connections
2. Run data collection for WFP and ACLED sources
3. Perform data quality validation
4. Create balanced panel for specific markets and commodities
5. Generate data quality report
6. Set up automated data collection schedule

**Expected Time**: 90 minutes

**Deliverable**: Working data pipeline with quality dashboard

---

## Module 4: Three-Tier Analysis

### Learning Objectives
After completing this module, you will:
- Execute complete three-tier econometric analysis
- Interpret results from each tier
- Understand diagnostic testing and model validation
- Troubleshoot common analysis issues
- Generate policy-relevant insights

### 4.1 Analysis Workflow Overview

#### Complete Analysis Pipeline

```
Data Preparation → Tier 1 (Pooled Panel) → Tier 2 (Commodity VECM) → 
Tier 3 (Factor Analysis) → Diagnostics → Results Integration → 
Report Generation
```

#### Prerequisites Check

Before starting analysis, verify:

```python
# Data availability check
def check_analysis_prerequisites(markets, commodities, start_date, end_date):
    """Verify data availability and quality for analysis"""
    
    checks = {
        'data_coverage': False,
        'minimum_observations': False,
        'time_series_length': False,
        'market_coverage': False,
        'quality_threshold': False
    }
    
    # Check data coverage
    coverage = calculate_data_coverage(markets, commodities, start_date, end_date)
    checks['data_coverage'] = coverage > 0.8
    
    # Check minimum observations per market-commodity
    min_obs = get_minimum_observations(markets, commodities, start_date, end_date)
    checks['minimum_observations'] = min_obs >= 20
    
    # Check time series length
    time_span = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days
    checks['time_series_length'] = time_span >= 90  # Minimum 3 months
    
    # Check market coverage
    active_markets = count_active_markets(markets, start_date, end_date)
    checks['market_coverage'] = active_markets >= len(markets) * 0.8
    
    # Check overall quality
    quality_score = calculate_overall_quality(markets, commodities, start_date, end_date)
    checks['quality_threshold'] = quality_score >= 0.7
    
    return checks
```

### 4.2 Tier 1: Pooled Panel Analysis

#### Model Specification

The Tier 1 model tests overall market integration using a pooled panel approach:

**Basic Model**:
```
P_{ijt} = α + β₁ P_{ref,jt} + β₂ X_{ijt} + γᵢ + δⱼ + τₜ + εᵢⱼₜ
```

Where:
- `P_{ijt}`: Price of commodity j in market i at time t
- `P_{ref,jt}`: Reference price of commodity j at time t
- `X_{ijt}`: Control variables (conflict, exchange rate, etc.)
- `γᵢ`: Market fixed effects
- `δⱼ`: Commodity fixed effects
- `τₜ`: Time fixed effects
- `εᵢⱼₜ`: Error term

#### Implementation

```python
class Tier1PooledPanelModel:
    def __init__(self, data, config=None):
        self.data = data
        self.config = config or self.get_default_config()
        self.results = None
    
    def run_analysis(self):
        """Execute complete Tier 1 analysis"""
        
        # 1. Data preparation
        prepared_data = self.prepare_data()
        
        # 2. Model estimation
        model_results = self.estimate_model(prepared_data)
        
        # 3. Diagnostic testing
        diagnostics = self.run_diagnostics(model_results)
        
        # 4. Results packaging
        self.results = self.package_results(model_results, diagnostics)
        
        return self.results
    
    def prepare_data(self):
        """Prepare data for panel regression"""
        
        # Create price differences for integration testing
        self.data['price_diff'] = self.data.groupby(['market', 'commodity'])['price'].diff()
        
        # Calculate reference prices (usually Aden or Sana'a)
        reference_market = self.config.get('reference_market', 'Aden')
        ref_prices = self.data[self.data['market'] == reference_market].copy()
        ref_prices = ref_prices[['commodity', 'date', 'price']].rename(
            columns={'price': 'ref_price'}
        )
        
        # Merge reference prices
        merged_data = self.data.merge(
            ref_prices, 
            on=['commodity', 'date'], 
            how='left'
        )
        
        # Create dummy variables for fixed effects
        merged_data = pd.get_dummies(
            merged_data, 
            columns=['market', 'commodity'], 
            prefix=['market', 'commodity']
        )
        
        # Add time dummies
        merged_data['year'] = merged_data['date'].dt.year
        merged_data['month'] = merged_data['date'].dt.month
        merged_data = pd.get_dummies(
            merged_data, 
            columns=['year', 'month'], 
            prefix=['year', 'month']
        )
        
        return merged_data
    
    def estimate_model(self, data):
        """Estimate fixed effects panel model"""
        
        import statsmodels.api as sm
        from linearmodels import PanelOLS
        
        # Set up panel data structure
        data_indexed = data.set_index(['market', 'commodity', 'date'])
        
        # Define variables
        dependent_var = 'price'
        independent_vars = ['ref_price', 'conflict_events', 'exchange_rate']
        
        # Add control variables if available
        if 'aid_per_capita' in data.columns:
            independent_vars.append('aid_per_capita')
        
        # Entity effects (market + commodity)
        entity_effects = True
        time_effects = True
        
        # Estimate model
        model = PanelOLS(
            dependent=data_indexed[dependent_var],
            exog=data_indexed[independent_vars],
            entity_effects=entity_effects,
            time_effects=time_effects
        )
        
        # Fit with clustered standard errors
        results = model.fit(
            cov_type='clustered',
            cluster_entity=True
        )
        
        return results
    
    def run_diagnostics(self, model_results):
        """Run comprehensive diagnostic tests"""
        
        diagnostics = {}
        
        # 1. Hausman test for fixed vs random effects
        try:
            hausman_stat, hausman_p = self.hausman_test(model_results)
            diagnostics['hausman_test'] = {
                'statistic': hausman_stat,
                'p_value': hausman_p,
                'interpretation': 'Fixed effects preferred' if hausman_p < 0.05 else 'Random effects acceptable'
            }
        except Exception as e:
            diagnostics['hausman_test'] = {'error': str(e)}
        
        # 2. Breusch-Pagan test for heteroskedasticity
        try:
            bp_stat, bp_p = self.breusch_pagan_test(model_results)
            diagnostics['breusch_pagan_test'] = {
                'statistic': bp_stat,
                'p_value': bp_p,
                'interpretation': 'Heteroskedasticity present' if bp_p < 0.05 else 'Homoskedasticity'
            }
        except Exception as e:
            diagnostics['breusch_pagan_test'] = {'error': str(e)}
        
        # 3. Serial correlation test
        try:
            ac_stat, ac_p = self.autocorrelation_test(model_results)
            diagnostics['autocorrelation_test'] = {
                'statistic': ac_stat,
                'p_value': ac_p,
                'interpretation': 'Serial correlation present' if ac_p < 0.05 else 'No serial correlation'
            }
        except Exception as e:
            diagnostics['autocorrelation_test'] = {'error': str(e)}
        
        # 4. Cross-sectional dependence test
        try:
            cd_stat, cd_p = self.cross_sectional_dependence_test(model_results)
            diagnostics['cross_sectional_dependence'] = {
                'statistic': cd_stat,
                'p_value': cd_p,
                'interpretation': 'Cross-sectional dependence present' if cd_p < 0.05 else 'Cross-sectional independence'
            }
        except Exception as e:
            diagnostics['cross_sectional_dependence'] = {'error': str(e)}
        
        return diagnostics
```

#### Results Interpretation

```python
def interpret_tier1_results(results):
    """Generate interpretation of Tier 1 results"""
    
    interpretation = {
        'integration_strength': None,
        'statistical_significance': None,
        'economic_significance': None,
        'policy_implications': []
    }
    
    # Extract key coefficient (price transmission)
    price_transmission_coef = results.params.get('ref_price', None)
    price_transmission_pvalue = results.pvalues.get('ref_price', None)
    
    if price_transmission_coef is not None:
        # Integration strength
        if price_transmission_coef > 0.8:
            interpretation['integration_strength'] = 'Strong integration'
        elif price_transmission_coef > 0.5:
            interpretation['integration_strength'] = 'Moderate integration'
        elif price_transmission_coef > 0.2:
            interpretation['integration_strength'] = 'Weak integration'
        else:
            interpretation['integration_strength'] = 'No integration'
        
        # Statistical significance
        if price_transmission_pvalue < 0.01:
            interpretation['statistical_significance'] = 'Highly significant (p < 0.01)'
        elif price_transmission_pvalue < 0.05:
            interpretation['statistical_significance'] = 'Significant (p < 0.05)'
        elif price_transmission_pvalue < 0.10:
            interpretation['statistical_significance'] = 'Marginally significant (p < 0.10)'
        else:
            interpretation['statistical_significance'] = 'Not significant'
        
        # Economic significance
        if price_transmission_coef > 0.5 and price_transmission_pvalue < 0.05:
            interpretation['economic_significance'] = 'Economically meaningful price transmission'
        else:
            interpretation['economic_significance'] = 'Limited economic significance'
    
    # Policy implications
    if price_transmission_coef and price_transmission_coef > 0.7:
        interpretation['policy_implications'].append(
            'Markets are well-integrated; regional price shocks will spread rapidly'
        )
        interpretation['policy_implications'].append(
            'Interventions in reference markets will have broad impact'
        )
    else:
        interpretation['policy_implications'].append(
            'Market integration is limited; targeted local interventions may be needed'
        )
        interpretation['policy_implications'].append(
            'Regional price policies may have uneven effects'
        )
    
    return interpretation
```

### 4.3 Tier 2: Commodity-Specific VECM Analysis

#### Vector Error Correction Model

Tier 2 examines long-run relationships and short-run dynamics for each commodity:

**VECM Specification**:
```
ΔP_{ijt} = α + βECT_{t-1} + Σγₖ ΔP_{ijt-k} + Σδₖ ΔX_{ijt-k} + εᵢⱼₜ
```

Where:
- `ECT_{t-1}`: Error correction term (lagged deviation from long-run equilibrium)
- `β`: Adjustment coefficient (speed of convergence)
- `γₖ`, `δₖ`: Short-run dynamic coefficients

#### Implementation

```python
class Tier2CommodityVECM:
    def __init__(self, data, commodity, config=None):
        self.data = data
        self.commodity = commodity
        self.config = config or self.get_default_config()
        self.results = {}
    
    def run_analysis(self):
        """Execute complete Tier 2 analysis for one commodity"""
        
        # 1. Prepare commodity-specific data
        commodity_data = self.prepare_commodity_data()
        
        # 2. Test for cointegration
        cointegration_results = self.test_cointegration(commodity_data)
        
        # 3. Estimate VECM if cointegrated
        if cointegration_results['rank'] > 0:
            vecm_results = self.estimate_vecm(commodity_data, cointegration_results)
            
            # 4. Generate impulse responses
            impulse_responses = self.calculate_impulse_responses(vecm_results)
            
            # 5. Variance decomposition
            variance_decomp = self.variance_decomposition(vecm_results)
            
        else:
            # Use VAR in first differences if no cointegration
            var_results = self.estimate_var(commodity_data)
            impulse_responses = self.calculate_impulse_responses(var_results)
            variance_decomp = self.variance_decomposition(var_results)
            vecm_results = None
        
        # Package results
        self.results = {
            'commodity': self.commodity,
            'cointegration': cointegration_results,
            'vecm_results': vecm_results,
            'impulse_responses': impulse_responses,
            'variance_decomposition': variance_decomp
        }
        
        return self.results
    
    def prepare_commodity_data(self):
        """Prepare data for specific commodity analysis"""
        
        # Filter for specific commodity
        commodity_data = self.data[
            self.data['commodity'] == self.commodity
        ].copy()
        
        # Pivot to wide format (markets as columns)
        price_matrix = commodity_data.pivot(
            index='date', 
            columns='market', 
            values='price'
        ).fillna(method='ffill').dropna()
        
        # Take logs for elasticity interpretation
        log_prices = np.log(price_matrix)
        
        return log_prices
    
    def test_cointegration(self, data):
        """Test for cointegration using Johansen test"""
        
        from statsmodels.tsa.vector_ar.vecm import coint_johansen
        
        # Johansen cointegration test
        johansen_result = coint_johansen(
            data.values, 
            det_order=1,  # Constant term
            k_ar_diff=4   # Number of lags
        )
        
        # Determine cointegration rank
        trace_stats = johansen_result.lr1
        critical_values = johansen_result.cvt
        
        # Count significant trace statistics
        rank = 0
        for i, (stat, crit_5pct) in enumerate(zip(trace_stats, critical_values[:, 1])):
            if stat > crit_5pct:
                rank = len(trace_stats) - i
                break
        
        return {
            'method': 'johansen',
            'rank': rank,
            'trace_statistics': trace_stats.tolist(),
            'critical_values': critical_values[:, 1].tolist(),
            'max_eigenvalue_stats': johansen_result.lr2.tolist(),
            'eigenvalues': johansen_result.eig.tolist()
        }
    
    def estimate_vecm(self, data, cointegration_results):
        """Estimate Vector Error Correction Model"""
        
        from statsmodels.tsa.vector_ar.vecm import VECM
        
        # Estimate VECM
        vecm_model = VECM(
            data,
            k_ar_diff=4,  # Number of lags
            coint_rank=cointegration_results['rank'],
            deterministic='ci'  # Constant in cointegrating relation
        )
        
        vecm_fitted = vecm_model.fit()
        
        # Extract results
        results = {
            'alpha': vecm_fitted.alpha.tolist(),  # Adjustment coefficients
            'beta': vecm_fitted.beta.tolist(),    # Cointegrating vectors
            'gamma': vecm_fitted.gamma.tolist(),  # Short-run coefficients
            'log_likelihood': float(vecm_fitted.llf),
            'aic': float(vecm_fitted.aic),
            'bic': float(vecm_fitted.bic),
            'residuals': vecm_fitted.resid.tolist()
        }
        
        return results
    
    def calculate_impulse_responses(self, model_results):
        """Calculate impulse response functions"""
        
        # This is a simplified implementation
        # In practice, you would use the actual VECM/VAR model
        
        periods = 20  # Response periods
        variables = list(self.data['market'].unique())
        
        # Placeholder for impulse responses
        # Real implementation would calculate orthogonalized responses
        impulse_responses = {}
        
        for shock_var in variables:
            impulse_responses[shock_var] = {}
            for response_var in variables:
                # Generate synthetic response (replace with actual calculation)
                if shock_var == response_var:
                    response = [1.0] + [0.8**i for i in range(1, periods)]
                else:
                    response = [0.0] + [0.3 * (0.6**i) for i in range(1, periods)]
                
                impulse_responses[shock_var][response_var] = response
        
        return impulse_responses
    
    def variance_decomposition(self, model_results):
        """Calculate forecast error variance decomposition"""
        
        periods = 20
        variables = list(self.data['market'].unique())
        
        # Placeholder implementation
        # Real implementation would calculate actual variance decomposition
        variance_decomp = {}
        
        for var in variables:
            variance_decomp[var] = {}
            for period in range(1, periods + 1):
                decomp = {}
                total = 0
                
                # Simple synthetic decomposition
                for shock_var in variables:
                    if shock_var == var:
                        contribution = max(0.8 - period * 0.03, 0.3)
                    else:
                        contribution = min(0.2 + period * 0.01, 0.3)
                    
                    decomp[shock_var] = contribution
                    total += contribution
                
                # Normalize to sum to 1
                for shock_var in variables:
                    decomp[shock_var] = decomp[shock_var] / total
                
                variance_decomp[var][f'period_{period}'] = decomp
        
        return variance_decomp
```

#### Results Interpretation

```python
def interpret_tier2_results(results):
    """Interpret VECM results for commodity"""
    
    interpretation = {
        'commodity': results['commodity'],
        'cointegration_strength': None,
        'adjustment_speed': None,
        'market_leadership': None,
        'policy_implications': []
    }
    
    # Cointegration interpretation
    coint_rank = results['cointegration']['rank']
    n_markets = len(results['impulse_responses'])
    
    if coint_rank == 0:
        interpretation['cointegration_strength'] = 'No cointegration - markets not integrated'
    elif coint_rank == 1:
        interpretation['cointegration_strength'] = 'One cointegrating relationship - common trend'
    elif coint_rank == n_markets - 1:
        interpretation['cointegration_strength'] = 'Full cointegration - strong integration'
    else:
        interpretation['cointegration_strength'] = f'{coint_rank} cointegrating relationships'
    
    # Adjustment speed (from alpha coefficients)
    if results['vecm_results']:
        alpha_coeffs = results['vecm_results']['alpha']
        avg_adjustment = np.mean([abs(alpha) for alpha in alpha_coeffs])
        
        if avg_adjustment > 0.3:
            interpretation['adjustment_speed'] = 'Fast adjustment to equilibrium'
        elif avg_adjustment > 0.1:
            interpretation['adjustment_speed'] = 'Moderate adjustment speed'
        else:
            interpretation['adjustment_speed'] = 'Slow adjustment to equilibrium'
    
    # Market leadership (from impulse responses)
    impulse_data = results['impulse_responses']
    if impulse_data:
        # Calculate influence scores
        influence_scores = {}
        for shock_market in impulse_data:
            total_influence = 0
            for response_market in impulse_data[shock_market]:
                if shock_market != response_market:
                    # Sum of absolute responses over first 10 periods
                    response_sum = sum(abs(r) for r in impulse_data[shock_market][response_market][:10])
                    total_influence += response_sum
            influence_scores[shock_market] = total_influence
        
        # Find most influential market
        if influence_scores:
            leader_market = max(influence_scores, key=influence_scores.get)
            interpretation['market_leadership'] = f'{leader_market} shows strongest price leadership'
    
    # Policy implications
    if coint_rank > 0:
        interpretation['policy_implications'].append(
            f'{results["commodity"]} markets are cointegrated - long-run price relationships exist'
        )
        interpretation['policy_implications'].append(
            'Price interventions will have persistent effects across markets'
        )
    else:
        interpretation['policy_implications'].append(
            f'{results["commodity"]} markets operate independently'
        )
        interpretation['policy_implications'].append(
            'Local interventions may not affect other markets'
        )
    
    return interpretation
```

### 4.4 Tier 3: Factor Analysis Validation

#### Principal Component Analysis

Tier 3 validates integration patterns using factor models:

```python
class Tier3FactorAnalysis:
    def __init__(self, data, config=None):
        self.data = data
        self.config = config or self.get_default_config()
        self.results = {}
    
    def run_analysis(self):
        """Execute complete Tier 3 factor analysis"""
        
        # 1. Prepare data matrix
        price_matrix = self.prepare_price_matrix()
        
        # 2. Principal Component Analysis
        pca_results = self.run_pca(price_matrix)
        
        # 3. Factor model estimation
        factor_results = self.estimate_factor_model(price_matrix, pca_results)
        
        # 4. Validation tests
        validation_results = self.run_validation_tests(price_matrix, factor_results)
        
        # 5. Interpretation
        interpretation = self.interpret_factors(pca_results, factor_results)
        
        self.results = {
            'pca_results': pca_results,
            'factor_results': factor_results,
            'validation': validation_results,
            'interpretation': interpretation
        }
        
        return self.results
    
    def prepare_price_matrix(self):
        """Prepare price data matrix for factor analysis"""
        
        # Create market-commodity combinations as columns
        price_data = []
        columns = []
        
        # Pivot data to wide format
        for commodity in self.data['commodity'].unique():
            commodity_data = self.data[self.data['commodity'] == commodity]
            commodity_pivot = commodity_data.pivot(
                index='date',
                columns='market',
                values='price'
            )
            
            # Add to overall matrix
            for market in commodity_pivot.columns:
                col_name = f"{commodity}_{market}"
                if col_name not in columns:
                    columns.append(col_name)
                    if len(price_data) == 0:
                        price_data = commodity_pivot[market].values.reshape(-1, 1)
                    else:
                        price_data = np.column_stack([
                            price_data, 
                            commodity_pivot[market].values
                        ])
        
        # Convert to DataFrame
        price_matrix = pd.DataFrame(
            price_data, 
            columns=columns,
            index=commodity_data.index.unique()
        )
        
        # Handle missing values
        price_matrix = price_matrix.fillna(method='ffill').dropna()
        
        # Convert to log returns for stationarity
        log_returns = np.log(price_matrix / price_matrix.shift(1)).dropna()
        
        return log_returns
    
    def run_pca(self, data):
        """Run Principal Component Analysis"""
        
        from sklearn.decomposition import PCA
        from sklearn.preprocessing import StandardScaler
        
        # Standardize data
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(data)
        
        # Fit PCA
        pca = PCA()
        pca_fitted = pca.fit(scaled_data)
        
        # Transform data
        principal_components = pca_fitted.transform(scaled_data)
        
        # Calculate explained variance ratios
        explained_variance = pca_fitted.explained_variance_ratio_
        cumulative_variance = np.cumsum(explained_variance)
        
        # Determine optimal number of factors (Kaiser criterion)
        eigenvalues = pca_fitted.explained_variance_
        n_factors_kaiser = sum(eigenvalues > 1)
        
        # Scree plot analysis (simplified)
        # Find "elbow" in eigenvalues
        eigenvalue_diffs = np.diff(eigenvalues)
        n_factors_scree = np.argmax(eigenvalue_diffs) + 1
        
        results = {
            'explained_variance_ratio': explained_variance.tolist(),
            'cumulative_variance': cumulative_variance.tolist(),
            'eigenvalues': eigenvalues.tolist(),
            'n_factors_kaiser': n_factors_kaiser,
            'n_factors_scree': n_factors_scree,
            'principal_components': principal_components.tolist(),
            'loadings': pca_fitted.components_.tolist(),
            'variable_names': data.columns.tolist()
        }
        
        return results
    
    def estimate_factor_model(self, data, pca_results):
        """Estimate dynamic factor model"""
        
        # Use optimal number of factors
        n_factors = min(pca_results['n_factors_kaiser'], 5)  # Limit to 5 factors
        
        # Extract first n_factors principal components
        factors = np.array(pca_results['principal_components'])[:, :n_factors]
        loadings = np.array(pca_results['loadings'])[:n_factors, :]
        
        # Calculate factor model fit
        reconstructed = factors @ loadings
        residuals = data.values - reconstructed
        
        # Calculate R-squared for each variable
        r_squared = {}
        for i, var_name in enumerate(data.columns):
            actual = data.iloc[:, i].values
            predicted = reconstructed[:, i]
            
            ss_res = np.sum((actual - predicted) ** 2)
            ss_tot = np.sum((actual - np.mean(actual)) ** 2)
            r_sq = 1 - (ss_res / ss_tot)
            r_squared[var_name] = r_sq
        
        # Overall model fit
        overall_r_squared = np.mean(list(r_squared.values()))
        
        results = {
            'n_factors': n_factors,
            'factors': factors.tolist(),
            'loadings': loadings.tolist(),
            'r_squared_by_variable': r_squared,
            'overall_r_squared': overall_r_squared,
            'residuals': residuals.tolist()
        }
        
        return results
    
    def run_validation_tests(self, data, factor_results):
        """Run validation tests for factor model"""
        
        validation = {}
        
        # 1. Kaiser-Meyer-Olkin (KMO) test
        kmo_score = self.calculate_kmo(data)
        validation['kmo_test'] = {
            'score': kmo_score,
            'interpretation': self.interpret_kmo(kmo_score)
        }
        
        # 2. Bartlett's test of sphericity
        bartlett_stat, bartlett_p = self.bartlett_test(data)
        validation['bartlett_test'] = {
            'statistic': bartlett_stat,
            'p_value': bartlett_p,
            'interpretation': 'Suitable for factor analysis' if bartlett_p < 0.05 else 'Not suitable'
        }
        
        # 3. Factor stability test (split-sample)
        stability_score = self.factor_stability_test(data, factor_results['n_factors'])
        validation['stability_test'] = {
            'score': stability_score,
            'interpretation': 'Stable factors' if stability_score > 0.8 else 'Unstable factors'
        }
        
        return validation
    
    def calculate_kmo(self, data):
        """Calculate Kaiser-Meyer-Olkin measure of sampling adequacy"""
        
        # Simplified KMO calculation
        corr_matrix = data.corr()
        inv_corr_matrix = np.linalg.pinv(corr_matrix)
        
        # Calculate anti-image correlation matrix
        anti_image = -inv_corr_matrix / np.sqrt(
            np.outer(np.diag(inv_corr_matrix), np.diag(inv_corr_matrix))
        )
        np.fill_diagonal(anti_image, 1)
        
        # KMO measure
        squared_corr = corr_matrix ** 2
        squared_anti_image = anti_image ** 2
        
        kmo = (np.sum(squared_corr) - np.sum(np.diag(squared_corr))) / \
              (np.sum(squared_corr) - np.sum(np.diag(squared_corr)) + 
               np.sum(squared_anti_image) - np.sum(np.diag(squared_anti_image)))
        
        return float(kmo)
    
    def interpret_kmo(self, kmo_score):
        """Interpret KMO score"""
        if kmo_score >= 0.9:
            return "Excellent suitability for factor analysis"
        elif kmo_score >= 0.8:
            return "Good suitability for factor analysis"
        elif kmo_score >= 0.7:
            return "Adequate suitability for factor analysis"
        elif kmo_score >= 0.6:
            return "Mediocre suitability for factor analysis"
        else:
            return "Poor suitability for factor analysis"
```

### 4.5 Complete Analysis Execution

#### Analysis Runner

```python
class ThreeTierAnalysisRunner:
    def __init__(self, config):
        self.config = config
        self.results = {}
    
    async def run_complete_analysis(self, start_date, end_date, markets, commodities):
        """Execute complete three-tier analysis"""
        
        # 1. Data preparation
        print("Preparing data...")
        data = await self.prepare_analysis_data(start_date, end_date, markets, commodities)
        
        # 2. Prerequisites check
        print("Checking prerequisites...")
        prereq_check = check_analysis_prerequisites(markets, commodities, start_date, end_date)
        if not all(prereq_check.values()):
            raise ValueError(f"Prerequisites not met: {prereq_check}")
        
        # 3. Tier 1 Analysis
        print("Running Tier 1 analysis...")
        tier1_model = Tier1PooledPanelModel(data, self.config.get('tier1', {}))
        tier1_results = tier1_model.run_analysis()
        self.results['tier1'] = tier1_results
        
        # 4. Tier 2 Analysis (for each commodity)
        print("Running Tier 2 analysis...")
        tier2_results = {}
        for commodity in commodities:
            print(f"  Processing {commodity}...")
            tier2_model = Tier2CommodityVECM(data, commodity, self.config.get('tier2', {}))
            commodity_results = tier2_model.run_analysis()
            tier2_results[commodity] = commodity_results
        self.results['tier2'] = tier2_results
        
        # 5. Tier 3 Analysis
        print("Running Tier 3 analysis...")
        tier3_model = Tier3FactorAnalysis(data, self.config.get('tier3', {}))
        tier3_results = tier3_model.run_analysis()
        self.results['tier3'] = tier3_results
        
        # 6. Cross-tier validation
        print("Running cross-tier validation...")
        validation_results = self.run_cross_tier_validation()
        self.results['validation'] = validation_results
        
        # 7. Generate summary
        print("Generating analysis summary...")
        summary = self.generate_analysis_summary()
        self.results['summary'] = summary
        
        return self.results
    
    def run_cross_tier_validation(self):
        """Validate consistency across tiers"""
        
        validation = {
            'tier1_tier2_consistency': None,
            'tier2_tier3_consistency': None,
            'overall_consistency_score': None
        }
        
        # Check Tier 1 vs Tier 2 consistency
        tier1_integration = self.results['tier1']['coefficients']['ref_price']
        tier2_integrations = [
            results['cointegration']['rank'] > 0 
            for results in self.results['tier2'].values()
        ]
        tier2_avg_integration = sum(tier2_integrations) / len(tier2_integrations)
        
        # Consistency score based on correlation
        if tier1_integration > 0.7 and tier2_avg_integration > 0.7:
            validation['tier1_tier2_consistency'] = 'High consistency'
        elif tier1_integration > 0.5 and tier2_avg_integration > 0.5:
            validation['tier1_tier2_consistency'] = 'Moderate consistency'
        else:
            validation['tier1_tier2_consistency'] = 'Low consistency'
        
        # Check Tier 2 vs Tier 3 consistency
        tier3_first_factor_variance = self.results['tier3']['pca_results']['explained_variance_ratio'][0]
        
        if tier2_avg_integration > 0.7 and tier3_first_factor_variance > 0.6:
            validation['tier2_tier3_consistency'] = 'High consistency'
        elif tier2_avg_integration > 0.5 and tier3_first_factor_variance > 0.4:
            validation['tier2_tier3_consistency'] = 'Moderate consistency'
        else:
            validation['tier2_tier3_consistency'] = 'Low consistency'
        
        # Overall consistency score
        consistency_scores = []
        if 'High' in validation['tier1_tier2_consistency']:
            consistency_scores.append(3)
        elif 'Moderate' in validation['tier1_tier2_consistency']:
            consistency_scores.append(2)
        else:
            consistency_scores.append(1)
            
        if 'High' in validation['tier2_tier3_consistency']:
            consistency_scores.append(3)
        elif 'Moderate' in validation['tier2_tier3_consistency']:
            consistency_scores.append(2)
        else:
            consistency_scores.append(1)
        
        avg_score = sum(consistency_scores) / len(consistency_scores)
        validation['overall_consistency_score'] = avg_score / 3  # Normalize to 0-1
        
        return validation
    
    def generate_analysis_summary(self):
        """Generate comprehensive analysis summary"""
        
        summary = {
            'overall_integration_score': None,
            'key_findings': [],
            'policy_recommendations': [],
            'methodological_notes': [],
            'data_quality_assessment': {}
        }
        
        # Calculate overall integration score
        tier1_score = max(0, self.results['tier1']['coefficients'].get('ref_price', 0))
        tier2_scores = [
            1 if results['cointegration']['rank'] > 0 else 0 
            for results in self.results['tier2'].values()
        ]
        tier2_score = sum(tier2_scores) / len(tier2_scores) if tier2_scores else 0
        tier3_score = self.results['tier3']['pca_results']['explained_variance_ratio'][0]
        
        overall_score = (tier1_score * 0.4 + tier2_score * 0.4 + tier3_score * 0.2)
        summary['overall_integration_score'] = overall_score
        
        # Key findings
        if overall_score > 0.7:
            summary['key_findings'].append("Markets show strong integration patterns")
        elif overall_score > 0.5:
            summary['key_findings'].append("Markets show moderate integration")
        else:
            summary['key_findings'].append("Markets show weak integration")
        
        # Add commodity-specific findings
        for commodity, results in self.results['tier2'].items():
            if results['cointegration']['rank'] > 0:
                summary['key_findings'].append(
                    f"{commodity} markets are cointegrated (rank: {results['cointegration']['rank']})"
                )
        
        # Policy recommendations
        if overall_score > 0.6:
            summary['policy_recommendations'].append(
                "Regional price interventions will have broad market impact"
            )
            summary['policy_recommendations'].append(
                "Monitor reference markets closely for early warning signals"
            )
        else:
            summary['policy_recommendations'].append(
                "Consider targeted local interventions rather than regional policies"
            )
            summary['policy_recommendations'].append(
                "Investigate barriers to market integration"
            )
        
        return summary
```

### Hands-on Exercise 4.1

**Objective**: Execute and interpret a complete three-tier analysis

**Tasks**:
1. Prepare dataset for 3 markets, 2 commodities, 6-month period
2. Run Tier 1 pooled panel analysis and interpret coefficients
3. Execute Tier 2 VECM analysis for each commodity
4. Perform Tier 3 factor analysis and validation
5. Generate comprehensive analysis report
6. Create policy recommendations based on results

**Expected Time**: 2 hours

**Deliverable**: Complete analysis report with interpretations and policy implications

This comprehensive Module 4 provides researchers and analysts with the knowledge and tools needed to conduct sophisticated econometric analysis of market integration patterns in conflict settings.