# Documentation Map

This document provides a complete map of all documentation in the Yemen Market Integration project.

## Quick Navigation

- [Getting Started](#getting-started)
- [Architecture](#architecture)
- [User Guides](#user-guides)
- [API Reference](#api-reference)
- [Development](#development)
- [Methodology](#methodology)
- [Deployment](#deployment)
- [Case Studies](#case-studies)
- [Results](#results)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [V2 Implementation](#v2-implementation)
- [Exchange Rate Research](#exchange-rate-research)
- [Research Methodology Package](#research-methodology-package)

## Documentation Structure

### Root Level Documents
- [README.md](/README.md) - Project overview and quick start
- [CONTRIBUTING.md](/CONTRIBUTING.md) - Contribution guidelines
- [CLAUDE.md](/CLAUDE.md) - AI assistant instructions and research guide
- [PROJECT_SUMMARY.md](/PROJECT_SUMMARY.md) - Executive project summary

### Getting Started
- [README.md](./00-getting-started/README.md) - Getting started overview
- [Installation](./00-getting-started/installation.md) - Setup instructions
- [Quick Start](./00-getting-started/quick-start.md) - Run your first analysis
- [First Analysis](./00-getting-started/first-analysis.md) - Tutorial walkthrough

### Architecture
- [README.md](./01-architecture/README.md) - Architecture overview
- [Overview](./01-architecture/overview.md) - System design
- [Components](./01-architecture/components.md) - Component details
- [Data Flow](./01-architecture/data-flow.md) - Data pipeline architecture
- [Security](./01-architecture/security.md) - Security considerations

### User Guides
- [README.md](./02-user-guides/README.md) - User guide overview
- [Data Pipeline](./02-user-guides/data-pipeline.md) - Working with data
- [Running Analyses](./02-user-guides/running-analyses.md) - Execute models
- [Interpreting Results](./02-user-guides/interpreting-results.md) - Understanding outputs
- [Visualization](./02-user-guides/visualization.md) - Creating visualizations
- [FAQ](./02-user-guides/faq.md) - Frequently asked questions

### API Reference
- [README.md](./03-api-reference/README.md) - API documentation overview
- **Config**
  - [Settings](./03-api-reference/config/settings.md) - Configuration reference
- **Data**
  - [ACAPS Processor](./03-api-reference/data/acaps_processor.md)
  - [ACLED Processor](./03-api-reference/data/acled_processor.md)
  - [HDX Client](./03-api-reference/data/hdx_client.md)
  - [Panel Builder](./03-api-reference/data/panel_builder.md)
  - [Spatial Joiner](./03-api-reference/data/spatial_joiner.md)
  - [WFP Processor](./03-api-reference/data/wfp_processor.md)
- **Features**
  - [Data Preparation](./03-api-reference/features/data_preparation.md)
  - [Feature Engineer](./03-api-reference/features/feature_engineer.md)
- **Models**
  - [Three-Tier Models](./03-api-reference/models/three_tier/)
- **Utils**
  - [Logging](./03-api-reference/utils/logging.md)

### Development
- [README.md](./04-development/README.md) - Developer guide
- **Setup**
  - [Development Setup](./04-development/setup/development-setup.md)
  - [Dependencies](./04-development/setup/dependencies.md)
  - [IDE Configuration](./04-development/setup/ide-configuration.md)
- **Coding Standards**
  - [Python Style Guide](./04-development/coding-standards/python-style-guide.md)
  - [Documentation Standards](./04-development/coding-standards/documentation-standards.md)
  - [Git Workflow](./04-development/coding-standards/git-workflow.md)
- **Testing**
  - [Unit Testing](./04-development/testing/unit-testing.md)
  - [Integration Testing](./04-development/testing/integration-testing.md)
- **Debugging**
  - [Common Issues](./04-development/debugging/common-issues.md)
  - [Logging Guide](./04-development/debugging/logging_guide.md)

### Methodology
- [README.md](./05-methodology/README.md) - Methodology overview
- **Data Processing**
  - [Data Sources](./05-methodology/data-processing/data_sources.md)
  - [Panel Construction](./05-methodology/data-processing/panel-construction.md)
  - [Missing Data](./05-methodology/data-processing/missing-data.md)
  - [Outlier Detection](./05-methodology/data-processing/outlier-detection.md)
- **Econometric Models**
  - [Panel Models](./05-methodology/econometric-models/panel-models.md)
  - [Time Series](./05-methodology/econometric-models/time-series.md)
  - [Cointegration](./05-methodology/econometric-models/cointegration.md)
  - [Threshold Models](./05-methodology/econometric-models/threshold-models.md)
- **Statistical Tests**
  - [Diagnostic Tests](./05-methodology/statistical-tests/diagnostic-tests.md)
  - [Unit Root Tests](./05-methodology/statistical-tests/unit-root-tests.md)
  - [Robustness Checks](./05-methodology/statistical-tests/robustness-checks.md)

### Deployment
- [README.md](./06-deployment/README.md) - Deployment guide
- **Docker**
  - [Development](./06-deployment/docker/development.md)
  - [Production](./06-deployment/docker/production.md)
- **Cloud**
  - [AWS](./06-deployment/cloud/aws.md)
  - [Azure](./06-deployment/cloud/azure.md)
  - [GCP](./06-deployment/cloud/gcp.md)
- **Monitoring**
  - [Logging](./06-deployment/monitoring/logging.md)
  - [Metrics](./06-deployment/monitoring/metrics.md)
  - [Alerts](./06-deployment/monitoring/alerts.md)

### Case Studies
- [README.md](./07-case-studies/README.md) - Case study overview
- [Wheat Analysis](./07-case-studies/wheat-analysis.md)
- [Fuel Crisis 2021](./07-case-studies/fuel-crisis-2021.md)
- [Exchange Rate Impact](./07-case-studies/exchange-rate-impact.md)
- [Conflict Spillovers](./07-case-studies/conflict-spillovers.md)

### Results
- [README.md](./08-results/README.md) - Results overview
- [Executive Summary](./08-results/executive-summary.md)
- **Policy Briefs**
  - [Market Interventions](./08-results/policy-briefs/market-interventions.md)
  - [Humanitarian Aid](./08-results/policy-briefs/humanitarian-aid.md)
  - [Conflict Mitigation](./08-results/policy-briefs/conflict-mitigation.md)

### Troubleshooting
- [README.md](./09-troubleshooting/README.md) - Troubleshooting guide
- [Common Issues](./09-troubleshooting/common-issues.md)
- [Data Issues](./09-troubleshooting/data-issues.md)
- [Model Convergence](./09-troubleshooting/model-convergence.md)
- [Performance](./09-troubleshooting/performance.md)
- [Error Codes](./09-troubleshooting/error-codes.md)

### Contributing
- [README.md](./10-contributing/README.md) - Contributing guidelines
- [Code of Conduct](./10-contributing/code-of-conduct.md)
- [Issue Templates](./10-contributing/issue-templates.md)
- [Pull Request Guide](./10-contributing/pull-request-guide.md)
- [Roadmap](./10-contributing/roadmap.md)
- [Future Vision](./10-contributing/future-vision.md)

### V2 Implementation
- [V2 Overview](./11-v2-implementation/V2_README.md) - V2 system documentation
- [Migration Guide](./11-v2-implementation/V2_MIGRATION_GUIDE.md) - V1 to V2 migration
- [API Testing](./11-v2-implementation/V2_API_TESTING_REPORT.md) - V2 API validation
- [Deployment Guide](./11-v2-implementation/V2_DEPLOYMENT_GUIDE.md) - V2 deployment
- [V2 Plan](./11-v2-implementation/V2_PLAN.md) - Implementation plan
- [V2 to V3 Migration](./11-v2-implementation/V2_TO_V3_MIGRATION_GUIDE.md) - Future migration

### Exchange Rate Research
- **Phase 1** - Event study methodology
  - [Event Study Methodology](./12-exchange-rate-research/Phase-1/)
- **Phase 2** - Cross-country framework
  - [Country Implementation](./12-exchange-rate-research/Phase-2/country-implementation-framework.md)
  - [Cross-Country Validation](./12-exchange-rate-research/Phase-2/cross-country-validation-framework.md)
- **Phase 3** - Consumer surplus analysis
  - [Consumer Surplus Methodology](./12-exchange-rate-research/Phase-3/)
- **Phase 4** - Implementation guides
  - [Exchange Rate Pipeline](./12-exchange-rate-research/Phase-4/First_run/exchange-rate-data-pipeline.md)
  - [Practical Guides](./12-exchange-rate-research/Phase-4/Second_run/)

### Research Methodology Package
- [Overview](./research-methodology-package/README.md) - Research package guide
- [CLAUDE.md](./research-methodology-package/CLAUDE.md) - Research-specific AI instructions
- **Foundation**
  - [PRD](./research-methodology-package/01-foundation/PRD_Yemen_Market_Integration.md)
  - [Literature Review](./research-methodology-package/01-foundation/literature/)
  - [Theory](./research-methodology-package/01-foundation/theory/)
- **Data**
  - [Sources](./research-methodology-package/02-data/sources/)
  - [Transformations](./research-methodology-package/02-data/transformations/)
- **Methodology**
  - [Main Document](./research-methodology-package/03-methodology/METHODOLOGY.md)
  - [Econometric Models](./research-methodology-package/03-methodology/econometric-models/)
  - [Identification](./research-methodology-package/03-methodology/identification/)
- **Implementation**
  - [Code Mappings](./research-methodology-package/04-implementation/code-mappings/)
  - [Diagnostics](./research-methodology-package/04-implementation/diagnostics/)
  - [Validation](./research-methodology-package/04-implementation/validation/)
- **Results**
  - [Main Findings](./research-methodology-package/05-results/main-findings/)
  - [Mechanisms](./research-methodology-package/05-results/mechanisms/)
- **Paper**
  - [Drafts](./research-methodology-package/06-paper/drafts/)
  - [Figures](./research-methodology-package/06-paper/figures/)
  - [Tables](./research-methodology-package/06-paper/tables/)

## Finding Documentation

### By Topic
- **Data Pipeline**: Start with [Data Pipeline Guide](./02-user-guides/data-pipeline.md)
- **Three-Tier Models**: See [API Reference](./03-api-reference/models/three_tier/)
- **Exchange Rates**: Check [Exchange Rate Research](./12-exchange-rate-research/)
- **V2 System**: Read [V2 Implementation](./11-v2-implementation/)
- **Research Methods**: Explore [Research Methodology Package](./research-methodology-package/)

### By User Type
- **Researchers**: Focus on [User Guides](./02-user-guides/) and [Methodology](./05-methodology/)
- **Developers**: Start with [Architecture](./01-architecture/) and [Development](./04-development/)
- **Policy Makers**: Read [Results](./08-results/) and [Case Studies](./07-case-studies/)
- **Contributors**: See [Contributing](./10-contributing/) and [Development](./04-development/)

## Documentation Standards

All documentation follows these principles:
1. **Clear purpose** - State the document's purpose upfront
2. **Target audience** - Specify who should read it
3. **Examples** - Include practical examples
4. **Cross-references** - Link to related content
5. **Versioning** - Track document updates

## Maintenance

This documentation map is maintained alongside the codebase. When adding new documentation:
1. Place it in the appropriate section
2. Update this map
3. Add cross-references
4. Update the main README if needed

---

*Last updated: June 2, 2025*