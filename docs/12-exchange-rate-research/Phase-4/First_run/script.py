# Let's try a simpler example focusing on the key components

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt

# Create a simple example of exchange rate imputation for conflict zones
def yemen_exchange_rate_imputation_example():
    # Create sample data - daily exchange rates with missing values
    # Simplified to focus on the imputation methods
    dates = pd.date_range(start='2023-01-01', end='2023-01-31', freq='D')
    
    # Create two zones with different rate patterns
    data = []
    
    # Houthi zone (stable around 535)
    for date in dates:
        # Add some random missing data
        if np.random.random() < 0.2:  # 20% missing
            rate = np.nan
        else:
            # Stable rate with small fluctuations
            rate = 535 + np.random.normal(0, 2)
        
        data.append({
            'date': date,
            'zone': 'houthi',
            'exchange_rate': rate
        })
    
    # Government zone (increasing, around 1800-2000)
    for date in dates:
        # Add some random missing data (more in conflict areas)
        if np.random.random() < 0.3:  # 30% missing
            rate = np.nan
        else:
            # Increasing rate with larger fluctuations
            base = 1800 + (date - dates[0]).days * 5  # 5 YER increase per day
            rate = base + np.random.normal(0, 20)  # Higher volatility
        
        data.append({
            'date': date,
            'zone': 'government',
            'exchange_rate': rate
        })
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Store original data for comparison
    df_original = df.copy()
    
    # Imputation methods
    
    # 1. Forward fill (use previous day's rate)
    df['ffill'] = df.groupby('zone')['exchange_rate'].transform(lambda x: x.fillna(method='ffill'))
    
    # 2. Backward fill for any remaining missing values
    df['ffill_bfill'] = df['ffill'].transform(lambda x: x.fillna(method='bfill'))
    
    # 3. Zone average for each day
    zone_daily_avg = df.groupby(['date', 'zone'])['exchange_rate'].transform('mean')
    df['zone_avg'] = df.groupby('zone')['exchange_rate'].transform('mean')
    
    # 4. Combined approach (hierarchical imputation)
    df['imputed'] = df['exchange_rate']
    
    # First try forward fill within zone
    df['imputed'] = df.groupby('zone')['imputed'].transform(lambda x: x.fillna(method='ffill'))
    
    # Then try backward fill within zone
    df['imputed'] = df.groupby('zone')['imputed'].transform(lambda x: x.fillna(method='bfill'))
    
    # Finally, use zone average for any remaining missing values
    df['imputed'] = df['imputed'].fillna(df['zone_avg'])
    
    return df

# Run the example
exchange_imputation = yemen_exchange_rate_imputation_example()

# Create a visualization of the imputation results
plt.figure(figsize=(10, 6))

# Plot the government zone data
gov_data = exchange_imputation[exchange_imputation['zone'] == 'government']
plt.subplot(2, 1, 1)
plt.plot(gov_data['date'], gov_data['exchange_rate'], 'o', label='Original data', alpha=0.7)
plt.plot(gov_data['date'], gov_data['imputed'], 'r-', label='Imputed values')
plt.title('Government Zone Exchange Rates (YER/USD)')
plt.ylabel('Exchange Rate')
plt.grid(True, alpha=0.3)
plt.legend()

# Plot the Houthi zone data
houthi_data = exchange_imputation[exchange_imputation['zone'] == 'houthi']
plt.subplot(2, 1, 2)
plt.plot(houthi_data['date'], houthi_data['exchange_rate'], 'o', label='Original data', alpha=0.7)
plt.plot(houthi_data['date'], houthi_data['imputed'], 'r-', label='Imputed values')
plt.title('Houthi Zone Exchange Rates (YER/USD)')
plt.xlabel('Date')
plt.ylabel('Exchange Rate')
plt.grid(True, alpha=0.3)
plt.legend()

plt.tight_layout()
plt.savefig('yemen_exchange_rate_imputation.png')
plt.close()

# Create a practical example of data quality checking for Yemen price data
def yemen_price_data_quality_check():
    """
    Example function to check data quality for Yemen price data.
    """
    # Create a sample dataset with typical issues
    markets = ['Sana', 'Aden', 'Taiz', 'Hodeidah', 'Marib']
    commodities = ['wheat_flour', 'rice', 'sugar', 'oil']
    dates = pd.date_range(start='2023-01-01', end='2023-03-31', freq='W')
    
    # Create sample data with common issues
    data = []
    for date in dates:
        for market in markets:
            for commodity in commodities:
                # Determine zone
                if market in ['Sana', 'Hodeidah']:
                    zone = 'houthi'
                    # Base prices lower in YER but stable
                    base_price = {
                        'wheat_flour': 350, 
                        'rice': 600, 
                        'sugar': 400, 
                        'oil': 2000
                    }[commodity]
                    
                    # Less variation in Houthi zones
                    variation = np.random.normal(0, base_price * 0.05)
                    price = base_price + variation
                    
                else:
                    zone = 'government'
                    # Higher base prices in YER due to inflation
                    base_price = {
                        'wheat_flour': 1200, 
                        'rice': 2100, 
                        'sugar': 1500, 
                        'oil': 7000
                    }[commodity]
                    
                    # More variation in government zones
                    variation = np.random.normal(0, base_price * 0.15)
                    price = base_price + variation
                
                # Add some typical data issues
                
                # 1. Missing values
                if np.random.random() < 0.1:  # 10% missing
                    price = np.nan
                
                # 2. Outliers (rare but impactful)
                if np.random.random() < 0.02:  # 2% outliers
                    price = price * (3 + np.random.random())  # 3-4x higher
                
                # 3. Incorrect sign (negative prices)
                if np.random.random() < 0.01:  # 1% negative
                    price = -price
                
                # 4. Data entry errors (typos, decimal place errors)
                if np.random.random() < 0.03:  # 3% data entry errors
                    error_type = np.random.choice(['typo', 'decimal'])
                    if error_type == 'typo':
                        # Add an extra digit
                        price = price * 10
                    else:
                        # Decimal place error
                        price = price / 10
                
                data.append({
                    'date': date,
                    'market': market,
                    'commodity': commodity,
                    'price_yer': price,
                    'zone': zone
                })
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Quality check function
    def check_quality(df):
        """
        Perform quality checks on Yemen price data.
        """
        results = {
            'total_rows': len(df),
            'issues_found': [],
            'recommendations': []
        }
        
        # 1. Check for missing values
        missing = df['price_yer'].isna().sum()
        missing_pct = missing / len(df) * 100
        
        results['issues_found'].append({
            'issue': 'Missing prices',
            'count': missing,
            'percentage': missing_pct,
            'severity': 'High' if missing_pct > 15 else 'Medium' if missing_pct > 5 else 'Low'
        })
        
        # 2. Check for negative prices
        negative = (df['price_yer'] < 0).sum()
        negative_pct = negative / len(df) * 100
        
        if negative > 0:
            results['issues_found'].append({
                'issue': 'Negative prices',
                'count': negative,
                'percentage': negative_pct,
                'severity': 'Critical' if negative_pct > 1 else 'High',
                'examples': df[df['price_yer'] < 0].head(3).to_dict('records')
            })
            
            results['recommendations'].append(
                "Negative prices are likely data entry errors. Consider replacing with absolute values or marking as missing."
            )
        
        # 3. Check for outliers by commodity and zone
        outliers = []
        for (commodity, zone), group in df.groupby(['commodity', 'zone']):
            # Use IQR method for outlier detection
            q1 = group['price_yer'].quantile(0.25)
            q3 = group['price_yer'].quantile(0.75)
            iqr = q3 - q1
            
            lower_bound = q1 - 3 * iqr
            upper_bound = q3 + 3 * iqr
            
            outliers_in_group = group[(group['price_yer'] < lower_bound) | (group['price_yer'] > upper_bound)]
            
            if len(outliers_in_group) > 0:
                outliers.append({
                    'commodity': commodity,
                    'zone': zone,
                    'count': len(outliers_in_group),
                    'percentage': len(outliers_in_group) / len(group) * 100,
                    'min_normal': lower_bound,
                    'max_normal': upper_bound,
                    'examples': outliers_in_group.head(2).to_dict('records')
                })
        
        if outliers:
            results['issues_found'].append({
                'issue': 'Price outliers',
                'details': outliers,
                'severity': 'High'
            })
            
            results['recommendations'].append(
                "Review outliers carefully. Some may be decimal place errors or typos, while others may reflect actual market disruptions."
            )
        
        # 4. Check for zone consistency
        # Prices in government zones should generally be higher in YER than Houthi zones
        zone_comparison = df.groupby(['commodity', 'zone'])['price_yer'].median().reset_index()
        zone_pivot = zone_comparison.pivot(index='commodity', columns='zone', values='price_yer')
        
        zone_inconsistencies = []
        for commodity in zone_pivot.index:
            if zone_pivot.loc[commodity, 'houthi'] > zone_pivot.loc[commodity, 'government']:
                zone_inconsistencies.append({
                    'commodity': commodity,
                    'houthi_median': zone_pivot.loc[commodity, 'houthi'],
                    'government_median': zone_pivot.loc[commodity, 'government']
                })
        
        if zone_inconsistencies:
            results['issues_found'].append({
                'issue': 'Zone price inconsistencies',
                'details': zone_inconsistencies,
                'severity': 'Critical'
            })
            
            results['recommendations'].append(
                "Zone inconsistencies suggest possible data entry errors or zone misclassification. "
                "Government zone prices in YER should generally be higher than Houthi zone prices."
            )
        
        # 5. Check data completeness by market-commodity pairs
        completeness = df.groupby(['market', 'commodity']).size().unstack()
        missing_pairs = (completeness == 0).sum().sum()
        
        if missing_pairs > 0:
            results['issues_found'].append({
                'issue': 'Missing market-commodity pairs',
                'count': missing_pairs,
                'severity': 'Medium'
            })
            
            results['recommendations'].append(
                "Some market-commodity combinations have no data. Consider if this reflects market realities or data collection gaps."
            )
        
        # Generate summary recommendation
        if any(issue['severity'] == 'Critical' for issue in results['issues_found']):
            results['overall_quality'] = 'Poor - Critical issues require immediate attention'
        elif any(issue['severity'] == 'High' for issue in results['issues_found']):
            results['overall_quality'] = 'Fair - Significant issues need addressing'
        elif any(issue['severity'] == 'Medium' for issue in results['issues_found']):
            results['overall_quality'] = 'Good - Minor issues exist'
        else:
            results['overall_quality'] = 'Excellent - No significant issues'
        
        return results
    
    # Run the quality check
    quality_results = check_quality(df)
    
    return df, quality_results

# Run the example
price_data, quality_results = yemen_price_data_quality_check()

# Example of a Yemen-specific price analysis function
def analyze_yemen_price_parity(price_data):
    """
    Analyze price parity between zones when converted to USD.
    """
    # Create sample exchange rates
    exchange_rates = {
        'houthi': 535,
        'government': 1900
    }
    
    # Convert YER prices to USD
    price_data['price_usd'] = price_data.apply(
        lambda row: row['price_yer'] / exchange_rates[row['zone']] if not pd.isna(row['price_yer']) else np.nan,
        axis=1
    )
    
    # Calculate price parity statistics by commodity
    parity_stats = []
    
    for commodity in price_data['commodity'].unique():
        commodity_data = price_data[price_data['commodity'] == commodity]
        
        # Get median prices by zone
        houthi_yer = commodity_data[commodity_data['zone'] == 'houthi']['price_yer'].median()
        gov_yer = commodity_data[commodity_data['zone'] == 'government']['price_yer'].median()
        
        houthi_usd = commodity_data[commodity_data['zone'] == 'houthi']['price_usd'].median()
        gov_usd = commodity_data[commodity_data['zone'] == 'government']['price_usd'].median()
        
        # Calculate differentials
        yer_diff_pct = ((gov_yer / houthi_yer) - 1) * 100 if houthi_yer and not pd.isna(houthi_yer) else np.nan
        usd_diff_pct = ((gov_usd / houthi_usd) - 1) * 100 if houthi_usd and not pd.isna(houthi_usd) else np.nan
        
        parity_stats.append({
            'commodity': commodity,
            'houthi_price_yer': houthi_yer,
            'government_price_yer': gov_yer,
            'yer_difference_pct': yer_diff_pct,
            'houthi_price_usd': houthi_usd,
            'government_price_usd': gov_usd,
            'usd_difference_pct': usd_diff_pct,
            'price_parity': 'Yes' if abs(usd_diff_pct) < 10 else 'No'
        })
    
    parity_df = pd.DataFrame(parity_stats)
    
    # Create a visualization of price parity
    plt.figure(figsize=(10, 6))
    
    # Plot YER price differences
    plt.subplot(2, 1, 1)
    plt.bar(parity_df['commodity'], parity_df['yer_difference_pct'])
    plt.axhline(y=0, color='r', linestyle='-', alpha=0.3)
    plt.title('YER Price Difference (Gov vs Houthi) %')
    plt.ylabel('Difference %')
    plt.grid(True, alpha=0.3)
    
    # Plot USD price differences
    plt.subplot(2, 1, 2)
    bars = plt.bar(parity_df['commodity'], parity_df['usd_difference_pct'])
    
    # Color bars based on parity
    for i, is_parity in enumerate(parity_df['price_parity'] == 'Yes'):
        bars[i].set_color('green' if is_parity else 'red')
    
    plt.axhline(y=0, color='r', linestyle='-', alpha=0.3)
    plt.axhline(y=10, color='g', linestyle='--', alpha=0.3)
    plt.axhline(y=-10, color='g', linestyle='--', alpha=0.3)
    plt.title('USD Price Difference (Gov vs Houthi) %')
    plt.xlabel('Commodity')
    plt.ylabel('Difference %')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('yemen_price_parity.png')
    plt.close()
    
    return parity_df

# Run the example
price_parity = analyze_yemen_price_parity(price_data)

# Print summary information
print("YEMEN MARKET DATA: PRACTICAL IMPLEMENTATION EXAMPLES")

print("\n1. Exchange Rate Imputation Example")
print("----------------------------------")
print(f"Created an example with intentionally missing exchange rate data.")
print(f"Imputation methods applied: forward fill, backward fill, and zone averages.")
print(f"Visualization saved as 'yemen_exchange_rate_imputation.png'")

print("\n2. Price Data Quality Check Example")
print("----------------------------------")
print(f"Total rows in sample data: {quality_results['total_rows']}")
print(f"Overall data quality: {quality_results['overall_quality']}")
print("\nIssues found:")
for issue in quality_results['issues_found']:
    print(f"- {issue['issue']} (Severity: {issue['severity']})")

print("\nRecommendations:")
for i, rec in enumerate(quality_results['recommendations'], 1):
    print(f"{i}. {rec}")

print("\n3. Price Parity Analysis Example")
print("----------------------------------")
print("This analysis demonstrates the core hypothesis testing for H1:")
print("When converted to USD, prices across currency zones should show parity.")
print("\nPrice comparison by commodity:")
print(price_parity[['commodity', 'yer_difference_pct', 'usd_difference_pct', 'price_parity']].to_string(index=False))
print("\nVisualization saved as 'yemen_price_parity.png'")

print("\nThe examples demonstrate practical implementation approaches for:")
print("1. Handling missing exchange rate data in conflict zones")
print("2. Validating data quality with Yemen-specific checks")
print("3. Testing the exchange rate mechanism hypothesis")
print("\nThese functions could form the basis for the practical implementation guides requested.")