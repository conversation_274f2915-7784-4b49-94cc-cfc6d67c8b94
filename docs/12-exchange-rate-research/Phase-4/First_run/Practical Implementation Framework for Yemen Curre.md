<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# Practical Implementation Framework for Yemen Currency Fragmentation Research

This comprehensive framework transforms sophisticated theoretical methodologies into actionable field procedures, bridging the critical gap between advanced econometric methods and the practical realities of conflict data collection in Yemen. The framework addresses the fundamental challenge facing researchers: despite having world-class theoretical foundations, implementing these methods with Yemen's complex, incomplete, and conflict-affected data requires specialized practical guidance that has been lacking in existing research approaches.

## Exchange Rate Data Collection and Imputation Pipeline for Yemen

The implementation of currency fragmentation research requires systematic procedures for collecting and processing exchange rate data across Yemen's dual currency zones, where Houthi-controlled areas maintain stable rates around 535 YER/USD while government-controlled areas experience depreciation to 2,000+ YER/USD[^1]. This represents a fundamental data collection challenge that must be addressed before any sophisticated econometric analysis can proceed.

![Yemen exchange rate by zone](https://pplx-res.cloudinary.com/image/upload/v1748846441/pplx_code_interpreter/2c28bdd3_amidr4.jpg)

Yemen exchange rate by zone

### Data Source Hierarchy and Collection Protocols

The primary challenge in implementing Yemen's exchange rate research involves establishing reliable data collection systems that can operate across conflict lines while maintaining analytical rigor. The data collection hierarchy prioritizes sources based on reliability, accessibility, and coverage across currency zones. The World Food Programme's Global Market Monitor represents the most reliable primary source, providing standardized exchange rate data across 23 districts with verified collection procedures[^8]. This source addresses the critical need for consistent data collection protocols that can function despite security constraints and territorial fragmentation.

Local money changers and traders provide essential real-time data through established networks, though this requires careful validation procedures to ensure accuracy. The collection protocol involves morning collection between 8:00-10:00 AM local time, capturing both buy and sell rates along with volume indicators and market conditions. For Houthi areas, expected rates range between 530-540 YER/USD with a volatility threshold of 2% daily change, while government areas show expected ranges of 1,800-2,200 YER/USD with acceptable volatility up to 5% daily change[^1].

Validation procedures incorporate zone-specific range checks and volatility assessments that account for the distinct characteristics of each currency zone. The validation algorithm flags rates outside expected ranges and identifies suspicious volatility patterns that may indicate data collection errors or significant market events. This systematic approach ensures data quality while acknowledging the operational constraints of collecting financial data in conflict environments.

### Missing Data Imputation Strategies

Missing data represents a pervasive challenge in conflict-affected data collection, requiring sophisticated imputation strategies that maintain analytical validity while acknowledging data limitations. The hierarchical imputation strategy begins with same-zone forward fill for gaps of 1-3 days, recognizing that exchange rates within currency zones exhibit sufficient stability to support short-term carry-forward procedures. Zone average interpolation addresses medium-term gaps of 4-7 days by utilizing daily averages across similar areas within the same currency regime.

![Yemen exchange rate imputation](https://pplx-res.cloudinary.com/image/upload/v1748846526/pplx_code_interpreter/33a4e35a_urgxbi.jpg)

Yemen exchange rate imputation

For longer gaps exceeding 8 days, trend-based projection utilizes recent rate movements to estimate missing values while incorporating confidence intervals that reflect the increased uncertainty associated with longer projection periods. This approach acknowledges that extended missing data periods often coincide with conflict escalation, requiring imputation methods that can function with incomplete information while providing appropriate uncertainty measures for downstream analysis.

The practical implementation includes quality control procedures that generate weekly completeness reports, tracking data availability across currency zones and identifying systematic collection challenges. These reports enable adaptive management of data collection protocols, ensuring that research can continue despite operational constraints while maintaining transparency about data limitations that may affect analytical conclusions.

## Yemen-Specific Data Adapters for Advanced Methods

The transformation of Yemen's heterogeneous data sources into analysis-ready formats requires specialized adapter functions that address the unique characteristics of conflict-affected data while maintaining compatibility with sophisticated econometric methods[^1][^2]. These adapters bridge the gap between raw field data and the structured panel datasets required for advanced analytical approaches, handling the specific challenges of Yemen's data landscape including irregular collection schedules, varying commodity classifications, and territorial control changes.

### WFP Data Structure Transformation

The World Food Programme data format requires systematic transformation to support panel econometric analysis, addressing inconsistencies in commodity naming, unit standardization, and temporal alignment across different collection systems. The transformation process begins with governorate name standardization, mapping the various naming conventions used across different data sources to consistent administrative boundaries that align with territorial control patterns and currency zone classifications.

Commodity classification mapping addresses the challenge of varying product specifications across different markets and time periods, ensuring that price comparisons reflect equivalent goods rather than quality variations that could confound currency fragmentation analysis. The mapping protocol includes conversion factors for different measurement units and quality adjustments that account for grade differences in commodities like wheat flour or rice that may vary significantly across supply chains.

Temporal alignment procedures synchronize monthly price collection with daily exchange rate data, creating month-average rates with quality indicators that reflect the reliability of underlying daily observations. This alignment addresses the fundamental challenge of matching different data collection frequencies while preserving the variation necessary for econometric identification of currency fragmentation effects.

### Handling Yemen-Specific Missing Data Patterns

Yemen's missing data patterns exhibit systematic characteristics related to conflict intensity, seasonal factors, and institutional capacity that require specialized handling procedures beyond standard econometric approaches. Missing data in conflict zones often correlates with security incidents, creating non-random missingness that could bias standard imputation methods if not properly addressed. The adapter functions incorporate conflict-aware missing data procedures that explicitly model the relationship between data availability and security conditions.

Seasonal missing data patterns related to Ramadan, harvest periods, and monsoon seasons require temporal imputation methods that account for these predictable variations in data availability. The adaptation procedures include Islamic calendar integration that enables proper handling of religious holidays and cultural events that systematically affect market operations and data collection schedules.

The adapter functions include error handling protocols for common data quality issues specific to Yemen's context, such as currency denomination confusion where the same numerical value might represent YER or USD depending on collection context, market location switches due to territorial control changes, and trader displacement that affects the consistency of data sources over time.

## Step-by-Step Implementation Examples Using Yemen Data

The practical application of advanced econometric methods to Yemen's currency fragmentation requires detailed implementation examples that demonstrate how sophisticated theoretical frameworks translate into working code using actual Yemen data structures and constraints[^1][^3]. These examples provide researchers with concrete guidance for applying methods ranging from machine learning clustering to Bayesian uncertainty quantification, addressing the specific challenges of Yemen's data environment while maintaining analytical rigor.

### Machine Learning Market Segmentation Example

The implementation of machine learning clustering for market segmentation demonstrates how to identify distinct market behaviors across Yemen's currency zones using price movement patterns, volatility characteristics, and correlation structures. The example utilizes actual price data from Taiz and Aden markets to illustrate clustering procedures that can distinguish between markets operating under different currency regimes despite potential territorial control ambiguities.

The clustering algorithm incorporates features specifically relevant to currency fragmentation analysis, including price volatility measures, correlation with global commodity prices, and sensitivity to conflict events. The implementation addresses practical challenges such as varying data availability across markets, seasonal adjustment procedures that account for Ramadan effects, and validation methods that ensure cluster assignments reflect meaningful economic distinctions rather than data artifacts.

Results interpretation guidelines provide researchers with criteria for assessing cluster validity and stability, including permutation tests that verify clustering robustness and temporal consistency checks that ensure cluster assignments remain stable across different sample periods. These procedures enable researchers to distinguish between genuine market segmentation and spurious patterns that might emerge from data limitations or analytical choices.

### Interactive Fixed Effects Implementation

The Interactive Fixed Effects (IFE) model implementation demonstrates how to capture unobserved common factors affecting multiple markets while allowing for heterogeneous responses across different currency zones[^1]. The example uses wheat price data to illustrate factor extraction procedures that can identify common shocks such as global commodity price movements, regional conflict spillovers, and seasonal demand patterns while controlling for market-specific heterogeneity.

The practical implementation addresses computational challenges specific to Yemen's unbalanced panel structure, including factor selection criteria appropriate for small sample sizes, convergence diagnostics for iterative estimation procedures, and robustness checks that verify factor stability across different specification choices. The example incorporates Ramadan effects as a known seasonal factor while allowing the model to identify additional unobserved factors that may represent conflict-related supply disruptions or currency regime effects.

Interpretation procedures provide guidance for distinguishing between global factors that affect all markets uniformly and local factors that create systematic differences between currency zones. The implementation includes visualization tools that enable researchers to assess factor loadings, temporal patterns, and the relative importance of different factors in explaining price variation across Yemen's fragmented market system.

### Nowcasting Implementation for Real-Time Analysis

The nowcasting implementation demonstrates how to combine high-frequency indicators with monthly price data to generate timely estimates of market conditions suitable for operational decision-making[^1]. The example focuses on fuel price prediction using daily conflict indicators, weekly cash flow measures, and real-time exchange rate data to generate weekly price forecasts that support humanitarian programming decisions.

![Yemen price parity](https://pplx-res.cloudinary.com/image/upload/v1748846528/pplx_code_interpreter/dd28a8d1_nqjdy7.jpg)

Yemen price parity

The practical implementation addresses the trade-offs between timeliness and accuracy inherent in real-time analysis, providing researchers with decision rules for determining when preliminary estimates are sufficiently reliable for operational use versus when additional data collection is necessary. The nowcasting model incorporates uncertainty quantification that reflects both parameter uncertainty and the inherent volatility of conflict-affected markets.

Validation procedures include out-of-sample forecasting tests using historical data, comparison with alternative simple forecasting methods to establish baseline performance, and assessment of forecast accuracy during different conflict phases to understand model limitations. These procedures enable researchers to provide honest uncertainty assessments that support appropriate decision-making while acknowledging the limitations of forecasting in complex conflict environments.

## Robustness Validation Procedures for Yemen Analysis

The validation of econometric results in conflict settings requires specialized robustness procedures that account for data limitations, small sample sizes, and the unique characteristics of Yemen's institutional environment[^1][^6]. These procedures ensure that analytical conclusions can withstand scrutiny despite the inevitable data constraints imposed by conflict conditions, providing researchers with systematic approaches to assess result reliability and communicate appropriate uncertainty levels.

### Placebo Testing with Pre-Conflict Data

Placebo testing using pre-2015 data provides essential validation of analytical methods by applying identical procedures to periods before Yemen's currency fragmentation, ensuring that identified effects genuinely reflect currency regime differences rather than spurious analytical artifacts. The placebo tests utilize 2010-2014 data when Yemen operated under a unified currency system, applying the same spatial and temporal analysis procedures to verify that methods do not generate false positive results in integrated market conditions.

The implementation of placebo tests addresses specific challenges in Yemen's context, including the need to account for gradual institutional deterioration preceding the 2015 conflict, seasonal patterns that persist across different political regimes, and baseline differences between regions that existed before currency fragmentation. These tests provide essential confidence in analytical methods by demonstrating that identified currency effects emerge only during periods when currency fragmentation actually occurred.

Results interpretation guidelines establish clear criteria for determining when placebo tests support or challenge main findings, including statistical thresholds appropriate for Yemen's sample sizes and practical significance measures that reflect the magnitude of effects necessary for policy relevance. These guidelines enable researchers to distinguish between robust findings that survive placebo testing and potentially spurious results that may reflect analytical choices rather than genuine economic relationships.

### Permutation Testing for Small Samples

Small sample sizes represent a pervasive challenge in conflict data analysis, requiring permutation testing approaches that can provide valid statistical inference without relying on asymptotic approximations that may be inappropriate for Yemen's data limitations[^1]. Permutation tests generate empirical sampling distributions by randomly reassigning treatment assignments across observations, creating valid statistical inference procedures that remain appropriate even with limited data availability.

The implementation of permutation tests addresses Yemen-specific challenges including the need to preserve spatial correlation structures when permuting across markets, temporal dependencies that require block permutation procedures, and stratification requirements that ensure permutation procedures respect fundamental data structures such as currency zone boundaries and administrative divisions.

Computational procedures include efficient algorithms for generating permutation distributions despite the combinatorial complexity of multiple dimensions, stopping rules that balance computational efficiency with statistical precision, and parallel processing approaches that enable feasible implementation with standard computing resources. These procedures ensure that rigorous statistical testing remains feasible despite computational constraints typical in humanitarian research environments.

### Synthetic Control Construction

Synthetic control methods provide powerful validation approaches for Yemen analysis by constructing counterfactual comparison units that approximate the characteristics of treated units before intervention, enabling causal inference approaches that remain valid despite the absence of obvious control groups in conflict settings[^6]. The construction of synthetic controls for Yemen requires careful attention to pre-treatment matching on economic characteristics, geographic features, and institutional variables that determine market integration patterns.

The implementation addresses practical challenges specific to Yemen's context, including the limited availability of appropriate donor countries for synthetic control construction, the need to account for time-varying confounders that affect both currency policies and market outcomes, and validation procedures that assess the quality of synthetic control matches across different dimensions of economic performance.

Diagnostic procedures include pre-treatment fit assessment that evaluates how well synthetic controls approximate actual Yemen characteristics before currency fragmentation, placebo testing across potential donor countries to verify method validity, and sensitivity analysis that examines how synthetic control construction choices affect main conclusions. These procedures ensure that synthetic control approaches provide meaningful validation rather than merely sophisticated restatement of assumptions.

## Policy Translation Framework: From Econometrics to Humanitarian Action

The translation of complex econometric findings into actionable humanitarian programming requires systematic frameworks that preserve analytical rigor while communicating insights in formats accessible to operational decision-makers[^1][^5]. This framework addresses the critical gap between sophisticated analytical capabilities and practical policy implementation, ensuring that research investments generate tangible improvements in humanitarian effectiveness rather than merely academic contributions.

### Coefficient Interpretation for Practitioners

The interpretation of econometric coefficients for humanitarian practitioners requires translation procedures that convert statistical measures into operationally meaningful quantities while preserving appropriate uncertainty assessments. A one-unit increase in conflict intensity leading to a 15% price increase must be translated into practical terms: this represents approximately 3-4 additional conflict events per month causing commodity prices to rise by amounts that significantly impact household purchasing power and food security outcomes.

The translation framework includes magnitude benchmarks that help practitioners assess whether identified effects represent operationally significant changes worthy of programmatic response versus statistical artifacts that may lack practical importance. These benchmarks incorporate humanitarian standards such as emergency thresholds for food price increases, minimal detectable effects for program evaluation, and cost-effectiveness criteria for intervention design that enable appropriate prioritization of analytical findings.

Confidence interval communication requires specialized approaches that convey uncertainty without undermining decision-making effectiveness, providing practitioners with ranges of plausible effects while offering guidance on appropriate decision rules when facing uncertain evidence. The framework includes visualization standards that effectively communicate uncertainty for non-statistical audiences while maintaining analytical honesty about the limitations of available evidence.

### Early Warning Indicator Construction

The construction of early warning indicators from econometric analysis requires systematic procedures for converting model outputs into operational monitoring systems that can function with real-time data flows and limited analytical capacity[^1]. Early warning systems must balance sensitivity to genuine deterioration signals with specificity that avoids false alarms that could overwhelm operational capacity or undermine credibility through excessive alerts.

The indicator construction framework includes threshold setting procedures that account for both statistical significance and operational relevance, ensuring that warning triggers reflect meaningful changes in underlying conditions rather than normal variation in volatile conflict environments. Threshold calibration utilizes historical analysis to identify levels of economic disruption that historically preceded humanitarian crises while accounting for the changing baseline conditions that characterize protracted conflicts.

Implementation procedures address practical challenges including data lag compensation that adjusts for delays in information availability, missing data protocols that enable indicator calculation despite incomplete reporting, and communication standards that ensure warnings reach appropriate decision-makers with sufficient lead time and contextual information to support effective response. These procedures ensure that sophisticated analytical capabilities translate into improved humanitarian preparedness and response effectiveness.

### Real-Time Monitoring Dashboard Specifications

Real-time monitoring systems require dashboard designs that effectively communicate complex analytical outputs to diverse audiences while maintaining analytical rigor and supporting appropriate decision-making processes[^1]. Dashboard specifications must balance comprehensive information presentation with usability constraints that enable rapid assessment of changing conditions by busy operational staff working under significant time and resource pressures.

The dashboard framework includes visualization standards optimized for humanitarian contexts, utilizing color schemes and graphic elements that effectively communicate urgency levels while remaining accessible to users with varying technical backgrounds and visual capabilities. Information hierarchy ensures that critical alerts receive appropriate prominence while supporting detailed analysis capabilities for users requiring additional context or verification of automated assessments.

Technical specifications address implementation constraints typical in humanitarian environments, including bandwidth limitations that require efficient data transmission, offline functionality that enables continued operation during connectivity disruptions, and mobile compatibility that supports field-based decision-making. These specifications ensure that sophisticated analytical capabilities remain accessible and useful despite the challenging technical environments characteristic of humanitarian operations.

The framework provides comprehensive practical guidance that transforms sophisticated theoretical foundations into actionable procedures suitable for implementation in Yemen's challenging operational environment. Through systematic attention to data collection realities, analytical constraints, and decision-making requirements, this implementation framework ensures that advanced econometric capabilities generate meaningful improvements in humanitarian understanding and response effectiveness rather than merely academic contributions with limited practical application.

<div style="text-align: center">⁂</div>

[^1]: yemen_panel_methodology.md

[^2]: panel-construction.md

[^3]: ml_pattern_recognition.py

[^4]: exchange_rate_implementation.md

[^5]: second_run_Consumer-Surplus-Analysis-Methodology-for-Dual-Cur.md

[^6]: cross-country-validation-framework.md

[^7]: CLAUDE.md

[^8]: available-sources.md

[^9]: testable-hypotheses.md

[^10]: Methodology-Integration-Guide-for-Advanced-Econometric-Methods.md

[^11]: country-implementation-framework.md

[^12]: Step-by-Step-Implementation-of-Advanced-Econometric-Methods.md

[^13]: https://south24.net/news/newse.php?nid=4577

[^14]: https://devchampions.org/publications/flash-reports/deterioration_of_the_foreign_exchange_rate_of_the_yemeni_rial

[^15]: https://sanaacenter.org/the-yemen-review/april-june-2024/22888

[^16]: https://www.middleeasteye.net/news/houthis-maintain-price-yemeni-currency-their-areas

[^17]: https://www.moneyness.ca/2020/05/one-country-two-monetary-systems.html?m=0

[^18]: https://fscluster.org/sites/default/files/2025-01/Yemen Market Functionality Assessment Report_January%202025.pdf

[^19]: https://fscluster.org/sites/default/files/2025-01/Inter-Agency Market and Price Monitoring Methodologies - April 2024_Final%20Version.pdf

[^20]: https://south24.net/news/news.php?nid=2615

[^21]: https://www.aljazeera.com/news/2021/8/22/yemen-currency-clash-deepens-crisis-in-war-torn-country

[^22]: https://haiweb.org/storage/2015/07/Yemen-Report-Pricing-Surveys.pdf

[^23]: https://reliefweb.int/report/yemen/yemen-market-trade-bulletin-issued-16-january-2025

[^24]: https://www.imf.org/external/pubs/ft/wp/2010/wp10144.pdf

[^25]: https://fscluster.org/sites/default/files/2024-07/Yemen Market and Trade Update_May%202024.pdf

[^26]: https://south24.net/news/newse.php?nid=4253

[^27]: https://sanaacenter.org/publications/analysis/11562

[^28]: http://pubdocs.worldbank.org/en/901061582293682832/Yemen-Economic-Update-January-EN.pdf

[^29]: https://odihpn.org/publication/humanitarian-information-analysis-evidence-yemen/

[^30]: https://www.acaps.org/fileadmin/Data_Product/Main_media/20190429_acaps_yemen_analysis_hub_analysis_ecosystem_in_yemen_0.pdf

[^31]: https://www.publicinternationallawandpolicygroup.org/addressing-the-currency-crisis

[^32]: https://fscluster.org/sites/default/files/2024-10/Yemen Market and Trade Update_August%202024.pdf

[^33]: https://acleddata.com/knowledge-base/acled-methodology-and-coding-decisions-around-the-yemen-civil-war/

[^34]: https://fscluster.org/sites/default/files/documents/WFP Market Monitoring in Yemen.pdf

[^35]: https://reliefweb.int/report/yemen/yemen-currency-devalues-historic-lows-exacerbating-hunger-needs-warns-irc

[^36]: https://www.6wresearch.com/industry-report/yemen-ai-and-machine-learning-in-business-market

[^37]: https://blogs.worldbank.org/en/opendata/leveraging-data-for-early-detection-of-food-and-nutrition-crises

[^38]: https://www.calpnetwork.org/wp-content/uploads/2020/03/**********.wfp291385-1.pdf

[^39]: https://openknowledge.fao.org/bitstreams/cad2fe0d-4b10-48eb-88a1-a2900aa78998/download

[^40]: https://www.imf.org/external/pubs/ft/wp/2007/wp0705.pdf

[^41]: https://www.acaps.org/fileadmin/Data_Product/Main_media/20200129_acaps_yemen_analysis_hub_drivers_and_impact_of_yer_volatility_.pdf

[^42]: https://www.sipri.org/publications/2022/sipri-insights-peace-and-security/challenges-data-collection-conflict-affected-areas-case-study-liptako-gourma-region

[^43]: https://www.aiddata.org/blog/data-innovation-in-conflict-zones-enabling-insights-without-endangering-lives

[^44]: https://documents.sfcg.org/wp-content/uploads/2015/12/SFCG-DRC-Conflict-Scan-Methodology_EN.pdf

[^45]: https://pmc.ncbi.nlm.nih.gov/articles/PMC3571111/

[^46]: https://www.d3systems.com/wp-content/uploads/2010/09/ensuring_data_quality_in_conflict_zones.pdf

[^47]: https://lasdel.net/docs/2023/02/sipriinsight2207_data_collection_1.pdf

[^48]: https://documents1.worldbank.org/curated/en/099926206242412700/pdf/IDU1dc601b321062b148fc1b59414e6cd5c70a66.pdf

[^49]: https://www.scirp.org/pdf/jss20241211_81769293.pdf

[^50]: https://documents1.worldbank.org/curated/en/099050923091537357/pdf/P17826203eb7ac0030b5540af4456d0dd7c.pdf

[^51]: https://scispace.com/pdf/workers-remittances-in-yemen-macroeconomic-determinants-and-25r8l1acq6.pdf

[^52]: https://papers.ssrn.com/sol3/Delivery.cfm/SSRN_ID3299420_code2078277.pdf?abstractid=3299420\&mirid=1

[^53]: https://www.undp.org/sites/g/files/zskgke326/files/migration/ye/UNDP-Market-Assessment-Aden--EN.pdf

[^54]: https://www.ark.international/ark-blog/conducting-primary-research-in-yemen-challenges-and-lessons

[^55]: https://sanaacenter.org/reports/humanitarian-aid/15353

[^56]: https://civilianimpactmonitoring.org/methodology

[^57]: https://www.jointdatacenter.org/wp-content/uploads/2023/10/Surviving-in-the-Times-of-War_Jan.pdf

[^58]: https://www.wfp.org/countries/yemen

[^59]: https://www.wfp.org/operations/annual-country-report?operation_id=YE01\&year=2022

[^60]: https://www.wfp.org/operations/ye02-yemen-interim-country-strategic-plan-2023-2025

[^61]: https://data.humdata.org/m/dataset/wfp-food-security-indicators-for-yemen

[^62]: https://www.calpnetwork.org/wp-content/uploads/2024/09/Yemen-MEB-and-MPCA-Transfer-Value-Calculation-Guidance_Final.pdf

[^63]: https://data.worldbank.org/indicator/PA.NUS.FCRF?locations=YE

[^64]: https://documents1.worldbank.org/curated/en/491911467986262746/pdf/98846-BR-SecM2000-0625-Box393178B-PUBLIC.pdf

[^65]: https://www.undp.org/arab-states/stories/long-term-price-yemens-conflict

[^66]: https://www.endvawnow.org/en/articles/1896-collecting-data-in-conflict-settings-special-considerations.html

[^67]: https://www.surveypractice.org/article/92925-processes-and-challenges-associated-with-conducting-survey-research-in-conflict-zones

[^68]: https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/bc378fd2db6888d7ee62a9468fc4aba7/891a180f-bc43-492c-b981-5e7c058c0a22/b9e0cf8e.md

[^69]: https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/bc378fd2db6888d7ee62a9468fc4aba7/7a016f15-7e02-4434-922c-d64e5c5b783e/dd28a8d1.png

[^70]: https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/bc378fd2db6888d7ee62a9468fc4aba7/7a016f15-7e02-4434-922c-d64e5c5b783e/33a4e35a.png

[^71]: https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/bc378fd2db6888d7ee62a9468fc4aba7/1ca83895-1b9e-4486-832b-c0bf2e54988f/2c28bdd3.png

