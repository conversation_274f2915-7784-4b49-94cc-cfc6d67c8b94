import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

# Let's create sample code for a method implementation example

def create_sample_ml_clustering_data():
    """
    Create sample data for demonstrating market clustering in Yemen
    """
    # Create sample market data
    markets = [
        # Houthi-controlled major markets
        {'market_id': 'SANA', 'zone': 'houthi', 'population': 2500000, 'capital': True, 
         'port_distance': 150, 'latitude': 15.35, 'longitude': 44.21},
        {'market_id': 'HODEIDAH', 'zone': 'houthi', 'population': 600000, 'capital': False, 
         'port_distance': 0, 'latitude': 14.80, 'longitude': 42.95},
        {'market_id': 'IBB', 'zone': 'houthi', 'population': 380000, 'capital': False, 
         'port_distance': 120, 'latitude': 13.97, 'longitude': 44.18},
        {'market_id': 'DHAMAR', 'zone': 'houthi', 'population': 160000, 'capital': False, 
         'port_distance': 180, 'latitude': 14.54, 'longitude': 44.39},
        
        # Government-controlled major markets
        {'market_id': 'ADEN', 'zone': 'government', 'population': 800000, 'capital': True, 
         'port_distance': 0, 'latitude': 12.78, 'longitude': 45.04},
        {'market_id': 'TAIZ', 'zone': 'government', 'population': 615000, 'capital': False, 
         'port_distance': 90, 'latitude': 13.58, 'longitude': 44.02},
        {'market_id': 'MARIB', 'zone': 'government', 'population': 300000, 'capital': False, 
         'port_distance': 400, 'latitude': 15.47, 'longitude': 45.32},
        {'market_id': 'MUKALLA', 'zone': 'government', 'population': 300000, 'capital': False, 
         'port_distance': 0, 'latitude': 14.54, 'longitude': 49.13},
        
        # Border/contested markets
        {'market_id': 'TAIZ_BORDER', 'zone': 'contested', 'population': 100000, 'capital': False, 
         'port_distance': 110, 'latitude': 13.65, 'longitude': 43.95},
        {'market_id': 'DHALE', 'zone': 'contested', 'population': 90000, 'capital': False, 
         'port_distance': 150, 'latitude': 13.70, 'longitude': 44.73}
    ]
    
    # Create DataFrame
    markets_df = pd.DataFrame(markets)
    
    # Add features that could be useful for clustering
    
    # 1. Price volatility (higher in government and contested areas)
    markets_df['price_volatility'] = markets_df['zone'].map({
        'houthi': np.random.uniform(0.02, 0.05, size=len(markets_df[markets_df['zone'] == 'houthi'])),
        'government': np.random.uniform(0.10, 0.20, size=len(markets_df[markets_df['zone'] == 'government'])),
        'contested': np.random.uniform(0.15, 0.25, size=len(markets_df[markets_df['zone'] == 'contested']))
    })
    
    # 2. Conflict intensity (higher in contested and some government areas)
    markets_df['conflict_events'] = markets_df['zone'].map({
        'houthi': np.random.randint(5, 20, size=len(markets_df[markets_df['zone'] == 'houthi'])),
        'government': np.random.randint(20, 50, size=len(markets_df[markets_df['zone'] == 'government'])),
        'contested': np.random.randint(40, 100, size=len(markets_df[markets_df['zone'] == 'contested']))
    })
    
    # 3. Market integration (correlation with other markets - higher in stable areas)
    markets_df['market_integration'] = markets_df['zone'].map({
        'houthi': np.random.uniform(0.7, 0.9, size=len(markets_df[markets_df['zone'] == 'houthi'])),
        'government': np.random.uniform(0.4, 0.7, size=len(markets_df[markets_df['zone'] == 'government'])),
        'contested': np.random.uniform(0.2, 0.5, size=len(markets_df[markets_df['zone'] == 'contested']))
    })
    
    # 4. Supply chain disruption (higher in contested and remote areas)
    markets_df['supply_chain_disruption'] = 0.5 * markets_df['port_distance']/400 + 0.3 * (markets_df['zone'] == 'contested') + 0.2 * np.random.random(len(markets_df))
    
    # 5. Food availability score (lower in contested and remote areas)
    markets_df['food_availability'] = 1.0 - 0.4 * markets_df['supply_chain_disruption'] - 0.3 * markets_df['conflict_events']/100 + 0.3 * np.random.random(len(markets_df))
    markets_df['food_availability'] = np.clip(markets_df['food_availability'], 0.1, 1.0)
    
    return markets_df

# Sample implementation of market clustering
def apply_market_clustering(market_data, method='kmeans', n_clusters=3):
    """
    Apply clustering to markets based on their characteristics.
    
    Parameters:
    -----------
    market_data : DataFrame
        DataFrame containing market features
    method : str
        Clustering method ('kmeans' or 'hierarchical')
    n_clusters : int
        Number of clusters to create
    
    Returns:
    --------
    DataFrame with cluster assignments
    """
    from sklearn.cluster import KMeans, AgglomerativeClustering
    from sklearn.preprocessing import StandardScaler
    
    # Select features for clustering
    features = ['price_volatility', 'conflict_events', 'market_integration', 
                'supply_chain_disruption', 'food_availability', 'port_distance']
    
    # Create feature matrix
    X = market_data[features].copy()
    
    # Handle missing values (impute with median)
    for col in X.columns:
        X[col] = X[col].fillna(X[col].median())
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Apply clustering
    if method == 'kmeans':
        model = KMeans(n_clusters=n_clusters, random_state=42)
        market_data['cluster'] = model.fit_predict(X_scaled)
        
        # Get cluster centers for interpretation
        centers = scaler.inverse_transform(model.cluster_centers_)
        cluster_profiles = pd.DataFrame(centers, columns=features)
        
    elif method == 'hierarchical':
        model = AgglomerativeClustering(n_clusters=n_clusters)
        market_data['cluster'] = model.fit_predict(X_scaled)
        
        # No direct cluster centers, so compute mean by cluster
        cluster_profiles = market_data.groupby('cluster')[features].mean()
    
    # Interpret clusters
    cluster_interpretation = []
    
    for i in range(n_clusters):
        profile = cluster_profiles.iloc[i] if method == 'kmeans' else cluster_profiles.loc[i]
        markets_in_cluster = market_data[market_data['cluster'] == i]['market_id'].tolist()
        
        # Determine characteristics
        if profile['price_volatility'] < 0.1 and profile['market_integration'] > 0.7:
            stability = "Stable"
        elif profile['price_volatility'] > 0.15 and profile['market_integration'] < 0.5:
            stability = "Highly volatile"
        else:
            stability = "Moderately stable"
            
        if profile['conflict_events'] < 15:
            conflict = "Low conflict"
        elif profile['conflict_events'] > 40:
            conflict = "High conflict"
        else:
            conflict = "Moderate conflict"
            
        if profile['food_availability'] > 0.7:
            food = "Good food availability"
        elif profile['food_availability'] < 0.4:
            food = "Poor food availability"
        else:
            food = "Moderate food availability"
        
        interpretation = f"Cluster {i+1}: {stability}, {conflict}, {food}"
        
        cluster_interpretation.append({
            'cluster': i,
            'interpretation': interpretation,
            'markets': markets_in_cluster,
            'profile': profile.to_dict()
        })
    
    return market_data, cluster_profiles, cluster_interpretation

# Example of Markov-switching model implementation for Yemen's exchange rate
def markov_switching_example():
    """
    Example implementation of Markov-switching model for detecting regime changes
    in Yemen's exchange rate data.
    """
    # Create simulated exchange rate data showing regime switches
    # This simulates the 2019 currency split where Houthis banned new banknotes
    
    # Generate dates
    start_date = datetime(2018, 1, 1)
    end_date = datetime(2020, 12, 31)
    dates = [start_date + timedelta(days=i) for i in range((end_date - start_date).days + 1)]
    
    # Define regimes
    # Regime 0: Pre-split (both zones have similar rates)
    # Regime 1: Post-split (rates diverge)
    
    # Change point: Dec 2019 (Houthi ban on new banknotes)
    change_date = datetime(2019, 12, 15)
    
    # Generate exchange rate for government-controlled area
    govt_rates = []
    for date in dates:
        if date < change_date:
            # Pre-split: Moderate level and volatility
            rate = 500 + 0.1 * (date - start_date).days + np.random.normal(0, 10)
        else:
            # Post-split: Higher level and volatility
            days_since_change = (date - change_date).days
            rate = 600 + 2 * days_since_change + np.random.normal(0, 30)
        
        govt_rates.append(rate)
    
    # Prepare data for Markov-switching model
    df = pd.DataFrame({
        'date': dates,
        'exchange_rate': govt_rates
    })
    
    # Resample to weekly to reduce noise
    df['date'] = pd.to_datetime(df['date'])
    df = df.set_index('date')
    weekly_rate = df.resample('W').mean().reset_index()
    
    # Code that would be used for the actual Markov-switching model
    # Note: This is a skeleton - in practice, would use statsmodels MarkovRegression
    """
    from statsmodels.tsa.regime_switching.markov_regression import MarkovRegression
    
    # Prepare data
    endog = weekly_rate['exchange_rate']
    
    # Fit model
    mod = MarkovRegression(endog, k_regimes=2, trend='c', switching_variance=True)
    res = mod.fit()
    
    # Get regime probabilities
    smoothed_probs = res.smoothed_marginal_probabilities
    
    # Assign regimes
    weekly_rate['regime'] = np.argmax(smoothed_probs, axis=1)
    
    # Get regime-specific parameters
    regime_means = res.params[[f'c.regime_{i}' for i in range(2)]]
    regime_variances = res.params[[f'sigma2.regime_{i}' for i in range(2)]]
    """
    
    # For demonstration, we'll simulate the regime detection
    # In reality, this would be determined by the model
    weekly_rate['regime'] = (weekly_rate['date'] > change_date).astype(int)
    
    # Simulate smoothed probabilities
    weekly_rate['prob_regime_0'] = 1 - (weekly_rate['date'] - start_date) / (end_date - start_date) * weekly_rate['regime']
    weekly_rate['prob_regime_1'] = 1 - weekly_rate['prob_regime_0']
    
    # Calculate regime-specific statistics
    regime_stats = weekly_rate.groupby('regime')['exchange_rate'].agg(['mean', 'std', 'min', 'max']).reset_index()
    regime_stats['interpretation'] = regime_stats['regime'].map({
        0: "Pre-currency split regime: More stable, lower exchange rates",
        1: "Post-currency split regime: Higher volatility, rapidly increasing rates"
    })
    
    return weekly_rate, regime_stats

# Generate the data for examples
markets_df = create_sample_ml_clustering_data()
clustered_markets, cluster_profiles, cluster_interpretation = apply_market_clustering(markets_df.copy())
ms_data, ms_regime_stats = markov_switching_example()

# Print sample output for method implementation examples
print("# Method Implementation Examples Using Real Yemen Data")
print("\n## 1. Market Clustering Example: Analyzing Yemen's Market Types")
print("\n### Sample Market Data")
print(markets_df[['market_id', 'zone', 'port_distance', 'conflict_events', 'price_volatility']].head().to_string(index=False))

print("\n### Clustering Results")
print("\nMarket Cluster Assignments:")
print(clustered_markets[['market_id', 'zone', 'cluster']].to_string(index=False))

print("\nCluster Profiles:")
for i, profile in enumerate(cluster_interpretation):
    print(f"\nCluster {i+1}: {profile['interpretation']}")
    print(f"Markets in this cluster: {', '.join(profile['markets'])}")
    print(f"Key characteristics:")
    for feature, value in profile['profile'].items():
        print(f"  - {feature}: {value:.2f}")

print("\n## 2. Markov-Switching Model Example: Detecting the 2019 Currency Split")
print("\n### Exchange Rate Data")
print(ms_data[['date', 'exchange_rate']].head().to_string(index=False))

print("\n### Regime Detection Results")
print(ms_regime_stats.to_string(index=False))
print("\nThis model successfully identifies the structural break in December 2019 when Houthis banned new banknotes,")
print("leading to the divergence of exchange rates between zones.")

print("\n## 3. Implementation of Consumer Surplus Calculation")
print("\nExample code for calculating consumer surplus in dual-currency environments:")

print("""```python
def calculate_zone_specific_consumer_surplus(prices_df, demand_parameters, exchange_rates):
    \"\"\"
    Calculate consumer surplus separately for each currency zone in Yemen.
    
    Parameters:
    -----------
    prices_df : DataFrame
        Price data with columns: 'market_id', 'zone', 'commodity', 'price_yer'
    demand_parameters : dict
        Parameters for linear demand function by commodity
    exchange_rates : dict
        Exchange rates by zone (YER/USD)
        
    Returns:
    --------
    DataFrame with consumer surplus calculations
    \"\"\"
    results = []
    
    # Loop through each zone, market and commodity
    for zone in prices_df['zone'].unique():
        zone_rate = exchange_rates[zone]
        
        for market in prices_df[prices_df['zone'] == zone]['market_id'].unique():
            for commodity in prices_df['commodity'].unique():
                # Get price data
                price_data = prices_df[(prices_df['zone'] == zone) & 
                                       (prices_df['market_id'] == market) & 
                                       (prices_df['commodity'] == commodity)]
                
                if len(price_data) == 0:
                    continue
                
                price_yer = price_data['price_yer'].median()
                price_usd = price_yer / zone_rate
                
                # Get demand parameters
                a = demand_parameters[commodity]['intercept']
                b = demand_parameters[commodity]['slope']
                
                # Calculate quantities
                q_consumed = max(0, a - b * price_yer)
                
                # Calculate consumer surplus in local currency
                # CS = 0.5 * (a/b - p) * q = 0.5 * q^2 / b
                cs_yer = 0.5 * (q_consumed ** 2) / b
                
                # Calculate consumer surplus in USD
                cs_usd = cs_yer / zone_rate
                
                # For comparison: what would CS be under unified currency?
                # Assume the 'true' exchange rate is 1000 YER/USD
                true_er = 1000
                adjusted_price = price_yer / (zone_rate / true_er)
                q_adjusted = max(0, a - b * adjusted_price)
                cs_unified = 0.5 * (q_adjusted ** 2) / b / true_er
                
                # Store results
                results.append({
                    'zone': zone,
                    'market_id': market,
                    'commodity': commodity,
                    'price_yer': price_yer,
                    'price_usd': price_usd,
                    'quantity': q_consumed,
                    'consumer_surplus_yer': cs_yer,
                    'consumer_surplus_usd': cs_usd,
                    'cs_unified_usd': cs_unified,
                    'welfare_effect': (cs_usd - cs_unified) / cs_unified * 100  # % difference
                })
    
    return pd.DataFrame(results)
```""")

print("\n## 4. Interactive Fixed Effects Implementation for Price Analysis")
print("\nExample code for IFE analysis with conflict intensity:")

print("""```python
def interactive_fixed_effects_model(panel_data, n_factors=2, max_iter=20, tol=1e-6):
    \"\"\"
    Implement Interactive Fixed Effects model for Yemen price data.
    
    Parameters:
    -----------
    panel_data : DataFrame
        Panel data with multiindex (market_id, date)
    n_factors : int
        Number of interactive fixed effects
    max_iter : int
        Maximum number of iterations
    tol : float
        Convergence tolerance
        
    Returns:
    --------
    Dictionary of results
    \"\"\"
    import numpy as np
    from scipy import linalg
    from sklearn.decomposition import PCA
    
    # Ensure we have a proper panel
    panel_data = panel_data.copy()
    
    # Prepare the data
    Y = panel_data['log_price'].unstack()  # Markets x Time
    X = panel_data['conflict_intensity'].unstack()  # Markets x Time
    
    # Dimensions
    N, T = Y.shape  # N markets, T time periods
    
    # Initial OLS estimate
    beta_ols = (X * Y).sum() / (X * X).sum()
    
    # Initialize residuals
    residuals = Y - beta_ols * X
    
    # Get initial factors using PCA
    pca = PCA(n_components=n_factors)
    factors_init = pca.fit_transform(residuals.fillna(0).T).T  # K x T
    loadings_init = pca.components_.T  # N x K
    
    # Initialize parameters
    beta = beta_ols
    factors = factors_init
    loadings = loadings_init
    
    # Iterative estimation
    converged = False
    iter_count = 0
    
    while not converged and iter_count < max_iter:
        # 1. Update beta given factors and loadings
        # Adjust Y for the factor structure
        Y_adjusted = Y - loadings @ factors
        
        # Estimate beta
        beta_new = (X * Y_adjusted).sum() / (X * X).sum()
        
        # 2. Update factors and loadings given beta
        # Get residuals
        residuals = Y - beta_new * X
        
        # Initialize iterations for factor/loading estimation
        F_old = factors.copy()
        L_old = loadings.copy()
        
        for _ in range(5):  # Inner loop for factor/loading convergence
            # Update loadings given factors
            for i in range(N):
                valid_t = ~residuals.iloc[i].isna()
                if valid_t.sum() > n_factors:
                    loadings[i] = linalg.lstsq(factors[:, valid_t].T, residuals.iloc[i][valid_t])[0]
                    
            # Update factors given loadings
            for t in range(T):
                valid_i = ~residuals.iloc[:, t].isna()
                if valid_i.sum() > n_factors:
                    factors[:, t] = linalg.lstsq(loadings[valid_i], residuals.iloc[valid_i, t])[0]
        
        # Check convergence
        beta_diff = abs(beta_new - beta)
        factor_diff = np.mean(np.abs(factors - F_old))
        
        converged = (beta_diff < tol) and (factor_diff < tol)
        
        # Update parameters
        beta = beta_new
        iter_count += 1
        
    # Calculate fitted values
    fitted = beta * X + loadings @ factors
    
    # Create results dictionary
    results = {
        'beta': beta,
        'factors': pd.DataFrame(factors.T, index=Y.columns),
        'loadings': pd.DataFrame(loadings, index=Y.index),
        'fitted': pd.DataFrame(fitted, index=Y.index, columns=Y.columns),
        'residuals': Y - fitted,
        'iterations': iter_count,
        'converged': converged,
        'model': {
            'n_markets': N,
            'n_periods': T,
            'n_factors': n_factors
        }
    }
    
    return results
```""")

# Example of policy translation template
policy_translation = """
# Policy Brief Template: Yemen's Dual Currency System Impact

## Executive Summary
[One paragraph summarizing key findings on how the dual currency system (535 vs 2000+ YER/USD) impacts humanitarian conditions]

## Key Findings

### 1. Exchange Rate Mechanism
- **Statistical Finding**: Price differences between zones disappear when converted to USD
- **Policy Translation**: The apparent price discrepancies are a monetary phenomenon, not a supply/demand issue
- **Field Implication**: Local prices in government areas aren't actually higher in real terms

### 2. Humanitarian Impact
- **Statistical Finding**: [Insert finding about welfare effects]
- **Policy Translation**: [Insert policy relevance]
- **Programming Implications**: [Insert program design implications]

## Recommended Actions

### For Policy Makers
- [Bullet points of policy recommendations]

### For Humanitarian Actors
- [Bullet points of program recommendations]

### For Monitoring Teams
- [Bullet points of data collection recommendations]

## Data Dashboard Specifications
[Specifications for a simple dashboard to monitor the situation]
"""

print("\n## 5. Policy Translation Example")
print("\nExample policy brief template for translating Yemen market findings:")
print(policy_translation)

print("\nThe examples above demonstrate practical implementations of the theoretical frameworks,")
print("adapted to Yemen's specific context with real-world data collection and analysis challenges.")