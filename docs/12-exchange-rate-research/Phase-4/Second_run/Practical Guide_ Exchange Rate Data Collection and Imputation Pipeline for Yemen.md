# Practical Guide: Exchange Rate Data Collection and Imputation Pipeline for Yemen

**Version:** 1.0
**Date:** June 2, 2025
**Audience:** Field Data Collectors, Data Analysts, Researchers

## 1. Introduction

This guide provides practical steps for collecting, cleaning, validating, and imputing Yemeni Rial (YER) to US Dollar (USD) exchange rate data in Yemen's complex, conflict-affected environment. Accurate exchange rate data is crucial for analyzing market dynamics, consumer welfare, and the impact of humanitarian aid, especially given the significant divergence between currency zones.

The primary challenge stems from the fragmented currency system:

*   **Houthi-controlled areas:** Relatively stable rate around 535-539 YER/USD.
*   **Government-controlled areas:** Highly depreciated and volatile rate, often exceeding 2,000 YER/USD.
*   **Parallel markets:** Exist alongside official rates, adding complexity.

This pipeline aims to create a consistent, reliable monthly exchange rate series for each relevant market, integrating data from various sources while acknowledging field realities.

## 2. Understanding Yemen's Exchange Rate Landscape

Before collecting data, it's vital to understand the context:

*   **Dual Zones:** The most significant feature is the split between Houthi-controlled northern areas (stable, lower rate) and internationally recognized government-controlled southern/eastern areas (depreciated, higher rate). Refer to `exchange_rate_implementation.md` for governorate mappings.
*   **Multiple Rates:** Within zones, multiple rates can coexist: official Central Bank rates (CBY-Aden, CBY-Sana'a), parallel market rates (money changers), and rates used by specific actors (NGOs, traders).
*   **Volatility & Data Gaps:** Rates, especially in government areas, can be highly volatile. Conflict can disrupt reporting, leading to data gaps.

## 3. Data Source Hierarchy and Prioritization

Given accessibility issues and varying reliability, prioritize sources as follows:

1.  **WFP Global Market Monitor (via HDX):** **Primary Accessible Source.** Often includes subnational exchange rate data. Check HDX regularly.
    *   *Link:* [https://data.humdata.org/dataset/global-market-monitor](https://data.humdata.org/dataset/global-market-monitor) (Verify this link and specific Yemen data availability within the dataset as per `available-sources.md`).
2.  **Official Central Bank Rates (CBY-Aden / CBY-Sana'a):** Use if accessible, reliable, and relevant to market transactions. Often highly distorted or unavailable (`available-sources.md`).
3.  **Yemen Cash Consortium / UN OCHA Trackers:** Often provide reliable, aggregated market rates. Check ReliefWeb or OCHA Yemen updates.
4.  **Direct Field Collection (Money Changers/Traders):** Essential for capturing real-time parallel market rates, especially where official/aggregated data is lacking or untrusted. Requires careful validation.
5.  **NGO Reports / Other Partners:** Can provide supplementary data points but require verification.

**Prioritization Logic:** Prefer verified, regularly updated sources (WFP GMM, Consortiums). Use official rates cautiously. Rely on field collection for parallel rates but triangulate rigorously. Always document the source for each data point.

## 4. Data Collection Protocols

**Frequency:** Aim for daily or weekly collection where feasible, especially for volatile parallel market rates. This high-frequency data will be aggregated (e.g., monthly average or end-of-month) for panel analysis, but daily data helps validation and understanding short-term dynamics.

**Governorate-Specific Considerations:**

*   **Houthi Zones (Sana'a, Sa'ada, Hajjah, etc.):**
    *   *Expected Rate:* Around 535-539 YER/USD (relatively stable).
    *   *Sources:* Check WFP GMM/Consortiums. Monitor CBY-Sana'a rate (if published). Collect parallel rates from trusted money changers (expect small deviations from official).
*   **Government Zones (Aden, Marib, Hadramaut, etc.):**
    *   *Expected Rate:* Highly variable, 2000+ YER/USD.
    *   *Sources:* Check WFP GMM/Consortiums. Monitor CBY-Aden rate (if published, often lags market). **Crucially, collect parallel market rates frequently** from multiple money changers.
*   **Contested Zones (Taiz city, front-line areas):**
    *   *Expected Rate:* Can fluctuate significantly, potentially between Houthi and Government levels.
    *   *Sources:* Collect from multiple available sources (traders, NGOs operating locally, WFP GMM if available). Note potential for rapid shifts based on control changes or conflict events.

**Field Collection Protocol (if direct collection is necessary):**

*   **Identify Sources:** Establish relationships with 2-3 trusted, active money changers or major traders in each target market.
*   **Collect:** Record **buy** and **sell** rates for USD (usually for cash transactions). Note the time of collection precisely.
*   **Use Template:** Employ the standardized `Data Collection Form` (see Section 8).
*   **Assess Reliability:** Assign a reliability score (1-5) based on source reputation, consistency, and transparency.
*   **Safety First:** Data collectors must prioritize their safety and follow all security protocols. Avoid unnecessary risks.

## 5. Data Cleaning and Validation

Raw exchange rate data requires careful cleaning and validation:

*   **Outlier Detection:** Flag rates falling outside plausible bounds (e.g., <400 or >3500 YER/USD, adjust based on current context) or showing extreme daily jumps (>10-15% without clear news trigger). Investigate flagged values – they might be typos or represent real shocks.
    *   *Method:* Calculate rolling 7-day averages and standard deviations. Flag points > 3 standard deviations from the mean. Compare with rates in adjacent markets or the zone average.
*   **Cross-Source Triangulation:** Compare rates from different sources for the same market and day. If discrepancies are large (>5%), investigate why (different times, different transaction types, source error?). Document the rate chosen for the final dataset and the justification.
*   **Timestamp Alignment:** This is critical for merging with price data (e.g., WFP). Record the exact date and, if possible, time (AM/PM) of the exchange rate observation. When merging with monthly price data, decide on an aggregation rule (e.g., average daily rates over the month, end-of-month rate, rate on the day of price collection). *Recommendation:* Use the average rate for the month, but also record the end-of-month rate for sensitivity checks.
*   **Use Checklist:** Systematically apply the `Validation Checklist` (see Section 8) to each data point or batch.

## 6. Missing Data Imputation

Missing exchange rate data is common, especially during intense conflict. Use a structured approach:

*   **Identify Patterns:** Determine if missingness is sporadic (few days) or prolonged (weeks/months), and if it correlates with conflict events or market closures.
*   **Use Decision Tree:** Follow the `Imputation Decision Tree` (see Section 8).
*   **Imputation Methods (in order of preference):**
    1.  **Short Gaps (1-3 consecutive periods):** Use forward fill (ffill) or backward fill (bfill) from the *same source* within the *same market*. Prefer ffill.
    2.  **Medium Gaps (4-10 consecutive periods):** Linear interpolation between the last observed point before the gap and the first observed point after the gap *within the same market/source*. Use with caution if the period involves high volatility.
    3.  **Long Gaps / Conflict Periods (>10 periods or known disruption):**
        *   **Method A (Preferred): Zone Average.** Use the average rate reported for *other markets within the same currency zone* during the missing period. If WFP GMM or Consortium data is available for the zone, use that average.
        *   **Method B (Fallback): Static Zone Rate.** If zone averages are unavailable, use a representative static rate for the zone (e.g., 535 for Houthi, a recent stable average like 2100 for Government - update this periodically). This is a strong assumption.
        *   **Method C (Advanced): Model-Based.** Predict missing rates using a simple model based on rates in other zones, conflict intensity, and global factors. (Reference `panel-construction.md`). *Use only if simpler methods fail and document heavily.*
*   **Flagging:** **Crucially, always add a binary indicator column (`is_imputed`) to flag any imputed values.** This allows for sensitivity analysis excluding imputed data.

**Conceptual Python Example (Zone Average Imputation):**

```python
import pandas as pd
import numpy as np

def impute_exchange_rates(df, max_interpolate_gap=10):
    """Imputes missing exchange rates using ffill, interpolation, and zone averages."""
    df = df.sort_values(by=['market_id', 'date'])
    df['exchange_rate_imputed'] = df['exchange_rate'].copy()
    df['is_imputed'] = df['exchange_rate'].isnull()

    # 1. Forward fill short gaps (within market)
    df['exchange_rate_imputed'] = df.groupby('market_id')['exchange_rate_imputed'].ffill(limit=3)

    # 2. Interpolate medium gaps (within market)
    df['exchange_rate_imputed'] = df.groupby('market_id')['exchange_rate_imputed'].transform(
        lambda x: x.interpolate(method='linear', limit=max_interpolate_gap, limit_direction='both')
    )

    # 3. Zone average for remaining gaps
    # Calculate monthly zone averages from available data
    zone_monthly_avg = df.groupby(['currency_zone', 'date'])['exchange_rate_imputed'].mean().reset_index()
    zone_monthly_avg = zone_monthly_avg.rename(columns={'exchange_rate_imputed': 'zone_avg_rate'})

    # Merge zone averages back
    df = pd.merge(df, zone_monthly_avg, on=['currency_zone', 'date'], how='left')

    # Apply zone average where still missing
    remaining_missing = df['exchange_rate_imputed'].isnull()
    df.loc[remaining_missing, 'exchange_rate_imputed'] = df.loc[remaining_missing, 'zone_avg_rate']

    # Fallback: Use static zone rate if zone average is also missing (e.g., no data for zone in that month)
    static_zone_rates = {'houthi': 535, 'government': 2100, 'contested': 1300} # Example rates
    final_missing = df['exchange_rate_imputed'].isnull()
    df.loc[final_missing, 'exchange_rate_imputed'] = df.loc[final_missing, 'currency_zone'].map(static_zone_rates)

    df['is_imputed'] = df['is_imputed'] | df['exchange_rate'].isnull() # Ensure flag is set if any imputation happened
    df = df.drop(columns=['zone_avg_rate'])
    return df

# Assuming 'df' has columns: 'date', 'market_id', 'currency_zone', 'exchange_rate'
# df = impute_exchange_rates(df)
```

## 7. Practical Example: 
