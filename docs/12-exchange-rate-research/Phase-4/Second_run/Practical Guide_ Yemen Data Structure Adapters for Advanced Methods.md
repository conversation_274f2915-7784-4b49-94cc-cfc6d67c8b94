# Practical Guide: Yemen Data Structure Adapters for Advanced Methods

**Version:** 1.0
**Date:** June 2, 2025
**Audience:** Data Analysts, Econometricians, Researchers

## 1. Introduction

This guide provides practical code examples and procedures for transforming raw data commonly available for Yemen (like WFP price data) into the structured formats required for the advanced econometric and machine learning methods outlined in the project's methodology guides (`MethodologyIntegrationGuide...`, `Step-by-Step...`).

Advanced methods often require specific data structures:

*   **Panel Regression (IFE, Bayesian):** Long format panel (entity × time) with specific columns for fixed effects, regressors.
*   **Time Series (MSM, STAR, Nowcasting):** Wide format (time × entity) or individual time series (`pandas` Series).
*   **Machine Learning (Clustering, Prediction):** Feature matrices (potentially market-level aggregates or panel features).

This guide focuses on creating the primary **long format panel** (market × commodity × time) as detailed in `panel-construction.md` and `yemen_panel_methodology.md`, which serves as the foundation for many analyses.

## 2. Core Data Sources and Initial Structure

We primarily rely on WFP Food Prices data (often downloaded from HDX as CSV) and ACLED conflict data.

**Typical WFP Raw Data Structure (Example Columns):**

*   `adm0_name` (Country)
*   `adm1_name` (Governorate)
*   `adm2_name` (District)
*   `mkt_name` (Market Name)
*   `cm_name` (Commodity Name)
*   `mp_month` (Month as integer)
*   `mp_year` (Year as integer)
*   `mp_price` (Price)
*   `mp_currency` (Currency - YER/USD)
*   `um_name` (Unit)
*   `um_size` (Unit Size)

**Target Panel Structure (Long Format):**

*   Index: `market_id`, `commodity_id`, `date`
*   Columns: `price_yer`, `price_usd`, `log_price_usd`, `conflict_intensity`, `currency_zone`, `is_imputed`, etc.

## 3. Adapters and Transformation Steps (Python Examples)

We will use `pandas` for data manipulation.

### Step 1: Load and Basic Cleaning

```python
import pandas as pd
import numpy as np

def load_and_clean_wfp(filepath):
    """Loads raw WFP CSV and performs initial cleaning."""
    df = pd.read_csv(filepath)

    # Rename columns for consistency (adjust based on actual CSV headers)
    rename_map = {
        'adm1_name': 'governorate',
        'mkt_name': 'market_name',
        'cm_name': 'commodity_name',
        'mp_year': 'year',
        'mp_month': 'month',
        'mp_price': 'price',
        'mp_currency': 'currency',
        'um_name': 'unit',
        # Add other necessary renames
    }
    df = df.rename(columns=rename_map)

    # Create a proper date column
    # Handle potential errors if year/month are not numeric
    df['year'] = pd.to_numeric(df['year'], errors='coerce')
    df['month'] = pd.to_numeric(df['month'], errors='coerce')
    df = df.dropna(subset=['year', 'month'])
    df['date'] = pd.to_datetime(df['year'].astype(int).astype(str) + '-' + df['month'].astype(int).astype(str) + '-01')

    # Select relevant columns (adjust as needed)
    cols_to_keep = [
        'governorate', 'market_name', 'commodity_name',
        'date', 'year', 'month', 'price', 'currency', 'unit'
    ]
    df = df[cols_to_keep]

    # Drop rows with missing critical info
    df = df.dropna(subset=['date', 'market_name', 'commodity_name', 'price'])

    # Convert price to numeric, coercing errors
    df['price'] = pd.to_numeric(df['price'], errors='coerce')
    df = df.dropna(subset=['price'])
    df = df[df['price'] > 0] # Remove zero or negative prices

    print(f"Loaded and cleaned WFP data: {df.shape[0]} rows")
    return df

# Example usage:
# wfp_raw_df = load_and_clean_wfp("/path/to/wfp_food_prices_yem.csv")
```

### Step 2: Standardize Identifiers (Markets, Commodities, Governorates)

Yemen data often suffers from inconsistent naming. Create mapping dictionaries based on project standards (referencing lists in `panel-construction.md`).

```python
# --- Standardization Mappings (Maintain these centrally) ---
GOVERNORATE_MAP = {
    'Sana\'a City': 'Sana\'a', 'Sanaa': 'Sana\'a', 'Amanat Al Asimah': 'Sana\'a',
    'Al Hudaydah': 'Al Hudaydah', 'Hodeida': 'Al Hudaydah',
    'Taiz': 'Taiz', 'Taizz': 'Taiz',
    'Aden': 'Aden',
    'Marib': 'Marib', 'Ma\'rib': 'Marib',
    # ... add all variations encountered ...
}

MARKET_MAP = {
    'Sana\'a City': 'SANA', 'Sanaa': 'SANA',
    'Aden': 'ADEN', 'Aden City': 'ADEN',
    'Taiz City': 'TAIZ', 'Taiz': 'TAIZ',
    'Hodeidah City': 'HODEIDAH', 'Al Hudaydah': 'HODEIDAH',
    'Marib City': 'MARIB', 'Marib': 'MARIB',
    # ... add all variations ...
}

COMMODITY_MAP = {
    'Wheat': 'wheat', 'Wheat flour': 'wheat_flour', 'Flour (wheat)': 'wheat_flour',
    'Rice (imported)': 'rice_imported', 'Rice (local)': 'rice_local', 'Rice': 'rice_imported', # Default to imported if unspecified
    'Sugar': 'sugar',
    'Oil (cooking)': 'cooking_oil', 'Vegetable oil': 'cooking_oil',
    'Beans (kidney, red)': 'beans_kidney_red', 'Beans (white)': 'beans_white', 'Beans': 'beans_kidney_red', # Default
    'Lentils': 'lentils',
    'Fuel (diesel)': 'diesel', 'Diesel': 'diesel',
    'Fuel (petrol)': 'petrol', 'Petrol': 'petrol',
    'Gas (cooking)': 'cooking_gas', 'LPG': 'cooking_gas',
    # ... add all variations ...
}
# -----------------------------------------------------------

def standardize_identifiers(df):
    """Applies standard names for governorates, markets, and commodities."""
    df['governorate_std'] = df['governorate'].replace(GOVERNORATE_MAP)
    df['market_id'] = df['market_name'].replace(MARKET_MAP)
    df['commodity_id'] = df['commodity_name'].replace(COMMODITY_MAP)

    # Handle unmapped entries (log them)
    unmapped_gov = df[~df['governorate'].isin(GOVERNORATE_MAP.keys()) & ~df['governorate_std'].isin(GOVERNORATE_MAP.values())]['governorate'].unique()
    unmapped_mkt = df[~df['market_name'].isin(MARKET_MAP.keys()) & ~df['market_id'].isin(MARKET_MAP.values())]['market_name'].unique()
    unmapped_com = df[~df['commodity_name'].isin(COMMODITY_MAP.keys()) & ~df['commodity_id'].isin(COMMODITY_MAP.values())]['commodity_name'].unique()

    if len(unmapped_gov) > 0: print(f"Warning: Unmapped Governorates: {unmapped_gov}")
    if len(unmapped_mkt) > 0: print(f"Warning: Unmapped Markets: {unmapped_mkt}")
    if len(unmapped_com) > 0: print(f"Warning: Unmapped Commodities: {unmapped_com}")

    # Keep only rows where standard IDs could be assigned
    df = df.dropna(subset=['market_id', 'commodity_id', 'governorate_std'])

    print(f"Standardized identifiers. Rows remaining: {df.shape[0]}")
    return df

# Example usage:
# wfp_std_df = standardize_identifiers(wfp_raw_df)
```

### Step 3: Standardize Units and Convert Prices

Prices need to be in a consistent unit (e.g., YER per KG or L). Refer to `panel-construction.md` for unit conversion logic.

```python
# --- Unit Conversion Factors (Maintain centrally) ---
# Target: YER per KG (solids) or YER per Liter (liquids)
UNIT_CONVERSION_FACTORS = {
    # Commodity: {Unit: Factor to multiply price by to get price per KG/Liter}
    'wheat': {'KG': 1, '50 KG': 1/50, '10 KG': 1/10, 'Bag (50kg)': 1/50},
    'wheat_flour': {'KG': 1, '50 KG': 1/50, 'Bag (50kg)': 1/50},
    'rice_imported': {'KG': 1, '50 KG': 1/50, 'Bag (50kg)': 1/50},
    'rice_local': {'KG': 1, '50 KG': 1/50, 'Bag (50kg)': 1/50},
    'sugar': {'KG': 1, '50 KG': 1/50, 'Bag (50kg)': 1/50},
    'cooking_oil': {'L': 1, '750 ML': 1/0.75, '1.8 L': 1/1.8, 'Gallon': 1/3.785, 'Bottle (1L)': 1},
    'diesel': {'L': 1, 'Gallon': 1/3.785, '20 L': 1/20},
    'petrol': {'L': 1, 'Gallon': 1/3.785, '20 L': 1/20},
    'cooking_gas': {'12.5 KG Cylinder': 1/12.5, 'Cylinder': 1/12.5}, # Price per KG
    # ... add all commodities and units encountered ...
}
# -----------------------------------------------------

def standardize_units_and_prices(df):
    """Converts prices to standard units (YER/kg or YER/L)."""
    df['price_standard_unit'] = np.nan
    df['standard_unit'] = ''

    for commodity, conversions in UNIT_CONVERSION_FACTORS.items():
        target_unit = 'KG' if commodity != 'cooking_oil' and 'diesel' not in commodity and 'petrol' not in commodity else 'L'
        commodity_mask = df['commodity_id'] == commodity
        df.loc[commodity_mask, 'standard_unit'] = target_unit

        for unit, factor in conversions.items():
            unit_mask = commodity_mask & (df['unit'] == unit)
            if unit_mask.any():
                df.loc[unit_mask, 'price_standard_unit'] = df.loc[unit_mask, 'price'] * factor

    # Handle unmapped units (log them)
    unmapped_units = df[df['price_standard_unit'].isnull()][['commodity_id', 'unit']].drop_duplicates()
    if not unmapped_units.empty:
        print(f"Warning: Unmapped units found: \n{unmapped_units}")

    # Drop rows where unit conversion failed
    df = df.dropna(subset=['price_standard_unit'])

    # Ensure prices are in YER (convert USD if present, requires exchange rate)
    # Placeholder: Assume input price is YER for now. Currency conversion needs exchange rate data.
    df['price_yer'] = df['price_standard_unit']
    # TODO: Add USD conversion using exchange rate pipeline output
    # df['price_usd'] = df['price_yer'] / df['exchange_rate']

    print(f"Standardized units. Rows remaining: {df.shape[0]}")
    return df[['market_id', 'commodity_id', 'date', 'governorate_std', 'price_yer', 'standard_unit']]

# Example usage:
# wfp_unit_std_df = standardize_units_and_prices(wfp_std_df)
```

### Step 4: Aggregate to Monthly Panel and Handle Duplicates

Data might have multiple observations per market/commodity/month. Aggregate using a robust measure like the median.

```python
def aggregate_to_monthly_panel(df):
    """Aggregates data to a unique monthly observation per market/commodity."""
    # Group by the panel identifiers
    grouped = df.groupby(['market_id', 'commodity_id', 'date'])

    # Aggregate price using median (robust to outliers)
    panel = grouped['price_yer'].median().reset_index()

    # Keep first observed governorate and unit for reference
    ref_data = grouped[['governorate_std', 'standard_unit']].first().reset_index()
    panel = pd.merge(panel, ref_data, on=['market_id', 'commodity_id', 'date'], how='left')

    # Check for remaining duplicates (should not happen after median)
    if panel.duplicated(subset=['market_id', 'commodity_id', 'date']).any():
        print("Error: Duplicates remain after aggregation!")
        # Handle duplicates further if necessary (e.g., drop)

    print(f"Aggregated to monthly panel. Rows: {panel.shape[0]}")
    return panel

# Example usage:
# monthly_panel_df = aggregate_to_monthly_panel(wfp_unit_std_df)
```

### Step 5: Create Full Panel Structure and Merge

Create a complete panel index (all market × commodity × date combinations) and merge the observed data into it. This makes missing values explicit (NaN).

```python
def create_full_panel_structure(observed_panel, start_date_str, end_date_str):
    """Creates a full panel index and merges observed data."""
    markets = observed_panel['market_id'].unique()
    commodities = observed_panel['commodity_id'].unique()
    dates = pd.date_range(start=start_date_str, end=end_date_str, freq='MS')

    # Create MultiIndex
    full_index = pd.MultiIndex.from_product([markets, commodities, dates],
                                            names=['market_id', 'commodity_id', 'date'])

    # Create DataFrame from index
    full_panel = pd.DataFrame(index=full_index).reset_index()

    # Merge observed data
    # Keep governorate/unit info associated with market/commodity
    ref_info = observed_panel[['market_id', 'commodity_id', 'governorate_std', 'standard_unit']].drop_duplicates()
    full_panel = pd.merge(full_panel, ref_info, on=['market_id', 'commodity_id'], how='left')

    # Merge price data
    full_panel = pd.merge(full_panel, observed_panel[['market_id', 'commodity_id', 'date', 'price_yer']],
                          on=['market_id', 'commodity_id', 'date'], how='left')

    print(f"Created full panel structure. Rows: {full_panel.shape[0]}")
    return full_panel

# Example usage:
# start_date = '2019-01-01'
# end_date = '2024-12-01'
# full_panel_df = create_full_panel_structure(monthly_panel_df, start_date, end_date)
```

### Step 6: Handle Missing Price Data (Yemen Specifics)

Missing data in Yemen is often linked to conflict. Simple interpolation can be misleading. Use methods from `panel-construction.md` and `exchange-rate-data-pipeline.md` (imputation section).

```python
def handle_missing_panel_prices(panel_df, method='mixed', conflict_df=None):
    """Handles missing prices in the full panel structure."""
    panel_df = panel_df.sort_values(by=['market_id', 'commodity_id', 'date'])
    panel_df['price_yer_filled'] = panel_df['price_yer'].copy()
    panel_df['is_imputed'] = panel_df['price_yer'].isnull()

    if method == 'mixed':
        # 1. Forward fill short gaps (<= 2 months) within entity
        panel_df['price_yer_filled'] = panel_df.groupby(['market_id', 'commodity_id'])['price_yer_filled'].ffill(limit=2)

        # 2. Interpolate medium gaps (3-6 months) within entity
        panel_df['price_yer_filled'] = panel_df.groupby(['market_id', 'commodity_id'])['price_yer_filled'].transform(
            lambda x: x.interpolate(method='linear', limit=6, limit_direction='both')
        )

        # 3. Consider conflict-aware imputation for remaining (Requires conflict data)
        # Example: If conflict > threshold, don't interpolate, leave NaN or use specific model
        if conflict_df is not None:
            # Merge conflict intensity here
            # Apply logic: e.g., panel_df.loc[panel_df['conflict_intensity'] > T, 'price_yer_filled'] = np.nan
            pass # Add conflict-aware logic if needed

        # 4. Fallback: Impute using market average for that commodity/month (use with caution)
        market_monthly_avg = panel_df.groupby(['commodity_id', 'date'])['price_yer_filled'].transform('mean')
        remaining_missing = panel_df['price_yer_filled'].isnull()
        panel_df.loc[remaining_missing, 'price_yer_filled'] = market_monthly_avg[remaining_missing]

    elif method == 'model_based':
        # Implement model-based imputation (e.g., using RF as in panel-construction.md)
        pass

    # Ensure flag is accurate
    panel_df['is_imputed'] = panel_df['is_imputed'] | panel_df['price_yer'].isnull()

    # Drop rows where price is still missing after all attempts (optional)
    # panel_df = panel_df.dropna(subset=['price_yer_filled'])

    print(f"Handled missing prices using '{method}' method.")
    return panel_df

# Example usage:
# final_panel_df = handle_missing_panel_prices(full_panel_df, method='mixed')
```

### Step 7: Add Other Variables (Conflict, Exchange Rates, Controls)

Merge other relevant datasets prepared according to their respective pipelines.

```python
def add_explanatory_variables(panel_df, conflict_data=None, exchange_rate_data=None, controls_data=None):
    """Merges conflict, exchange rate, and other control variables."""

    # Merge Conflict Data (assuming monthly conflict intensity per market)
    if conflict_data is not None:
        # Ensure conflict_data has 'market_id', 'date', 'conflict_intensity'
        panel_df = pd.merge(panel_df, conflict_data[['market_id', 'date', 'conflict_intensity']],
                              on=['market_id', 'date'], how='left')
        panel_df['conflict_intensity'] = panel_df['conflict_intensity'].fillna(0) # Assume 0 if no data

    # Merge Exchange Rate Data (from exchange-rate-data-pipeline.md output)
    if exchange_rate_data is not None:
        # Ensure exchange_rate_data has 'market_id', 'date', 'exchange_rate', 'currency_zone'
        panel_df = pd.merge(panel_df, exchange_rate_data[['market_id', 'date', 'exchange_rate', 'currency_zone']],
                              on=['market_id', 'date'], how='left')
        # Handle missing exchange rates (e.g., ffill by market, then zone average)
        panel_df['exchange_rate'] = panel_df.groupby('market_id')['exchange_rate'].ffill()
        # ... add more robust filling if needed ...

    # Merge Other Controls (e.g., population, distance, rainfall)
    if controls_data is not None:
        # Merge based on appropriate keys (market_id, date, governorate_std)
        pass

    # Calculate derived variables (e.g., USD prices, log prices)
    if 'exchange_rate' in panel_df.columns:
        panel_df['price_usd'] = panel_df['price_yer_filled'] / panel_df['exchange_rate']
        panel_df['log_price_usd'] = np.log(panel_df['price_usd'].replace(0, np.nan)) # Handle potential zeros

    panel_df['log_price_yer'] = np.log(panel_df['price_yer_filled'].replace(0, np.nan))

    print("Added explanatory variables.")
    return panel_df

# Example usage:
# Assume conflict_monthly, exchange_rates_monthly are prepared DataFrames
# final_panel_df = add_explanatory_variables(final_panel_df, conflict_monthly, exchange_rates_monthly)
```

## 4. Error Handling and Common Issues

*   **Inconsistent Naming:** Rigorously apply standardization maps (Step 2). Log and investigate unmapped entries.
*   **Unit Errors:** Double-check `UNIT_CONVERSION_FACTORS` (Step 3). Add visual checks (e.g., plot price distributions per commodity) to spot unit errors.
*   **Duplicate Entries:** Use robust aggregation (median) and checks (Step 4).
*   **Misleading Imputation:** Always flag imputed values (Step 6). Perform sensitivity analysis excluding imputed data.
*   **Merge Errors:** Ensure keys (`market_id`, `commodity_id`, `date`) are consistent across all datasets before merging (Step 7).
*   **Zero/Negative Prices:** Filter these out during initial cleaning (Step 1) or handle before log transformations.

## 5. Output Format for Advanced Methods

*   **Long Panel (for `linearmodels`, `pyhdfe`, ML feature engineering):**
    *   Index: `market_id`, `commodity_id`, `date`
    *   Columns: `price_usd`, `log_price_usd`, `conflict_intensity`, `currency_zone`, `is_imputed`, other controls, fixed effect identifiers.
    *   Save as Pickle (`.pkl`) or Feather (`.feather`) for efficient I/O.
    ```python
    # final_panel_df.set_index(['market_id', 'commodity_id', 'date'], inplace=True)
    # final_panel_df.to_pickle("/path/to/yemen_panel_long.pkl")
    ```
*   **Wide Panel (for DFM, some VARs):**
    *   Index: `date`
    *   Columns: MultiIndex (`market_id`, `commodity_id`)
    *   Values: `price_usd` or `log_price_usd`
    ```python
    # wide_panel = final_panel_df.pivot_table(index='date', columns=['market_id', 'commodity_id'], values='log_price_usd')
    # wide_panel.to_pickle("/path/to/yemen_panel_wide.pkl")
    ```
*   **Individual Time Series (for `statsmodels.tsa`, MSM, STAR):**
    *   Extract specific series from the long or wide panel.
    ```python
    # wheat_sana_ts = final_panel_df.loc[('SANA', 'wheat'), 'log_price_usd']
    ```

This guide provides the core adapters to transform raw Yemen data into usable formats. Analysts should adapt and extend these steps based on the specific requirements of their chosen advanced methods and any newly encountered data peculiarities.
