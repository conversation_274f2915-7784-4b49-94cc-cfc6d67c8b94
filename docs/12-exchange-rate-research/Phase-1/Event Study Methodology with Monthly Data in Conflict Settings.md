# Event Study Methodology with Monthly Data in Conflict Settings

**Version:** 1.0
**Date:** June 1, 2025
**Prepared for:** Yemen Market Integration Research Project

## 1. Introduction

This document details the event study methodology adapted for analyzing the impact of specific conflict-related events on market outcomes (e.g., prices, volatility, integration measures) using **monthly** data within the Yemen context. Standard event study methodologies often rely on daily or weekly data, but applying this framework to monthly data requires specific adjustments to define event windows, estimate normal returns (counterfactuals), and conduct statistical inference, especially given the complexities of a conflict environment.

Consistent with user instructions, this methodology is optimized for **monthly data frequency**. We focus on identifying the average abnormal effect of predefined events (e.g., major battles, sieges, policy shocks related to currency) on market variables around the time of the event.

This guide covers:
- Defining Events and Event Windows with Monthly Data
- Estimating Normal Performance (Counterfactual)
- Calculating Abnormal Returns (AR) and Cumulative Abnormal Returns (CAR)
- Statistical Inference: Hypothesis Testing for AR and CAR
- Confidence Interval Construction
- Addressing Confounding Factors and Robustness Checks
- Mathematical Frameworks and Implementation Notes

## 2. Defining Events and Event Windows with Monthly Data

**Challenge:** Monthly data aggregates effects occurring within the month. An event occurring mid-month might have its impact partially reflected in the event month and partially in the following month.

**Protocol:**

1.  **Event Definition:** Clearly define the specific type of event being studied (e.g., start of a major offensive in a governorate, imposition of a specific blockade, significant exchange rate policy announcement). Events should be precisely dated.
2.  **Event Time:** Designate month $t=0$ as the month in which the event occurs. If an event occurs late in the month (e.g., after the 15th), sensitivity analysis might consider $t=0$ as the *following* month.
3.  **Event Window:** Define the period around the event month over which effects are measured. Given monthly data, a typical window might be relatively short, e.g., $[-3, +3]$ months or $[-1, +1]$ month relative to the event month $t=0$. The window $W = [T_1, T_2]$ includes months from $T_1$ to $T_2$.
4.  **Estimation Window:** Define a period *before* the event window used to estimate the parameters of the normal performance model. This window, $E = [T_0, T_1-1]$, must be sufficiently long (e.g., 12-24 months) and should ideally be free from the influence of the event being studied or other major confounding events.

**Example:**
- Event: Major battle starts in Market A on June 10th, 2020.
- Event Month ($t=0$): June 2020.
- Event Window (e.g., $[-1, +1]$): May 2020 ($t=-1$), June 2020 ($t=0$), July 2020 ($t=+1$).
- Estimation Window (e.g., 12 months prior, excluding event window buffer): May 2019 to April 2020.

## 3. Estimating Normal Performance (Counterfactual)

The core idea is to estimate what the market outcome (e.g., log price return) *would have been* in the event window had the event not occurred.

**Protocol:** Use a model estimated over the estimation window ($E$) to predict normal performance during the event window ($W$).

**Models for Normal Performance (Monthly Data):**

1.  **Constant Mean Return Model:** Assumes the mean return (e.g., average monthly log price change) is constant.
    *   **Specification:** $\hat{\mu}_i = \frac{1}{L_E} \sum_{t=T_0}^{T_1-1} R_{it}$, where $R_{it}$ is the return for entity $i$ in month $t$, and $L_E$ is the length of the estimation window.
    *   **Normal Return:** $E[R_{it} | X_t] = \hat{\mu}_i$ for $t \in W$.
    *   **Suitability:** Simple, but often unrealistic for volatile monthly prices.

2.  **Market Model (CAPM Adaptation):** Relates the entity's return to a market-wide or aggregate index return ($R_{mt}$).
    *   **Specification:** Estimate $R_{it} = \alpha_i + \beta_i R_{mt} + \epsilon_{it}$ over the estimation window $E$.
    *   **Normal Return:** $E[R_{it} | X_t] = \hat{\alpha}_i + \hat{\beta}_i R_{mt}$ for $t \in W$.
    *   **Suitability:** Requires a relevant market index (e.g., average price change across all markets/commodities, potentially excluding the affected entity). Controls for market-wide movements.

3.  **Panel Data Model (Recommended for this context):** Leverage the panel structure to estimate normal performance, controlling for fixed effects and potentially time-varying factors.
    *   **Specification:** Estimate a panel model (e.g., PanelOLS with entity and time fixed effects) over the estimation window $E$, potentially excluding observations for the specific entity $i$ undergoing the event.
        $$R_{jt} = \theta_j + \tau_t + \beta X_{jt} + \epsilon_{jt} \quad \text{for } j \neq i, t \in E$$
        Or, estimate on all data *before* the estimation window starts for *any* event.
    *   **Normal Return:** Predict $E[R_{it} | X_t]$ for the specific entity $i$ during its event window $W$ using the estimated parameters ($\{\hat{\theta}_j, \hat{\tau}_t, \hat{\beta}\}$) and the actual values of controls $X_{it}$ and time effects $\hat{\tau}_t$ during the window.
        $$E[R_{it} | X_t] = \hat{\theta}_i + \hat{\tau}_t + \hat{\beta} X_{it} \quad \text{for } t \in W$$
        (Requires estimating or imputing the entity fixed effect $\hat{\theta}_i$ if entity $i$ was excluded). Alternatively, use a model with only time effects estimated on non-event periods/entities.
    *   **Suitability:** Controls for entity-specific averages and common time trends/shocks, providing a more robust counterfactual in a panel setting.

**Implementation Note:** Ensure the chosen model adequately captures the dynamics during the estimation window (check residuals).

```python
import statsmodels.api as sm
from linearmodels.panel import PanelOLS

def estimate_normal_performance_panel(panel_data, entity_id, event_date, estimation_window_months=12, event_window_months=1):
    """Estimates normal performance using a panel model (conceptual)."""
    
    event_month = pd.Timestamp(event_date).to_period('M')
    
    # Define windows
    event_window_start = event_month - event_window_months
    event_window_end = event_month + event_window_months
    estimation_window_end = event_window_start - 1
    estimation_window_start = estimation_window_end - estimation_window_months + 1

    # Filter data for estimation (exclude event entity and window for all entities)
    estimation_data = panel_data[
        (panel_data.index.get_level_values('date').to_period('M') >= estimation_window_start) &
        (panel_data.index.get_level_values('date').to_period('M') <= estimation_window_end)
    ]
    # Optional: Exclude the specific entity having the event
    # estimation_data = estimation_data[estimation_data.index.get_level_values('entity') != entity_id]

    # Define outcome and controls (use price changes/returns)
    outcome = 'd_log_price' # Example: monthly log return
    controls = ['control1', 'control2'] # Example controls
    formula = f"{outcome} ~ {' + '.join(controls)} + TimeEffects" # EntityEffects might be omitted if entity i is excluded
    
    # Estimate model on estimation window data
    mod = PanelOLS.from_formula(formula, data=estimation_data)
    results = mod.fit()

    # Predict normal performance for the specific entity in the event window
    event_entity_data_window = panel_data.loc[pd.IndexSlice[entity_id, :]][
        (panel_data.loc[pd.IndexSlice[entity_id, :]].index.get_level_values('date').to_period('M') >= event_window_start) &
        (panel_data.loc[pd.IndexSlice[entity_id, :]].index.get_level_values('date').to_period('M') <= event_window_end)
    ]

    # Need to get time effect coefficients and apply them
    time_effects_coeffs = results.params[results.params.index.str.contains("TimeEffects")]
    # Match time effects to the event window dates
    # ... (logic to align time effects)
    
    # Predict using estimated coefficients and controls
    # predicted_normal_return = results.predict(event_entity_data_window[controls], ...) # Simplified
    # Requires careful handling of fixed effects prediction
    
    # Placeholder prediction (replace with proper prediction logic)
    predicted_normal_return = pd.Series(np.nan, index=event_entity_data_window.index)

    return predicted_normal_return
```

## 4. Calculating Abnormal Returns (AR) and Cumulative Abnormal Returns (CAR)

**Protocol:** Calculate the difference between actual and normal performance for each month in the event window, then cumulate these differences.

**Definitions:**

1.  **Abnormal Return (AR):** The difference between the actual return $R_{it}$ and the estimated normal return $E[R_{it} | X_t]$ for entity $i$ in month $t$ of the event window.
    $$AR_{it} = R_{it} - E[R_{it} | X_t]$$
    where $t \in W = [T_1, T_2]$.

2.  **Cumulative Abnormal Return (CAR):** The sum of abnormal returns over a specific period within the event window, typically from $t=k$ to $t=l$.
    $$CAR_i(k, l) = \sum_{t=k}^{l} AR_{it}$$
    Often calculated for the entire event window: $CAR_i(T_1, T_2)$.

3.  **Average Abnormal Return (AAR):** The average of abnormal returns across $N$ events at a specific point $t$ relative to the event month.
    $$AAR_t = \frac{1}{N} \sum_{i=1}^{N} AR_{it}$$

4.  **Cumulative Average Abnormal Return (CAAR):** The sum of average abnormal returns over a period $[k, l]$.
    $$CAAR(k, l) = \sum_{t=k}^{l} AAR_t = \frac{1}{N} \sum_{i=1}^{N} CAR_i(k, l)$$

```python
def calculate_abnormal_returns(actual_returns, normal_returns):
    """Calculates AR, CAR for a single event."""
    ar = actual_returns - normal_returns
    car = ar.cumsum()
    return ar, car

def calculate_average_returns(ar_list):
    """Calculates AAR, CAAR across multiple events."""
    # Assumes ar_list contains AR series aligned by relative event time
    all_ars = pd.concat(ar_list, axis=1)
    aar = all_ars.mean(axis=1)
    caar = aar.cumsum()
    return aar, caar
```

## 5. Statistical Inference: Hypothesis Testing

**Protocol:** Test the significance of the observed abnormal returns (AR, CAR, AAR, CAAR).

**Null Hypothesis ($H_0$):** The event has no effect on the outcome variable (i.e., the true abnormal return is zero).

**Test Statistics:**

1.  **Testing Individual $AR_{it}$ or $CAR_i(k,l)$:**
    *   Requires the standard deviation of the abnormal return, $\sigma(AR_{it})$. This is typically estimated as the standard deviation of the residuals ($\{\hat{\epsilon}_{it}\}$) from the normal performance model estimated over the estimation window $E$. Let this be $s_i$.
    *   Test statistic for $AR_{it}$: $t_{AR_{it}} = \frac{AR_{it}}{s_i}$. Compare to a t-distribution with $L_E-k$ degrees of freedom (where $k$ is the number of parameters in the normal model).
    *   Test statistic for $CAR_i(k,l)$: Assuming independence of ARs (strong assumption, often violated), $\sigma(CAR_i(k,l)) \approx s_i \sqrt{l-k+1}$. Then $t_{CAR_i(k,l)} = \frac{CAR_i(k,l)}{s_i \sqrt{l-k+1}}$.
    *   **Issue:** Standard errors can be misestimated if there's event-induced volatility or cross-correlation.

2.  **Testing Average Effects ($AAR_t$, $CAAR(k,l)$) - Cross-Sectional Test:**
    *   **Method (Simple t-test):** Calculate the standard deviation of the abnormal returns across events at time $t$, $s(AR_t) = \sqrt{\frac{1}{N-1} \sum_{i=1}^N (AR_{it} - AAR_t)^2}$.
    *   Test statistic for $AAR_t$: $t_{AAR_t} = \frac{AAR_t}{s(AR_t) / \sqrt{N}}$. Compare to t-distribution with $N-1$ df.
    *   Test statistic for $CAAR(k,l)$: Calculate $CAR_i(k,l)$ for each event $i$. Calculate the standard deviation $s(CAR(k,l))$ across events. $t_{CAAR(k,l)} = \frac{CAAR(k,l)}{s(CAR(k,l)) / \sqrt{N}}$.
    *   **Suitability:** Standard and widely used. Assumes abnormal returns are independent across events.

3.  **Standardized Abnormal Return (SAR) Test (Patell Test Adaptation):** Accounts for potential heteroskedasticity in abnormal returns.
    *   Standardize each abnormal return: $SAR_{it} = \frac{AR_{it}}{s_i}$, where $s_i$ is the residual standard deviation for entity $i$ from the estimation window.
    *   Test statistic for $AAR_t$: $t_{Patell} = \frac{1}{\sqrt{N}} \sum_{i=1}^N SAR_{it}$. Under $H_0$, $t_{Patell} \sim N(0,1)$ for large $N$.
    *   Can be adapted for CARs.
    *   **Suitability:** More robust if variance differs across entities/events.

4.  **Non-Parametric Tests (Sign Test, Rank Test):**
    *   **Sign Test:** Tests if the proportion of positive abnormal returns is significantly different from 50%. $H_0: p=0.5$. Use binomial test.
    *   **Wilcoxon Signed-Rank Test:** Tests if the distribution of abnormal returns is centered around zero. Ranks the absolute values and sums ranks of positive ARs.
    *   **Suitability:** Robust to non-normality of abnormal returns. Less powerful if ARs are approximately normal.

**Considerations for Monthly Data & Conflict:**
- **Serial Correlation:** Monthly ARs are likely serially correlated. Standard t-tests for CAR/CAAR that assume independence will have incorrect standard errors. Adjustments (e.g., Newey-West for AAR time series) or simulation methods might be needed.
- **Cross-Sectional Correlation:** Events might affect multiple entities simultaneously, or entities might be correlated due to other factors. This violates independence assumption of simple cross-sectional t-tests. Consider portfolio approaches or methods robust to cross-correlation (e.g., Kolari & Pynnönen adjustment, though complex).
- **Event-Induced Volatility:** Volatility might increase around the event, violating assumption of constant variance from estimation window. Use standardized tests (Patell) or robust methods.

```python
from scipy import stats

def test_aar_significance(aar_series, ar_list):
    """Performs simple cross-sectional t-test for AAR significance."""
    all_ars = pd.concat(ar_list, axis=1)
    std_devs = all_ars.std(axis=1)
    n_events = all_ars.shape[1]
    
    t_stats = (aar_series / (std_devs / np.sqrt(n_events)))
    p_values = stats.t.sf(np.abs(t_stats), n_events-1) * 2 # Two-tailed test
    
    return t_stats, p_values

def test_caar_significance(car_list):
    """Performs simple cross-sectional t-test for final CAAR significance."""
    final_cars = [car.iloc[-1] for car in car_list]
    n_events = len(final_cars)
    mean_car = np.mean(final_cars)
    std_car = np.std(final_cars, ddof=1)
    
    t_stat = mean_car / (std_car / np.sqrt(n_events))
    p_value = stats.t.sf(np.abs(t_stat), n_events-1) * 2
    
    print(f"CAAR({car_list[0].index[0]} to {car_list[0].index[-1]}): {mean_car:.4f}")
    print(f"T-statistic: {t_stat:.4f}, P-value: {p_value:.4f}")
    return t_stat, p_value
```

## 6. Confidence Interval Construction

**Protocol:** Construct confidence intervals for the key metrics (AAR, CAAR).

**Methods:**

1.  **Using Standard Errors from t-tests:**
    *   For $AAR_t$: $CI = AAR_t \pm t_{\alpha/2, N-1} \times SE(AAR_t)$, where $SE(AAR_t) = s(AR_t) / \sqrt{N}$.
    *   For $CAAR(k,l)$: $CI = CAAR(k,l) \pm t_{\alpha/2, N-1} \times SE(CAAR(k,l))$, where $SE(CAAR(k,l)) = s(CAR(k,l)) / \sqrt{N}$.

2.  **Bootstrap Confidence Intervals:**
    *   Resample events with replacement $B$ times.
    *   Calculate AAR/CAAR for each bootstrap sample.
    *   Construct confidence interval from the empirical distribution of bootstrap statistics (e.g., percentile method).
    *   **Suitability:** Robust to non-normality and can implicitly handle some forms of dependence if resampling is done carefully (e.g., block bootstrap if time dependence is also a concern across events).

## 7. Addressing Confounding Factors and Robustness Checks

**Protocol:** Ensure the estimated abnormal returns are attributable to the event and not other simultaneous factors.

**Strategies:**

- **Careful Event Selection:** Define events as precisely as possible. Avoid periods with multiple major overlapping events.
- **Control Variables:** Include relevant control variables ($X_{it}$) in the normal performance model if using Market Model or Panel Model approaches.
- **Matching:** If event assignment is non-random (e.g., only certain markets experience battles), consider matching event entities to control entities with similar pre-event characteristics.
- **Difference-in-Differences:** Frame the event study within a DiD approach if suitable control groups exist.
- **Sensitivity Analysis:**
    - Vary the length of the estimation and event windows.
    - Use different models for normal performance.
    - Exclude influential events or outliers.
    - Test for effects of confounding events occurring within the event window.

## 8. Conclusion

Conducting event studies with monthly data in conflict settings requires adapting standard techniques. Careful definition of event windows, robust estimation of normal performance (preferably using panel models), and appropriate statistical tests (considering potential serial and cross-sectional correlation, and non-normality) are essential. By employing methods like cross-sectional t-tests, standardized return tests, or non-parametric tests, and constructing confidence intervals, the impact of specific conflict-related events on monthly market dynamics can be rigorously assessed. Robustness checks are crucial to ensure findings are reliable and attributable to the events under study.

