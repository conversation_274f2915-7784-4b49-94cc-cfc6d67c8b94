import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from datetime import datetime, timedelta
import numpy as np

# Create timeline data for the research implementation
timeline_data = {
    'Phase': [
        'Information Gathering', 'Information Gathering', 'Information Gathering',
        'Visual Generation', 'Visual Generation', 'Visual Generation',
        'Data Collection', 'Data Collection', 'Data Collection', 'Data Collection',
        'Analysis', 'Analysis', 'Analysis',
        'Validation', 'Validation', 'Validation',
        'Publication', 'Publication'
    ],
    'Activity': [
        'Literature Review', 'Framework Development', 'Methodology Design',
        'Document Creation', 'Chart Development', 'Tool Preparation',
        'Syria Data Collection', 'Lebanon Data Collection', 'Somalia Data Collection', 'Yemen Baseline',
        'Cross-Country Analysis', 'External Validity Testing', 'Robustness Checks',
        'Peer Review Process', 'Methodology Validation', 'Results Verification',
        'Academic Publication', 'Policy Dissemination'
    ],
    'Start_Month': [1, 2, 3, 4, 5, 6, 7, 9, 11, 13, 15, 16, 17, 18, 19, 20, 21, 22],
    'Duration_Months': [2, 2, 2, 1, 1, 1, 3, 3, 3, 2, 2, 1, 1, 2, 1, 1, 3, 2],
    'Deliverables': [
        'Academic Sources', 'Theoretical Framework', 'Research Protocol',
        'Research Documents', 'Visualizations', 'Data Collection Tools',
        'Syria Dataset', 'Lebanon Dataset', 'Somalia Dataset', 'Yemen Validation',
        'Comparative Results', 'External Validity Measures', 'Sensitivity Analysis',
        'Expert Feedback', 'Methodology Approval', 'Quality Assurance',
        'Journal Articles', 'Policy Briefs'
    ]
}

df_timeline = pd.DataFrame(timeline_data)

# Create the timeline chart
fig, ax = plt.subplots(figsize=(14, 10))

# Define colors for each phase
phase_colors = {
    'Information Gathering': '#2E86AB',
    'Visual Generation': '#A23B72', 
    'Data Collection': '#F18F01',
    'Analysis': '#C73E1D',
    'Validation': '#8E44AD',
    'Publication': '#27AE60'
}

# Create Gantt chart
y_pos = range(len(df_timeline))
for i, row in df_timeline.iterrows():
    ax.barh(i, row['Duration_Months'], left=row['Start_Month'], 
            color=phase_colors[row['Phase']], alpha=0.7, height=0.6)
    
    # Add activity labels
    ax.text(row['Start_Month'] + row['Duration_Months']/2, i, 
            row['Activity'], ha='center', va='center', fontsize=8, fontweight='bold')

# Customize the chart
ax.set_yticks(y_pos)
ax.set_yticklabels([f"{row['Activity']}\n({row['Deliverables']})" 
                    for _, row in df_timeline.iterrows()], fontsize=9)
ax.set_xlabel('Timeline (Months)', fontsize=12, fontweight='bold')
ax.set_title('Cross-Country Validation Research Implementation Timeline\nConflict Economics Currency Fragmentation Study', 
             fontsize=14, fontweight='bold', pad=20)

# Add phase legend
legend_elements = [patches.Patch(color=color, label=phase) 
                  for phase, color in phase_colors.items()]
ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))

# Set x-axis limits and grid
ax.set_xlim(0, 25)
ax.grid(axis='x', alpha=0.3)
ax.set_axisbelow(True)

# Add milestone markers
milestones = [
    (6, 'Framework Complete'),
    (14, 'Data Collection Complete'), 
    (18, 'Analysis Complete'),
    (21, 'Validation Complete'),
    (24, 'Publication Ready')
]

for month, milestone in milestones:
    ax.axvline(x=month, color='red', linestyle='--', alpha=0.7)
    ax.text(month, len(df_timeline), milestone, rotation=90, 
            ha='center', va='bottom', fontsize=9, color='red', fontweight='bold')

plt.tight_layout()
plt.savefig('research_timeline.png', dpi=300, bbox_inches='tight')
plt.show()

print("Research Implementation Timeline created and saved as 'research_timeline.png'")

# Create summary statistics table
print("\nRESEARCH IMPLEMENTATION SUMMARY:")
print("=" * 50)

phase_summary = df_timeline.groupby('Phase').agg({
    'Duration_Months': 'sum',
    'Activity': 'count'
}).rename(columns={'Duration_Months': 'Total_Duration', 'Activity': 'Number_of_Activities'})

for phase, data in phase_summary.iterrows():
    print(f"{phase}: {data['Number_of_Activities']} activities, {data['Total_Duration']} total months")

print(f"\nTotal Project Duration: {df_timeline['Start_Month'].max() + df_timeline.loc[df_timeline['Start_Month'].idxmax(), 'Duration_Months']} months")
print(f"Total Activities: {len(df_timeline)} activities")
print(f"Key Deliverables: {len(df_timeline['Deliverables'].unique())} unique deliverable types")