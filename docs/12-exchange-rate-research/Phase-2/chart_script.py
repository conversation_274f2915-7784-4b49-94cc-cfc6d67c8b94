import plotly.express as px
import plotly.graph_objects as go
import pandas as pd

# Create the dataset
data = {
    'Country': ['Yemen', 'Syria', 'Lebanon', 'Somalia'],
    'Exchange_Rate_Diff': [3.7, 4.2, 2.8, 1.5],
    'Institutional_Cap': [2.1, 2.3, 4.2, 1.8],
    'Conflict_Intensity': [8.5, 7.8, 3.1, 6.9],
    'Fragmentation_Type': [
        'Geo Political',
        'Multi-Currency', 
        'Banking Crisis',
        'Dollarization'
    ],
    'Primary_Currencies': [
        'YER Gov/Rebel',
        'SYP/TL/USD',
        'LBP Off/Par/USD', 
        'USD/SOS/Mobile'
    ]
}

df = pd.DataFrame(data)

# Define colors for each country
colors = ['#1FB8CD', '#FFC185', '#ECEBD5', '#5D878F']

# Create bubble chart
fig = go.Figure()

for i, country in enumerate(df['Country']):
    row = df[df['Country'] == country].iloc[0]
    
    # Use institutional capacity (inverted) to determine size
    # Lower institutional capacity = larger bubbles = bigger problems
    bubble_size = (6 - row['Institutional_Cap']) * 15 + 20
    
    fig.add_trace(go.<PERSON>atter(
        x=[row['Exchange_Rate_Diff']],
        y=[row['Conflict_Intensity']],
        mode='markers+text',
        name=country,
        text=[country],
        textposition="middle center",
        textfont=dict(size=10, color='white'),
        marker=dict(
            size=bubble_size,
            color=colors[i],
            line=dict(width=2, color='white'),
            opacity=0.8
        ),
        hovertemplate=(
            f"<b>{country}</b><br>" +
            "Rate Diff: %{x}<br>" +
            "Conflict: %{y}<br>" +
            f"Instit Cap: {row['Institutional_Cap']}<br>" +
            f"Type: {row['Fragmentation_Type']}<br>" +
            f"Currency: {row['Primary_Currencies']}" +
            "<extra></extra>"
        ),
        cliponaxis=False
    ))

# Update layout
fig.update_layout(
    title="Currency Fragmentation Patterns",
    xaxis_title="Rate Diff Level",
    yaxis_title="Conflict Level",
    legend=dict(
        orientation='h', 
        yanchor='bottom', 
        y=1.05, 
        xanchor='center', 
        x=0.5
    ),
    showlegend=True
)

# Save the chart
fig.write_image("currency_fragmentation_analysis.png")