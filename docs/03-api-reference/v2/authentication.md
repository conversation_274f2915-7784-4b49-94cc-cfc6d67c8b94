# Authentication

The V2 API uses <PERSON><PERSON><PERSON> (JSON Web Tokens) for authentication.

## Authentication Flow

1. **Login** - Obtain access and refresh tokens
2. **Use Access Token** - Include in Authorization header
3. **Refresh Token** - Get new access token when expired

## Login

```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "<EMAIL>",
    "password": "your-password"
}
```

Response:
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
    "token_type": "bearer",
    "expires_in": 1800
}
```

## Using Access Tokens

Include the access token in the Authorization header:

```http
GET /api/v1/markets
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGc...
```

## Refresh Token

When the access token expires, use the refresh token to get a new one:

```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGc..."
}
```

## API Keys

For programmatic access, use API keys:

### Create API Key

```http
POST /api/v1/auth/api-keys
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "My Integration",
    "scopes": ["read:markets", "read:prices", "write:analysis"]
}
```

Response:
```json
{
    "id": "key_123",
    "key": "ymi_live_abc123...",
    "name": "My Integration",
    "scopes": ["read:markets", "read:prices", "write:analysis"],
    "created_at": "2025-06-02T10:00:00Z"
}
```

### Using API Keys

Include the API key in the X-API-Key header:

```http
GET /api/v1/markets
X-API-Key: ymi_live_abc123...
```

## Scopes

Available permission scopes:

- `read:markets` - Read market data
- `read:prices` - Read price data
- `read:commodities` - Read commodity data
- `write:analysis` - Create analysis jobs
- `read:analysis` - Read analysis results
- `write:policy` - Run policy simulations
- `admin:users` - Manage users
- `admin:api_keys` - Manage API keys

## Security Best Practices

1. **Never share tokens** - Keep access tokens and API keys secure
2. **Use HTTPS** - Always use encrypted connections in production
3. **Rotate keys** - Regularly rotate API keys
4. **Limit scopes** - Only request necessary permissions
5. **Monitor usage** - Track API key usage for anomalies