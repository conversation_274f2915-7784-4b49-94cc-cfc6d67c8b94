# V2 API Reference

## Overview

The Yemen Market Integration V2 API is a RESTful service built with FastAPI that provides comprehensive access to market data, econometric analysis, and policy simulations.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

Most endpoints require JWT authentication. See [Authentication](./authentication.md) for details.

## Available Endpoints

### Core Data APIs
- [Markets API](./endpoints/markets.md) - Market information and metadata
- [Commodities API](./endpoints/commodities.md) - Commodity definitions and categories
- [Prices API](./endpoints/prices.md) - Price observations and time series

### Analysis APIs
- [Analysis API](./endpoints/analysis.md) - Three-tier econometric analysis
- [Policy API](./endpoints/policy.md) - Policy simulation endpoints

### Real-time APIs
- [SSE Events](./endpoints/sse.md) - Server-sent events for real-time updates

### System APIs
- [Health Check](./endpoints/health.md) - System health monitoring
- [Authentication](./endpoints/auth.md) - User authentication and API keys

## Common Patterns

### Pagination

All list endpoints support pagination:

```
GET /api/v1/markets?page=1&size=20
```

### Filtering

Most endpoints support filtering:

```
GET /api/v1/prices?market_id=SANAA&commodity=WHEAT&start_date=2023-01-01
```

### Error Responses

All errors follow a consistent format:

```json
{
    "detail": "Error message",
    "status_code": 400,
    "type": "validation_error",
    "timestamp": "2025-06-02T10:00:00Z"
}
```

## Rate Limiting

- Default: 100 requests per hour
- Analysis endpoints: 10 requests per hour

## OpenAPI Documentation

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
- OpenAPI JSON: http://localhost:8000/openapi.json