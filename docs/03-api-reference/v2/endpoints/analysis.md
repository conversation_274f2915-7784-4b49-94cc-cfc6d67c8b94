# Analysis API

Run three-tier econometric analysis on market integration data.

## Create Analysis

Start a new three-tier analysis job.

```http
POST /api/v1/analysis/three-tier
Content-Type: application/json
Authorization: Bearer {token}
```

### Request Body

```json
{
    "name": "Q1 2024 Market Integration Analysis",
    "description": "Quarterly analysis of market integration patterns",
    "start_date": "2024-01-01",
    "end_date": "2024-03-31",
    "markets": ["SANAA_CENTRAL", "ADEN_MAIN", "TAIZ_CENTRAL"],
    "commodities": ["WHEAT_FLOUR", "RICE_IMPORTED"],
    "commodity_groups": ["FOOD"],
    "confidence_level": 0.95,
    "include_diagnostics": true,
    "tier_config": {
        "tier1": {
            "model_type": "FIXED_EFFECTS",
            "cluster_standard_errors": true
        },
        "tier2": {
            "cointegration_test": "ENGLE_GRANGER",
            "max_lags": 12
        },
        "tier3": {
            "factors": 3,
            "rotation": "VARIMAX"
        }
    }
}
```

### Response

```json
{
    "id": "analysis_123",
    "status": "PENDING",
    "created_at": "2024-04-01T10:00:00Z",
    "estimated_duration": 300,
    "queue_position": 2
}
```

## Get Analysis Status

Check the status of an analysis job.

```http
GET /api/v1/analysis/{analysis_id}/status
```

### Response

```json
{
    "id": "analysis_123",
    "status": "RUNNING",
    "progress": {
        "current_phase": "TIER2_COMMODITY",
        "completed_phases": ["DATA_PREPARATION", "TIER1_POOLED"],
        "overall_progress": 0.45,
        "current_commodity": "WHEAT_FLOUR"
    },
    "started_at": "2024-04-01T10:00:30Z",
    "updated_at": "2024-04-01T10:05:00Z"
}
```

### Status Values

- `PENDING` - Job queued
- `RUNNING` - Analysis in progress
- `COMPLETED` - Successfully completed
- `FAILED` - Analysis failed
- `CANCELLED` - Job cancelled

## Get Analysis Results

Retrieve results from a completed analysis.

```http
GET /api/v1/analysis/{analysis_id}/results
```

### Response

```json
{
    "id": "analysis_123",
    "status": "COMPLETED",
    "summary": {
        "overall_integration": 0.72,
        "conflict_impact": -0.15,
        "price_transmission": 0.85
    },
    "tier1_results": {
        "model": "FIXED_EFFECTS",
        "coefficients": {
            "conflict_intensity": -0.023,
            "distance": -0.0012,
            "same_governorate": 0.156
        },
        "statistics": {
            "r_squared": 0.67,
            "adjusted_r_squared": 0.65,
            "f_statistic": 125.4,
            "observations": 5420
        }
    },
    "tier2_results": {
        "WHEAT_FLOUR": {
            "cointegration": {
                "test": "ENGLE_GRANGER",
                "statistic": -4.23,
                "p_value": 0.001,
                "is_cointegrated": true
            },
            "vecm": {
                "adjustment_speed": -0.32,
                "half_life_days": 2.2
            }
        }
    },
    "tier3_results": {
        "factor_analysis": {
            "factors_extracted": 3,
            "variance_explained": [0.45, 0.23, 0.15],
            "total_variance": 0.83
        },
        "conflict_validation": {
            "spatial_spillovers": true,
            "spillover_radius_km": 50
        }
    },
    "diagnostics": {
        "autocorrelation": {
            "durbin_watson": 1.98,
            "ljung_box_p": 0.45
        },
        "heteroskedasticity": {
            "breusch_pagan_p": 0.12
        },
        "normality": {
            "jarque_bera_p": 0.34
        }
    }
}
```

## Individual Tier Endpoints

### Tier 1: Pooled Panel Analysis

```http
POST /api/v1/analysis/tier1
```

### Tier 2: Commodity-Specific Analysis

```http
POST /api/v1/analysis/tier2
```

### Tier 3: Validation & Extensions

```http
POST /api/v1/analysis/tier3
```

## Cancel Analysis

Cancel a running analysis job.

```http
POST /api/v1/analysis/{analysis_id}/cancel
```

## List Analyses

Get a list of all analysis jobs.

```http
GET /api/v1/analysis?status=COMPLETED&page=1&size=10
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| status | string | Filter by status |
| created_after | datetime | Filter by creation date |
| page | integer | Page number |
| size | integer | Items per page |

## Errors

| Status Code | Description |
|-------------|-------------|
| 400 | Invalid analysis parameters |
| 404 | Analysis not found |
| 409 | Analysis already running |
| 503 | Analysis queue full |