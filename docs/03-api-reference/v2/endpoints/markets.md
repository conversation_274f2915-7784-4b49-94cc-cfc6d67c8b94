# Markets API

Access market information including locations, types, and metadata.

## List Markets

Get a paginated list of all markets.

```http
GET /api/v1/markets?page=1&size=20&governorate=SANAA
```

### Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| page | integer | Page number | 1 |
| size | integer | Items per page | 20 |
| governorate | string | Filter by governorate | - |
| market_type | string | Filter by type (URBAN, RURAL) | - |

### Response

```json
{
    "items": [
        {
            "id": "SANAA_CENTRAL",
            "name": "Sana'a Central Market",
            "governorate": "SANAA",
            "district": "OLD_CITY",
            "market_type": "URBAN",
            "coordinates": {
                "latitude": 15.3694,
                "longitude": 44.1910
            },
            "control_zone": "HOUTHI",
            "is_active": true,
            "metadata": {
                "population": 150000,
                "accessibility": "MAIN_ROAD"
            }
        }
    ],
    "total": 47,
    "page": 1,
    "size": 20,
    "pages": 3
}
```

## Get Market

Get detailed information about a specific market.

```http
GET /api/v1/markets/{market_id}
```

### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| market_id | string | Market identifier |

### Response

```json
{
    "id": "SANAA_CENTRAL",
    "name": "Sana'a Central Market",
    "governorate": "SANAA",
    "district": "OLD_CITY",
    "market_type": "URBAN",
    "coordinates": {
        "latitude": 15.3694,
        "longitude": 44.1910
    },
    "control_zone": "HOUTHI",
    "is_active": true,
    "metadata": {
        "population": 150000,
        "accessibility": "MAIN_ROAD",
        "main_commodities": ["WHEAT", "RICE", "FUEL"],
        "collection_frequency": "WEEKLY"
    },
    "statistics": {
        "total_observations": 1250,
        "first_observation": "2019-01-01",
        "last_observation": "2024-12-31",
        "completeness": 0.92
    }
}
```

## Market Statistics

Get statistical summary for a market.

```http
GET /api/v1/markets/{market_id}/statistics?start_date=2023-01-01&end_date=2023-12-31
```

### Query Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| start_date | date | Start date (YYYY-MM-DD) | Yes |
| end_date | date | End date (YYYY-MM-DD) | Yes |

### Response

```json
{
    "market_id": "SANAA_CENTRAL",
    "period": {
        "start": "2023-01-01",
        "end": "2023-12-31"
    },
    "price_statistics": {
        "observations": 624,
        "commodities": 12,
        "average_price_change": 0.15,
        "volatility": 0.23
    },
    "conflict_statistics": {
        "total_events": 45,
        "fatalities": 120,
        "average_intensity": 2.7
    },
    "integration_metrics": {
        "regional_correlation": 0.82,
        "national_correlation": 0.65,
        "cointegration_partners": ["ADEN_MAIN", "TAIZ_CENTRAL"]
    }
}
```

## Errors

| Status Code | Description |
|-------------|-------------|
| 404 | Market not found |
| 422 | Validation error (invalid parameters) |