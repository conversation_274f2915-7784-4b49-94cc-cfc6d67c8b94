# API Reference

**Target Audience**: Developers, Researchers implementing custom analyses

## Overview

This section provides comprehensive API documentation for the Yemen Market Integration Platform.

## V2 REST API (Current)

The V2 system provides a modern RESTful API built with FastAPI. See the **[V2 API Reference](./v2/README.md)** for:

- REST endpoints for markets, prices, and analysis
- JWT authentication and API keys
- Real-time updates via Server-Sent Events (SSE)
- OpenAPI/Swagger documentation

## V1 Python API (Legacy)

The following documentation covers the V1 Python modules (now archived in `archive/v1_reference/`). These are provided for reference and migration purposes.

## Module Organization

### Core Modules

#### [Configuration](config/)
- [`settings`](config/settings.md) - Configuration management and environment settings

#### [Data Processing](data/)
- [`acaps_processor`](data/acaps_processor.md) - ACAPS control area data processing
- [`acled_processor`](data/acled_processor.md) - ACLED conflict event processing
- [`hdx_client`](data/hdx_client.md) - Humanitarian Data Exchange client
- [`panel_builder`](data/panel_builder.md) - Panel dataset construction
- [`spatial_joiner`](data/spatial_joiner.md) - Spatial data joining utilities
- [`wfp_processor`](data/wfp_processor.md) - WFP price data processing

#### [Feature Engineering](features/)
- [`data_preparation`](features/data_preparation.md) - Data preparation pipeline
- [`feature_engineer`](features/feature_engineer.md) - Feature engineering utilities

#### [Models](models/)
- [`three_tier`](models/three_tier/) - Three-tier econometric framework
  - [Tier 1: Pooled Panel Models](models/three_tier/tier1_pooled.md)
  - [Tier 2: Commodity-Specific Models](models/three_tier/tier2_commodity.md)
  - [Tier 3: Validation Models](models/three_tier/tier3_validation.md)

#### [Utilities](utils/)
- [`logging`](utils/logging.md) - Enhanced logging with progress tracking

## Quick Reference

### V2 Import Patterns
```python
# V2 uses dependency injection and domain-driven design
from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import MarketId, Commodity
from src.application.services import ThreeTierAnalysisService
from src.infrastructure.external_services import HDXClient, WFPClient
```

### V1 Import Patterns (Legacy)
```python
# V1 modules (archived)
from yemen_market.data import WFPProcessor, ACLEDProcessor, PanelBuilder
from yemen_market.features import DataPreparation, FeatureEngineer
from yemen_market.models.three_tier import PooledPanelModel, ThresholdVECM
from yemen_market.utils.logging import info, timer, progress
```

### Common Workflows

#### 1. Data Pipeline
```python
# Load and process data
wfp = WFPProcessor(data_dir="data/raw")
wfp_data = wfp.process()

# Build panel dataset
builder = PanelBuilder(data_dir="data/processed")
panel = builder.create_balanced_panel()
```

#### 2. Model Estimation
```python
# Tier 1: Pooled panel
model = PooledPanelModel()
results = model.fit(panel)

# Tier 2: Commodity-specific
vecm = ThresholdVECM(commodity="wheat")
vecm_results = vecm.estimate(panel)
```

#### 3. Enhanced Logging
```python
from yemen_market.utils.logging import info, timer, progress

with timer("analysis"):
    with progress("Processing markets", total=21) as update:
        for market in markets:
            # Process market
            update(1)
```

## API Conventions

### Type Annotations
All public APIs use type annotations:
```python
def process_data(
    df: pd.DataFrame,
    config: Dict[str, Any],
    validate: bool = True
) -> pd.DataFrame:
    """Process raw data with validation."""
```

### Return Values
- Single values: Direct return
- Multiple values: Named tuples or dataclasses
- Errors: Raise specific exceptions with context

### Naming Conventions
- Classes: `PascalCase`
- Functions/methods: `snake_case`
- Constants: `UPPER_SNAKE_CASE`
- Private: Leading underscore `_private_method`

## Version Compatibility

- **Python**: 3.9+
- **Pandas**: 1.3+
- **NumPy**: 1.21+
- **Statsmodels**: 0.13+

## See Also

- [User Guides](../02-user-guides/) - Task-oriented documentation
- [Architecture Overview](../01-architecture/overview.md) - System design
- [Development Guide](../04-development/) - Contributing to the codebase