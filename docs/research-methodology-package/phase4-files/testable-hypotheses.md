# Complete Testable Hypotheses (H1-H10): Quick Reference Guide

## Original Hypotheses (H1-H3)

### H1: Exchange Rate Mechanism (PRIMARY)
**Statement**: Negative price premiums disappear when analyzed in USD rather than YER.
**Test**: Compare YER vs USD price differentials across currency zones
**Expected**: No systematic USD price differences for tradeable goods

### H2: Aid Distribution Channel  
**Statement**: Humanitarian aid depresses local prices, varying by modality.
**Test**: Exploit variation in aid distribution timing and location
**Expected**: In-kind > cash effects; spillovers to neighboring markets

### H3: Demand Destruction
**Statement**: Conflict reduces purchasing power more than supply.
**Test**: Correlate displacement intensity with price changes
**Expected**: Larger effects for non-essential goods; persistence after conflict

## New Theory-Based Hypotheses (H4-H10)

### H4: Currency Zone Switching Effects
**Statement**: Markets switching control show discrete price jumps matching exchange regime.
**Test**: Regression discontinuity around control-change events
**Expected**: Immediate price adjustment to new currency zone equilibrium

### H5: Cross-Border Arbitrage
**Statement**: Price differentials = transport costs + exchange differentials for tradeables.
**Test**: `price_diff = β₁transport + β₂exchange_diff`
**Expected**: β₂ ≈ 1 for perfectly tradeable goods

### H6: Currency Substitution Dynamics
**Statement**: USD pricing probability increases with exchange volatility.
**Test**: Binary choice model for posted price currency
**Expected**: Higher volatility → more USD pricing

### H7: Aid Effectiveness Differential
**Statement**: Aid works better in locally stable currency.
**Test**: Triple-diff (aid × currency_match × post)
**Expected**: YER aid more effective in Houthi areas, USD in government

### H8: Information Spillover
**Statement**: Exchange rate info affects pricing across zones.
**Test**: VAR with cross-zone prices and exchange rates
**Expected**: Lead-lag relationships in price movements

### H9: Threshold Effects in Integration
**Statement**: Small exchange gaps don't affect integration; large gaps (>100%) create regime switch.
**Test**: Threshold VECM with exchange differential
**Expected**: Non-linear relationship with clear threshold

### H10: Long-run Convergence
**Statement**: USD prices converge to global; YER prices don't.
**Test**: Separate ECM for each currency
**Expected**: Cointegration in USD only

## Implementation Priority

### Immediate Tests (Data Available)
1. H1 - Exchange rate mechanism (PRIORITY)
2. H5 - Cross-border arbitrage  
3. H10 - Long-run convergence

### Requires Additional Data
4. H2 - Aid distribution (need OCHA 3W)
5. H3 - Demand destruction (need displacement data)
6. H4 - Zone switching (need control change dates)

### Advanced Econometrics
7. H6 - Currency substitution (need price denomination data)
8. H7 - Aid effectiveness (need detailed aid data)
9. H8 - Information spillover (need high-frequency data)
10. H9 - Threshold effects (need sufficient time series)

## Additional Hypotheses from Phase 1 Completion

### S1: Spatial - Currency Boundaries Trump Geographic Distance
**Statement**: Price correlation stronger within currency zones than across, even controlling for physical distance.
**Test**: Spatial regression with currency zone interactions
**Expected**: Markets 10km apart across zones less integrated than markets 200km apart within zones

### N1: Network - Network Density Moderates Price Transmission
**Statement**: Markets with denser trader networks (proxied by reporting frequency, hub proximity) show stronger price integration.
**Test**: Include network proxies and interactions in transmission equations
**Expected**: High-network markets show faster price adjustment and stronger correlations

### P1: Political Economy - Seigniorage Incentives Prevent Reunification
**Statement**: Currency zone persistence driven by seigniorage revenues that exceed reunification benefits.
**Test**: Correlate exchange rate policy with fiscal deficits and seigniorage reliance
**Expected**: Higher seigniorage dependence → more aggressive devaluation policies

## Quick Testing Code Templates

### H1: Exchange Rate Test
```stata
* Create USD prices
gen price_usd = price_yer / exchange_rate

* Test difference
reg price_usd houthi_zone commodity_fe time_fe, cluster(market)
```

### S1: Spatial Boundary Test
```stata
* Test spatial effects with currency zones
gen same_zone = (zone_i == zone_j)
reg price_corr c.distance##i.same_zone, cluster(market_pair)
test distance + distance#1.same_zone = 0  // Within zone effect
```

### N1: Network Effects Test  
```stata
* Network-augmented transmission
gen high_network = (trader_density > 0.8)
reg price_corr c.distance##i.same_zone##i.high_network, cluster(market_pair)
```

### P1: Political Economy Test
```stata
* Seigniorage and exchange rate policy
gen seigniorage_gdp = (m2_growth * m2_stock) / gdp
reg exchange_rate_change seigniorage_gdp fiscal_deficit political_control
```

### H5: Arbitrage Test
```stata
* Price differential analysis
gen price_diff = price_i - price_j
gen exchange_diff = exchange_i - exchange_j

reg price_diff transport_cost exchange_diff if tradeable==1
test exchange_diff = 1
```

### H9: Threshold Test
```r
# Threshold VECM
library(tsDyn)
threshold_model <- TVECM(price_data, 
                        lag=2, 
                        ngridTh=300,
                        th1=list(exact=1.0))  # 100% differential
```