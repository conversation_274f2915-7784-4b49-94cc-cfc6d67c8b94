# Methodology to Code Mapping Guide

## Overview

This document provides a direct mapping between research methodology components and their required code implementations. Use this as a translation guide from academic methods to software features.

## Core Hypothesis Implementations

### H1: Exchange Rate Mechanism
**Methodology**: Currency zones drive price differentials
**Code Requirements**:
```python
# Required implementations:
class CurrencyZoneClassifier:
    """Maps markets to currency zones based on control areas"""
    def classify(self, market_location: str) -> CurrencyZone
    
class ExchangeRateConverter:
    """Handles all currency conversions with proper rate selection"""
    def convert_to_usd(self, price_yer: float, zone: CurrencyZone, date: datetime) -> float
    
class DualPriceModel:
    """Database model storing both YER and USD prices"""
    price_yer: DecimalField
    price_usd: DecimalField
    exchange_rate_applied: DecimalField
    currency_zone: ForeignKey(CurrencyZone)
```

### H2: Aid Distribution Channel
**Methodology**: Aid effectiveness varies by currency zone
**Code Requirements**:
```python
class AidEffectivenessCalculator:
    """Calculates aid impact by currency zone"""
    def calculate_purchasing_power(self, aid_amount: float, target_zone: CurrencyZone) -> float
    def estimate_price_impact(self, aid_type: str, zone: CurrencyZone) -> float
    
class AidAllocationOptimizer:
    """Optimizes aid distribution across zones"""
    def optimize_allocation(self, total_budget: float, zone_needs: Dict) -> Dict
```

### H3-H10: Extended Hypotheses
**See**: `01-theoretical-foundation/hypotheses/testable-hypotheses.md`
**Each requires specific model implementations documented below**

## Econometric Method Implementations

### Panel Data Models
**Method**: Three-tier panel analysis framework
**Code Architecture**:
```python
# Tier 1: Pooled Panel Analysis
class PooledPanelAnalyzer:
    """Implements PanelOLS with multi-way fixed effects"""
    def __init__(self, data: PanelDataset):
        self.model = PanelOLS(...)
        
    def add_fixed_effects(self, entity_effects=True, time_effects=True)
    def add_clustered_errors(self, cluster_var: str)
    def estimate(self) -> PanelResults

# Tier 2: Commodity-Specific Analysis  
class CommodityAnalyzer:
    """VECM models for specific commodities"""
    def test_cointegration(self, commodity: str) -> CointegrationTest
    def estimate_vecm(self, commodity: str) -> VECMResults

# Tier 3: Market-Pair Analysis
class MarketIntegrationAnalyzer:
    """Bilateral market integration testing"""
    def calculate_integration_index(self, market1: str, market2: str) -> float
```

### Advanced Machine Learning Methods
**Method**: Interactive Fixed Effects, Clustering, Non-linear prediction
**Implementation Requirements**:
```python
# From ml_pattern_recognition.py
class MarketClusterAnalyzer:
    """Identifies market typologies using ML"""
    def create_market_features(self) -> pd.DataFrame
    def cluster_markets(self, n_clusters: int) -> KMeansModel
    def classify_new_market(self, market_data: Dict) -> str

class InteractiveFixedEffects:
    """IFE implementation for unobserved heterogeneity"""
    def estimate_factors(self, n_factors: int) -> FactorModel
    def predict_with_factors(self, new_data: pd.DataFrame) -> np.array
```

### Regime-Switching Models
**Method**: Detect currency regime changes
**Implementation Requirements**:
```python
# From regime_switching_models.py
class MarkovSwitchingDetector:
    """Identifies regime changes in currency systems"""
    def fit(self, price_series: pd.Series, n_regimes: int)
    def get_regime_probabilities(self) -> pd.DataFrame
    def predict_regime(self, current_data: pd.Series) -> int

class ThresholdModel:
    """Panel threshold models for non-linearities"""
    def estimate_threshold(self, threshold_var: str) -> float
    def test_threshold_significance(self) -> TestResults
```

### Bayesian Uncertainty Quantification
**Method**: Communicate uncertainty for policy decisions
**Implementation Requirements**:
```python
# From bayesian_uncertainty.py
class BayesianPanelModel:
    """Bayesian estimation with uncertainty quantification"""
    def set_priors(self, prior_config: Dict)
    def sample_posterior(self, n_samples: int) -> MCMCResults
    def get_credible_intervals(self, level: float) -> Dict
    
class UncertaintyCommunicator:
    """Translates statistical uncertainty to policy language"""
    def create_policy_summary(self, posterior: MCMCResults) -> PolicyBrief
    def visualize_uncertainty(self, results: MCMCResults) -> Plot
```

## Data Pipeline Requirements

### Exchange Rate Collection System
**Methodology**: Multi-source rate collection with validation
**System Requirements**:
```python
class ExchangeRateCollector:
    """Collects rates from multiple sources"""
    sources = [
        CBYAdenScraper(),
        CBYSanaaScraper(), 
        MoneyChangerAPI(),
        NGOReportParser()
    ]
    
    def collect_daily_rates(self) -> List[ExchangeRate]
    def validate_rates(self, rates: List[ExchangeRate]) -> List[ExchangeRate]
    def impute_missing_rates(self, date_range: DateRange) -> List[ExchangeRate]

class RateValidationEngine:
    """Validates exchange rates against rules"""
    def check_bounds(self, rate: float, zone: CurrencyZone) -> bool
    def check_temporal_consistency(self, rate_series: pd.Series) -> bool
    def flag_anomalies(self, rates: List[ExchangeRate]) -> List[Anomaly]
```

### Price Data Management
**Methodology**: Dual-currency price tracking with quality assurance
**System Requirements**:
```python
class PriceDataManager:
    """Manages price data with currency conversions"""
    def ingest_price_data(self, source: DataSource) -> List[Price]
    def apply_exchange_rates(self, prices: List[Price]) -> List[DualPrice]
    def validate_price_quality(self, prices: List[Price]) -> QualityReport

class MissingDataHandler:
    """Handles missing data with conflict awareness"""
    def identify_missing_pattern(self, data: pd.DataFrame) -> MissingPattern
    def impute_conflict_aware(self, data: pd.DataFrame) -> pd.DataFrame
```

## Welfare Analysis Implementation

### Consumer Surplus Calculations
**Methodology**: Zone-specific welfare measurement
**Code Requirements**:
```python
class WelfareAnalyzer:
    """Calculates consumer surplus by currency zone"""
    def estimate_demand_system(self, zone: CurrencyZone) -> DemandSystem
    def calculate_consumer_surplus(self, prices: pd.Series, zone: CurrencyZone) -> float
    def compare_welfare_across_zones(self) -> WelfareComparison

class WelfareLossCalculator:
    """Quantifies welfare loss from fragmentation"""
    def calculate_integrated_welfare(self) -> float
    def calculate_fragmented_welfare(self) -> Dict[str, float]
    def quantify_total_loss(self) -> WelfareLoss
```

## Policy Application Features

### Aid Optimization Engine
**Methodology**: Currency-zone aware aid allocation
**Implementation Requirements**:
```python
class AidOptimizationEngine:
    """Optimizes aid delivery considering currency zones"""
    def calculate_zone_effectiveness(self, zone: CurrencyZone) -> float
    def optimize_currency_mix(self, budget: float) -> CurrencyAllocation
    def predict_impact(self, allocation: AidAllocation) -> ImpactPrediction

class EarlyWarningSystem:
    """Monitors currency fragmentation risks"""
    def calculate_fragmentation_index(self) -> float
    def detect_risk_threshold(self) -> RiskLevel
    def generate_alerts(self) -> List[Alert]
```

## Validation and Testing Framework

### Model Validation Suite
**Methodology**: Comprehensive robustness testing
**Implementation Requirements**:
```python
class ValidationSuite:
    """Runs all validation tests"""
    tests = [
        PlaceboTest(pre_2015_data),
        PermutationTest(n_permutations=1000),
        LeaveOneOutValidation(),
        TemporalStabilityTest(),
        CrossCountryValidation()
    ]
    
    def run_all_tests(self, model: EconometricModel) -> ValidationReport
    def check_good_enough_criteria(self, results: ValidationReport) -> bool
```

## API Design Requirements

### Core Endpoints
```python
# Analysis API
POST /api/v1/analysis/panel
POST /api/v1/analysis/regime-switching  
POST /api/v1/analysis/welfare

# Data API
GET /api/v1/data/prices/{market}/{commodity}
GET /api/v1/data/exchange-rates/{zone}/{date}
POST /api/v1/data/validate

# Policy API
GET /api/v1/policy/aid-recommendations
GET /api/v1/policy/early-warning
POST /api/v1/policy/simulate
```

### Real-time Streaming
```python
# SSE endpoints for monitoring
GET /api/v1/stream/exchange-rates
GET /api/v1/stream/price-alerts
GET /api/v1/stream/fragmentation-index
```

## Database Schema Requirements

### Core Tables
```sql
-- Currency zones
CREATE TABLE currency_zones (
    id UUID PRIMARY KEY,
    name VARCHAR(50),
    control_authority VARCHAR(50),
    stable_rate DECIMAL(10,2),
    last_updated TIMESTAMP
);

-- Dual prices
CREATE TABLE market_prices (
    id UUID PRIMARY KEY,
    market_id UUID REFERENCES markets(id),
    commodity_id UUID REFERENCES commodities(id),
    date DATE,
    price_yer DECIMAL(12,2),
    price_usd DECIMAL(12,4),
    exchange_rate_used DECIMAL(10,2),
    currency_zone_id UUID REFERENCES currency_zones(id),
    data_quality_score FLOAT,
    created_at TIMESTAMP
);

-- Exchange rates
CREATE TABLE exchange_rates (
    id UUID PRIMARY KEY,
    currency_zone_id UUID REFERENCES currency_zones(id),
    date DATE,
    official_rate DECIMAL(10,2),
    market_rate DECIMAL(10,2),
    source VARCHAR(100),
    confidence_score FLOAT
);
```

## Performance Requirements

### Computational Efficiency
- Panel models: < 30 seconds for 5-year dataset
- ML clustering: < 2 minutes for all markets
- Bayesian sampling: < 10 minutes for standard model
- Real-time updates: < 1 second latency

### Scalability Targets
- Handle 10,000+ markets
- Process 1M+ price observations
- Support 100+ concurrent users
- Stream to 1000+ monitoring clients

## Error Handling Requirements

### Data Quality Errors
```python
class DataQualityError(Exception):
    """Raised when data fails quality checks"""
    def __init__(self, failures: List[QualityFailure]):
        self.failures = failures
        
class ExchangeRateError(Exception):
    """Raised when exchange rate issues detected"""
    def __init__(self, zone: CurrencyZone, date: datetime, issue: str):
        self.zone = zone
        self.date = date
        self.issue = issue
```

### Model Convergence Errors
```python
class ConvergenceError(Exception):
    """Raised when models fail to converge"""
    def __init__(self, model_type: str, iterations: int):
        self.model_type = model_type
        self.iterations = iterations
```

## Monitoring and Logging

### Key Metrics to Track
```python
# Application metrics
- API response times
- Model estimation duration  
- Data ingestion success rate
- Validation test pass rate

# Business metrics
- Currency fragmentation index
- Average welfare loss
- Aid effectiveness score
- Early warning triggers
```

### Logging Requirements
```python
# Structured logging for all operations
logger.info("Model estimation completed", {
    "model_type": "panel_ols",
    "observations": 50000,
    "duration_seconds": 28.3,
    "converged": True,
    "r_squared": 0.82
})
```

---

**This mapping guide bridges the gap between research methodology and code implementation.**
**Each methodology component has specific code requirements that must be implemented.**
**Use this as your primary reference when building the src/ system.**