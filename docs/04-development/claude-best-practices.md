# Claude AI Assistant Configuration Best Practices

## Overview
This guide documents best practices for configuring AI assistants (<PERSON>, <PERSON><PERSON><PERSON><PERSON> Copilot, etc.) in research and development projects.

## CLAUDE.md Best Practices

### What to Include ✅
1. **Project-Specific Context**
   - Unique domain knowledge
   - Non-obvious project quirks
   - Critical research insights

2. **Data Peculiarities**
   - Format inconsistencies
   - Missing data patterns
   - Domain-specific gotchas

3. **Architecture Decisions**
   - Key technology choices
   - Important trade-offs made
   - Current implementation status

4. **Common Mistakes**
   - Project-specific pitfalls
   - Easy-to-miss requirements
   - Domain-specific errors

### What to Exclude ❌
1. **Generic Coding Standards**
   - "Write clean code"
   - "Follow PEP 8"
   - "Add comments"

2. **Obvious Best Practices**
   - "Test your code"
   - "Handle errors"
   - "Use version control"

3. **Lengthy Explanations**
   - Multi-paragraph descriptions
   - Tutorial-style content
   - Philosophical discussions

## File Structure

### Recommended CLAUDE.md Structure
```markdown
# Project Name - AI Assistant Guide

## Project Context
[2-3 sentences on what makes this project unique]

## Critical Knowledge
### Domain Specifics
- Key insight 1
- Key insight 2

### Data Quirks
- Quirk 1
- Quirk 2

## Common Pitfalls
1. **Specific Issue**: Brief explanation
2. **Another Issue**: How to avoid

## Quick Commands
```bash
# Most used commands
```

## Current Status
- Component X: 80% complete
- Known issues: Y needs fixing
```

### Target Length
- **Ideal**: 50-100 lines
- **Maximum**: 150 lines
- **Rule**: If it's over 150 lines, refactor

## .claude Directory

### What It's For
- Local Claude Desktop configuration
- User-specific settings
- Temporary caches

### What It's NOT For
- Project documentation
- Research notes
- Shared configurations

### Best Practice
Always add to .gitignore:
```gitignore
.claude/
**/.claude/
```

## Examples

### Good CLAUDE.md Entry ✅
```markdown
## Data Quirks
- Exchange rates differ 4x between regions (535 vs 2,100 YER/USD)
- Missing price data correlates with conflict events - not random
```

### Bad CLAUDE.md Entry ❌
```markdown
## Coding Standards
- Always write clean, maintainable code
- Follow the DRY principle
- Comment your functions
```

## Maintenance

### Regular Reviews
- Review CLAUDE.md monthly
- Remove outdated information
- Add new project-specific insights
- Keep it focused and concise

### Team Contributions
- Each team member can propose additions
- Focus on non-obvious insights
- Remove generic advice

## Benefits of Good Configuration

1. **Faster AI Responses** - Less context to parse
2. **Better Suggestions** - Focused on project needs
3. **Fewer Errors** - Aware of project pitfalls
4. **Team Alignment** - Shared understanding

## Common Anti-Patterns

1. **Kitchen Sink Approach** - Including everything
2. **Tutorial Mode** - Teaching the AI basics
3. **Stale Content** - Never updating the file
4. **Generic Templates** - Copy-pasting from other projects

## Conclusion

A well-crafted CLAUDE.md file should read like a concise briefing for a new team member who already knows how to code but needs to understand what makes THIS project unique.

---
*Remember: The AI already knows how to program. Tell it what's special about YOUR project.*