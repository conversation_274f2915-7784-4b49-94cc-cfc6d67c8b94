#!/usr/bin/env python3
"""Command-line interface for V1 to V2 migration operations."""

import asyncio
import click
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any
import logging

from .migration_orchestrator import (
    MigrationOrchestrator, 
    MigrationConfiguration, 
    MigrationStatus,
    MigrationPhase
)
from .migration_validator import MigrationValidator
from .backup_rollback_manager import BackupRollbackManager


def setup_logging(verbose: bool = False) -> None:
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )


def progress_callback(migration_state) -> None:
    """Progress callback for migration operations."""
    status_color = {
        MigrationStatus.IN_PROGRESS: 'yellow',
        MigrationStatus.COMPLETED: 'green',
        MigrationStatus.FAILED: 'red',
        MigrationStatus.PAUSED: 'blue',
        MigrationStatus.ROLLING_BACK: 'red'
    }
    
    color = status_color.get(migration_state.overall_status, 'white')
    
    click.echo(
        f"\r{migration_state.current_phase.value}: "
        f"{migration_state.overall_progress_percentage:.1f}% "
        f"[{migration_state.overall_status.value}]",
        color=color,
        nl=False
    )
    
    if migration_state.overall_status in [MigrationStatus.COMPLETED, MigrationStatus.FAILED]:
        click.echo()  # New line for completion


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def cli(verbose: bool) -> None:
    """Yemen Market Integration V1 to V2 Migration Tool."""
    setup_logging(verbose)


@cli.command()
@click.option('--v1-path', required=True, type=click.Path(exists=True), 
              help='Path to V1 data directory')
@click.option('--v2-db-url', required=True, 
              help='PostgreSQL connection string for V2 database')
@click.option('--backup-path', required=True, type=click.Path(), 
              help='Path for backup storage')
@click.option('--export-path', required=True, type=click.Path(), 
              help='Path for export storage')
@click.option('--batch-size', default=1000, type=int, 
              help='Batch size for processing')
@click.option('--workers', default=4, type=int, 
              help='Number of parallel workers')
@click.option('--timeout', default=480, type=int, 
              help='Migration timeout in minutes')
@click.option('--no-backup', is_flag=True, 
              help='Skip backup creation (DANGEROUS)')
@click.option('--no-rollback', is_flag=True, 
              help='Disable automatic rollback on failure')
@click.option('--dry-run', is_flag=True, 
              help='Validate configuration without executing migration')
def migrate(
    v1_path: str,
    v2_db_url: str, 
    backup_path: str,
    export_path: str,
    batch_size: int,
    workers: int,
    timeout: int,
    no_backup: bool,
    no_rollback: bool,
    dry_run: bool
) -> None:
    """Execute complete V1 to V2 migration."""
    
    migration_id = f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Create migration configuration
    config = MigrationConfiguration(
        migration_id=migration_id,
        v1_data_path=Path(v1_path),
        v2_connection_string=v2_db_url,
        backup_storage_path=Path(backup_path),
        export_storage_path=Path(export_path),
        batch_size=batch_size,
        parallel_workers=workers,
        timeout_minutes=timeout,
        create_backups=not no_backup,
        enable_rollback=not no_rollback
    )
    
    click.echo(f"Migration ID: {migration_id}")
    click.echo(f"V1 Data Path: {v1_path}")
    click.echo(f"V2 Database: {v2_db_url.split('@')[-1] if '@' in v2_db_url else v2_db_url}")
    click.echo(f"Backup Enabled: {not no_backup}")
    click.echo(f"Rollback Enabled: {not no_rollback}")
    
    if dry_run:
        click.echo("\n=== DRY RUN MODE ===")
        click.echo("Configuration validated successfully")
        return
    
    # Confirm before proceeding
    if not click.confirm('\nProceed with migration?'):
        click.echo("Migration cancelled")
        return
    
    async def run_migration():
        orchestrator = MigrationOrchestrator(config)
        orchestrator.add_progress_callback(progress_callback)
        
        try:
            click.echo("\nStarting migration...")
            migration_state = await orchestrator.execute_migration()
            
            # Display results
            if migration_state.overall_status == MigrationStatus.COMPLETED:
                click.echo(f"\n✅ Migration completed successfully!", color='green')
                
                # Display statistics
                perf = migration_state.performance_summary
                click.echo(f"Duration: {perf.get('total_duration_seconds', 0) / 3600:.1f} hours")
                click.echo(f"Records migrated: {perf.get('total_records_migrated', 0):,}")
                click.echo(f"Average throughput: {perf.get('average_throughput_rps', 0):.0f} rec/s")
                click.echo(f"Data coverage: {perf.get('data_coverage_achieved', 0):.1%}")
                
            elif migration_state.overall_status == MigrationStatus.FAILED:
                click.echo(f"\n❌ Migration failed!", color='red')
                for error in migration_state.error_summary[-3:]:  # Show last 3 errors
                    click.echo(f"  - {error}", color='red')
                
            elif migration_state.overall_status == MigrationStatus.ROLLED_BACK:
                click.echo(f"\n🔄 Migration rolled back successfully", color='blue')
                click.echo("System restored to pre-migration state")
            
            # Save final state
            state_file = Path(export_path) / f"migration_state_{migration_id}.json"
            orchestrator.export_migration_state(state_file)
            click.echo(f"\nMigration state saved to: {state_file}")
            
        except KeyboardInterrupt:
            click.echo("\n⏸️  Migration interrupted by user")
            await orchestrator.cancel_migration()
        except Exception as e:
            click.echo(f"\n💥 Migration failed with error: {e}", color='red')
            sys.exit(1)
    
    # Run migration
    asyncio.run(run_migration())


@cli.command()
@click.option('--v2-db-url', required=True, 
              help='PostgreSQL connection string for V2 database')
@click.option('--output', '-o', type=click.Path(), 
              help='Output file for validation report')
@click.option('--quick', is_flag=True, 
              help='Run quick validation only')
def validate(v2_db_url: str, output: Optional[str], quick: bool) -> None:
    """Validate migration results."""
    
    async def run_validation():
        async with MigrationValidator(v2_db_url) as validator:
            if quick:
                click.echo("Running quick validation...")
                results = await validator.quick_validation()
                
                click.echo(f"\nValidation Status: {results.get('status', 'unknown')}")
                
                counts = results.get('table_counts', {})
                for table, count in counts.items():
                    click.echo(f"  {table}: {count:,} records")
                
                coverage = results.get('market_commodity_coverage', 0)
                click.echo(f"Market-Commodity Coverage: {coverage:.1%}")
                
                orphaned = results.get('orphaned_price_records', 0)
                if orphaned > 0:
                    click.echo(f"⚠️  Orphaned price records: {orphaned}", color='yellow')
                
            else:
                click.echo("Running comprehensive validation...")
                report = await validator.validate_migration()
                
                # Display summary
                click.echo(f"\nValidation Status: {report.overall_status}")
                click.echo(f"Rules Passed: {report.passed_rules}/{report.total_rules}")
                
                if report.critical_failures:
                    click.echo(f"❌ Critical Failures: {len(report.critical_failures)}", color='red')
                    for failure in report.critical_failures[:3]:
                        click.echo(f"  - {failure.rule_name}", color='red')
                
                if report.warnings:
                    click.echo(f"⚠️  Warnings: {len(report.warnings)}", color='yellow')
                
                # Coverage report
                coverage = report.data_coverage_report
                overall_coverage = coverage.get('overall_coverage', 0)
                click.echo(f"Overall Data Coverage: {overall_coverage:.1%}")
                
                # Recommendations
                if report.recommendations:
                    click.echo("\nRecommendations:")
                    for rec in report.recommendations[:5]:
                        click.echo(f"  • {rec}")
                
                # Save detailed report
                if output:
                    validator.export_validation_report(report, Path(output))
                    click.echo(f"\nDetailed report saved to: {output}")
    
    asyncio.run(run_validation())


@cli.command()
@click.option('--backup-path', required=True, type=click.Path(exists=True), 
              help='Path to backup storage')
@click.option('--v2-db-url', required=True, 
              help='PostgreSQL connection string for V2 database')
@click.option('--target-state', 
              type=click.Choice(['pre_migration', 'checkpoint', 'specific_backup']),
              default='pre_migration',
              help='Target state for rollback')
@click.option('--backup-id', 
              help='Specific backup ID (required for specific_backup target)')
@click.option('--dry-run', is_flag=True, 
              help='Create rollback plan without executing')
def rollback(
    backup_path: str,
    v2_db_url: str,
    target_state: str,
    backup_id: Optional[str],
    dry_run: bool
) -> None:
    """Rollback migration to previous state."""
    
    if target_state == 'specific_backup' and not backup_id:
        click.echo("❌ --backup-id required for specific_backup target", color='red')
        return
    
    async def run_rollback():
        async with BackupRollbackManager(v2_db_url, Path(backup_path)) as backup_manager:
            # Create rollback plan
            click.echo(f"Creating rollback plan for target state: {target_state}")
            rollback_plan = await backup_manager.create_rollback_plan(target_state, backup_id)
            
            click.echo(f"\nRollback Plan: {rollback_plan.rollback_id}")
            click.echo(f"Target Backup: {rollback_plan.backup_to_restore}")
            click.echo(f"Estimated Duration: {rollback_plan.estimated_duration_minutes} minutes")
            click.echo(f"Required Downtime: {rollback_plan.required_downtime_minutes} minutes")
            click.echo(f"Risk Assessment: {rollback_plan.risk_assessment}")
            
            click.echo(f"\nRollback Steps:")
            for i, step in enumerate(rollback_plan.rollback_steps, 1):
                critical = "🔴" if step.get('critical') else "🟡"
                click.echo(f"  {i}. {critical} {step['name']} ({step.get('estimated_duration_minutes', 0)}min)")
            
            if dry_run:
                click.echo("\n=== DRY RUN MODE ===")
                click.echo("Rollback plan created successfully")
                return
            
            # Confirm before proceeding
            if rollback_plan.risk_assessment == 'high':
                click.echo(f"\n⚠️  HIGH RISK ROLLBACK", color='red')
                
            if not click.confirm(f'\nProceed with rollback? This will restore the database to a previous state.'):
                click.echo("Rollback cancelled")
                return
            
            # Execute rollback
            click.echo("\nExecuting rollback...")
            result = await backup_manager.execute_rollback(rollback_plan)
            
            if result.success:
                click.echo(f"\n✅ Rollback completed successfully!", color='green')
                click.echo(f"Duration: {result.duration_seconds / 60:.1f} minutes")
                click.echo(f"Steps completed: {result.steps_completed}")
                click.echo(f"Validation passed: {result.final_validation_passed}")
                
                if result.data_loss_detected:
                    click.echo(f"⚠️  Data loss detected during rollback", color='red')
                
            else:
                click.echo(f"\n❌ Rollback failed!", color='red')
                click.echo(f"Steps completed: {result.steps_completed}/{result.steps_completed + result.steps_failed}")
                
                for error in result.error_messages[-3:]:
                    click.echo(f"  - {error}", color='red')
                
                click.echo("\nRecovery recommendations:")
                for rec in result.recovery_recommendations:
                    click.echo(f"  • {rec}")
    
    asyncio.run(run_rollback())


@cli.command()
@click.option('--backup-path', required=True, type=click.Path(exists=True), 
              help='Path to backup storage')
def list_backups(backup_path: str) -> None:
    """List available backups."""
    
    async def list_backups_async():
        # Create a temporary backup manager to access registry
        async with BackupRollbackManager("postgresql://dummy", Path(backup_path)) as backup_manager:
            status = backup_manager.get_backup_status()
            
            click.echo(f"Backup Storage: {backup_path}")
            click.echo(f"Total Backups: {status['total_backups']}")
            click.echo(f"Total Size: {status['total_backup_size_mb']:.1f} MB")
            
            if status['total_backups'] == 0:
                click.echo("No backups found")
                return
            
            click.echo(f"\nBackup Types:")
            for backup_type, count in status['backup_types'].items():
                if count > 0:
                    click.echo(f"  {backup_type}: {count}")
            
            click.echo(f"\nLatest Backup: {status.get('latest_backup', 'N/A')}")
            click.echo(f"Oldest Backup: {status.get('oldest_backup', 'N/A')}")
            
            # List individual backups
            click.echo(f"\nBackup Details:")
            for backup_id, backup_metadata in backup_manager.backup_registry.items():
                age_hours = (datetime.now() - backup_metadata.timestamp).total_seconds() / 3600
                size_mb = backup_metadata.backup_size_bytes / (1024 * 1024)
                
                click.echo(f"  📦 {backup_id}")
                click.echo(f"     Type: {backup_metadata.backup_type}")
                click.echo(f"     Created: {backup_metadata.timestamp.strftime('%Y-%m-%d %H:%M:%S')} ({age_hours:.1f}h ago)")
                click.echo(f"     Size: {size_mb:.1f} MB")
                click.echo(f"     Status: {backup_metadata.status}")
                click.echo(f"     Recovery Tested: {'✅' if backup_metadata.recovery_tested else '❌'}")
                click.echo()
    
    asyncio.run(list_backups_async())


@cli.command()
@click.option('--state-file', required=True, type=click.Path(exists=True), 
              help='Migration state file to analyze')
def status(state_file: str) -> None:
    """Show migration status from state file."""
    
    try:
        with open(state_file) as f:
            state_data = json.load(f)
        
        click.echo(f"Migration ID: {state_data['migration_id']}")
        click.echo(f"Overall Status: {state_data['overall_status']}")
        click.echo(f"Current Phase: {state_data['current_phase']}")
        click.echo(f"Progress: {state_data['overall_progress_percentage']:.1f}%")
        
        if state_data.get('start_time'):
            start_time = datetime.fromisoformat(state_data['start_time'])
            if state_data.get('end_time'):
                end_time = datetime.fromisoformat(state_data['end_time'])
                duration = (end_time - start_time).total_seconds() / 3600
                click.echo(f"Duration: {duration:.1f} hours")
            else:
                elapsed = (datetime.now() - start_time).total_seconds() / 3600
                click.echo(f"Elapsed: {elapsed:.1f} hours")
        
        # Show phase status
        click.echo(f"\nPhase Status:")
        phase_progress = state_data.get('phase_progress', {})
        for phase, progress in phase_progress.items():
            status_icon = {
                'completed': '✅',
                'in_progress': '🔄',
                'failed': '❌',
                'not_started': '⏳'
            }.get(progress.get('status', 'not_started'), '❓')
            
            click.echo(f"  {status_icon} {phase}: {progress.get('progress_percentage', 0):.1f}%")
        
        # Show errors if any
        errors = state_data.get('error_summary', [])
        if errors:
            click.echo(f"\nErrors ({len(errors)}):")
            for error in errors[-3:]:  # Show last 3 errors
                click.echo(f"  ❌ {error}")
        
        # Show performance summary
        perf = state_data.get('performance_summary', {})
        if perf:
            click.echo(f"\nPerformance Summary:")
            if 'total_records_migrated' in perf:
                click.echo(f"  Records Migrated: {perf['total_records_migrated']:,}")
            if 'average_throughput_rps' in perf:
                click.echo(f"  Average Throughput: {perf['average_throughput_rps']:.0f} rec/s")
            if 'data_coverage_achieved' in perf:
                click.echo(f"  Data Coverage: {perf['data_coverage_achieved']:.1%}")
        
    except Exception as e:
        click.echo(f"❌ Failed to read state file: {e}", color='red')


@cli.command()
@click.option('--backup-path', required=True, type=click.Path(exists=True), 
              help='Path to backup storage')
@click.option('--retention-days', default=30, type=int, 
              help='Retention period in days')
@click.option('--dry-run', is_flag=True, 
              help='Show what would be cleaned without deleting')
def cleanup(backup_path: str, retention_days: int, dry_run: bool) -> None:
    """Cleanup old backups based on retention policy."""
    
    async def run_cleanup():
        async with BackupRollbackManager("postgresql://dummy", Path(backup_path)) as backup_manager:
            if dry_run:
                click.echo(f"=== DRY RUN MODE ===")
                click.echo(f"Would cleanup backups older than {retention_days} days")
                
                # Show what would be cleaned
                cleanup_date = datetime.now() - timedelta(days=retention_days)
                old_backups = [
                    backup_id for backup_id, backup_metadata in backup_manager.backup_registry.items()
                    if backup_metadata.retention_until < cleanup_date
                ]
                
                if old_backups:
                    click.echo(f"\nBackups to be cleaned ({len(old_backups)}):")
                    for backup_id in old_backups:
                        click.echo(f"  📦 {backup_id}")
                else:
                    click.echo("\nNo backups to cleanup")
                
            else:
                click.echo(f"Cleaning up backups older than {retention_days} days...")
                cleaned_backups = backup_manager.cleanup_old_backups(retention_days)
                
                if cleaned_backups:
                    click.echo(f"✅ Cleaned up {len(cleaned_backups)} old backups")
                    for backup_id in cleaned_backups:
                        click.echo(f"  🗑️  {backup_id}")
                else:
                    click.echo("No old backups to cleanup")
    
    asyncio.run(run_cleanup())


if __name__ == '__main__':
    cli()