#!/usr/bin/env python3
"""Validation tool for v1 to v2 migration."""

import asyncio
import sys
from pathlib import Path
from typing import Dict, List, Tuple

import click
import pandas as pd
from rich.console import Console
from rich.table import Table

# Add v2 to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.infrastructure.adapters import V1Adapter
from src.shared.container import Container


console = Console()


class MigrationValidator:
    """Validates data migration from v1 to v2."""
    
    def __init__(self, v1_path: Path, container: Container):
        """Initialize validator."""
        self.v1_adapter = V1Adapter(v1_path)
        self.container = container
        self.validation_results = {
            "markets": {"status": "pending", "issues": []},
            "prices": {"status": "pending", "issues": []},
            "analysis": {"status": "pending", "issues": []}
        }
    
    async def validate_all(self) -> Dict[str, Dict]:
        """Validate all aspects of migration."""
        console.print("[bold blue]Validating migration...[/bold blue]")
        
        # Validate markets
        await self._validate_markets()
        
        # Validate prices
        await self._validate_prices()
        
        # Validate analysis compatibility
        await self._validate_analysis()
        
        return self.validation_results
    
    async def _validate_markets(self) -> None:
        """Validate market data migration."""
        console.print("\n[cyan]Validating markets...[/cyan]")
        
        try:
            # Get v1 markets
            v1_data = self.v1_adapter.import_v1_data()
            if "markets" not in v1_data:
                self.validation_results["markets"]["status"] = "no_data"
                return
            
            v1_markets = v1_data["markets"]
            v1_count = len(v1_markets)
            
            # Get v2 markets
            async with self.container.unit_of_work() as uow:
                v2_markets = await uow.markets.find_active_at(pd.Timestamp.now())
                v2_count = len(v2_markets)
            
            # Compare counts
            if v1_count != v2_count:
                self.validation_results["markets"]["issues"].append(
                    f"Count mismatch: v1={v1_count}, v2={v2_count}"
                )
            
            # Check for missing markets
            v1_ids = set(v1_markets.get("market_id", []))
            v2_ids = {m.market_id.value for m in v2_markets}
            
            missing_in_v2 = v1_ids - v2_ids
            if missing_in_v2:
                self.validation_results["markets"]["issues"].append(
                    f"Missing in v2: {list(missing_in_v2)[:5]}..."
                )
            
            # Set status
            if not self.validation_results["markets"]["issues"]:
                self.validation_results["markets"]["status"] = "valid"
            else:
                self.validation_results["markets"]["status"] = "invalid"
                
        except Exception as e:
            self.validation_results["markets"]["status"] = "error"
            self.validation_results["markets"]["issues"].append(str(e))
    
    async def _validate_prices(self) -> None:
        """Validate price data migration."""
        console.print("\n[cyan]Validating prices...[/cyan]")
        
        try:
            # Sample validation - check price ranges
            async with self.container.unit_of_work() as uow:
                # Get sample prices for a common commodity
                from src.core.domain.market.value_objects import MarketId, Commodity
                
                commodity = Commodity(
                    code="WHEAT_FLOUR",
                    name="Wheat Flour",
                    category="cereal",
                    standard_unit="kg"
                )
                
                # Get any market
                markets = await uow.markets.find_active_at(pd.Timestamp.now())
                if markets:
                    sample_prices = await uow.prices.find_by_market_and_commodity(
                        markets[0].market_id,
                        commodity,
                        pd.Timestamp("2023-01-01"),
                        pd.Timestamp("2023-12-31")
                    )
                    
                    if sample_prices:
                        price_values = [float(p.price.amount) for p in sample_prices]
                        
                        # Basic sanity checks
                        if min(price_values) <= 0:
                            self.validation_results["prices"]["issues"].append(
                                "Found zero or negative prices"
                            )
                        
                        if max(price_values) > 10000:
                            self.validation_results["prices"]["issues"].append(
                                "Found extremely high prices (>10000)"
                            )
            
            if not self.validation_results["prices"]["issues"]:
                self.validation_results["prices"]["status"] = "valid"
            else:
                self.validation_results["prices"]["status"] = "invalid"
                
        except Exception as e:
            self.validation_results["prices"]["status"] = "error"
            self.validation_results["prices"]["issues"].append(str(e))
    
    async def _validate_analysis(self) -> None:
        """Validate analysis compatibility."""
        console.print("\n[cyan]Validating analysis compatibility...[/cyan]")
        
        try:
            # Test running a simple analysis through v1 adapter
            test_result = await self.v1_adapter.run_analysis(
                market_ids=["SANAA_CENTRAL"],
                commodity_ids=["WHEAT_FLOUR"],
                start_date="2023-01-01",
                end_date="2023-03-31"
            )
            
            if test_result:
                self.validation_results["analysis"]["status"] = "compatible"
            else:
                self.validation_results["analysis"]["status"] = "no_results"
                
        except Exception as e:
            self.validation_results["analysis"]["status"] = "incompatible"
            self.validation_results["analysis"]["issues"].append(str(e))
    
    def print_report(self) -> None:
        """Print validation report."""
        console.print("\n[bold]Migration Validation Report[/bold]")
        
        # Create summary table
        table = Table(title="Validation Summary")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="bold")
        table.add_column("Issues", style="red")
        
        for component, results in self.validation_results.items():
            status = results["status"]
            issue_count = len(results["issues"])
            
            # Color code status
            if status == "valid" or status == "compatible":
                status_display = f"[green]{status}[/green]"
            elif status == "pending":
                status_display = f"[yellow]{status}[/yellow]"
            else:
                status_display = f"[red]{status}[/red]"
            
            table.add_row(
                component.capitalize(),
                status_display,
                str(issue_count) if issue_count > 0 else "-"
            )
        
        console.print(table)
        
        # Print detailed issues
        for component, results in self.validation_results.items():
            if results["issues"]:
                console.print(f"\n[bold red]{component.capitalize()} Issues:[/bold red]")
                for issue in results["issues"]:
                    console.print(f"  • {issue}")


@click.command()
@click.option(
    "--v1-path",
    type=click.Path(exists=True, path_type=Path),
    default="../",
    help="Path to v1 codebase"
)
@click.option(
    "--db-url",
    default="postgresql://localhost/yemen_market_v2",
    help="Database URL for v2"
)
def main(v1_path: Path, db_url: str):
    """Validate v1 to v2 migration."""
    # Create container
    container = Container()
    container.config.database.url.from_value(db_url)
    
    # Create validator
    validator = MigrationValidator(v1_path, container)
    
    # Run validation
    try:
        asyncio.run(validator.validate_all())
        validator.print_report()
        
        # Check if all valid
        all_valid = all(
            r["status"] in ["valid", "compatible", "no_data"]
            for r in validator.validation_results.values()
        )
        
        if all_valid:
            console.print("\n[bold green]✓ Migration validation passed![/bold green]")
        else:
            console.print("\n[bold red]✗ Migration validation failed![/bold red]")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"\n[bold red]Validation error: {e}[/bold red]")
        sys.exit(1)


if __name__ == "__main__":
    main()