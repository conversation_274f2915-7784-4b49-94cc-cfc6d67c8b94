# V1 to V2 Data Migration System

This directory contains a comprehensive data migration system for transferring data from the Yemen Market Integration V1 system to the V2 PostgreSQL-based architecture.

## Overview

The migration system provides:

- **Zero-downtime migration** with comprehensive backup and rollback capabilities
- **Data validation and integrity checking** throughout the migration process
- **Progress tracking and monitoring** with real-time status updates
- **Batch processing and parallel execution** for optimal performance
- **Comprehensive error handling and recovery** procedures

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   V1 Data       │    │  Migration      │    │   V2 Database   │
│   Sources       │───▶│  Orchestrator   │───▶│   (PostgreSQL)  │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        │                       ▼                       │
        │              ┌─────────────────┐              │
        │              │  Backup &       │              │
        └──────────────│  Rollback       │──────────────┘
                       │  Manager        │
                       └─────────────────┘
```

## Components

### 1. Data Exporter (`v1_data_exporter.py`)
- Exports data from V1 system with validation
- Supports parallel processing and compression
- Maintains data integrity with checksums
- Tracks export metadata and coverage

### 2. Data Transformer (`data_transformer.py`)
- Transforms V1 data to V2 schema format
- Handles data type conversions and standardization
- Validates data quality during transformation
- Provides detailed transformation statistics

### 3. PostgreSQL Importer (`postgres_importer.py`)
- High-performance batch import to PostgreSQL
- Connection pooling and parallel processing
- Transaction management and error recovery
- Performance monitoring and optimization

### 4. Migration Validator (`migration_validator.py`)
- Comprehensive validation suite with 20+ validation rules
- Data integrity and consistency checks
- Performance and coverage validation
- Detailed reporting and recommendations

### 5. Backup & Rollback Manager (`backup_rollback_manager.py`)
- Pre-migration, checkpoint, and post-migration backups
- Automated rollback procedures with risk assessment
- Backup integrity testing and verification
- Retention policy management

### 6. Migration Orchestrator (`migration_orchestrator.py`)
- Coordinates entire migration process
- Real-time progress tracking and monitoring
- Error handling and recovery coordination
- Phase-based execution with checkpoints

## Quick Start

### Prerequisites

1. **Python 3.8+** with required dependencies:
   ```bash
   pip install asyncpg pandas click psutil
   ```

2. **PostgreSQL tools** (`pg_dump`, `pg_restore`) in PATH

3. **Access to V1 data** and V2 PostgreSQL database

### Basic Migration

```bash
# Navigate to migration tools
cd /path/to/yemen-market-integration/v2/tools/migration

# Run migration with basic options
python migrate_cli.py migrate \
    --v1-path /path/to/v1/data \
    --v2-db-url ********************************/db \
    --backup-path /path/to/backups \
    --export-path /path/to/exports
```

### Migration with Custom Options

```bash
python migrate_cli.py migrate \
    --v1-path /path/to/v1/data \
    --v2-db-url ********************************/db \
    --backup-path /path/to/backups \
    --export-path /path/to/exports \
    --batch-size 5000 \
    --workers 8 \
    --timeout 720
```

## Detailed Usage

### Migration Process

The migration follows these phases:

1. **Preparation**: Initialize components and validate configuration
2. **Pre-backup**: Create backup of current V2 state
3. **Export**: Extract data from V1 system
4. **Transform**: Convert data to V2 schema format
5. **Import**: Load data into V2 PostgreSQL database
6. **Validation**: Comprehensive data validation and integrity checks
7. **Post-backup**: Create backup of migrated state
8. **Cleanup**: Clean up temporary files and finalize

### Command Reference

#### Migration Commands

```bash
# Full migration
migrate_cli.py migrate [OPTIONS]

# Options:
--v1-path PATH          # Path to V1 data directory [required]
--v2-db-url TEXT        # PostgreSQL connection string [required]
--backup-path PATH      # Backup storage path [required]  
--export-path PATH      # Export storage path [required]
--batch-size INTEGER    # Batch size for processing [default: 1000]
--workers INTEGER       # Number of parallel workers [default: 4]
--timeout INTEGER       # Migration timeout in minutes [default: 480]
--no-backup            # Skip backup creation (DANGEROUS)
--no-rollback          # Disable automatic rollback
--dry-run              # Validate configuration only
```

#### Validation Commands

```bash
# Quick validation
migrate_cli.py validate --v2-db-url URL --quick

# Comprehensive validation
migrate_cli.py validate --v2-db-url URL --output report.json
```

#### Backup Management

```bash
# List backups
migrate_cli.py list-backups --backup-path /path/to/backups

# Rollback to pre-migration state
migrate_cli.py rollback \
    --backup-path /path/to/backups \
    --v2-db-url URL \
    --target-state pre_migration

# Cleanup old backups
migrate_cli.py cleanup \
    --backup-path /path/to/backups \
    --retention-days 30
```

#### Status Monitoring

```bash
# Check migration status
migrate_cli.py status --state-file migration_state_*.json
```

### Configuration Options

#### Migration Configuration

```python
config = MigrationConfiguration(
    migration_id="custom_migration_001",
    v1_data_path=Path("/data/v1"),
    v2_connection_string="********************************/db",
    backup_storage_path=Path("/backups"),
    export_storage_path=Path("/exports"),
    
    # Performance options
    batch_size=1000,
    parallel_workers=4,
    enable_compression=True,
    
    # Safety options
    create_backups=True,
    enable_rollback=True,
    validate_before_import=True,
    
    # Quality requirements
    min_data_coverage=0.80,
    target_data_coverage=0.884,
    max_validation_failures=5,
    timeout_minutes=480
)
```

## Data Coverage and Quality

### Expected Data Coverage

The migration system maintains V1's **88.4% data coverage** target:

- **Markets**: 21 markets from balanced panel
- **Commodities**: 16 commodities (grains, legumes, vegetables, fuel)
- **Price Observations**: ~25,200 total observations
- **Temporal Coverage**: 75 months (2019-2025)
- **Conflict Events**: ACLED data from 2019-2024

### Quality Assurance

#### Validation Rules (20+ checks)

1. **Count Validation**
   - Market count verification (21 expected)
   - Commodity count verification (16 expected)
   - Minimum price observations (20,000+)

2. **Integrity Validation**
   - Foreign key constraints
   - Referential integrity
   - Orphaned record detection

3. **Quality Validation**
   - Negative/zero price detection
   - Extreme outlier detection
   - Invalid coordinate detection
   - Future/historical date validation

4. **Consistency Validation**
   - Market name consistency
   - Currency distribution analysis
   - Data format standardization

5. **Performance Validation**
   - Query performance benchmarks
   - Index effectiveness testing

#### Data Transformation Quality

- **Standardization**: Market IDs, commodity codes, units
- **Validation**: Coordinate bounds, price ranges, date formats
- **Deduplication**: Remove duplicate observations
- **Enrichment**: Add missing metadata and classifications

## Backup and Recovery

### Backup Types

1. **Pre-migration Backup**
   - Full database backup before migration starts
   - Tested for integrity and recoverability
   - Forms basis for rollback operations

2. **Checkpoint Backups**
   - Created at key migration phases
   - Allows partial rollback to specific points
   - Minimizes data loss in case of issues

3. **Post-migration Backup**
   - Complete backup after successful migration
   - Establishes new baseline for V2 system
   - Comprehensive integrity testing

### Rollback Procedures

#### Automatic Rollback
- Triggered on critical migration failures
- Uses pre-migration backup as restore point
- Validates system state after rollback

#### Manual Rollback
```bash
# Create rollback plan
migrate_cli.py rollback \
    --backup-path /backups \
    --v2-db-url URL \
    --target-state pre_migration \
    --dry-run

# Execute rollback
migrate_cli.py rollback \
    --backup-path /backups \
    --v2-db-url URL \
    --target-state pre_migration
```

#### Rollback Risk Assessment

- **Low Risk**: Recent backup, tested recovery, small data changes
- **Medium Risk**: Older backup or untested recovery
- **High Risk**: Very old backup or significant data changes

## Performance Optimization

### Batch Processing

```python
# Optimal batch sizes by data type
batch_sizes = {
    'markets': 100,        # Small reference data
    'commodities': 50,     # Very small reference data  
    'prices': 5000,        # Large transactional data
    'conflicts': 1000,     # Medium event data
    'analysis_results': 10 # Large JSON documents
}
```

### Parallel Processing

- **Export**: Parallel file processing by data type
- **Transform**: Batch transformation with multiple workers
- **Import**: Connection pooling with concurrent batches
- **Validation**: Parallel rule execution

### Database Optimization

```sql
-- Pre-import optimizations
SET synchronous_commit = OFF;
SET wal_buffers = '16MB';
SET checkpoint_segments = 32;

-- Post-import optimizations
CREATE INDEX CONCURRENTLY idx_prices_market_date 
    ON price_observations(market_id, observed_date);
VACUUM ANALYZE;
```

## Monitoring and Logging

### Progress Tracking

The system provides real-time progress tracking:

```python
def progress_callback(migration_state):
    print(f"Phase: {migration_state.current_phase.value}")
    print(f"Progress: {migration_state.overall_progress_percentage:.1f}%")
    print(f"Status: {migration_state.overall_status.value}")
```

### Logging Configuration

```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('migration.log')
    ]
)
```

### Metrics Collection

Key metrics tracked during migration:

- **Throughput**: Records per second by data type
- **Coverage**: Percentage of expected data migrated
- **Quality**: Validation pass rates and error counts
- **Performance**: Query execution times and resource usage
- **Reliability**: Success rates and error patterns

## Troubleshooting

### Common Issues

#### 1. Connection Timeouts
```bash
# Increase timeout for large datasets
--timeout 720  # 12 hours
```

#### 2. Memory Issues
```bash
# Reduce batch size and workers
--batch-size 500 --workers 2
```

#### 3. Validation Failures
```bash
# Run validation separately to diagnose
migrate_cli.py validate --v2-db-url URL --output report.json
```

#### 4. Backup Failures
```bash
# Check disk space and permissions
df -h /backup/path
ls -la /backup/path
```

### Recovery Procedures

#### Partial Migration Failure
1. Check migration state file for error details
2. Review logs for specific failure points
3. Fix data quality issues if identified
4. Resume from checkpoint or restart migration

#### Database Corruption
1. Stop all migration processes
2. Assess extent of corruption
3. Execute rollback to pre-migration state
4. Investigate root cause before retrying

#### Rollback Failure
1. Manual database restoration from backup files
2. Contact database administrator
3. Review backup integrity and procedures

### Performance Issues

#### Slow Import Performance
```python
# Increase batch size and connection pool
config.batch_size = 10000
config.connection_pool_size = 20
```

#### High Memory Usage
```python
# Process data in smaller chunks
config.batch_size = 500
config.parallel_workers = 2
```

#### Database Lock Conflicts
```sql
-- Check for blocking queries
SELECT * FROM pg_stat_activity WHERE wait_event_type = 'Lock';

-- Consider reducing concurrency
config.max_concurrent_batches = 2
```

## Best Practices

### Before Migration

1. **Test on Non-Production**: Always test migration on copy of production data
2. **Backup Verification**: Verify all backups can be restored successfully
3. **Resource Planning**: Ensure adequate disk space and processing capacity
4. **Downtime Planning**: Schedule migration during low-usage periods
5. **Communication**: Notify stakeholders of migration schedule and procedures

### During Migration

1. **Monitor Progress**: Use progress callbacks and log monitoring
2. **Resource Monitoring**: Watch CPU, memory, and disk usage
3. **Error Response**: Have escalation procedures for critical failures
4. **Communication**: Provide regular status updates to stakeholders

### After Migration

1. **Validation**: Run comprehensive validation suite
2. **Performance Testing**: Verify V2 system performance meets requirements
3. **Documentation**: Update system documentation with migration details
4. **Backup Schedule**: Establish regular backup schedule for V2 system
5. **Monitoring**: Set up ongoing monitoring and alerting

## Security Considerations

### Access Control

- Database connections use principle of least privilege
- Backup files encrypted at rest
- Audit trail maintained for all migration operations

### Data Protection

- Connection strings stored securely (environment variables)
- Temporary files cleaned up after migration
- Sensitive data masked in logs and reports

### Network Security

- Database connections over encrypted channels (SSL/TLS)
- Backup transfers use secure protocols
- Network access restricted to authorized hosts

## Support and Contact

For migration support:

1. **Documentation**: Review this guide and API documentation
2. **Logs**: Check migration logs for detailed error information
3. **Validation**: Run validation reports to identify issues
4. **Rollback**: Use rollback procedures if migration fails

## License

This migration system is part of the Yemen Market Integration project and follows the same licensing terms as the main project.