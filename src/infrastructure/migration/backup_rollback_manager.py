"""Comprehensive backup and rollback management for migration operations."""

import asyncio
import asyncpg
import pandas as pd
import subprocess
import shutil
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import logging
from dataclasses import dataclass, asdict
import json
import gzip
import hashlib
import time
import os
import tempfile

from src.infrastructure.observability.structured_logging import StructuredLogger


@dataclass
class BackupMetadata:
    """Metadata for backup operations."""
    backup_id: str
    backup_type: str  # 'full', 'incremental', 'schema_only', 'data_only'
    timestamp: datetime
    database_name: str
    tables_included: List[str]
    backup_size_bytes: int
    compression_used: bool
    checksum: str
    backup_duration_seconds: float
    status: str  # 'completed', 'failed', 'in_progress'
    backup_path: str
    recovery_tested: bool
    retention_until: datetime


@dataclass
class RollbackPlan:
    """Plan for rollback operations."""
    rollback_id: str
    target_state: str  # 'pre_migration', 'checkpoint', 'specific_backup'
    backup_to_restore: str
    estimated_duration_minutes: int
    required_downtime_minutes: int
    rollback_steps: List[Dict[str, Any]]
    validation_steps: List[str]
    risk_assessment: str  # 'low', 'medium', 'high'
    created_at: datetime


@dataclass
class RollbackResult:
    """Result of rollback operation."""
    rollback_id: str
    success: bool
    duration_seconds: float
    steps_completed: int
    steps_failed: int
    final_validation_passed: bool
    data_loss_detected: bool
    error_messages: List[str]
    recovery_recommendations: List[str]


class BackupRollbackManager:
    """Comprehensive backup and rollback management system."""
    
    def __init__(
        self,
        connection_string: str,
        backup_storage_path: Path,
        pg_dump_path: str = "pg_dump",
        pg_restore_path: str = "pg_restore"
    ):
        """Initialize the backup and rollback manager."""
        self.connection_string = connection_string
        self.backup_storage_path = Path(backup_storage_path)
        self.pg_dump_path = pg_dump_path
        self.pg_restore_path = pg_restore_path
        self.logger = StructuredLogger("BackupRollbackManager")
        
        # Ensure backup storage exists
        self.backup_storage_path.mkdir(parents=True, exist_ok=True)
        
        # Backup tracking
        self.backup_registry = {}
        self.rollback_history = {}
        
        # Load existing backup registry
        self._load_backup_registry()
        
        # Connection pool
        self.connection_pool = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._initialize_connection_pool()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._cleanup_connection_pool()
    
    async def _initialize_connection_pool(self):
        """Initialize database connection pool."""
        try:
            self.connection_pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=1,
                max_size=3,
                command_timeout=600
            )
            self.logger.info("Backup manager connection pool initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize connection pool: {e}")
            raise
    
    async def _cleanup_connection_pool(self):
        """Cleanup connection pool."""
        if self.connection_pool:
            await self.connection_pool.close()
            self.logger.info("Backup manager connection pool closed")
    
    async def create_pre_migration_backup(
        self,
        include_analysis_results: bool = True
    ) -> BackupMetadata:
        """Create comprehensive backup before migration starts."""
        backup_id = f"pre_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"Creating pre-migration backup: {backup_id}")
        
        try:
            # Determine tables to backup
            tables_to_backup = await self._get_migration_tables()
            if not include_analysis_results:
                tables_to_backup = [t for t in tables_to_backup if t != 'analysis_results']
            
            # Create full database backup
            backup_metadata = await self._create_database_backup(
                backup_id=backup_id,
                backup_type="full",
                tables=tables_to_backup,
                compress=True
            )
            
            # Test backup integrity
            await self._test_backup_integrity(backup_metadata)
            
            # Mark as pre-migration backup
            backup_metadata.backup_type = "pre_migration"
            self._register_backup(backup_metadata)
            
            self.logger.info(f"Pre-migration backup completed: {backup_id}")
            return backup_metadata
            
        except Exception as e:
            self.logger.error(f"Pre-migration backup failed: {e}")
            raise
    
    async def create_checkpoint_backup(
        self,
        checkpoint_name: str,
        tables: Optional[List[str]] = None
    ) -> BackupMetadata:
        """Create checkpoint backup during migration process."""
        backup_id = f"checkpoint_{checkpoint_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"Creating checkpoint backup: {backup_id}")
        
        try:
            if not tables:
                tables = await self._get_migration_tables()
            
            # Create incremental backup focused on recently modified tables
            backup_metadata = await self._create_database_backup(
                backup_id=backup_id,
                backup_type="checkpoint",
                tables=tables,
                compress=True
            )
            
            # Quick integrity check
            await self._verify_backup_exists(backup_metadata)
            
            self._register_backup(backup_metadata)
            
            self.logger.info(f"Checkpoint backup completed: {backup_id}")
            return backup_metadata
            
        except Exception as e:
            self.logger.error(f"Checkpoint backup failed: {e}")
            raise
    
    async def create_post_migration_backup(self) -> BackupMetadata:
        """Create backup after successful migration."""
        backup_id = f"post_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"Creating post-migration backup: {backup_id}")
        
        try:
            # Create full backup of migrated data
            tables = await self._get_migration_tables()
            
            backup_metadata = await self._create_database_backup(
                backup_id=backup_id,
                backup_type="post_migration",
                tables=tables,
                compress=True
            )
            
            # Comprehensive integrity test
            await self._test_backup_integrity(backup_metadata)
            
            self._register_backup(backup_metadata)
            
            self.logger.info(f"Post-migration backup completed: {backup_id}")
            return backup_metadata
            
        except Exception as e:
            self.logger.error(f"Post-migration backup failed: {e}")
            raise
    
    async def _create_database_backup(
        self,
        backup_id: str,
        backup_type: str,
        tables: List[str],
        compress: bool = True
    ) -> BackupMetadata:
        """Create database backup using pg_dump."""
        start_time = datetime.now()
        backup_path = self.backup_storage_path / f"{backup_id}.sql"
        
        if compress:
            backup_path = backup_path.with_suffix('.sql.gz')
        
        try:
            # Parse connection string for pg_dump
            conn_params = self._parse_connection_string()
            
            # Build pg_dump command
            dump_cmd = [
                self.pg_dump_path,
                "--host", conn_params['host'],
                "--port", str(conn_params['port']),
                "--username", conn_params['user'],
                "--dbname", conn_params['database'],
                "--verbose",
                "--no-password",
                "--format=custom" if not compress else "--format=plain",
                "--encoding=UTF8"
            ]
            
            # Add table specifications
            for table in tables:
                dump_cmd.extend(["--table", table])
            
            # Set environment for password
            env = os.environ.copy()
            env['PGPASSWORD'] = conn_params['password']
            
            self.logger.info(f"Starting database dump: {' '.join(dump_cmd[:5])}...")
            
            # Execute pg_dump
            if compress:
                # Use gzip compression
                with gzip.open(backup_path, 'wt') as gz_file:
                    process = subprocess.run(
                        dump_cmd,
                        stdout=gz_file,
                        stderr=subprocess.PIPE,
                        env=env,
                        check=True,
                        text=True
                    )
            else:
                with open(backup_path, 'w') as f:
                    process = subprocess.run(
                        dump_cmd,
                        stdout=f,
                        stderr=subprocess.PIPE,
                        env=env,
                        check=True,
                        text=True
                    )
            
            # Calculate backup metadata
            backup_size = backup_path.stat().st_size
            checksum = self._calculate_file_checksum(backup_path)
            duration = (datetime.now() - start_time).total_seconds()
            
            backup_metadata = BackupMetadata(
                backup_id=backup_id,
                backup_type=backup_type,
                timestamp=start_time,
                database_name=conn_params['database'],
                tables_included=tables,
                backup_size_bytes=backup_size,
                compression_used=compress,
                checksum=checksum,
                backup_duration_seconds=duration,
                status='completed',
                backup_path=str(backup_path),
                recovery_tested=False,
                retention_until=datetime.now() + timedelta(days=30)
            )
            
            self.logger.info(
                f"Backup completed: {backup_id}, Size: {backup_size / (1024*1024):.1f}MB, "
                f"Duration: {duration:.1f}s"
            )
            
            return backup_metadata
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"pg_dump failed: {e.stderr}")
            # Cleanup partial backup
            if backup_path.exists():
                backup_path.unlink()
            raise
        except Exception as e:
            self.logger.error(f"Backup creation failed: {e}")
            # Cleanup partial backup
            if backup_path.exists():
                backup_path.unlink()
            raise
    
    async def _test_backup_integrity(self, backup_metadata: BackupMetadata) -> bool:
        """Test backup integrity by attempting restoration to temporary database."""
        self.logger.info(f"Testing backup integrity: {backup_metadata.backup_id}")
        
        temp_db_name = f"test_restore_{backup_metadata.backup_id}"
        
        try:
            # Create temporary database for testing
            await self._create_temporary_database(temp_db_name)
            
            # Attempt restoration
            restore_success = await self._restore_backup_to_database(
                backup_metadata,
                temp_db_name
            )
            
            if restore_success:
                # Verify data integrity
                integrity_check = await self._verify_restored_data_integrity(temp_db_name)
                
                if integrity_check:
                    backup_metadata.recovery_tested = True
                    self.logger.info(f"Backup integrity test passed: {backup_metadata.backup_id}")
                    return True
                else:
                    self.logger.error(f"Backup integrity test failed: data corruption detected")
                    return False
            else:
                self.logger.error(f"Backup integrity test failed: restoration failed")
                return False
            
        except Exception as e:
            self.logger.error(f"Backup integrity test error: {e}")
            return False
        finally:
            # Cleanup temporary database
            try:
                await self._drop_temporary_database(temp_db_name)
            except Exception as e:
                self.logger.warning(f"Failed to cleanup temp database {temp_db_name}: {e}")
    
    async def create_rollback_plan(
        self,
        target_state: str,
        backup_id: Optional[str] = None
    ) -> RollbackPlan:
        """Create detailed rollback plan."""
        rollback_id = f"rollback_plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.logger.info(f"Creating rollback plan: {rollback_id} to state: {target_state}")
        
        try:
            # Determine target backup
            if target_state == "pre_migration":
                target_backup = self._find_backup_by_type("pre_migration")
            elif target_state == "checkpoint":
                target_backup = self._find_latest_checkpoint_backup()
            elif target_state == "specific_backup" and backup_id:
                target_backup = self.backup_registry.get(backup_id)
            else:
                raise ValueError(f"Invalid target state or missing backup ID: {target_state}")
            
            if not target_backup:
                raise ValueError(f"No suitable backup found for target state: {target_state}")
            
            # Estimate rollback complexity and duration
            current_data_size = await self._estimate_current_data_size()
            estimated_duration = max(10, min(120, current_data_size / (100 * 1024 * 1024)))  # 10-120 minutes
            downtime_estimate = max(5, estimated_duration * 0.8)  # 80% of total time
            
            # Create rollback steps
            rollback_steps = self._generate_rollback_steps(target_backup)
            
            # Assess risk
            risk_level = self._assess_rollback_risk(target_backup, current_data_size)
            
            # Create validation steps
            validation_steps = self._generate_rollback_validation_steps()
            
            rollback_plan = RollbackPlan(
                rollback_id=rollback_id,
                target_state=target_state,
                backup_to_restore=target_backup.backup_id,
                estimated_duration_minutes=int(estimated_duration),
                required_downtime_minutes=int(downtime_estimate),
                rollback_steps=rollback_steps,
                validation_steps=validation_steps,
                risk_assessment=risk_level,
                created_at=datetime.now()
            )
            
            self.logger.info(
                f"Rollback plan created: {rollback_id}, "
                f"Estimated duration: {estimated_duration:.0f}min, "
                f"Risk: {risk_level}"
            )
            
            return rollback_plan
            
        except Exception as e:
            self.logger.error(f"Failed to create rollback plan: {e}")
            raise
    
    async def execute_rollback(self, rollback_plan: RollbackPlan) -> RollbackResult:
        """Execute rollback operation according to plan."""
        self.logger.info(f"Executing rollback: {rollback_plan.rollback_id}")
        
        start_time = datetime.now()
        steps_completed = 0
        steps_failed = 0
        error_messages = []
        
        try:
            target_backup = self.backup_registry.get(rollback_plan.backup_to_restore)
            if not target_backup:
                raise ValueError(f"Target backup not found: {rollback_plan.backup_to_restore}")
            
            # Execute rollback steps
            for i, step in enumerate(rollback_plan.rollback_steps):
                step_name = step.get('name', f'Step {i+1}')
                self.logger.info(f"Executing rollback step: {step_name}")
                
                try:
                    await self._execute_rollback_step(step, target_backup)
                    steps_completed += 1
                except Exception as e:
                    steps_failed += 1
                    error_message = f"Step '{step_name}' failed: {str(e)}"
                    error_messages.append(error_message)
                    self.logger.error(error_message)
                    
                    # Check if step is critical
                    if step.get('critical', True):
                        raise Exception(f"Critical rollback step failed: {step_name}")
            
            # Run validation steps
            validation_passed = await self._run_rollback_validation(rollback_plan.validation_steps)
            
            # Check for data loss
            data_loss_detected = await self._detect_data_loss_after_rollback(target_backup)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            # Generate recovery recommendations
            recovery_recommendations = self._generate_recovery_recommendations(
                steps_failed > 0, validation_passed, data_loss_detected
            )
            
            rollback_result = RollbackResult(
                rollback_id=rollback_plan.rollback_id,
                success=steps_failed == 0 and validation_passed,
                duration_seconds=duration,
                steps_completed=steps_completed,
                steps_failed=steps_failed,
                final_validation_passed=validation_passed,
                data_loss_detected=data_loss_detected,
                error_messages=error_messages,
                recovery_recommendations=recovery_recommendations
            )
            
            # Record rollback in history
            self.rollback_history[rollback_plan.rollback_id] = rollback_result
            
            if rollback_result.success:
                self.logger.info(f"Rollback completed successfully: {rollback_plan.rollback_id}")
            else:
                self.logger.error(f"Rollback completed with issues: {rollback_plan.rollback_id}")
            
            return rollback_result
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            error_messages.append(f"Rollback execution failed: {str(e)}")
            
            rollback_result = RollbackResult(
                rollback_id=rollback_plan.rollback_id,
                success=False,
                duration_seconds=duration,
                steps_completed=steps_completed,
                steps_failed=steps_failed + 1,
                final_validation_passed=False,
                data_loss_detected=True,  # Assume data loss if rollback fails
                error_messages=error_messages,
                recovery_recommendations=["Contact database administrator for manual recovery"]
            )
            
            self.rollback_history[rollback_plan.rollback_id] = rollback_result
            self.logger.error(f"Rollback failed: {rollback_plan.rollback_id}")
            
            return rollback_result
    
    async def _execute_rollback_step(
        self, 
        step: Dict[str, Any], 
        target_backup: BackupMetadata
    ) -> None:
        """Execute a single rollback step."""
        step_type = step.get('type')
        
        if step_type == 'backup_verification':
            await self._verify_backup_exists(target_backup)
        
        elif step_type == 'database_preparation':
            await self._prepare_database_for_rollback()
        
        elif step_type == 'data_truncation':
            tables = step.get('tables', [])
            await self._truncate_tables(tables)
        
        elif step_type == 'backup_restoration':
            database_name = step.get('database_name')
            await self._restore_backup_to_database(target_backup, database_name)
        
        elif step_type == 'index_rebuilding':
            await self._rebuild_indexes()
        
        elif step_type == 'statistics_update':
            await self._update_table_statistics()
        
        elif step_type == 'constraint_validation':
            await self._validate_database_constraints()
        
        else:
            raise ValueError(f"Unknown rollback step type: {step_type}")
    
    async def _run_rollback_validation(self, validation_steps: List[str]) -> bool:
        """Run validation steps after rollback."""
        try:
            async with self.connection_pool.acquire() as connection:
                for validation_step in validation_steps:
                    if validation_step == 'table_counts':
                        # Verify table counts are reasonable
                        counts = await connection.fetch("""
                            SELECT 
                                schemaname, 
                                tablename, 
                                n_live_tup 
                            FROM pg_stat_user_tables 
                            WHERE schemaname = 'public'
                        """)
                        
                        # Basic sanity checks
                        total_rows = sum(row['n_live_tup'] for row in counts)
                        if total_rows < 1000:  # Minimum expected data
                            return False
                    
                    elif validation_step == 'data_integrity':
                        # Check for basic data integrity
                        integrity_issues = await connection.fetchval("""
                            SELECT COUNT(*)
                            FROM price_observations po
                            LEFT JOIN markets m ON po.market_id = m.market_id
                            WHERE m.market_id IS NULL
                        """)
                        
                        if integrity_issues > 0:
                            return False
                    
                    elif validation_step == 'constraint_check':
                        # Check constraint violations
                        constraint_violations = await connection.fetchval("""
                            SELECT COUNT(*)
                            FROM information_schema.constraint_column_usage
                            WHERE table_schema = 'public'
                        """)
                        
                        # This is a basic check - in reality would be more sophisticated
                        if constraint_violations < 5:  # Expect some constraints
                            return False
                
                return True
                
        except Exception as e:
            self.logger.error(f"Rollback validation failed: {e}")
            return False
    
    # Utility methods
    
    def _parse_connection_string(self) -> Dict[str, str]:
        """Parse PostgreSQL connection string."""
        # Simple parsing - in production would use proper URL parsing
        parts = {}
        
        # Handle standard postgresql:// URLs
        if self.connection_string.startswith('postgresql://'):
            # Remove protocol
            url_part = self.connection_string[13:]
            
            # Split user:pass@host:port/db
            if '@' in url_part:
                auth_part, host_part = url_part.split('@', 1)
                if ':' in auth_part:
                    parts['user'], parts['password'] = auth_part.split(':', 1)
                else:
                    parts['user'] = auth_part
                    parts['password'] = ''
            else:
                host_part = url_part
                parts['user'] = 'postgres'
                parts['password'] = ''
            
            if '/' in host_part:
                host_port, parts['database'] = host_part.split('/', 1)
            else:
                host_port = host_part
                parts['database'] = 'postgres'
            
            if ':' in host_port:
                parts['host'], parts['port'] = host_port.split(':', 1)
                parts['port'] = int(parts['port'])
            else:
                parts['host'] = host_port
                parts['port'] = 5432
        
        else:
            # Default values
            parts = {
                'host': 'localhost',
                'port': 5432,
                'user': 'postgres',
                'password': '',
                'database': 'postgres'
            }
        
        return parts
    
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """Calculate SHA256 checksum of file."""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    
    async def _get_migration_tables(self) -> List[str]:
        """Get list of tables involved in migration."""
        try:
            async with self.connection_pool.acquire() as connection:
                tables = await connection.fetch("""
                    SELECT tablename 
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                    AND tablename IN (
                        'markets', 'commodities', 'price_observations', 
                        'conflict_events', 'analysis_results', 'domain_events'
                    )
                """)
                return [row['tablename'] for row in tables]
        except Exception as e:
            self.logger.warning(f"Failed to get migration tables: {e}")
            return ['markets', 'commodities', 'price_observations', 'conflict_events', 'analysis_results']
    
    async def _create_temporary_database(self, db_name: str) -> None:
        """Create temporary database for testing."""
        conn_params = self._parse_connection_string()
        
        # Connect to maintenance database to create new database
        maintenance_conn_string = f"postgresql://{conn_params['user']}:{conn_params['password']}@{conn_params['host']}:{conn_params['port']}/postgres"
        
        conn = await asyncpg.connect(maintenance_conn_string)
        try:
            await conn.execute(f'CREATE DATABASE "{db_name}"')
        finally:
            await conn.close()
    
    async def _drop_temporary_database(self, db_name: str) -> None:
        """Drop temporary database."""
        conn_params = self._parse_connection_string()
        
        # Connect to maintenance database
        maintenance_conn_string = f"postgresql://{conn_params['user']}:{conn_params['password']}@{conn_params['host']}:{conn_params['port']}/postgres"
        
        conn = await asyncpg.connect(maintenance_conn_string)
        try:
            # Terminate active connections first
            await conn.execute(f"""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = '{db_name}' AND pid <> pg_backend_pid()
            """)
            
            await conn.execute(f'DROP DATABASE IF EXISTS "{db_name}"')
        finally:
            await conn.close()
    
    async def _restore_backup_to_database(
        self, 
        backup_metadata: BackupMetadata, 
        target_db_name: str
    ) -> bool:
        """Restore backup to specified database."""
        try:
            conn_params = self._parse_connection_string()
            
            # Build pg_restore command
            restore_cmd = [
                self.pg_restore_path,
                "--host", conn_params['host'],
                "--port", str(conn_params['port']),
                "--username", conn_params['user'],
                "--dbname", target_db_name,
                "--verbose",
                "--no-password",
                "--clean",
                "--if-exists"
            ]
            
            # Handle different backup formats
            if backup_metadata.compression_used:
                # For compressed SQL files, use zcat | psql
                psql_cmd = [
                    "psql",
                    "--host", conn_params['host'],
                    "--port", str(conn_params['port']),
                    "--username", conn_params['user'],
                    "--dbname", target_db_name,
                    "--quiet"
                ]
                
                # Set environment
                env = os.environ.copy()
                env['PGPASSWORD'] = conn_params['password']
                
                # Use zcat to decompress and pipe to psql
                with gzip.open(backup_metadata.backup_path, 'rt') as gz_file:
                    process = subprocess.run(
                        psql_cmd,
                        stdin=gz_file,
                        stderr=subprocess.PIPE,
                        env=env,
                        check=True,
                        text=True
                    )
            else:
                restore_cmd.append(backup_metadata.backup_path)
                
                env = os.environ.copy()
                env['PGPASSWORD'] = conn_params['password']
                
                process = subprocess.run(
                    restore_cmd,
                    stderr=subprocess.PIPE,
                    env=env,
                    check=True,
                    text=True
                )
            
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"pg_restore failed: {e.stderr}")
            return False
        except Exception as e:
            self.logger.error(f"Backup restoration failed: {e}")
            return False
    
    async def _verify_restored_data_integrity(self, db_name: str) -> bool:
        """Verify data integrity in restored database."""
        try:
            conn_params = self._parse_connection_string()
            test_conn_string = f"postgresql://{conn_params['user']}:{conn_params['password']}@{conn_params['host']}:{conn_params['port']}/{db_name}"
            
            conn = await asyncpg.connect(test_conn_string)
            try:
                # Basic integrity checks
                
                # Check for reasonable data counts
                table_counts = await conn.fetch("""
                    SELECT 
                        'markets' as table_name, COUNT(*) as count FROM markets
                    UNION ALL
                    SELECT 
                        'commodities' as table_name, COUNT(*) as count FROM commodities
                    UNION ALL
                    SELECT 
                        'price_observations' as table_name, COUNT(*) as count FROM price_observations
                """)
                
                # Verify minimum data exists
                for row in table_counts:
                    if row['table_name'] in ['markets', 'commodities'] and row['count'] < 5:
                        return False
                    if row['table_name'] == 'price_observations' and row['count'] < 100:
                        return False
                
                # Check referential integrity
                orphaned_prices = await conn.fetchval("""
                    SELECT COUNT(*)
                    FROM price_observations po
                    LEFT JOIN markets m ON po.market_id = m.market_id
                    WHERE m.market_id IS NULL
                """)
                
                if orphaned_prices > 0:
                    return False
                
                return True
                
            finally:
                await conn.close()
                
        except Exception as e:
            self.logger.error(f"Data integrity verification failed: {e}")
            return False
    
    async def _verify_backup_exists(self, backup_metadata: BackupMetadata) -> None:
        """Verify backup file exists and is readable."""
        backup_path = Path(backup_metadata.backup_path)
        
        if not backup_path.exists():
            raise FileNotFoundError(f"Backup file not found: {backup_path}")
        
        if backup_path.stat().st_size != backup_metadata.backup_size_bytes:
            raise ValueError(f"Backup file size mismatch: expected {backup_metadata.backup_size_bytes}, got {backup_path.stat().st_size}")
        
        # Verify checksum
        actual_checksum = self._calculate_file_checksum(backup_path)
        if actual_checksum != backup_metadata.checksum:
            raise ValueError(f"Backup file checksum mismatch: expected {backup_metadata.checksum}, got {actual_checksum}")
    
    def _register_backup(self, backup_metadata: BackupMetadata) -> None:
        """Register backup in registry."""
        self.backup_registry[backup_metadata.backup_id] = backup_metadata
        self._save_backup_registry()
    
    def _load_backup_registry(self) -> None:
        """Load backup registry from file."""
        registry_file = self.backup_storage_path / "backup_registry.json"
        
        if registry_file.exists():
            try:
                with open(registry_file) as f:
                    registry_data = json.load(f)
                
                # Convert to BackupMetadata objects
                for backup_id, backup_data in registry_data.items():
                    # Convert string dates back to datetime
                    backup_data['timestamp'] = datetime.fromisoformat(backup_data['timestamp'])
                    backup_data['retention_until'] = datetime.fromisoformat(backup_data['retention_until'])
                    
                    self.backup_registry[backup_id] = BackupMetadata(**backup_data)
                    
            except Exception as e:
                self.logger.warning(f"Failed to load backup registry: {e}")
                self.backup_registry = {}
        else:
            self.backup_registry = {}
    
    def _save_backup_registry(self) -> None:
        """Save backup registry to file."""
        registry_file = self.backup_storage_path / "backup_registry.json"
        
        try:
            # Convert to serializable format
            registry_data = {}
            for backup_id, backup_metadata in self.backup_registry.items():
                data = asdict(backup_metadata)
                # Convert datetime to string
                data['timestamp'] = data['timestamp'].isoformat()
                data['retention_until'] = data['retention_until'].isoformat()
                registry_data[backup_id] = data
            
            with open(registry_file, 'w') as f:
                json.dump(registry_data, f, indent=2, default=str)
                
        except Exception as e:
            self.logger.error(f"Failed to save backup registry: {e}")
    
    def _find_backup_by_type(self, backup_type: str) -> Optional[BackupMetadata]:
        """Find most recent backup of specified type."""
        matching_backups = [
            backup for backup in self.backup_registry.values()
            if backup.backup_type == backup_type
        ]
        
        if matching_backups:
            return max(matching_backups, key=lambda b: b.timestamp)
        return None
    
    def _find_latest_checkpoint_backup(self) -> Optional[BackupMetadata]:
        """Find latest checkpoint backup."""
        checkpoint_backups = [
            backup for backup in self.backup_registry.values()
            if backup.backup_type == "checkpoint"
        ]
        
        if checkpoint_backups:
            return max(checkpoint_backups, key=lambda b: b.timestamp)
        return None
    
    async def _estimate_current_data_size(self) -> int:
        """Estimate current database size in bytes."""
        try:
            async with self.connection_pool.acquire() as connection:
                size = await connection.fetchval("SELECT pg_database_size(current_database())")
                return size or 0
        except Exception:
            return 100 * 1024 * 1024  # Default 100MB
    
    def _generate_rollback_steps(self, target_backup: BackupMetadata) -> List[Dict[str, Any]]:
        """Generate detailed rollback steps."""
        return [
            {
                "name": "Verify Backup Integrity",
                "type": "backup_verification",
                "description": "Verify target backup exists and is valid",
                "critical": True,
                "estimated_duration_minutes": 2
            },
            {
                "name": "Prepare Database",
                "type": "database_preparation",
                "description": "Prepare database for rollback operation",
                "critical": True,
                "estimated_duration_minutes": 1
            },
            {
                "name": "Truncate Tables",
                "type": "data_truncation",
                "description": "Remove current data from migration tables",
                "tables": target_backup.tables_included,
                "critical": True,
                "estimated_duration_minutes": 5
            },
            {
                "name": "Restore Backup",
                "type": "backup_restoration",
                "description": "Restore data from backup",
                "database_name": target_backup.database_name,
                "critical": True,
                "estimated_duration_minutes": 15
            },
            {
                "name": "Rebuild Indexes",
                "type": "index_rebuilding",
                "description": "Rebuild database indexes",
                "critical": False,
                "estimated_duration_minutes": 10
            },
            {
                "name": "Update Statistics",
                "type": "statistics_update",
                "description": "Update table statistics",
                "critical": False,
                "estimated_duration_minutes": 2
            },
            {
                "name": "Validate Constraints",
                "type": "constraint_validation",
                "description": "Validate database constraints",
                "critical": True,
                "estimated_duration_minutes": 5
            }
        ]
    
    def _assess_rollback_risk(self, target_backup: BackupMetadata, current_size: int) -> str:
        """Assess rollback operation risk level."""
        # Calculate backup age
        backup_age_hours = (datetime.now() - target_backup.timestamp).total_seconds() / 3600
        
        # Calculate size difference
        size_ratio = current_size / target_backup.backup_size_bytes if target_backup.backup_size_bytes > 0 else 1
        
        # Risk assessment logic
        if backup_age_hours > 168:  # More than 1 week old
            return "high"
        elif backup_age_hours > 24:  # More than 1 day old
            return "medium" if size_ratio < 2 else "high"
        elif not target_backup.recovery_tested:
            return "medium"
        else:
            return "low"
    
    def _generate_rollback_validation_steps(self) -> List[str]:
        """Generate validation steps for rollback."""
        return [
            "table_counts",
            "data_integrity", 
            "constraint_check"
        ]
    
    async def _prepare_database_for_rollback(self) -> None:
        """Prepare database for rollback operation."""
        async with self.connection_pool.acquire() as connection:
            # Disable triggers temporarily
            await connection.execute("SET session_replication_role = 'replica'")
            
            # Clear query plan cache
            await connection.execute("DISCARD PLANS")
    
    async def _truncate_tables(self, tables: List[str]) -> None:
        """Truncate specified tables."""
        async with self.connection_pool.acquire() as connection:
            for table in tables:
                await connection.execute(f'TRUNCATE TABLE "{table}" CASCADE')
    
    async def _rebuild_indexes(self) -> None:
        """Rebuild database indexes."""
        async with self.connection_pool.acquire() as connection:
            await connection.execute("REINDEX DATABASE CONCURRENTLY")
    
    async def _update_table_statistics(self) -> None:
        """Update table statistics."""
        async with self.connection_pool.acquire() as connection:
            await connection.execute("ANALYZE")
    
    async def _validate_database_constraints(self) -> None:
        """Validate database constraints."""
        async with self.connection_pool.acquire() as connection:
            # This would include more sophisticated constraint checking
            await connection.execute("SELECT 1")  # Placeholder
    
    async def _detect_data_loss_after_rollback(self, target_backup: BackupMetadata) -> bool:
        """Detect if data loss occurred during rollback."""
        try:
            async with self.connection_pool.acquire() as connection:
                # Check if data counts are reasonable compared to backup
                current_counts = await connection.fetch("""
                    SELECT 
                        'markets' as table_name, COUNT(*) as count FROM markets
                    UNION ALL
                    SELECT 
                        'price_observations' as table_name, COUNT(*) as count FROM price_observations
                """)
                
                # Simple heuristic - if we have significantly less data, might be loss
                for row in current_counts:
                    if row['table_name'] == 'price_observations' and row['count'] < 1000:
                        return True
                    if row['table_name'] == 'markets' and row['count'] < 10:
                        return True
                
                return False
                
        except Exception:
            return True  # Assume data loss if we can't verify
    
    def _generate_recovery_recommendations(
        self, 
        steps_failed: bool, 
        validation_passed: bool, 
        data_loss_detected: bool
    ) -> List[str]:
        """Generate recovery recommendations based on rollback results."""
        recommendations = []
        
        if steps_failed:
            recommendations.append("Review failed rollback steps and retry manually")
            recommendations.append("Check database logs for specific error details")
        
        if not validation_passed:
            recommendations.append("Run comprehensive data validation")
            recommendations.append("Check referential integrity constraints")
        
        if data_loss_detected:
            recommendations.append("CRITICAL: Data loss detected - investigate immediately")
            recommendations.append("Consider restoring from earlier backup")
            recommendations.append("Contact database administrator for recovery assistance")
        
        if not steps_failed and validation_passed and not data_loss_detected:
            recommendations.append("Rollback completed successfully")
            recommendations.append("Monitor system for stability")
            recommendations.append("Update backup schedule based on lessons learned")
        
        return recommendations
    
    def cleanup_old_backups(self, retention_days: int = 30) -> List[str]:
        """Cleanup old backups based on retention policy."""
        cleanup_date = datetime.now() - timedelta(days=retention_days)
        cleaned_backups = []
        
        for backup_id, backup_metadata in list(self.backup_registry.items()):
            if backup_metadata.retention_until < cleanup_date:
                try:
                    # Remove backup file
                    backup_path = Path(backup_metadata.backup_path)
                    if backup_path.exists():
                        backup_path.unlink()
                    
                    # Remove from registry
                    del self.backup_registry[backup_id]
                    cleaned_backups.append(backup_id)
                    
                    self.logger.info(f"Cleaned up old backup: {backup_id}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to cleanup backup {backup_id}: {e}")
        
        if cleaned_backups:
            self._save_backup_registry()
        
        return cleaned_backups
    
    def get_backup_status(self) -> Dict[str, Any]:
        """Get status of all backups."""
        return {
            "total_backups": len(self.backup_registry),
            "backup_types": {
                backup_type: len([b for b in self.backup_registry.values() if b.backup_type == backup_type])
                for backup_type in ["pre_migration", "checkpoint", "post_migration", "full"]
            },
            "total_backup_size_mb": sum(b.backup_size_bytes for b in self.backup_registry.values()) / (1024 * 1024),
            "latest_backup": max(self.backup_registry.values(), key=lambda b: b.timestamp).backup_id if self.backup_registry else None,
            "oldest_backup": min(self.backup_registry.values(), key=lambda b: b.timestamp).backup_id if self.backup_registry else None
        }