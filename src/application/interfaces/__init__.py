"""Application layer interfaces."""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic
from contextlib import asynccontextmanager

from ...core.domain.shared.events import DomainEvent

# Type variables for generic command/result handling
TCommand = TypeVar('TCommand')
TResult = TypeVar('TResult')
TQuery = TypeVar('TQuery')
TQueryResult = TypeVar('TQueryResult')


class Command(ABC):
    """Base command interface."""
    
    @abstractmethod
    def validate(self) -> None:
        """Validate command parameters."""
        pass


class CommandHandler(ABC, Generic[TCommand, TResult]):
    """Abstract command handler interface."""
    
    @abstractmethod
    async def handle(self, command: TCommand) -> TResult:
        """Handle command and return result."""
        pass


class Query(ABC):
    """Base query interface."""
    
    @abstractmethod
    def validate(self) -> None:
        """Validate query parameters."""
        pass


class QueryHandler(ABC, <PERSON>ric[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ueryResult]):
    """Abstract query handler interface."""
    
    @abstractmethod
    async def handle(self, query: TQuery) -> TQueryResult:
        """Handle query and return result."""
        pass


class UnitOfWork(ABC):
    """Abstract Unit of Work pattern interface."""
    
    @abstractmethod
    async def __aenter__(self):
        """Async context manager entry."""
        pass
    
    @abstractmethod
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        pass
    
    @abstractmethod
    async def commit(self):
        """Commit current transaction."""
        pass
    
    @abstractmethod
    async def rollback(self):
        """Rollback current transaction."""
        pass
    
    @property
    @abstractmethod
    def markets(self):
        """Market repository."""
        pass
    
    @property
    @abstractmethod
    def prices(self):
        """Price repository."""
        pass


class EventBus(ABC):
    """Abstract event bus interface."""
    
    @abstractmethod
    async def publish(self, event: DomainEvent):
        """Publish domain event."""
        pass
    
    @abstractmethod
    def subscribe(self, event_type: type, handler):
        """Subscribe to event type."""
        pass


class Cache(ABC):
    """Abstract cache interface."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """Set value in cache."""
        pass
    
    @abstractmethod
    async def delete(self, key: str):
        """Delete value from cache."""
        pass
    
    @abstractmethod
    async def clear(self):
        """Clear all cache entries."""
        pass


class DataProcessor(ABC):
    """Abstract data processor interface."""
    
    @abstractmethod
    async def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process input data and return results."""
        pass
    
    @abstractmethod
    def validate_input(self, data: Dict[str, Any]) -> bool:
        """Validate input data format."""
        pass


class ModelEstimator(ABC):
    """Abstract model estimator interface."""
    
    @abstractmethod
    async def estimate(self, data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate model with given data and configuration."""
        pass
    
    @abstractmethod
    def get_supported_models(self) -> List[str]:
        """Get list of supported model types."""
        pass


class ExternalDataClient(ABC):
    """Abstract external data client interface."""
    
    @abstractmethod
    async def fetch_data(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch data from external source."""
        pass
    
    @abstractmethod
    def validate_connection(self) -> bool:
        """Validate connection to external service."""
        pass