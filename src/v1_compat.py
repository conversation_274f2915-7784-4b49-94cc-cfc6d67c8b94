"""
V1 Compatibility Layer for Yemen Market Integration

This module provides backward compatibility for V1 imports,
mapping them to the new V2 Clean Architecture structure.
"""

import sys
import logging
from contextlib import contextmanager
from pathlib import Path
from typing import Any, Dict, Optional, Union


# ===============================================
# Logging Compatibility Layer
# ===============================================

def setup_logging(level: str = "INFO"):
    """Setup logging with V1 interface."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def info(message: str, **kwargs):
    """Log info message."""
    logging.info(message, extra=kwargs)

def error(message: str, **kwargs):
    """Log error message."""
    logging.error(message, extra=kwargs)

def warning(message: str, **kwargs):
    """Log warning message."""
    logging.warning(message, extra=kwargs)

def debug(message: str, **kwargs):
    """Log debug message."""
    logging.debug(message, extra=kwargs)

@contextmanager
def timer(operation_name: str):
    """Time an operation."""
    import time
    start = time.time()
    try:
        yield
    finally:
        duration = time.time() - start
        info(f"{operation_name} completed in {duration:.2f} seconds")

def bind(**kwargs):
    """Bind context variables (simplified implementation)."""
    # In V1 this was used for structured logging context
    # For now, just log the binding
    debug(f"Context bound: {kwargs}")


# ===============================================
# Data Processing Compatibility
# ===============================================

class PanelBuilder:
    """V1 Compatible Panel Builder."""
    
    def __init__(self):
        self._v2_builder = None
        self._attempted_import = False
    
    def _ensure_v2_builder(self):
        """Lazy import of V2 builder."""
        if not self._attempted_import:
            self._attempted_import = True
            try:
                from infrastructure.processors.panel_builder import PanelBuilder as V2PanelBuilder
                self._v2_builder = V2PanelBuilder()
                info("V2 PanelBuilder imported successfully")
            except ImportError as e:
                warning(f"V2 PanelBuilder not available: {e}")
                self._v2_builder = None
    
    def build_panel(self, *args, **kwargs):
        """Build panel using V2 implementation."""
        self._ensure_v2_builder()
        if self._v2_builder:
            return self._v2_builder.build_panel(*args, **kwargs)
        else:
            error("V2 PanelBuilder not available")
            return None


# ===============================================
# Analysis Compatibility
# ===============================================

class ThreeTierAnalysis:
    """V1 Compatible Three Tier Analysis."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        info("ThreeTierAnalysis initialized with V1 compatibility layer")
    
    def run_full_analysis(self, panel_df, conflict_data=None):
        """Run full analysis using V2 backend."""
        try:
            # Import V2 command
            from application.commands.run_three_tier_analysis import ThreeTierAnalysisCommand
            command = ThreeTierAnalysisCommand()
            return command.execute(panel_df, conflict_data, self.config)
        except ImportError as e:
            error(f"V2 ThreeTierAnalysisCommand not available: {e}")
            # Return mock results for now
            return {
                'tier1': None,
                'tier2': {},
                'tier3': None,
                'status': 'error',
                'message': 'V2 backend not available'
            }
    
    def run_single_tier(self, tier_num: int, panel_df, conflict_data=None):
        """Run single tier analysis."""
        info(f"Running single tier {tier_num} analysis")
        # Simplified implementation
        return {f'tier{tier_num}': None, 'status': 'completed'}


# ===============================================
# Feature Engineering Compatibility
# ===============================================

def prepare_data_for_modeling(df, config=None):
    """Prepare data for modeling - V1 compatible function."""
    try:
        from application.services.data_preparation_service import DataPreparationService
        service = DataPreparationService()
        return service.prepare_panel_data(df, config or {})
    except ImportError as e:
        warning(f"V2 DataPreparationService not available: {e}, returning original data")
        return df


# ===============================================
# Model Migration Compatibility
# ===============================================

class ModelMigrationHelper:
    """V1 Compatible Model Migration Helper."""
    
    def __init__(self):
        try:
            from infrastructure.migration.v1_to_v2_migrator import V1ToV2Migrator
            self._migrator = V1ToV2Migrator()
        except ImportError as e:
            warning(f"V2 migrator not available: {e}")
            self._migrator = None
    
    def migrate_model(self, *args, **kwargs):
        """Migrate model to V2."""
        if self._migrator:
            return self._migrator.migrate(*args, **kwargs)
        else:
            return None


# ===============================================
# Module-level exports for backward compatibility
# ===============================================

# Export classes and functions that scripts expect
__all__ = [
    'setup_logging', 'info', 'error', 'warning', 'debug', 'timer', 'bind',
    'PanelBuilder', 'ThreeTierAnalysis', 'ModelMigrationHelper',
    'prepare_data_for_modeling'
]