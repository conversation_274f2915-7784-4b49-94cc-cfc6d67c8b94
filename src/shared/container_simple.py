"""Simplified dependency injection container for development/testing."""

from pathlib import Path
from typing import Dict, Any, Optional


class SimpleProvider:
    """Simple provider for basic dependency injection."""
    
    def __init__(self, factory_func, **kwargs):
        self.factory_func = factory_func
        self.kwargs = kwargs
        self._instance = None
    
    def __call__(self):
        if self._instance is None:
            self._instance = self.factory_func(**self.kwargs)
        return self._instance


class SimpleConfig:
    """Simple configuration object."""
    
    def __init__(self):
        self._values = {}
    
    def from_env(self, key: str, default: Any = None):
        import os
        self._values[key] = os.getenv(key, default)
        return self
    
    def from_value(self, value: Any):
        self._values['value'] = value
        return self
    
    def __call__(self):
        return self._values.get('value', None)


class SimpleContainer:
    """Simplified container for development/testing."""
    
    def __init__(self):
        # Configuration
        self.config = SimpleNestedConfig()
        
        # Mock services for testing
        self._services = {}
    
    def get_service(self, name: str):
        """Get a service by name."""
        return self._services.get(name, None)
    
    def register_service(self, name: str, service: Any):
        """Register a service."""
        self._services[name] = service


class SimpleNestedConfig:
    """Nested configuration for hierarchical settings."""
    
    def __init__(self):
        self.database = SimpleSubConfig()
        self.cache = SimpleSubConfig()
        self.events = SimpleSubConfig()
        self.external = SimpleExternalConfig()
        self.storage = SimpleSubConfig()


class SimpleSubConfig:
    """Sub-configuration object."""
    
    def __init__(self):
        self.url = SimpleConfig()
        self.type = SimpleConfig()
        self.default_ttl = SimpleConfig()
        self.max_size = SimpleConfig()
        self.queue_size = SimpleConfig()
        self.policy_results_path = SimpleConfig()
        self.timeout = SimpleConfig()
        self.api_key = SimpleConfig()
        self.email = SimpleConfig()
        
        # Avoid recursion by creating specific sub-configs
        self.redis = SimpleRedisConfig()
        self.memory = SimpleMemoryConfig()
        self.hdx = SimpleHDXConfig()
        self.wfp = SimpleWFPConfig()
        self.acled = SimpleACLEDConfig()


class SimpleRedisConfig:
    """Redis-specific configuration."""
    
    def __init__(self):
        self.url = SimpleConfig()


class SimpleMemoryConfig:
    """Memory cache specific configuration."""
    
    def __init__(self):
        self.max_size = SimpleConfig()


class SimpleHDXConfig:
    """HDX-specific configuration."""
    
    def __init__(self):
        self.timeout = SimpleConfig()


class SimpleWFPConfig:
    """WFP-specific configuration."""
    
    def __init__(self):
        self.api_key = SimpleConfig()


class SimpleACLEDConfig:
    """ACLED-specific configuration."""
    
    def __init__(self):
        self.api_key = SimpleConfig()
        self.email = SimpleConfig()


class SimpleExternalConfig:
    """External services configuration."""
    
    def __init__(self):
        self.hdx = SimpleHDXConfig()
        self.wfp = SimpleWFPConfig()
        self.acled = SimpleACLEDConfig()


# For backward compatibility
Container = SimpleContainer