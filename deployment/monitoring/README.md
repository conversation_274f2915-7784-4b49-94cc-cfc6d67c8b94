# Yemen Market Integration V2 - Monitoring and Observability

This directory contains the complete monitoring and observability setup for the Yemen Market Integration V2 system, providing comprehensive insights into system health, performance, and business metrics.

## Overview

Our observability stack includes:

- **Prometheus** - Metrics collection and alerting
- **Grafana** - Visualization and dashboards
- **Jaeger** - Distributed tracing
- **Loki** - Log aggregation and search
- **Sentry** - Error tracking and performance monitoring
- **Custom Business Metrics** - Yemen-specific analytics

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│   OpenTelemetry │───▶│      Jaeger     │
│                 │    │                 │    │   (Tracing)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Prometheus    │    │      Loki       │    │     Sentry      │
│   (Metrics)     │    │    (Logs)       │    │   (Errors)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │     Grafana     │
                    │  (Dashboards)   │
                    └─────────────────┘
```

## Quick Start

### 1. Deploy Monitoring Stack

```bash
# Set up monitoring infrastructure
./scripts/setup-monitoring.sh

# Configure environment variables
export SENTRY_DSN="your-sentry-dsn"
export SLACK_WEBHOOK_URL="your-slack-webhook"
export PAGERDUTY_SERVICE_KEY="your-pagerduty-key"
```

### 2. Access Dashboards

```bash
# Grafana (default: admin/yemen-market-admin)
kubectl port-forward -n monitoring svc/kube-prometheus-stack-grafana 3000:80

# Prometheus
kubectl port-forward -n monitoring svc/kube-prometheus-stack-prometheus 9090:9090

# Jaeger
kubectl port-forward -n monitoring svc/yemen-market-jaeger-query 16686:16686
```

### 3. View Key Metrics

Visit Grafana at http://localhost:3000 and explore:
- **System Overview** - Service health and availability
- **Analysis Performance** - Model execution metrics
- **Data Pipeline Health** - Data quality and freshness

## Components

### Prometheus Metrics

#### System Metrics
- `yemen_market_http_requests_total` - HTTP request count
- `yemen_market_http_request_duration_seconds` - Request latency
- `yemen_market_active_analyses` - Active analysis count
- `yemen_market_memory_usage_mb` - Memory usage by component

#### Business Metrics
- `yemen_market_data_coverage_ratio` - Data coverage by commodity/region
- `yemen_market_exchange_rate` - Exchange rates by currency zone
- `yemen_market_conflict_impact_score` - Conflict impact on prices
- `yemen_market_model_convergence_total` - Model convergence results

### Grafana Dashboards

#### 1. System Overview
- Service availability and error rates
- Request volume and latency trends
- Active analyses and resource usage
- Alert status summary

#### 2. Analysis Performance
- Analysis duration percentiles
- Model convergence rates
- Memory usage by component
- Queue and processing metrics

#### 3. Data Pipeline Health
- Data coverage and freshness
- Missing data patterns
- External API performance
- Cache hit rates

### Distributed Tracing

Jaeger provides:
- Request flow visualization
- Performance bottleneck identification
- Error propagation tracking
- Cross-service dependency mapping

### Log Aggregation

Loki collects:
- Application logs with correlation IDs
- Error logs with stack traces
- Performance metrics
- Business event logs

### Error Tracking

Sentry captures:
- Application exceptions with context
- Performance degradation alerts
- Release tracking and deployment issues
- User impact analysis

## Service Level Objectives (SLOs)

### 1. API Availability
- **Target**: 99% over 30 days
- **Measurement**: Non-5xx response rate
- **Error Budget**: 7.2 hours/month

### 2. Analysis Latency
- **Target**: 95% complete within 5 minutes
- **Measurement**: P95 analysis duration
- **Error Budget**: 5% can exceed threshold

### 3. Data Quality
- **Target**: 95% data coverage
- **Measurement**: Average coverage ratio
- **Error Budget**: 5% coverage gaps allowed

## Alert Rules

### Critical Alerts
- `HighErrorRate` - Error rate > 5% for 5 minutes
- `ServiceDown` - Service unavailable for 2 minutes
- `SLOViolation_Availability` - Availability below 99%

### Warning Alerts
- `HighLatency` - P95 latency > 2 seconds
- `LowDataCoverage` - Coverage < 70%
- `ModelConvergenceFailure` - Convergence rate < 80%

### Info Alerts
- `HighExchangeRateDivergence` - Unusual FX patterns
- `SignificantConflictImpact` - High conflict effects

## Runbooks

Critical alerts include runbook links:

- [High Error Rate](/deployment/monitoring/runbooks/high-error-rate.md)
- [Service Down](/deployment/monitoring/runbooks/service-down.md)
- [Data Quality Issues](/deployment/monitoring/runbooks/data-quality.md)
- [Model Convergence](/deployment/monitoring/runbooks/model-convergence.md)

## Configuration

### Environment Variables

```bash
# Sentry Integration
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production

# OpenTelemetry
OTLP_ENDPOINT=http://jaeger-collector:4317
OTEL_SERVICE_NAME=yemen-market-integration

# Prometheus
PROMETHEUS_PUSH_GATEWAY=http://prometheus-pushgateway:9091

# Log Level
LOG_LEVEL=INFO
```

### Custom Metrics

Add business-specific metrics:

```python
from src.infrastructure.observability import business_metrics

# Record data coverage
business_metrics.record_data_coverage(
    commodity="Wheat",
    region="Sana'a",
    data_source="WFP",
    coverage_ratio=0.95
)

# Record analysis performance
business_metrics.record_analysis_duration(
    analysis_type="cointegration",
    tier="tier2",
    commodity="Wheat",
    duration_seconds=120.5
)
```

### Custom Dashboards

Create Yemen-specific visualizations:

```json
{
  "title": "Market Integration Analysis",
  "panels": [
    {
      "title": "Exchange Rate Divergence",
      "target": "yemen_market_exchange_rate_divergence_ratio"
    },
    {
      "title": "Conflict Impact Heatmap",
      "target": "yemen_market_conflict_impact_score"
    }
  ]
}
```

## Best Practices

### Metrics
- Use consistent labeling across metrics
- Implement rate limiting for high-cardinality metrics
- Set appropriate retention policies
- Use recording rules for expensive queries

### Logging
- Include correlation IDs in all logs
- Structure logs as JSON for searchability
- Avoid logging sensitive information
- Use appropriate log levels

### Tracing
- Sample traces appropriately in production
- Include business context in spans
- Trace critical user journeys
- Monitor trace collection overhead

### Alerting
- Focus on user-impacting issues
- Implement escalation policies
- Use multi-window burn rate alerts
- Test alert channels regularly

## Troubleshooting

### Common Issues

#### High Memory Usage
```bash
# Check memory usage by component
kubectl top pods -l app=yemen-market

# Review memory metrics
kubectl exec -it prometheus-0 -- promtool query instant \
  'yemen_market_memory_usage_mb'
```

#### Missing Metrics
```bash
# Verify metrics endpoint
kubectl exec -it yemen-market-api-0 -- curl localhost:9090/metrics

# Check ServiceMonitor configuration
kubectl get servicemonitor -n monitoring -o yaml
```

#### Alert Fatigue
- Review alert thresholds and windows
- Implement alert deduplication
- Use appropriate severity levels
- Create runbooks for common issues

## Security

### Access Control
- Grafana uses RBAC for dashboard access
- Prometheus scraping restricted by NetworkPolicy
- Sentry configured with appropriate project permissions

### Data Protection
- Sensitive data filtered from logs and traces
- Metrics don't include PII
- TLS encryption for data in transit

## Performance

### Resource Usage
- Prometheus: 4GB RAM, 100GB disk
- Grafana: 1GB RAM, 10GB disk
- Jaeger: 2GB RAM, 50GB disk
- Loki: 2GB RAM, 100GB disk

### Retention Policies
- Metrics: 30 days
- Logs: 7 days
- Traces: 3 days
- Long-term storage via object storage

## Support

For monitoring issues:
1. Check the [troubleshooting guide](#troubleshooting)
2. Review relevant runbooks
3. Consult the team via #monitoring-support

## Related Documentation

- [V2 Architecture](/v2/docs/architecture/)
- [Deployment Guide](/v2/docs/deployment/)
- [API Reference](/v2/docs/api/)
- [Development Setup](/v2/docs/development/)