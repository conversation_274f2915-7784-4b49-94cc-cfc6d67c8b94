groups:
  - name: availability
    interval: 30s
    rules:
      - alert: HighErrorRate
        expr: |
          (
            sum(rate(yemen_market_http_requests_total{status=~"5.."}[5m]))
            /
            sum(rate(yemen_market_http_requests_total[5m]))
          ) > 0.05
        for: 5m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: High error rate detected
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          runbook_url: https://docs.yemen-market.org/runbooks/high-error-rate

      - alert: ServiceDown
        expr: up{job="yemen-market-api"} == 0
        for: 2m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: Service is down
          description: "Yemen Market API service has been down for more than 2 minutes"
          runbook_url: https://docs.yemen-market.org/runbooks/service-down

  - name: performance
    interval: 30s
    rules:
      - alert: HighLatency
        expr: |
          histogram_quantile(0.95,
            sum(rate(yemen_market_http_request_duration_seconds_bucket[5m])) by (le)
          ) > 2
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: High API latency detected
          description: "95th percentile latency is {{ $value }}s for the last 5 minutes"
          runbook_url: https://docs.yemen-market.org/runbooks/high-latency

      - alert: AnalysisTimeout
        expr: |
          histogram_quantile(0.99,
            sum(rate(yemen_market_analysis_duration_seconds_bucket[5m])) by (le, analysis_type)
          ) > 1800
        for: 10m
        labels:
          severity: warning
          team: data
        annotations:
          summary: Analysis taking too long
          description: "{{ $labels.analysis_type }} analysis P99 duration is {{ $value }}s"
          runbook_url: https://docs.yemen-market.org/runbooks/analysis-timeout

  - name: data_quality
    interval: 60s
    rules:
      - alert: LowDataCoverage
        expr: |
          avg(yemen_market_data_coverage_ratio) by (commodity, region) < 0.7
        for: 15m
        labels:
          severity: warning
          team: data
        annotations:
          summary: Low data coverage detected
          description: "Data coverage for {{ $labels.commodity }} in {{ $labels.region }} is {{ $value | humanizePercentage }}"
          runbook_url: https://docs.yemen-market.org/runbooks/low-data-coverage

      - alert: StaleData
        expr: |
          yemen_market_data_freshness_hours > 48
        for: 5m
        labels:
          severity: warning
          team: data
        annotations:
          summary: Stale data detected
          description: "{{ $labels.data_source }} data for {{ $labels.commodity }} is {{ $value }} hours old"
          runbook_url: https://docs.yemen-market.org/runbooks/stale-data

      - alert: HighMissingDataRate
        expr: |
          rate(yemen_market_missing_data_points_total[1h]) > 100
        for: 10m
        labels:
          severity: warning
          team: data
        annotations:
          summary: High rate of missing data points
          description: "Missing data rate is {{ $value }} points/hour for {{ $labels.commodity }} in {{ $labels.market }}"
          runbook_url: https://docs.yemen-market.org/runbooks/missing-data

  - name: model_health
    interval: 60s
    rules:
      - alert: LowModelConvergence
        expr: |
          (
            sum(rate(yemen_market_model_convergence_total{converged="True"}[1h])) by (model_type)
            /
            sum(rate(yemen_market_model_convergence_total[1h])) by (model_type)
          ) < 0.8
        for: 30m
        labels:
          severity: warning
          team: data
        annotations:
          summary: Low model convergence rate
          description: "{{ $labels.model_type }} convergence rate is {{ $value | humanizePercentage }}"
          runbook_url: https://docs.yemen-market.org/runbooks/model-convergence

      - alert: ModelEstimationSlow
        expr: |
          histogram_quantile(0.95,
            sum(rate(yemen_market_model_estimation_seconds_bucket[5m])) by (le, model_type)
          ) > 300
        for: 15m
        labels:
          severity: warning
          team: data
        annotations:
          summary: Slow model estimation
          description: "{{ $labels.model_type }} P95 estimation time is {{ $value }}s"
          runbook_url: https://docs.yemen-market.org/runbooks/slow-estimation

  - name: business_metrics
    interval: 60s
    rules:
      - alert: HighExchangeRateDivergence
        expr: |
          yemen_market_exchange_rate_divergence_ratio > 2
        for: 30m
        labels:
          severity: info
          team: analysis
        annotations:
          summary: High exchange rate divergence detected
          description: "Exchange rate divergence ratio is {{ $value }} for {{ $labels.comparison_type }}"
          runbook_url: https://docs.yemen-market.org/runbooks/exchange-divergence

      - alert: SignificantConflictImpact
        expr: |
          yemen_market_conflict_impact_score > 0.3
        for: 1h
        labels:
          severity: info
          team: analysis
        annotations:
          summary: Significant conflict impact on prices
          description: "Conflict impact score for {{ $labels.commodity }} in {{ $labels.market }} is {{ $value }}"
          runbook_url: https://docs.yemen-market.org/runbooks/conflict-impact

  - name: infrastructure
    interval: 30s
    rules:
      - alert: HighMemoryUsage
        expr: |
          yemen_market_memory_usage_mb / 1024 > 8
        for: 10m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: High memory usage detected
          description: "{{ $labels.component }} memory usage is {{ $value }}GB during {{ $labels.operation }}"
          runbook_url: https://docs.yemen-market.org/runbooks/high-memory

      - alert: DatabaseSlow
        expr: |
          histogram_quantile(0.95,
            sum(rate(yemen_market_database_query_duration_seconds_bucket[5m])) by (le, query_type)
          ) > 1
        for: 10m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: Slow database queries
          description: "{{ $labels.query_type }} queries P95 duration is {{ $value }}s"
          runbook_url: https://docs.yemen-market.org/runbooks/slow-database

      - alert: ExternalAPIFailures
        expr: |
          (
            sum(rate(yemen_market_external_api_calls_total{status!="200"}[5m])) by (api)
            /
            sum(rate(yemen_market_external_api_calls_total[5m])) by (api)
          ) > 0.1
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: High external API failure rate
          description: "{{ $labels.api }} failure rate is {{ $value | humanizePercentage }}"
          runbook_url: https://docs.yemen-market.org/runbooks/external-api-failures

  - name: slo_violations
    interval: 60s
    rules:
      - alert: SLOViolation_Availability
        expr: |
          (
            1 - (
              sum(rate(yemen_market_http_requests_total{status!~"5.."}[30d]))
              /
              sum(rate(yemen_market_http_requests_total[30d]))
            )
          ) < 0.99
        for: 5m
        labels:
          severity: critical
          team: platform
          slo: availability
        annotations:
          summary: Availability SLO violation
          description: "30-day availability is {{ $value | humanizePercentage }}, below 99% target"
          runbook_url: https://docs.yemen-market.org/runbooks/slo-availability

      - alert: SLOViolation_AnalysisLatency
        expr: |
          histogram_quantile(0.95,
            sum(rate(yemen_market_analysis_duration_seconds_bucket[7d])) by (le)
          ) > 300
        for: 30m
        labels:
          severity: warning
          team: data
          slo: analysis_latency
        annotations:
          summary: Analysis latency SLO violation
          description: "7-day P95 analysis latency is {{ $value }}s, above 300s target"
          runbook_url: https://docs.yemen-market.org/runbooks/slo-latency

      - alert: SLOViolation_DataQuality
        expr: |
          avg(yemen_market_data_coverage_ratio) < 0.95
        for: 1h
        labels:
          severity: warning
          team: data
          slo: data_quality
        annotations:
          summary: Data quality SLO violation
          description: "Average data coverage is {{ $value | humanizePercentage }}, below 95% target"
          runbook_url: https://docs.yemen-market.org/runbooks/slo-data-quality