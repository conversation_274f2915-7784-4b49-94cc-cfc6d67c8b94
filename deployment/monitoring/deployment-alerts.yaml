# Deployment-specific alerting rules for Yemen Market Integration V2
# Provides comprehensive monitoring during production deployment

groups:
  - name: deployment.alerts
    interval: 30s
    rules:
      # High-priority deployment alerts
      - alert: DeploymentHighErrorRate
        expr: |
          (
            rate(http_requests_total{job="yemen-market-api-blue",code=~"5.."}[5m]) /
            rate(http_requests_total{job="yemen-market-api-blue"}[5m])
          ) > 0.05
        for: 2m
        labels:
          severity: critical
          component: deployment
          environment: production
        annotations:
          summary: "High error rate detected in V2 deployment"
          description: "V2 deployment showing {{ $value | humanizePercentage }} error rate for more than 2 minutes"
          runbook_url: "https://docs.yemen-market.com/runbooks/high-error-rate"
          action_required: "Consider rolling back to V1"

      - alert: DeploymentHighLatency
        expr: |
          histogram_quantile(0.95,
            rate(http_request_duration_seconds_bucket{job="yemen-market-api-blue"}[5m])
          ) > 2.0
        for: 3m
        labels:
          severity: critical
          component: deployment
          environment: production
        annotations:
          summary: "High latency detected in V2 deployment"
          description: "V2 deployment 95th percentile latency is {{ $value }}s"
          runbook_url: "https://docs.yemen-market.com/runbooks/high-latency"
          action_required: "Investigate performance degradation"

      - alert: DeploymentLowThroughput
        expr: |
          rate(http_requests_total{job="yemen-market-api-blue"}[5m]) < 
          (rate(http_requests_total{job="yemen-market-api-green"}[5m]) * 0.8)
        for: 5m
        labels:
          severity: warning
          component: deployment
          environment: production
        annotations:
          summary: "V2 throughput significantly lower than V1"
          description: "V2 throughput is {{ $value }} RPS vs V1 baseline"
          runbook_url: "https://docs.yemen-market.com/runbooks/low-throughput"

      # Database-related deployment alerts
      - alert: DeploymentDatabaseConnectionErrors
        expr: |
          rate(database_connection_errors_total{service="yemen-market-api-blue"}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
          component: database
          environment: production
        annotations:
          summary: "Database connection errors in V2 deployment"
          description: "V2 experiencing {{ $value }} database connection errors per second"
          action_required: "Check database migration and connectivity"

      - alert: DeploymentDatabaseSlowQueries
        expr: |
          histogram_quantile(0.95,
            rate(database_query_duration_seconds_bucket{service="yemen-market-api-blue"}[5m])
          ) > 5.0
        for: 3m
        labels:
          severity: warning
          component: database
          environment: production
        annotations:
          summary: "Slow database queries in V2 deployment"
          description: "V2 database queries 95th percentile: {{ $value }}s"

      # Resource utilization alerts
      - alert: DeploymentHighMemoryUsage
        expr: |
          (
            container_memory_working_set_bytes{pod=~"yemen-market-api-.*",namespace="yemen-market-v2"} /
            container_spec_memory_limit_bytes{pod=~"yemen-market-api-.*",namespace="yemen-market-v2"}
          ) > 0.9
        for: 5m
        labels:
          severity: warning
          component: resources
          environment: production
        annotations:
          summary: "High memory usage in V2 deployment"
          description: "V2 pod {{ $labels.pod }} using {{ $value | humanizePercentage }} of memory limit"

      - alert: DeploymentHighCPUUsage
        expr: |
          (
            rate(container_cpu_usage_seconds_total{pod=~"yemen-market-api-.*",namespace="yemen-market-v2"}[5m]) /
            container_spec_cpu_quota{pod=~"yemen-market-api-.*",namespace="yemen-market-v2"} * 
            container_spec_cpu_period{pod=~"yemen-market-api-.*",namespace="yemen-market-v2"}
          ) > 0.8
        for: 5m
        labels:
          severity: warning
          component: resources
          environment: production
        annotations:
          summary: "High CPU usage in V2 deployment"
          description: "V2 pod {{ $labels.pod }} using {{ $value | humanizePercentage }} of CPU limit"

      # Traffic migration alerts
      - alert: TrafficMigrationStalled
        expr: |
          (
            rate(http_requests_total{job="yemen-market-api-blue"}[10m]) /
            (rate(http_requests_total{job="yemen-market-api-blue"}[10m]) + 
             rate(http_requests_total{job="yemen-market-api-green"}[10m]))
          ) == 0.1
        for: 15m
        labels:
          severity: warning
          component: traffic-migration
          environment: production
        annotations:
          summary: "Traffic migration appears stalled at 10%"
          description: "V2 has been receiving 10% traffic for more than 15 minutes"
          action_required: "Check deployment automation or manual intervention needed"

      - alert: UnexpectedTrafficDistribution
        expr: |
          abs(
            (rate(http_requests_total{job="yemen-market-api-blue"}[5m]) /
             (rate(http_requests_total{job="yemen-market-api-blue"}[5m]) + 
              rate(http_requests_total{job="yemen-market-api-green"}[5m]))) -
            on() kube_ingress_info{ingress="yemen-market-canary-ingress",annotation_nginx_ingress_kubernetes_io_canary_weight!=""}
          ) > 0.05
        for: 2m
        labels:
          severity: warning
          component: traffic-migration
          environment: production
        annotations:
          summary: "Actual traffic distribution doesn't match configuration"
          description: "Traffic routing appears inconsistent with ingress configuration"

      # Worker deployment alerts
      - alert: WorkerDeploymentFailure
        expr: |
          kube_deployment_status_replicas{deployment="yemen-market-worker",namespace="yemen-market-v2"} !=
          kube_deployment_status_replicas_available{deployment="yemen-market-worker",namespace="yemen-market-v2"}
        for: 5m
        labels:
          severity: critical
          component: workers
          environment: production
        annotations:
          summary: "V2 worker deployment not fully available"
          description: "{{ $value }} worker replicas are not available"
          action_required: "Check worker pod status and logs"

      - alert: WorkerJobBacklog
        expr: |
          redis_list_length{key="celery",instance="redis-service.yemen-market-v2.svc.cluster.local:6379"} > 100
        for: 5m
        labels:
          severity: warning
          component: workers
          environment: production
        annotations:
          summary: "High job backlog in V2 worker queue"
          description: "{{ $value }} jobs pending in worker queue"

      # Data migration alerts
      - alert: DataMigrationFailure
        expr: |
          kube_job_status_failed{job_name=~"v1-to-v2-migration-.*",namespace="yemen-market-v2"} > 0
        for: 0m
        labels:
          severity: critical
          component: data-migration
          environment: production
        annotations:
          summary: "V1 to V2 data migration failed"
          description: "Data migration job {{ $labels.job_name }} has failed"
          action_required: "Check migration logs and data integrity"

      - alert: DataMigrationTimeout
        expr: |
          time() - kube_job_created{job_name=~"v1-to-v2-migration-.*",namespace="yemen-market-v2"} > 3600
        for: 0m
        labels:
          severity: warning
          component: data-migration
          environment: production
        annotations:
          summary: "Data migration taking longer than expected"
          description: "Migration job {{ $labels.job_name }} running for {{ $value | humanizeDuration }}"

      # External dependencies
      - alert: ExternalServiceDegradation
        expr: |
          probe_success{instance=~"https://api.worldbank.org.*"} == 0
        for: 2m
        labels:
          severity: warning
          component: external-deps
          environment: production
        annotations:
          summary: "External service unavailable during deployment"
          description: "{{ $labels.instance }} is not responding"

      # Deployment progress monitoring
      - alert: DeploymentTimeout
        expr: |
          time() - kube_deployment_created{deployment="yemen-market-api",namespace="yemen-market-v2"} > 1800
        for: 0m
        labels:
          severity: critical
          component: deployment
          environment: production
        annotations:
          summary: "Deployment taking too long"
          description: "V2 deployment has been running for {{ $value | humanizeDuration }}"
          action_required: "Check deployment status and consider rollback"

  - name: rollback.alerts
    interval: 30s
    rules:
      # Rollback-related alerts
      - alert: AutomaticRollbackTriggered
        expr: |
          increase(rollback_triggered_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
          component: rollback
          environment: production
        annotations:
          summary: "Automatic rollback has been triggered"
          description: "Deployment automation triggered rollback due to {{ $labels.reason }}"
          action_required: "Investigate rollback reason and deployment status"

      - alert: RollbackInProgress
        expr: |
          rollback_in_progress == 1
        for: 0m
        labels:
          severity: warning
          component: rollback
          environment: production
        annotations:
          summary: "Rollback currently in progress"
          description: "V2 rollback to V1 is currently executing"

      - alert: RollbackFailed
        expr: |
          rollback_status{status="failed"} == 1
        for: 0m
        labels:
          severity: critical
          component: rollback
          environment: production
        annotations:
          summary: "Rollback process failed"
          description: "Automated rollback process has failed"
          action_required: "Manual intervention required immediately"

  - name: post-deployment.alerts
    interval: 60s
    rules:
      # Post-deployment validation alerts
      - alert: PostDeploymentValidationFailed
        expr: |
          kube_job_status_failed{job_name=~"post-deployment-validation-.*",namespace="yemen-market-v2"} > 0
        for: 0m
        labels:
          severity: warning
          component: validation
          environment: production
        annotations:
          summary: "Post-deployment validation failed"
          description: "Validation job {{ $labels.job_name }} failed"
          action_required: "Review validation results and deployment quality"

      - alert: DataConsistencyIssue
        expr: |
          abs(
            yemen_market_total_records{source="v2"} - 
            yemen_market_total_records{source="v1"}
          ) / yemen_market_total_records{source="v1"} > 0.01
        for: 5m
        labels:
          severity: critical
          component: data-consistency
          environment: production
        annotations:
          summary: "Data inconsistency detected between V1 and V2"
          description: "V2 record count differs from V1 by {{ $value | humanizePercentage }}"
          action_required: "Investigate data migration integrity"